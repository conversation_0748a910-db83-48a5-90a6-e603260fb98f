/* ===== BEHAVIORAL TRIGGERS & PERSUASION ===== */
/* Based on Behavioral Psychology & Persuasion Principles */

/* Progress Indicators - Completion Motivation */
.progress-bar {
  width: 100%;
  height: 8px;
  background-color: var(--grey-200);
  border-radius: var(--border-radius-sm);
  overflow: hidden;
  margin: var(--spacing) 0;
}

.progress-bar-fill {
  height: 100%;
  background-color: var(--primary-color);
  border-radius: var(--border-radius-sm);
  transition: width 0.5s var(--transition-smooth);
}

.progress-bar-fill.success {
  background-color: var(--success-color);
}

.progress-bar-fill.warning {
  background-color: var(--warning-color);
}

.progress-bar-fill.danger {
  background-color: var(--danger-color);
}

/* Progress Steps - Visual Journey */
.progress-steps {
  display: flex;
  justify-content: space-between;
  margin: var(--spacing-lg) 0;
  position: relative;
}

.progress-steps::before {
  content: '';
  position: absolute;
  top: 14px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--grey-300);
  z-index: 1;
}

.progress-step {
  position: relative;
  z-index: 2;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  width: 100px;
}

.progress-step-indicator {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: var(--grey-300);
  color: var(--text-muted);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-semibold);
  margin-bottom: var(--spacing-xs);
  border: 2px solid var(--light-color);
  transition: var(--transition);
}

.progress-step-label {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  margin-top: var(--spacing-xs);
  transition: var(--transition);
}

.progress-step.active .progress-step-indicator {
  background-color: var(--primary-color);
  color: var(--light-color);
}

.progress-step.active .progress-step-label {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

.progress-step.completed .progress-step-indicator {
  background-color: var(--success-color);
  color: var(--light-color);
}

.progress-step.completed .progress-step-label {
  color: var(--success-color);
}

/* Achievement Badges - Recognition & Motivation */
.achievement-badge {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: var(--grey-100);
  border: 1px solid var(--grey-300);
  border-radius: var(--border-radius);
  font-size: var(--font-size-sm);
  color: var(--text-color);
  margin-right: var(--spacing-xs);
  margin-bottom: var(--spacing-xs);
  transition: var(--transition);
}

.achievement-badge:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow-sm);
}

.achievement-badge-icon {
  width: 16px;
  height: 16px;
  margin-right: var(--spacing-xs);
  color: var(--primary-color);
}

.achievement-badge.gold {
  background-color: rgba(255, 215, 0, 0.1);
  border-color: rgba(255, 215, 0, 0.3);
  color: #a67c00;
}

.achievement-badge.silver {
  background-color: rgba(192, 192, 192, 0.1);
  border-color: rgba(192, 192, 192, 0.3);
  color: #707070;
}

.achievement-badge.bronze {
  background-color: rgba(205, 127, 50, 0.1);
  border-color: rgba(205, 127, 50, 0.3);
  color: #8b4513;
}

/* Urgency Indicators - Scarcity & FOMO */
.urgency-indicator {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: rgba(var(--warning-rgb), 0.1);
  border-left: 3px solid var(--warning-color);
  color: var(--warning-dark);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  margin: var(--spacing-sm) 0;
}

.urgency-indicator-icon {
  margin-right: var(--spacing-xs);
}

.urgency-countdown {
  display: inline-flex;
  align-items: center;
  font-weight: var(--font-weight-bold);
  margin-left: var(--spacing-xs);
}

.urgency-countdown-unit {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  margin: 0 var(--spacing-xs);
}

.urgency-countdown-value {
  font-size: var(--font-size);
  font-weight: var(--font-weight-bold);
  color: var(--warning-dark);
}

.urgency-countdown-label {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
  text-transform: uppercase;
}

/* Nudges - Subtle Direction */
.nudge {
  position: relative;
  padding: var(--spacing-sm) var(--spacing);
  background-color: var(--primary-light);
  border-radius: var(--border-radius);
  color: var(--primary-dark);
  font-size: var(--font-size-sm);
  margin: var(--spacing-sm) 0;
  max-width: 300px;
}

.nudge::after {
  content: '';
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
}

.nudge.top::after {
  bottom: 100%;
  left: 50%;
  margin-left: -8px;
  border-width: 0 8px 8px 8px;
  border-color: transparent transparent var(--primary-light) transparent;
}

.nudge.bottom::after {
  top: 100%;
  left: 50%;
  margin-left: -8px;
  border-width: 8px 8px 0 8px;
  border-color: var(--primary-light) transparent transparent transparent;
}

.nudge.left::after {
  top: 50%;
  right: 100%;
  margin-top: -8px;
  border-width: 8px 8px 8px 0;
  border-color: transparent var(--primary-light) transparent transparent;
}

.nudge.right::after {
  top: 50%;
  left: 100%;
  margin-top: -8px;
  border-width: 8px 0 8px 8px;
  border-color: transparent transparent transparent var(--primary-light);
}

/* Social Proof Indicators - Trust Building */
.trust-indicator {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm);
  background-color: var(--light-color);
  border: 1px solid var(--grey-200);
  border-radius: var(--border-radius);
  margin: var(--spacing-sm) 0;
}

.trust-indicator-icon {
  color: var(--success-color);
  margin-right: var(--spacing-sm);
}

.trust-indicator-text {
  font-size: var(--font-size-sm);
  color: var(--text-color);
}

.trust-indicator-count {
  font-weight: var(--font-weight-semibold);
  color: var(--dark-color);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .progress-steps {
    flex-wrap: wrap;
    justify-content: flex-start;
    gap: var(--spacing);
  }

  .progress-steps::before {
    display: none;
  }

  .progress-step {
    width: auto;
    flex-direction: row;
    text-align: left;
    margin-right: var(--spacing-lg);
  }

  .progress-step-label {
    margin-top: 0;
    margin-left: var(--spacing-xs);
  }

  .urgency-countdown {
    flex-wrap: wrap;
  }
}
