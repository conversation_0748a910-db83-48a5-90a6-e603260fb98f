<h2>Categories</h2>

<div class="actions">
    <a href="/categories/create" class="button">Create New Category</a>
    <a href="/expenses" class="button secondary">View Expenses</a>
</div>

<?php if (isset($_SESSION['success'])) : ?>
    <div class="alert alert-success" role="alert">
        <?= htmlspecialchars($_SESSION['success']) ?>
        <?php unset($_SESSION['success']); // Clear the message after displaying it ?>
    </div>
<?php endif; ?>

<?php if (empty($categories)) : ?>
    <div class="notice">
        <p>No categories found. Create your first category to get started organizing your expenses.</p>
    </div>
<?php else : ?>
    <div class="category-grid">
        <?php foreach ($categories as $category) :
            $categoryId = htmlspecialchars($category['id']);
            $expenseCount = $category['expense_count'] ?? 0;
            $totalExpenses = $category['total_expenses'] ?? 0;
            $isInUse = $category['is_in_use'] ?? false;
            ?>
            <div class="category-card" data-category-id="<?= $categoryId ?>">
                <div class="card-header">
                    <h3 class="category-name"><?= htmlspecialchars($category['name']) ?></h3>
                </div>

                <div class="card-body">
                    <p class="description <?= empty($category['description']) ? 'empty' : '' ?>">
                        <?= !empty($category['description'])
                            ? htmlspecialchars($category['description'])
                            : 'No description' ?>
                    </p>

                    <div class="category-stats">
                        <div class="stat-item">
                            <span class="stat-label">Expenses:</span>
                            <span class="stat-value"><?= $expenseCount ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Total:</span>
                            <span class="stat-value">$<?= number_format($totalExpenses, 2) ?></span>
                        </div>
                    </div>
                </div>

                <div class="card-footer">
                    <div class="category-actions">
                        <a href="/categories/<?= $categoryId ?>/edit" class="button small" aria-label="Edit category">
                            <i class="icon-edit"></i> Edit
                        </a>

                        <?php if ($expenseCount > 0) : ?>
                            <a href="/expenses?category_id=<?= $categoryId ?>" 
                               class="button small secondary" 
                               aria-label="View category expenses">
                                <i class="icon-eye"></i> View Expenses
                            </a>
                            <a href="/categories/<?= $categoryId ?>/statistics" 
                               class="button small secondary" 
                               aria-label="View category statistics">
                                <i class="icon-chart"></i> Statistics
                            </a>
                        <?php endif; ?>

                        <form method="POST" action="/categories/<?= $categoryId ?>/delete"
                            class="inline-form delete-category-form">
                            <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($csrf_token) ?>">
                            <button type="submit" 
                                    class="button small danger" 
                                    <?= $isInUse ? 'data-has-expenses="true"' : '' ?>>
                                <i class="icon-trash"></i> Delete
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
    </div>

    <?php if (isset($expenseBreakdown) && !empty($expenseBreakdown)) :
        $total = array_sum(array_column($expenseBreakdown, 'total'));
        ?>
        <div class="category-statistics">
            <h3>Category Statistics</h3>

            <div class="stats-container">
                <div class="chart-wrapper">
                    <canvas id="categoryChart" width="300" height="300"></canvas>
                </div>

                <div class="stats-breakdown">
                    <h4>Expense Distribution</h4>
                    <ul class="distribution-list">
                        <?php foreach ($expenseBreakdown as $stat) :
                            $percentage = $total > 0 ? ($stat['total'] / $total) * 100 : 0;
                            $catId = htmlspecialchars($stat['id']);
                            ?>
                            <li class="distribution-item" data-category-id="<?= $catId ?>">
                                <div class="color-indicator"></div>
                                <span class="category-name"><?= htmlspecialchars($stat['name']) ?></span>
                                <div class="amount-info">
                                    <span class="amount">$<?= number_format($stat['total'], 2) ?></span>
                                    <span class="percentage"><?= number_format($percentage, 1) ?>%</span>
                                </div>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            </div>
        </div>

        <?php
        // Add Chart.js and categories.js to the scripts array
        $scripts ??= [];
        $scripts[] = 'https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js';
        $scripts[] = '/assets/js/pages/categories.js';
        ?>
    <?php endif; ?>
<?php endif; ?>

<!-- Category styles are now included in main.css -->
