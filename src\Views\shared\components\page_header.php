<?php

/**
 * Page header component
 * Displays a consistent page header with title and optional actions
 *
 * @param string $title The page title
 * @param string $subtitle Optional subtitle
 * @param array $actions Optional array of action buttons
 * @param string $backUrl Optional URL for a back button
 * @param string $backText Optional text for the back button
 */

// Default parameters
$title = $title ?? 'Page Title';
$subtitle = $subtitle ?? '';
$actions = $actions ?? [];
$backUrl = $backUrl ?? '';
$backText = $backText ?? 'Back';
?>

<header class="page-header">
    <div class="header-content">
        <h2><?= htmlspecialchars($title) ?></h2>
        <?php if ($subtitle) : ?>
            <p class="subtitle"><?= htmlspecialchars($subtitle) ?></p>
        <?php endif; ?>
    </div>

    <div class="header-actions">
        <?php if ($backUrl) : ?>
            <?php
            $url = $backUrl;
            $text = $backText;
            $icon = 'arrow-back';
            $type = 'secondary';
            include __DIR__ . '/action_button.php';
            ?>
        <?php endif; ?>

        <?php foreach ($actions as $action) : ?>
            <?php
            $url = $action['url'] ?? '#';
            $text = $action['text'] ?? 'Action';
            $icon = $action['icon'] ?? '';
            $type = $action['type'] ?? '';
            $size = $action['size'] ?? '';
            $title = $action['title'] ?? '';
            include __DIR__ . '/action_button.php';
            ?>
        <?php endforeach; ?>
    </div>
</header>
