<?php

/**
 * Profile index view
 * Shows user profile information in a read-only format
 */

?>

<h2>Your Profile</h2>

<?php include __DIR__ . '/components/messages.php'; ?>

<div class="profile-container">
    <div class="profile-header">
        <?php if (!empty($user['profile_image'])) : ?>
            <div class="profile-image">
                <img src="/assets/images/profiles/<?= htmlspecialchars($user['profile_image']) ?>"
                     alt="Profile image for <?= htmlspecialchars($user['name']) ?>"
                     class="profile-avatar">
            </div>
        <?php endif; ?>

        <div class="profile-info">
            <h3><?= htmlspecialchars($user['name']) ?></h3>
            <p class="profile-email"><?= htmlspecialchars($user['email']) ?></p>
        </div>
    </div>

    <div class="profile-details">
        <div class="detail-section">
            <h4>Account Information</h4>
            <div class="detail-row">
                <div class="detail-label">Member Since</div>
                <div class="detail-value"><?= date('F j, Y', strtotime($user['created_at'])) ?></div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Preferred Currency</div>
                <div class="detail-value"><?= htmlspecialchars($user['currency'] ?? 'USD') ?></div>
            </div>
            <div class="detail-row">
                <div class="detail-label">Date Format</div>
                <div class="detail-value"><?= htmlspecialchars($user['date_format'] ?? 'Y-m-d') ?></div>
            </div>
            <?php if (!empty($user['monthly_budget'])) : ?>
                <div class="detail-row">
                    <div class="detail-label">Monthly Budget</div>
                    <div class="detail-value">$<?= number_format($user['monthly_budget'], 2) ?></div>
                </div>
            <?php endif; ?>
        </div>

        <div class="detail-section">
            <h4>Login Preferences</h4>
            <ul class="notification-list">
                <li class="<?= !empty($user['remember_me_default']) ? 'enabled' : 'disabled' ?>">
                    Remember me on login
                </li>
            </ul>
        </div>

        <div class="detail-section">
            <h4>Debug Preferences</h4>
            <ul class="notification-list">
                <li class="<?= !empty($user['show_performance_metrics']) ? 'enabled' : 'disabled' ?>">
                    Show performance metrics
                </li>
            </ul>
        </div>

        <div class="detail-section">
            <h4>Notification Preferences</h4>
            <ul class="notification-list">
                <li class="<?= !empty($user['notify_weekly_summary']) ? 'enabled' : 'disabled' ?>">
                    Weekly spending summary
                </li>
                <li class="<?= !empty($user['notify_budget_alerts']) ? 'enabled' : 'disabled' ?>">
                    Budget alerts
                </li>
                <li class="<?= !empty($user['notify_recurring_expenses']) ? 'enabled' : 'disabled' ?>">
                    Recurring expense reminders
                </li>
            </ul>
        </div>
    </div>

    <div class="profile-actions">
        <a href="/profile/edit" class="button primary">Edit Profile</a>
        <a href="/profile/password" class="button secondary">Change Password</a>
    </div>
</div>

<script>
// Simple script to log session information for debugging
document.addEventListener('DOMContentLoaded', function() {
    console.log("Profile page loaded successfully");
});
</script>

