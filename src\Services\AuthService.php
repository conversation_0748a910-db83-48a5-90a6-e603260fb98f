<?php

declare(strict_types=1);

namespace App\Services;

use App\Auth\Authenticator;
use App\Core\Security\Session;
use App\Exceptions\InvalidArgumentException;
use App\Services\EmailService;

class AuthService
{
    public function __construct(
        private readonly Authenticator $auth,
        private readonly UserService $userService,
        private readonly ?EmailService $emailService = null
    ) {
    }

    public function login(string $email, string $password, bool $remember = false): bool
    {
        $success = $this->auth->attemptLogin($email, $password, $remember);

        if ($success) {
            if ($remember) {
                // If remember me is checked, create a persistent token
                $user = $this->userService->getUserByEmail($email);
                if ($user) {
                    $this->userService->createRememberToken($user['id']);

                    // Set session cookie to persist for 30 days
                    $params = session_get_cookie_params();
                    setcookie(
                        session_name(),
                        session_id(),
                        [
                            'expires' => time() + 86400 * 30,
                            'path' => $params['path'],
                            'domain' => $params['domain'],
                            'secure' => $params['secure'],
                            'httponly' => $params['httponly'],
                            'samesite' => $params['samesite'] ?? 'Lax'
                        ]
                    );

                    // Set a flag in the session to indicate it should persist
                    $_SESSION['remember_me'] = true;
                    // Set the last activity timestamp
                    $_SESSION['last_activity'] = time();

                    // The token is created in the Authenticator class and in AuthController
                    // We don't need to create it again here
                }
            } else {
                // If remember me is not checked, ensure session expires when browser closes
                // Delete any existing remember tokens for this user
                $user = $this->userService->getUserByEmail($email);
                if ($user) {
                    $this->userService->deleteRememberTokensByUserId($user['id']);
                }

                // Set the remember_me flag to false to prevent auto-login
                $_SESSION['remember_me'] = false;
                // Set the last activity timestamp
                $_SESSION['last_activity'] = time();

                // Explicitly set the session cookie to expire when browser closes
                $params = session_get_cookie_params();
                setcookie(
                    session_name(),
                    session_id(),
                    [
                        'expires' => 0, // 0 means "until the browser is closed"
                        'path' => $params['path'],
                        'domain' => $params['domain'],
                        'secure' => $params['secure'],
                        'httponly' => $params['httponly'],
                        'samesite' => $params['samesite'] ?? 'Lax'
                    ]
                );

                // Remove any remember token cookie
                if (isset($_COOKIE['remember_token'])) {
                    setcookie(
                        'remember_token',
                        '',
                        [
                            'expires' => time() - 3600,
                            'path' => '/',
                            'domain' => '',
                            'secure' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on',
                            'httponly' => true,
                            'samesite' => 'Lax'
                        ]
                    );
                }
            }
        }

        return $success;
    }

    public function logout(): void
    {
        // Get the current user ID before logout
        $userId = $this->auth->getCurrentUserId();
        if ($userId) {
            $this->userService->deleteRememberTokensByUserId($userId);
        }

        $this->auth->logout();
    }

    public function isAuthenticated(): bool
    {
        return $this->auth->isAuthenticated();
    }

    public function register(string $name, string $email, string $password, string $passwordConfirm): bool
    {
        if ($password !== $passwordConfirm) {
            throw new InvalidArgumentException('Passwords do not match');
        }

        return $this->userService->registerUser($name, $email, $password);
    }

    public function requestPasswordReset(string $email): bool
    {
        $result = $this->auth->requestPasswordReset($email);

        if ($result && $this->emailService) {
            $user = $this->userService->getUserByEmail($email);
            if ($user && isset($user['reset_token'])) {
                $resetUrl = $this->getResetUrl($user['reset_token']);
                $this->sendPasswordResetEmail($email, $resetUrl);
            }
        }

        return $result;
    }

    public function validateResetToken(string $token): ?array
    {
        return $this->auth->validateResetToken($token);
    }

    /**
     * Validate a remember token and return the token data if valid
     *
     * @param string $token The remember token to validate
     * @return array|null The token data if valid, null otherwise
     */
    public function validateRememberToken(string $token): ?array
    {
        // Validate token format to prevent SQL injection
        if (!preg_match('/^[a-f0-9]+$/i', $token)) {
            return null;
        }

        return \App\Models\User::findRememberToken($token);
    }

    public function resetPassword(int $userId, string $password): bool
    {
        return $this->auth->resetPassword($userId, $password);
    }

    public function validatePasswordRules(string $password): array
    {
        $errors = [];

        if (strlen($password) < 8) {
            $errors[] = 'Password must be at least 8 characters';
        }

        if (!preg_match('/\d/', $password)) {
            $errors[] = 'Password must contain at least one digit';
        }

        if (!preg_match('/[^a-zA-Z\d]/', $password)) {
            $errors[] = 'Password must contain at least one special character';
        }

        return $errors;
    }

    private function getResetUrl(string $token): string
    {
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        return "$protocol://$host/password/new?token=" . urlencode($token);
    }

    private function sendPasswordResetEmail(string $email, string $resetUrl): void
    {
        if (!$this->emailService) {
            return;
        }

        $subject = 'Password Reset Request';
        $message = "Click the link below to reset your password:\n\n$resetUrl\n\nThis link will expire in 24 hours.";
        $this->emailService->send($email, $subject, $message);
    }
}
