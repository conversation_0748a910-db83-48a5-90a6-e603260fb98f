<?php
/**
 * Filter controls partial for expenses
 * Renders horizontal filter bar with date range, category multi-select, amount range, and search filters.
 *
 * @var array $categories Array of category objects with 'id' and 'name' keys
 * @var array $filters Associative array of current filter values (e.g. ['category_ids' => ..., 'date_from' => ..., 'date_to' => ..., 'minAmount' => ..., 'maxAmount' => ..., 'search' => ...])
 */

// Prepare selected category IDs array
$selectedCategoryIds = [];
if (isset($filters['category_ids'])) {
    if (is_array($filters['category_ids'])) {
        $selectedCategoryIds = $filters['category_ids'];
    } elseif (!empty($filters['category_ids'])) {
        $selectedCategoryIds = explode(',', $filters['category_ids']);
    }
}
// Legacy support for single category_id
if (empty($selectedCategoryIds) && !empty($filters['category_id'])) {
    $selectedCategoryIds = [$filters['category_id']];
}
?>

<div class="filter-bar-container container">
<div class="filter-bar" aria-label="Expense filters">
    <div class="filter-bar__mobile-toggle">
        <button type="button" class="mobile-filter-toggle" aria-expanded="false" aria-controls="filter-controls">
            <span class="mobile-filter-toggle__icon">🔍</span>
            <span class="mobile-filter-toggle__text">Filters</span>
        </button>
    </div>
    
    <div id="filter-controls" class="filter-bar__controls u-flex u-flex-wrap-md">
    <!-- Date Range Filter -->
    <div class="filter-item filter-item--date">
        <label class="filter-item__label">Date Range</label>
        <div class="filter-item__content">
            <button type="button" 
                    class="filter-dropdown__toggle" 
                    aria-expanded="false" 
                    aria-label="Select date range"
                    data-filter="date">
                <span class="filter-dropdown__text">
                    <?php 
                    $dateText = 'All Dates';
                    if (!empty($filters['date_preset'])) {
                        $presets = [
                            'today' => 'Today',
                            'yesterday' => 'Yesterday', 
                            'this_week' => 'This Week',
                            'last_week' => 'Last Week',
                            'this_month' => 'This Month',
                            'last_30_days' => 'Last 30 Days',
                            'last_month' => 'Last Month',
                            'last_3_months' => 'Last 3 Months',
                            'this_year' => 'This Year',
                            'last_year' => 'Last Year'
                        ];
                        $dateText = $presets[$filters['date_preset']] ?? 'Custom Range';
                    } elseif (!empty($filters['date_from']) || !empty($filters['date_to'])) {
                        $dateText = 'Custom Range';
                    }
                    echo htmlspecialchars($dateText);
                    ?>
                </span>
                <span class="filter-dropdown__arrow" aria-hidden="true">▼</span>
            </button>
            <div class="filter-dropdown__panel" role="menu" aria-label="Date range options">
                <div class="date-presets">
                    <div class="date-presets__group">
                        <h4 class="date-presets__heading">Quick Filters</h4>
                        <button type="button" class="date-preset date-preset--primary" data-preset="last_30_days" role="menuitem">Last 30 Days</button>
                        <button type="button" class="date-preset date-preset--primary" data-preset="last_3_months" role="menuitem">Last 3 Months</button>
                        <button type="button" class="date-preset date-preset--primary" data-preset="this_year" role="menuitem">This Year</button>
                    </div>
                    <div class="date-presets__group">
                        <h4 class="date-presets__heading">Other Periods</h4>
                        <button type="button" class="date-preset" data-preset="today" role="menuitem">Today</button>
                        <button type="button" class="date-preset" data-preset="yesterday" role="menuitem">Yesterday</button>
                        <button type="button" class="date-preset" data-preset="this_week" role="menuitem">This Week</button>
                        <button type="button" class="date-preset" data-preset="last_week" role="menuitem">Last Week</button>
                        <button type="button" class="date-preset" data-preset="this_month" role="menuitem">This Month</button>
                        <button type="button" class="date-preset" data-preset="last_month" role="menuitem">Last Month</button>
                        <button type="button" class="date-preset" data-preset="last_year" role="menuitem">Last Year</button>
                    </div>
                </div>
                <div class="date-custom">
                    <label class="date-custom__label">Custom Range</label>
                    <div class="date-custom__inputs">
                        <input type="date" 
                               name="date_from" 
                               id="date_from"
                               value="<?= htmlspecialchars($filters['date_from'] ?? '') ?>"
                               aria-label="Start date"
                               class="filter-input filter-input--date">
                        <span class="date-separator">to</span>
                        <input type="date" 
                               name="date_to" 
                               id="date_to"
                               value="<?= htmlspecialchars($filters['date_to'] ?? '') ?>"
                               aria-label="End date"
                               class="filter-input filter-input--date">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Category Multi-Select Filter -->
    <div class="filter-item filter-item--category">
        <label class="filter-item__label">Categories</label>
        <div class="filter-item__content">
            <button type="button" 
                    class="filter-dropdown__toggle" 
                    aria-expanded="false" 
                    aria-label="Select categories"
                    data-filter="category">
                <span class="filter-dropdown__text">
                    <?php if (empty($selectedCategoryIds)) : ?>
                        All Categories
                    <?php else : ?>
                        <?= count($selectedCategoryIds) ?> selected
                    <?php endif; ?>
                </span>
                <span class="filter-dropdown__arrow" aria-hidden="true">▼</span>
            </button>
            <div class="filter-dropdown__panel" role="menu" aria-label="Category options">
                <div class="category-options">
                    <?php foreach ($categories as $category) : ?>
                        <?php
                        $isSelected = in_array($category['id'], $selectedCategoryIds);
                        $categoryCount = $category['expense_count'] ?? 0;
                        $categoryColor = $category['color'] ?? '#6c757d';
                        ?>
                        <label class="category-option" role="menuitemcheckbox" aria-checked="<?= $isSelected ? 'true' : 'false' ?>">
                            <input type="checkbox" 
                                   name="category_ids[]" 
                                   value="<?= htmlspecialchars($category['id']) ?>"
                                   <?= $isSelected ? 'checked' : '' ?>
                                   aria-label="Filter by <?= htmlspecialchars($category['name']) ?> category"
                                   class="category-option__checkbox">
                            <span class="category-option__indicator">
                                <span class="category-option__color" style="background-color: <?= htmlspecialchars($categoryColor) ?>"></span>
                                <span class="category-option__checkmark" aria-hidden="true">✓</span>
                            </span>
                            <span class="category-option__name"><?= htmlspecialchars($category['name']) ?></span>
                            <span class="category-option__count">(<?= $categoryCount ?>)</span>
                        </label>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Amount Range Filter -->
    <div class="filter-item filter-item--amount">
        <label class="filter-item__label">Amount Range</label>
        <div class="filter-item__content">
            <button type="button" 
                    class="filter-dropdown__toggle" 
                    aria-expanded="false" 
                    aria-label="Select amount range"
                    data-filter="amount">
                <span class="filter-dropdown__text">
                    <?php 
                    $amountText = 'All Amounts';
                    $minAmount = $filters['minAmount'] ?? '';
                    $maxAmount = $filters['maxAmount'] ?? '';
                    if (!empty($minAmount) || !empty($maxAmount)) {
                        if (!empty($minAmount) && !empty($maxAmount)) {
                            $amountText = '$' . number_format($minAmount, 2) . ' - $' . number_format($maxAmount, 2);
                        } elseif (!empty($minAmount)) {
                            $amountText = '$' . number_format($minAmount, 2) . '+';
                        } else {
                            $amountText = 'Up to $' . number_format($maxAmount, 2);
                        }
                    }
                    echo htmlspecialchars($amountText);
                    ?>
                </span>
                <span class="filter-dropdown__arrow" aria-hidden="true">▼</span>
            </button>
            <div class="filter-dropdown__panel" role="menu" aria-label="Amount range options">
                <div class="amount-slider-container">
                    <div class="amount-slider" 
                         data-min="0" 
                         data-max="1000" 
                         data-step="10"
                         data-value-min="<?= htmlspecialchars($filters['minAmount'] ?? '0') ?>"
                         data-value-max="<?= htmlspecialchars($filters['maxAmount'] ?? '1000') ?>">
                        <div class="amount-slider__track"></div>
                        <div class="amount-slider__range"></div>
                        <div class="amount-slider__handle amount-slider__handle--min" role="slider" aria-label="Minimum amount" tabindex="0"></div>
                        <div class="amount-slider__handle amount-slider__handle--max" role="slider" aria-label="Maximum amount" tabindex="0"></div>
                    </div>
                    <div class="amount-inputs">
                        <input type="number" 
                               name="minAmount" 
                               id="min_amount"
                               value="<?= htmlspecialchars($filters['minAmount'] ?? '') ?>"
                               step="0.01"
                               placeholder="Min"
                               aria-label="Minimum amount"
                               class="filter-input filter-input--amount">
                        <span class="amount-separator">to</span>
                        <input type="number" 
                               name="maxAmount" 
                               id="max_amount"
                               value="<?= htmlspecialchars($filters['maxAmount'] ?? '') ?>"
                               step="0.01"
                               placeholder="Max"
                               aria-label="Maximum amount"
                               class="filter-input filter-input--amount">
                    </div>
                    <div class="amount-presets">
                        <button type="button" class="amount-preset" data-min="0" data-max="50">Under $50</button>
                        <button type="button" class="amount-preset" data-min="50" data-max="200">$50-$200</button>
                        <button type="button" class="amount-preset" data-min="200" data-max="">Over $200</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Filter -->
    <div class="filter-item filter-item--search">
        <label class="filter-item__label" for="search">Search</label>
        <div class="filter-item__content">
            <div class="search-input-container">
                <span class="search-input__icon" aria-hidden="true">🔍</span>
                <input type="text" 
                       name="search" 
                       id="search"
                       value="<?= htmlspecialchars($filters['search'] ?? '') ?>"
                       placeholder="Search expenses..."
                       aria-label="Search expenses by description or notes"
                       class="filter-input filter-input--search"
                       data-debounce="300">
                <button type="button" 
                        class="search-input__clear u-margin-end-sm" 
                        aria-label="Clear search"
                        style="<?= empty($filters['search']) ? 'display: none;' : '' ?>">×</button>
            </div>
        </div>
    </div>

    <!-- Actions Area -->
    <div class="filter-item filter-item--actions">
        <div id="active-filters" class="active-filters" aria-label="Active filters"></div>
        <button type="button" 
                class="btn btn-secondary filter-clear-all" 
                aria-label="Clear all filters">
            Clear All
        </button>
    </div>
    </div><!-- End of filter-bar__controls -->
</div><!-- End of filter-bar -->
</div><!-- End of filter-bar-container -->

<div class="filter-summary">
    <div id="results-counter" class="results-counter" aria-live="polite"></div>
    <div id="filter-breadcrumb" class="filter-breadcrumb" aria-label="Applied filters summary"></div>
</div>
