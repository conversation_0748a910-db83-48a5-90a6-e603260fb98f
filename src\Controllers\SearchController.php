<?php

declare(strict_types=1);

namespace App\Controllers;

use App\Core\Response;
use App\Core\LogManager;
use App\Services\ExpenseManagerService;
use App\Services\CategoryService;

class SearchController extends BaseController
{
    private const ITEMS_PER_PAGE = 20;

    public function __construct(
        private readonly ExpenseManagerService $expenseManager,
        private readonly CategoryService $categoryService
    ) {
    }

    public function index(): Response
    {
        try {
            $userId = $this->getUserId();
            $query = $this->getQueryValue('query', '');

            if (empty($query)) {
                return $this->showSearchForm();
            }

            return $this->performSearch($userId);
        } catch (\Exception $e) {
            return $this->handleSearchError($e);
        }
    }

    private function showSearchForm(): Response
    {
        $categories = $this->categoryService->getAllCategories();
        return $this->view('search/index', [
            'categories' => $categories
        ]);
    }

    private function performSearch(int $userId): Response
    {
        $searchParams = $this->extractSearchParams();
        $viewData = $this->buildSearchViewData($userId, $searchParams);
        return $this->view('search/results', $viewData);
    }

    private function handleSearchError(\Exception $e): Response
    {
        LogManager::logException($e, "Error during search");
        return $this->redirect('/dashboard', 'An error occurred during search', 'error');
    }

    private function extractSearchParams(): array
    {
        return [
            'query' => $this->getQueryValue('query', ''),
            'dateFrom' => $this->getQueryValue('date_from', ''),
            'dateTo' => $this->getQueryValue('date_to', ''),
            'categoryId' => $this->getQueryInt('category_id'),
            'minAmount' => $this->getQueryFloat('min_amount'),
            'maxAmount' => $this->getQueryFloat('max_amount'),
            'hasDocument' => $this->getQueryValue('has_document', ''),
            'page' => $this->getQueryInt('page', 1)
        ];
    }

    private function buildSearchViewData(int $userId, array $searchParams): array
    {
        [$expenses, $totalCount] = $this->executeSearch($userId, $searchParams);
        $categories = $this->categoryService->getAllCategories();

        return [
            'expenses' => $expenses,
            'categories' => $categories,
            'query' => $searchParams['query'],
            'filters' => $this->buildFiltersArray($searchParams),
            'categoryName' => $this->getCategoryName($categories, $searchParams['categoryId']),
            'pagination' => $this->buildPagination($searchParams['page'], $totalCount)
        ];
    }

    private function executeSearch(int $userId, array $params): array
    {
        $params['itemsPerPage'] = self::ITEMS_PER_PAGE;
        return $this->expenseManager->searchExpenses($userId, $params);
    }

    private function buildFiltersArray(array $params): array
    {
        $filters = [];

        $this->addDateFilters($filters, $params);
        $this->addAmountFilters($filters, $params);
        $this->addDocumentFilter($filters, $params);

        return $filters;
    }

    private function addDateFilters(array &$filters, array $params): void
    {
        if (!empty($params['dateFrom'])) {
            $filters[] = "From: " . $params['dateFrom'];
        }

        if (!empty($params['dateTo'])) {
            $filters[] = "To: " . $params['dateTo'];
        }
    }

    private function addAmountFilters(array &$filters, array $params): void
    {
        if (!empty($params['minAmount'])) {
            $filters[] = "Min Amount: $" . number_format($params['minAmount'], 2);
        }

        if (!empty($params['maxAmount'])) {
            $filters[] = "Max Amount: $" . number_format($params['maxAmount'], 2);
        }
    }

    private function addDocumentFilter(array &$filters, array $params): void
    {
        if ($params['hasDocument'] === '1') {
            $filters[] = "Has Document: Yes";
        } elseif ($params['hasDocument'] === '0') {
            $filters[] = "Has Document: No";
        }
    }

    private function getCategoryName(array $categories, int $categoryId): string
    {
        if ($categoryId <= 0) {
            return '';
        }

        return $this->findCategoryNameById($categories, $categoryId);
    }

    private function findCategoryNameById(array $categories, int $categoryId): string
    {
        foreach ($categories as $category) {
            if ($category['id'] == $categoryId) {
                return $category['name'];
            }
        }

        return '';
    }

    private function buildPagination(int $currentPage, int $totalCount): array
    {
        $totalPages = ceil($totalCount / self::ITEMS_PER_PAGE);

        return [
            'currentPage' => $currentPage,
            'totalPages' => $totalPages,
            'totalItems' => $totalCount,
            'itemsPerPage' => self::ITEMS_PER_PAGE
        ];
    }
}
