/* ===== CHECKBOX FIX ===== */
/* This file fixes the conflict between _inputs-extended.css and _checkbox-radio.css */

/* Override the hidden checkbox styles */
.checkbox-form-group input[type="checkbox"] {
  position: static !important;
  opacity: 1 !important;
  width: 18px !important;
  height: 18px !important;
  margin-right: 8px !important;
  cursor: pointer !important;
  accent-color: var(--primary-color) !important;
}

/* Remove the pseudo-elements that are used for custom styling */
.checkbox-form-group input[type="checkbox"] + label:before,
.checkbox-form-group input[type="checkbox"] + label:after {
  display: none !important;
}

/* Style the label */
.checkbox-form-group input[type="checkbox"] + label {
  padding-left: 0 !important;
  cursor: pointer !important;
  display: inline-block !important;
  line-height: 1.5 !important;
  font-size: var(--font-size) !important;
  user-select: none !important;
}

/* Style the checkbox container */
.checkbox-form-group {
  display: flex !important;
  align-items: center !important;
  flex-wrap: wrap !important;
}

/* Style the help text for checkboxes */
.checkbox-form-group .form-hint {
  flex-basis: 100% !important;
  margin-left: 26px !important;
  margin-top: 4px !important;
}
