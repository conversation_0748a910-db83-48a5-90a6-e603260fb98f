<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <system.webServer>
        <rewrite>
            <rules>
                <!-- Handle favicon.ico directly -->
                <rule name="Favicon" stopProcessing="true">
                    <match url="^favicon\.ico$" />
                    <action type="Rewrite" url="public/favicon.ico" />
                </rule>
                
                <!-- Handle static files in public directory -->
                <rule name="Static Assets" stopProcessing="true">
                    <match url="^assets/(.*)" />
                    <action type="Rewrite" url="public/assets/{R:1}" />
                </rule>
                
                <!-- Redirect all other requests to index.php -->
                <rule name="Front Controller" stopProcessing="true">
                    <match url="^(.*)$" />
                    <conditions>
                        <add input="{REQUEST_FILENAME}" matchType="IsFile" negate="true" />
                        <add input="{REQUEST_FILENAME}" matchType="IsDirectory" negate="true" />
                    </conditions>
                    <action type="Rewrite" url="public/index.php" />
                </rule>
            </rules>
        </rewrite>
        
        <!-- Set cache control for static assets -->
        <staticContent>
            <clientCache cacheControlMode="UseMaxAge" cacheControlMaxAge="365.00:00:00" />
            <mimeMap fileExtension=".ico" mimeType="image/x-icon" />
        </staticContent>
    </system.webServer>
</configuration>
