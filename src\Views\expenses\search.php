<?php
/**
 * Search results view for expenses
 * Used by the search() controller action
 *
 * @var string $query Search query string
 * @var array $categories Available categories for filtering
 * @var array $filters Active filters array
 * @var array $expenses Array of expense records
 * @var array $field Form field configuration array
 * @var array $documentOptions Document status filter options
 * @var array $categoryOptions Category filter options
 */

// Include filter controls partial wrapped in form
$categories = $categories ?? [];
$filters = $filters ?? [];

// Prepare form content for filter controls
ob_start();
include __DIR__ . '/partials/_filter_controls.php';
$filterContent = ob_get_clean();

// Render form using shared form component
$action = '/expenses/search';
$method = 'GET';
$class = 'filter-form';
$content = $filterContent;
$submitText = null; // No submit button needed since filters auto-submit
$cancelUrl = null; // No cancel button needed
$hideFormActions = true;
include __DIR__ . '/../shared/form.php';

// Include shared helper functions
include_once __DIR__ . '/../shared/helpers.php';

// Include header partial
include __DIR__ . '/partials/_header.php';

// Include messages component
include __DIR__ . '/../shared/messages.php';
?>

<section class="expenses-overview container u-spacing-stack-lg">
    <h1 class="page-title">Search Expenses</h1>
    
    <!-- Hidden CSRF token for JavaScript operations -->
    <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($csrf_token ?? '') ?>">
    
    <!-- Results counter and active filters -->
    <div id="results-counter"></div>
    <div id="active-filters"></div>
    
    <?php include __DIR__ . '/partials/_empty_state.php'; ?>
    
    <?php include __DIR__ . '/partials/_list_table.php'; ?>
    
    <?php include __DIR__ . '/partials/_pagination.php'; ?>
</section>

<script>
// Inject filter data for JavaScript filtering
window.expenseFilterData = {
    totalCount: <?= json_encode($totalCount ?? 0) ?>,
    categoryCounts: <?= json_encode($categoryCounts ?? []) ?>,
    amountBounds: <?= json_encode($amountBounds ?? ['min' => 0, 'max' => 0]) ?>
};
</script>

<?php
// Add expenses-specific scripts
$scripts ??= [];
$scripts[] = '/assets/js/pages/expenses-search.js';
?>
