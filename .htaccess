# Enable URL rewriting
RewriteEngine On

# Handle favicon.ico directly
RewriteRule ^favicon\.ico$ public/favicon.ico [L]

# Handle static files in public directory
RewriteRule ^(assets/.*)$ public/$1 [L]

# Redirect all other requests to index.php (front controller)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ public/index.php [QSA,L]

# Prevent directory listing
Options -Indexes

# Set default charset
AddDefaultCharset UTF-8

# Protect sensitive files
<FilesMatch "\.(env|htaccess|log)$">
    Require all denied
</FilesMatch>

# Disable ETags (improves caching)
FileETag None

# Set cache control for static assets
<FilesMatch "\.(css|js|jpg|jpeg|png|gif|ico|svg)$">
    Header set Cache-Control "max-age=31536000, public"
</FilesMatch>

#ngrok http --host-header=pet.local 80