<?php
// _table_row.php
// Renders a single expense table row.
// Expects:
//   - $expense: array with keys 'id', 'date', 'description', 'category_name', 'amount'

// Helper function to safely escape values
if (!function_exists('e')) {
    function e($value)
    {
        if (is_null($value)) {
            return '';
        }
        if (is_array($value)) {
            return '';
        }
        if (is_object($value) && !method_exists($value, '__toString')) {
            return '';
        }
        return htmlspecialchars((string)$value, ENT_QUOTES, 'UTF-8');
    }
}

$id = e($expense['id'] ?? '');
$date = e($expense['date'] ?? '');
$description = e($expense['description'] ?? '');

// Debug expense data
error_log("[Debug] _table_row.php - Expense ID: {$id}");
error_log("[Debug] _table_row.php - Expense data: " . json_encode([
    'category_id' => $expense['category_id'] ?? 'not set',
    'user_category_id' => $expense['user_category_id'] ?? 'not set',
    'category_name' => $expense['category_name'] ?? 'not set',
    'user_category_name' => $expense['user_category_name'] ?? 'not set'
]));

// Use the category_name that was already determined in ExpenseManagerService
$categoryName = $expense['category_name'] ?? 'Uncategorized';
error_log("[Debug] _table_row.php - Using category_name: " . $categoryName);
$categoryLabel = e($categoryName);

// Debug final category name
error_log("[Debug] _table_row.php - Final category name: {$categoryLabel}");
$amountValue = (float)($expense['amount'] ?? 0);
$amountClass = $amountValue < 0 ? 'negative' : 'positive';
$formattedDate = formatDate($expense['date'] ?? '');
$formattedAmount = formatCurrency($amountValue);
?>
<tr data-expense-id="<?= $id ?>" class="expense-row" style="transition: var(--hover-lift);">
    <td class="select-row-column" data-label="Select">
        <input type="checkbox" class="row-selector" data-expense-id="<?= $id ?>" aria-label="Select expense <?= $id ?>">
    </td>
    <td class="date-column" data-label="Date">
        <time datetime="<?= $date ?>">
            <?= $formattedDate ?>
        </time>
    </td>
    <td class="description-column" data-label="Description">
        <?= $description ?>
    </td>
    <td class="category-column" data-label="Category">
        <?php
        $bgColor = getCategoryColorFreeform(e($categoryName));
        $fgColor = getContrastTextColor($bgColor);
        ?>
        <span class="badge" style="background-color: <?= $bgColor ?>; color: <?= $fgColor ?>;">
            <?= $categoryLabel ?>
        </span>
    </td>
    <td class="amount-column" data-label="Amount" style="text-align: right; font-family: var(--font-mono, monospace);">
        <span class="amount <?= $amountClass ?>">
            <?= $formattedAmount ?>
        </span>
    </td>
    <td class="actions-column" data-label="Actions">
        <?php
        // Define menu items array
        $menuItems = [
            ['key' => 'view', 'label' => 'View',   'url' => "/expenses/{$id}"],
            ['key' => 'select', 'label' => 'Select this row', 'url' => null],
            ['key' => 'edit', 'label' => 'Edit',     'url' => "/expenses/{$id}/edit"],
            ['key' => 'delete', 'label' => 'Delete', 'url' => "/expenses/{$id}/delete"]
        ];

        // Render actions button
        $url   = "/expenses/{$id}";
        $text  = '';
        $icon  = 'more-vert';
        $size  = 'small';
        $type  = 'secondary';
        $title = 'Actions';
        include __DIR__ . '/../../shared/components/action_button.php';
        ?>
    </td>
</tr>
