<h2>Edit Profile</h2>

<div class="form-container">
    <form method="POST" action="/profile" enctype="multipart/form-data" id="profile-form">
        <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($csrf_token) ?>">

        <?php include __DIR__ . '/components/messages.php'; ?>

        <!-- Name Field -->
        <?php
        $nameField = [
            'type' => 'text',
            'name' => 'name',
            'label' => 'Full Name',
            'value' => $_SESSION['old']['name'] ?? $user['name'] ?? '',
            'required' => true
        ];
        extract($nameField);
        include __DIR__ . '/components/form_field.php';
        ?>

        <!-- Email Field -->
        <?php
        $emailField = [
            'type' => 'email',
            'name' => 'email',
            'label' => 'Email Address',
            'value' => $_SESSION['old']['email'] ?? $user['email'] ?? '',
            'required' => true
        ];
        extract($emailField);
        include __DIR__ . '/components/form_field.php';
        ?>

        <!-- Currency Preference -->
        <?php
        $currencies = [
            'USD' => 'US Dollar ($)',
            'EUR' => 'Euro (€)',
            'GBP' => 'British Pound (£)',
            'JPY' => 'Japanese Yen (¥)',
            'CAD' => 'Canadian Dollar (C$)',
            'AUD' => 'Australian Dollar (A$)',
        ];

        $currencyField = [
            'type' => 'select',
            'name' => 'currency',
            'label' => 'Preferred Currency',
            'value' => $_SESSION['old']['currency'] ?? $user['currency'] ?? 'USD',
            'options' => $currencies
        ];
        extract($currencyField);
        include __DIR__ . '/components/form_field.php';
        ?>

        <!-- Date Format Preference -->
        <?php
        $formats = [
            'Y-m-d' => date('Y-m-d') . ' (YYYY-MM-DD)',
            'm/d/Y' => date('m/d/Y') . ' (MM/DD/YYYY)',
            'd/m/Y' => date('d/m/Y') . ' (DD/MM/YYYY)',
            'M d, Y' => date('M d, Y') . ' (Month D, YYYY)',
        ];

        $dateFormatField = [
            'type' => 'select',
            'name' => 'date_format',
            'label' => 'Date Format',
            'value' => $_SESSION['old']['date_format'] ?? $user['date_format'] ?? 'Y-m-d',
            'options' => $formats
        ];
        extract($dateFormatField);
        include __DIR__ . '/components/form_field.php';
        ?>

        <!-- Monthly Budget Field -->
        <?php
        $budgetField = [
            'type' => 'number',
            'name' => 'monthly_budget',
            'label' => 'Monthly Budget',
            'value' => $_SESSION['old']['monthly_budget'] ?? $user['monthly_budget'] ?? '',
            'prepend' => '$',
            'attributes' => ['step' => '0.01', 'min' => '0'],
            'help' => 'Set your target monthly spending limit (optional)'
        ];
        extract($budgetField);
        include __DIR__ . '/components/form_field.php';
        ?>

        <!-- Profile Image Field -->
        <div class="form-group">
            <label for="profile_image">Profile Image</label>
            <?php if (!empty($user['profile_image'])) : ?>
                <div class="current-image">
                    <img src="/assets/images/profiles/<?= htmlspecialchars($user['profile_image']) ?>"
                         alt="Current profile image"
                         class="profile-thumbnail">
                    <div class="image-actions">
                        <label for="remove_image" class="checkbox-label">
                            <input type="checkbox" name="remove_image" id="remove_image" value="1">
                            Remove current image
                        </label>
                    </div>
                </div>
            <?php endif; ?>

            <input type="file" name="profile_image" id="profile_image" class="file-input" accept="image/*">
            <div class="file-info">
                Supported formats: JPG, PNG, GIF. Max size: 2MB.
            </div>
        </div>

        <!-- Login Preferences -->
        <h4>Login Preferences</h4>

        <?php
        // Debug output
        error_log("profile/edit.php - user remember_me_default: " . (isset($user['remember_me_default']) ? $user['remember_me_default'] : 'not set'));
        error_log("profile/edit.php - session old remember_me_default: " . (isset($_SESSION['old']['remember_me_default']) ? $_SESSION['old']['remember_me_default'] : 'not set'));

        $rememberMeValue = !empty($user['remember_me_default']) || !empty($_SESSION['old']['remember_me_default']);
        error_log("profile/edit.php - calculated checkbox value: " . ($rememberMeValue ? 'checked' : 'unchecked'));

        $rememberMeField = [
            'type' => 'checkbox',
            'name' => 'remember_me_default',
            'id' => 'remember_me_default',
            'label' => 'Remember me on login',
            'value' => $rememberMeValue,
            'help' => 'When checked, the "Remember me" option will be pre-selected on the login page',
            'attributes' => []
        ];
        extract($rememberMeField);
        include __DIR__ . '/components/form_field.php';
        ?>

        <!-- Debug Preferences -->
        <h4>Debug Preferences</h4>

        <?php
        $showPerformanceValue = !empty($user['show_performance_metrics']) || !empty($_SESSION['old']['show_performance_metrics']);
        error_log("profile/edit.php - calculated show_performance_metrics value: " . ($showPerformanceValue ? 'checked' : 'unchecked'));

        $performanceMetricsField = [
            'type' => 'checkbox',
            'name' => 'show_performance_metrics',
            'id' => 'show_performance_metrics',
            'label' => 'Show performance metrics',
            'value' => $showPerformanceValue,
            'help' => 'When checked, performance metrics will be displayed at the bottom of pages',
            'attributes' => []
        ];
        extract($performanceMetricsField);
        include __DIR__ . '/components/form_field.php';
        ?>

        <!-- Notification Preferences -->
        <h4>Notification Preferences</h4>

        <?php
        $weeklySummaryField = [
            'type' => 'checkbox',
            'name' => 'notify_weekly_summary',
            'id' => 'notify_weekly_summary',
            'label' => 'Weekly spending summary',
            'value' => !empty($user['notify_weekly_summary']) || !empty($_SESSION['old']['notify_weekly_summary']),
            'help' => 'Receive a weekly email summary of your spending',
            'attributes' => []
        ];
        extract($weeklySummaryField);
        include __DIR__ . '/components/form_field.php';

        $budgetAlertsField = [
            'type' => 'checkbox',
            'name' => 'notify_budget_alerts',
            'id' => 'notify_budget_alerts',
            'label' => 'Budget alerts',
            'value' => !empty($user['notify_budget_alerts']) || !empty($_SESSION['old']['notify_budget_alerts']),
            'help' => 'Get notified when you approach your monthly budget limit',
            'attributes' => []
        ];
        extract($budgetAlertsField);
        include __DIR__ . '/components/form_field.php';

        $recurringExpensesField = [
            'type' => 'checkbox',
            'name' => 'notify_recurring_expenses',
            'id' => 'notify_recurring_expenses',
            'label' => 'Recurring expense reminders',
            'value' => !empty($user['notify_recurring_expenses']) ||
                      !empty($_SESSION['old']['notify_recurring_expenses']),
            'help' => 'Receive reminders about upcoming recurring expenses',
            'attributes' => []
        ];
        extract($recurringExpensesField);
        include __DIR__ . '/components/form_field.php';
        ?>

        <!-- Form Actions -->
        <div class="form-actions">
            <a href="/profile" class="button primary" id="done-btn">Done</a>
            <a href="/profile/password" class="button secondary">Change Password</a>
        </div>
    </form>
</div>



<?php
// Add profile-specific scripts
$scripts ??= [];
$scripts[] = '/assets/js/pages/profile.js';
?>

<script>
    // Get the form
    const form = document.getElementById('profile-form');

    // Function to submit the form using AJAX
    function submitForm() {
        console.log('Auto-submitting form after change using AJAX');

        // Get all checkbox states
        const rememberMeCheckbox = document.getElementById('remember_me_default');
        const performanceMetricsCheckbox = document.getElementById('show_performance_metrics');

        // Create a new FormData object (don't use the form directly to avoid file upload issues)
        const formData = new FormData();

        // Add the CSRF token
        const csrfToken = document.querySelector('input[name="csrf_token"]').value;
        formData.append('csrf_token', csrfToken);

        // Add text fields
        const textFields = ['name', 'email', 'currency', 'date_format', 'monthly_budget'];
        textFields.forEach(field => {
            const element = document.getElementById(field);
            if (element) {
                formData.append(field, element.value);
            }
        });

        // Add checkbox values
        formData.append('remember_me_default', rememberMeCheckbox.checked ? '1' : '0');
        formData.append('show_performance_metrics', performanceMetricsCheckbox.checked ? '1' : '0');

        // Set a cookie for remember_me
        const rememberMeValue = rememberMeCheckbox.checked ? 'true' : 'false';
        const expiryDate = new Date();
        expiryDate.setDate(expiryDate.getDate() + 30);
        document.cookie = `remember_me=${rememberMeValue}; expires=${expiryDate.toUTCString()}; path=/; SameSite=Lax`;

        // Log what's being submitted
        console.log('Submitting form data:');
        for (const [key, value] of formData.entries()) {
            console.log(`${key}: ${value}`);
        }

        // Show a small notification that changes are being saved
        const notification = document.createElement('div');
        notification.textContent = 'Saving changes...';
        notification.style.position = 'fixed';
        notification.style.bottom = '20px';
        notification.style.right = '20px';
        notification.style.padding = '10px 20px';
        notification.style.backgroundColor = '#4CAF50';
        notification.style.color = 'white';
        notification.style.borderRadius = '4px';
        notification.style.zIndex = '9999';
        document.body.appendChild(notification);

        // Submit the form using fetch API
        fetch('/profile/update-ajax', {
            method: 'POST',
            body: formData,
            credentials: 'same-origin'
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            console.log('Success:', data);
            notification.textContent = 'Changes saved!';

            // Remove the notification after 2 seconds
            setTimeout(() => {
                notification.remove();
            }, 2000);

            // Reload the page if needed (e.g., to see performance metrics changes)
            if (data.reload) {
                window.location.reload();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            notification.textContent = 'Error saving changes';
            notification.style.backgroundColor = '#f44336';

            // Remove the notification after 3 seconds
            setTimeout(() => {
                notification.remove();
            }, 3000);
        });
    }

    // Add change event listeners to all checkboxes
    const checkboxes = document.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            console.log(`${this.id} changed to:`, this.checked);
            submitForm();
        });
    });

    // Add change event listeners to all select elements
    const selects = document.querySelectorAll('select');
    selects.forEach(select => {
        select.addEventListener('change', function() {
            console.log(`${this.id} changed to:`, this.value);
            submitForm();
        });
    });

    // Add input event listeners to text/email/number inputs with debounce
    const textInputs = document.querySelectorAll('input[type="text"], input[type="email"], input[type="number"]');
    let debounceTimer;
    textInputs.forEach(input => {
        input.addEventListener('input', function() {
            clearTimeout(debounceTimer);
            debounceTimer = setTimeout(() => {
                console.log(`${this.id} changed to:`, this.value);
                submitForm();
            }, 1000); // Wait 1 second after typing stops
        });
    });

    // Handle the Done button
    document.getElementById('done-btn').addEventListener('click', function(event) {
        // No need to prevent default - just let it navigate to /profile
        console.log('Done button clicked');
    });
</script>
