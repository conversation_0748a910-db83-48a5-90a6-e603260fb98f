<?php

/**
 * Expense Form View
 *
 * @var array $categories Available expense categories
 * @var array $merchants Available merchants/vendors
 * @var array $paymentMethods Available payment methods
 * @var array $paymentMethodsWithKeys Payment methods with keys
 * @var array $errors Validation errors
 * @var array $old Old input values for form repopulation
 * @var object|null $expense Expense object for editing (null for new expense)
 * @var string $csrf_token CSRF protection token
 * @var array $viewData View data containing form fields configuration
 */

use App\Utils\ArrayHelper;

// Defensive coercion and old-input merging
$categories           = ArrayHelper::ensureArray($categories ?? null);
$merchants            = ArrayHelper::ensureArray($merchants ?? null);
$paymentMethods       = ArrayHelper::ensureArray($paymentMethods ?? null);
$paymentMethodsWithKeys = ArrayHelper::ensureArray($paymentMethodsWithKeys ?? null);
$errors               = ArrayHelper::ensureArray($errors ?? null);
$old                  = ArrayHelper::ensureArray($old ?? null);
// Merge old primitive values into $expense for form repopulation
foreach ($old as $field => $value) {
    if (array_key_exists($field, $expense)) {
        $expense[$field] = $value;
    }
}


// Helper function to safely handle htmlspecialchars
function e($value)
{
    if (is_null($value)) {
        return '';
    }
    if (is_array($value)) {
        return '';
    }
    if (is_object($value) && !method_exists($value, '__toString')) {
        return '';
    }
    return htmlspecialchars((string)$value, ENT_QUOTES, 'UTF-8');
}

// Ensure expense is an array
if (isset($expense) && !is_array($expense)) {
    $expense = [];
    error_log("Warning: expense is not an array in form.php");
}

// Prepare form fields data structure
$viewData = [
    'formFields' => [
        'document_upload' => [
            [
                'type' => 'file',
                'name' => 'document',
                'id' => 'document',
                'label' => 'Receipt or Document',
                'class' => 'file-input',
                'attributes' => ['aria-describedby' => 'file-help'],
                'help' => 'Supported formats: TXT, CSV, PDF, LOG. Max size: 1MB.<br>Uploading a receipt? We can automatically extract information from it.<br><strong>If you upload a receipt, the other fields below become optional.</strong>',
                'existing_file' => isset($expense) && !empty($expense['file_id']) ? [
                    'message' => 'Current document attached. Upload a new one to replace it.',
                    'view_url' => "/expenses/{$expense['id']}/document",
                    'download_url' => "/expenses/{$expense['id']}/document/download"
                ] : null
            ]
        ],
        'basic_info' => [
            [
                'type' => 'select',
                'name' => 'merchant_id',
                'id' => 'merchant_id',
                'label' => 'Merchant/Vendor Name',
                'class' => 'form-control',
                'attributes' => ['aria-describedby' => 'merchant-help'],
                'help' => 'Select an existing merchant or add a new one',
                'options' => [
                    '' => 'Select a merchant',
                    1 => 'Landlord',
                    2 => 'Electric Company',
                    3 => 'Water Authority',
                    4 => 'Gas Company',
                    5 => 'Internet Provider',
                    6 => 'Phone Company',
                    7 => 'Pharmacy',
                    8 => 'Medical Lab',
                    9 => 'Therapist',
                    10 => 'Optical Store',
                    11 => 'Transit Authority',
                    12 => 'Taxi Service',
                    13 => 'Car Rental',
                    14 => 'Airline',
                    15 => 'Hotel',
                    16 => 'Supermarket',
                    17 => 'Cafe',
                    18 => 'Restaurant',
                    19 => 'Bar',
                    20 => 'Takeaway',
                    '_new' => '+ Enter New Merchant'
                ],
                'value' => isset($_SESSION['old']['merchant_id']) ? $_SESSION['old']['merchant_id'] : ($expense['merchant_id'] ?? ''),
                'new_field' => [
                    'type' => 'text',
                    'name' => 'merchant',
                    'id' => 'merchant',
                    'label' => 'Custom Merchant Name',
                    'class' => 'form-control',
                    'placeholder' => 'Enter merchant name',
                    'value' => $_SESSION['old']['merchant'] ?? '',
                    'show' => (isset($_SESSION['old']['merchant_id']) && $_SESSION['old']['merchant_id'] === '_new') || (!empty($_SESSION['old']['merchant']))
                ]
            ],
            [
                'type' => 'number',
                'name' => 'amount',
                'id' => 'amount',
                'label' => 'Amount',
                'class' => 'form-control',
                'attributes' => ['step' => '0.01', 'min' => '0.01'],
                'placeholder' => '0.00',
                'value' => isset($_SESSION['old']['amount']) ? e($_SESSION['old']['amount']) : e($expense['amount'] ?? ''),
                'prefix' => '$'
            ],
            [
                'type' => 'date',
                'name' => 'date',
                'id' => 'date',
                'label' => 'Date',
                'class' => 'form-control',
                'value' => isset($_SESSION['old']['date']) && is_scalar($_SESSION['old']['date'])
                    ? htmlspecialchars((string)$_SESSION['old']['date'])
                    : (isset($expense['date']) && is_scalar($expense['date'])
                        ? htmlspecialchars((string)$expense['date'])
                        : htmlspecialchars(date('Y-m-d')))
            ],
            [
                'type' => 'text',
                'name' => 'description',
                'id' => 'description',
                'label' => 'Description',
                'class' => 'form-control',
                'placeholder' => 'Brief description of expense',
                'value' => isset($_SESSION['old']['description']) && is_scalar($_SESSION['old']['description'])
                    ? htmlspecialchars((string)$_SESSION['old']['description'])
                    : (isset($expense['description']) && is_scalar($expense['description'])
                        ? htmlspecialchars((string)$expense['description'])
                        : '')
            ]
        ],
        'categorization' => [
            [
                'type' => 'select',
                'name' => 'category_id',
                'id' => 'category_id',
                'label' => 'Category',
                'class' => 'form-control',
                'options' => function () use ($categories) {
                    $options = ['' => 'Select a category'];

                    // Add categories from the database
                    if (is_array($categories)) {
                        foreach ($categories as $category) {
                            if (isset($category['id']) && isset($category['name'])) {
                                $options[$category['id']] = $category['name'];
                            }
                        }
                    }

                    // Add option to create a new category
                    $options['_new'] = '+ Add New Category';

                    return $options;
                },
                'value' => isset($_SESSION['old']['category_id']) ? $_SESSION['old']['category_id'] : ($expense['category_id'] ?? ''),
                'new_field' => [
                    'type' => 'text',
                    'name' => 'new_category',
                    'id' => 'new_category',
                    'label' => 'New Category Name',
                    'class' => 'form-control',
                    'placeholder' => 'Enter new category name',
                    'value' => isset($_SESSION['old']['new_category']) && is_scalar($_SESSION['old']['new_category'])
                        ? htmlspecialchars((string)$_SESSION['old']['new_category'])
                        : '',
                    'show' => (isset($_SESSION['old']['category_id']) && $_SESSION['old']['category_id'] === '_new') || (!empty($_SESSION['old']['new_category']))
                ]
            ],
            [
                'type' => 'select',
                'name' => 'payment_method_id',
                'id' => 'payment_method_id',
                'label' => 'Payment Method',
                'class' => 'form-control',
                'options' => [
                    '' => 'Select payment method',
                    1 => 'Cash',
                    2 => 'Credit Card',
                    3 => 'Debit Card',
                    4 => 'Mobile Payment',
                    5 => 'Bank Transfer',
                    6 => 'Digital Wallet',
                    7 => 'Other'
                ],
                'value' => isset($_SESSION['old']['payment_method_id']) ? $_SESSION['old']['payment_method_id'] : ($expense['payment_method_id'] ?? '')
            ]
        ],
        'details' => [
            [
                'type' => 'items',
                'name' => 'items',
                'label' => 'Items Breakdown (Optional)',
                'help' => 'Break down your expense into individual items. These will be saved in the notes.',
                'items' => function () use ($expense) {
                    $items = [];
                    if (isset($_SESSION['old']['items']) && is_array($_SESSION['old']['items'])) {
                        $items = $_SESSION['old']['items'];
                    } elseif (isset($expense['items'])) {
                        if (is_array($expense['items'])) {
                            $items = $expense['items'];
                        } elseif (is_string($expense['items'])) {
                            $decoded = json_decode($expense['items'], true);
                            if (is_array($decoded)) {
                                $items = $decoded;
                            }
                        }
                    }
                    return $items;
                }
            ],
            [
                'type' => 'hidden',
                'name' => 'receipt_text',
                'id' => 'receipt_text',
                'value' => isset($_SESSION['old']['receipt_text'])
                    ? e($_SESSION['old']['receipt_text'])
                    : e($expense['receipt_text'] ?? '')
            ],
            [
                'type' => 'textarea',
                'name' => 'notes',
                'id' => 'notes',
                'label' => 'Notes',
                'class' => 'form-control',
                'attributes' => ['rows' => '3'],
                'placeholder' => 'Any additional information about this expense',
                'help' => 'Items breakdown will be automatically added to these notes',
                'value' => isset($_SESSION['old']['notes'])
                    ? e($_SESSION['old']['notes'])
                    : (isset($expense['notes']) ? e($expense['notes']) : '')
            ]
        ]
    ]
];
?>
<?php include __DIR__ . '/partials/_header.php'; ?>

<?php include __DIR__ . '/../shared/messages.php'; ?>

<section class="expense-form container u-spacing-stack-lg">
    <h1 class="page-title"><?= isset($expense) && isset($expense['id']) ? 'Edit Expense' : 'Add Expense' ?></h1>

    <form method="POST"
          action="<?= isset($expense) && isset($expense['id'])
              ? '/expenses/' . e($expense['id'])
              : '/expenses'
            ?>"
          enctype="multipart/form-data">
        <input type="hidden" name="csrf_token" value="<?= e($csrf_token ?? '') ?>">
        <?php if (isset($expense)) : ?>
            <input type="hidden" name="_method" value="PUT">
        <?php endif; ?>

        <?php foreach ($viewData['formFields'] as $sectionKey => $fields) : ?>
            <fieldset class="form-fieldset">
                <legend class="fieldset-legend"><?= ucwords(str_replace('_', ' ', $sectionKey)) ?></legend>
                <?php foreach ($fields as $field) : ?>
                    <?php extract($field); ?>
                    <?php include_once __DIR__ . '/../shared/form_field.php'; ?>
                <?php endforeach; ?>
            </fieldset>
        <?php endforeach; ?>

        <div class="form-actions">
            <button type="submit" class="btn btn-primary">Save Expense</button>
            <a href="/expenses" class="btn btn-secondary">Cancel</a>
        </div>
    </form>

    <?php if (!isset($expense)) : ?>
        <?php include __DIR__ . '/partials/_receipt_upload.php'; ?>
    <?php endif; ?>
</section>

<?php include __DIR__ . '/partials/_form_logic.php'; ?>

> 
 
 
