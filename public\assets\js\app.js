import { initializeSharedComponents } from './components/shared-components.js';
import { initializeLayout } from './layout/layout.js';
import { initializeUserDropdown } from './components/dropdown.js';
import { initializeModernTable } from './components/modern-table.js';
import './components/action_menu.js';
import './components/multi_select.js';

document.addEventListener('DOMContentLoaded', initializeApp);

function initializeApp() {
  initializeLayout();
  initializeSharedComponents();
  initializeUserDropdown();
  initializeAppFeatures();
}

function initializeAppFeatures() {
  initializeDropdowns();
  initializeTooltips();
  initializeFilterEnhancements();
  // Initialize modern table first to avoid conflicts
  initializeModernTable();
  // Then initialize regular table sorting
  initializeTableSorting();
  initializeCategoryCards();
  setupAnimations();
  initializeSearch();
  enhanceCategorySelection();
  enhanceTrustSignals();
  initializeProgressiveEngagement();
  setupSmoothScrolling();
}

function setupSmoothScrolling() {
  document.querySelectorAll('a[href^="#"]').forEach(anchor => {
    anchor.addEventListener('click', handleSmoothScroll);
  });
}

function handleSmoothScroll(e) {
  e.preventDefault();
  const targetSelector = this.getAttribute('href');
  const target = document.querySelector(targetSelector);

  if (target) {
    window.scrollTo({
      top: target.offsetTop - 80,
      behavior: 'smooth'
    });
  }
}

function initializeDropdowns() {
  const dropdownToggles = document.querySelectorAll('.dropdown-toggle');

  dropdownToggles.forEach(toggle => {
    toggle.addEventListener('click', e => toggleDropdown(e, toggle));
  });

  document.addEventListener('click', closeAllDropdowns);
}

function toggleDropdown(e, toggle) {
  e.stopPropagation();
  const dropdownMenu = toggle.nextElementSibling;

  closeAllDropdowns();
  dropdownMenu.classList.toggle('show');

  if (!dropdownMenu.classList.contains('show')) return;

  setupDropdownKeyboardNavigation(dropdownMenu, toggle);
}

function setupDropdownKeyboardNavigation(dropdownMenu, toggle) {
  const menuItems = dropdownMenu.querySelectorAll('a');
  if (!menuItems.length) return;

  menuItems[0].focus();

  dropdownMenu.addEventListener('keydown', e => {
    if (e.key === 'Escape') {
      dropdownMenu.classList.remove('show');
      toggle.focus();
    }
  });
}

function closeAllDropdowns() {
  document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
    if (menu.id !== 'user-dropdown') {
      menu.classList.remove('show');
    }
  });
}

function initializeTooltips() {
  document.querySelectorAll('[data-tooltip]').forEach(setupTooltip);
}

function setupTooltip(element) {
  const tooltipText = element.getAttribute('data-tooltip');
  const tooltip = createTooltipElement(tooltipText);

  element.addEventListener('mouseenter', () => showTooltip(element, tooltip));
  element.addEventListener('mouseleave', () => hideTooltip(tooltip));
}

function createTooltipElement(text) {
  const tooltip = document.createElement('div');
  tooltip.className = 'tooltip';
  tooltip.textContent = text;
  return tooltip;
}

function showTooltip(element, tooltip) {
  document.body.appendChild(tooltip);
  positionTooltip(element, tooltip);
  tooltip.classList.add('show');
}

function positionTooltip(element, tooltip) {
  const rect = element.getBoundingClientRect();
  tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
  tooltip.style.top = rect.bottom + 10 + 'px';
}

function hideTooltip(tooltip) {
  tooltip.classList.remove('show');
  if (tooltip.parentNode) tooltip.parentNode.removeChild(tooltip);
}

function initializeFilterEnhancements() {
  const filterForm = document.querySelector('.filters form');
  if (!filterForm) return;

  addResetButton(filterForm);
  addDateRangeFilter(filterForm);
}

function addResetButton(filterForm) {
  const resetButton = createResetButton();
  resetButton.addEventListener('click', () => resetFilters(filterForm));
  insertResetButton(resetButton, filterForm);
}

function createResetButton() {
  const resetButton = document.createElement('button');
  resetButton.type = 'button';
  resetButton.className = 'button secondary small';
  resetButton.textContent = 'Reset Filters';
  resetButton.style.marginLeft = '10px';
  return resetButton;
}

function insertResetButton(resetButton, form) {
  const selectElement = form.querySelector('select');
  if (selectElement) {
    selectElement.parentNode.insertBefore(resetButton, selectElement.nextSibling);
  }
}

function resetFilters(form) {
  clearFormFields(form);
  form.submit();
}

function clearFormFields(form) {
  form.querySelectorAll('select, input').forEach(field => {
    if (field.tagName === 'SELECT') {
      field.selectedIndex = 0;
    } else if (field.type === 'text' || field.type === 'number') {
      field.value = '';
    }
  });
}

function addDateRangeFilter(filterForm) {
  if (filterForm.querySelector('input[type="date"]')) return;

  const dateRangeDiv = createDateRangeElement();
  filterForm.appendChild(dateRangeDiv);
}

function createDateRangeElement() {
  const dateRangeDiv = document.createElement('div');
  dateRangeDiv.className = 'date-range';
  dateRangeDiv.style.marginTop = '10px';
  dateRangeDiv.innerHTML = `
    <label>Date Range:</label>
    <div class="date-inputs">
      <input type="date" name="start_date" placeholder="Start Date">
      <span>to</span>
      <input type="date" name="end_date" placeholder="End Date">
      <button type="submit" class="button small">Apply</button>
    </div>
  `;
  return dateRangeDiv;
}

function initializeTableSorting() {
  // Explicitly exclude tables inside .modern-table
  const regularTables = Array.from(document.querySelectorAll('table')).filter(table => {
    return !table.closest('.modern-table');
  });
  
  console.log(`Found ${regularTables.length} regular tables for basic sorting`);
  
  regularTables.forEach(table => {
    const headers = table.querySelectorAll('th');
    headers.forEach(header => setupSortableHeader(header, table));
  });

  addSortStyles();
}

function setupSortableHeader(header, table) {
  if (isUnsortableColumn(header)) return;

  styleSortableHeader(header);
  addSortIndicator(header, table);
}

function isUnsortableColumn(header) {
  return ['actions', 'document'].includes(header.textContent.toLowerCase());
}

function styleSortableHeader(header) {
  header.style.cursor = 'pointer';
  header.style.userSelect = 'none';
}

function addSortIndicator(header, table) {
  const sortIndicator = createSortIndicator();
  header.appendChild(sortIndicator);

  header.addEventListener('click', () => sortTable(header, table, sortIndicator));
}

function createSortIndicator() {
  const sortIndicator = document.createElement('span');
  sortIndicator.className = 'sort-indicator';
  sortIndicator.textContent = ' ↕';
  sortIndicator.style.opacity = '0.5';
  return sortIndicator;
}

function sortTable(header, table, sortIndicator) {
  const columnIndex = Array.from(header.parentNode.children).indexOf(header);
  const tbody = table.querySelector('tbody');
  const rows = Array.from(tbody.querySelectorAll('tr'));

  const columnType = determineColumnType(header);
  const sortDirection = toggleSortDirection(header);

  resetSortIndicators(table);
  updateSortIndicator(sortIndicator, sortDirection);

  const sortedRows = getSortedRows(rows, columnIndex, sortDirection, columnType);
  applySort(tbody, sortedRows);
}

function determineColumnType(header) {
  const headerText = header.textContent;

  if (headerText.includes('Amount') || headerText.includes('ID')) {
    return 'numeric';
  } else if (headerText.includes('Date')) {
    return 'date';
  }

  return 'text';
}

function toggleSortDirection(header) {
  const sortDirection = header.getAttribute('data-sort') === 'asc' ? 'desc' : 'asc';
  header.setAttribute('data-sort', sortDirection);
  return sortDirection;
}

function updateSortIndicator(indicator, direction) {
  indicator.textContent = direction === 'asc' ? ' ↑' : ' ↓';
  indicator.style.opacity = '1';
}

function applySort(tbody, sortedRows) {
  sortedRows.forEach(row => {
    tbody.appendChild(row);
    animateRow(row);
  });
}

function animateRow(row) {
  row.style.animation = 'none';
  row.offsetHeight; // Force reflow
  row.style.animation = 'highlightRow 1s';
}

function resetSortIndicators(table) {
  table.querySelectorAll('.sort-indicator').forEach(indicator => {
    indicator.textContent = ' ↕';
    indicator.style.opacity = '0.5';
  });
}

function getSortedRows(rows, columnIndex, sortDirection, columnType) {
  const sortMultiplier = sortDirection === 'asc' ? 1 : -1;

  return rows.sort((a, b) => {
    const aValue = getCellValue(a.cells[columnIndex], columnType);
    const bValue = getCellValue(b.cells[columnIndex], columnType);

    return aValue > bValue ? sortMultiplier : -sortMultiplier;
  });
}

function getCellValue(cell, columnType) {
  const rawValue = cell.textContent.trim();

  switch (columnType) {
    case 'numeric':
      return parseFloat(rawValue.replace(/[^0-9.-]+/g, ''));
    case 'date':
      return new Date(rawValue);
    default:
      return rawValue;
  }
}

function addSortStyles() {
  addStyleIfMissing('sort-animations', `
    @keyframes highlightRow {
      0% { background-color: rgba(58, 123, 213, 0.2); }
      100% { background-color: transparent; }
    }
  `);
}

function initializeCategoryCards() {
  document.querySelectorAll('.category-item').forEach(styleCategory);
}

function styleCategory(item) {
  const categoryName = item.querySelector('h4').textContent;
  const hue = getStringHash(categoryName) % 360;

  applyCategoryStyles(item, hue);
  setupCategoryHoverEffects(item, hue);
}

function applyCategoryStyles(item, hue) {
  item.style.borderLeft = `4px solid hsl(${hue}, 70%, 60%)`;
}

function setupCategoryHoverEffects(item, hue) {
  item.addEventListener('mouseenter', () => {
    item.style.backgroundColor = `hsl(${hue}, 30%, 95%)`;
  });

  item.addEventListener('mouseleave', () => {
    item.style.backgroundColor = '#f8f9fa';
  });
}

function setupAnimations() {
  animateMainContent();
  setupButtonAnimations();
  setupRowAnimations();
  addAnimationStyles();
  observeNewElements();
}

function animateMainContent() {
  const main = document.querySelector('main');
  if (main) main.style.animation = 'fadeIn 0.5s ease-in-out';
}

function setupButtonAnimations() {
  document.querySelectorAll('.button').forEach(button => {
    setupButtonPressEffect(button);
  });
}

function setupButtonPressEffect(button) {
  ['mousedown', 'mouseup', 'mouseleave'].forEach(event => {
    button.addEventListener(event, () => {
      button.style.transform = event === 'mousedown' ? 'scale(0.98)' : '';
    });
  });
}

function setupRowAnimations() {
  document.querySelectorAll('tbody tr').forEach(row => {
    setupRowHoverEffect(row);
  });
}

function setupRowHoverEffect(row) {
  row.addEventListener('mouseenter', () => {
    row.style.transition = 'background-color 0.2s';
    row.style.borderLeft = '3px solid var(--primary-color)';
  });

  row.addEventListener('mouseleave', () => {
    row.style.borderLeft = '';
  });
}

function addAnimationStyles() {
  addStyleIfMissing('animation-styles', `
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }

    @keyframes pulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.05); }
      100% { transform: scale(1); }
    }

    @keyframes highlight-new {
      0%, 100% { background-color: transparent; }
      20% { background-color: rgba(58, 123, 213, 0.1); }
    }

    @keyframes highlightRow {
      0% { background-color: rgba(58, 123, 213, 0.15); }
      70% { background-color: rgba(58, 123, 213, 0.05); }
      100% { background-color: transparent; }
    }

    .button, a {
      transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .new-item {
      animation: highlight-new 2s cubic-bezier(0.22, 1, 0.36, 1);
    }
  `);
}

function observeNewElements() {
  const observer = createMutationObserver();
  observeTargetElements(observer);
}

function createMutationObserver() {
  return new MutationObserver(mutations => {
    mutations.forEach(mutation => {
      if (mutation.type === 'childList') {
        mutation.addedNodes.forEach(highlightNewNode);
      }
    });
  });
}

function highlightNewNode(node) {
  if (node.nodeType !== 1) return;

  node.classList.add('new-item');
  setTimeout(() => node.classList.remove('new-item'), 2000);
}

function observeTargetElements(observer) {
  const targets = [
    ...document.querySelectorAll('tbody'),
    ...document.querySelectorAll('.category-list')
  ];

  targets.filter(Boolean).forEach(target => {
    observer.observe(target, { childList: true });
  });
}

function initializeSearch() {
  if (document.querySelector('.search-container')) return;

  const actionsSection = document.querySelector('.actions');
  if (!actionsSection) return;

  const searchContainer = createSearchElement();
  actionsSection.appendChild(searchContainer);

  setupSearchEventListeners(searchContainer);
  addSearchStyles();
}

function createSearchElement() {
  const searchContainer = document.createElement('div');
  searchContainer.className = 'search-container';

  searchContainer.innerHTML = `
    <input type="text" id="expenseSearch" placeholder="Search expenses..." class="search-input">
    <button type="button" class="search-button">
      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <circle cx="11" cy="11" r="8"></circle>
        <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
      </svg>
    </button>
  `;

  return searchContainer;
}

function setupSearchEventListeners(searchContainer) {
  const searchInput = searchContainer.querySelector('#expenseSearch');

  searchInput.addEventListener('input', performSearch);
  searchInput.addEventListener('keydown', e => {
    if (e.key === 'Enter') performSearch();
  });

  searchContainer.querySelector('.search-button').addEventListener('click', performSearch);
}

function addSearchStyles() {
  addStyleIfMissing('search-styles', `
    .highlight {
      background-color: #fff3cd;
      padding: 2px;
      border-radius: 2px;
    }

    .search-container {
      display: flex;
      margin-left: auto;
    }

    .search-input {
      padding: 8px 12px;
      border: 1px solid #ced4da;
      border-radius: 4px 0 0 4px;
      width: 200px;
    }

    .search-button {
      background-color: var(--primary-color, #3a7bd5);
      color: white;
      border: none;
      border-radius: 0 4px 4px 0;
      padding: 8px 12px;
      cursor: pointer;
    }

    .no-results-message {
      margin-top: 10px;
      padding: 10px;
      background-color: #f8f9fa;
      border-left: 4px solid #ffc107;
    }
  `);
}

function performSearch() {
  const searchInput = document.getElementById('expenseSearch');
  const expenseTable = document.querySelector('table');

  if (!searchInput || !expenseTable) return;

  const searchTerm = searchInput.value.toLowerCase().trim();
  const rows = expenseTable.querySelectorAll('tbody tr');

  const matchFound = filterAndHighlightRows(rows, searchTerm);
  updateNoResultsMessage(matchFound, searchTerm, expenseTable);
}

function filterAndHighlightRows(rows, searchTerm) {
  let matchFound = false;

  rows.forEach(row => {
    const isMatch = row.textContent.toLowerCase().includes(searchTerm);
    row.style.display = isMatch ? '' : 'none';

    if (!isMatch) return;

    matchFound = true;
    if (searchTerm) {
      highlightMatches(row, searchTerm);
    } else {
      clearHighlights(row);
    }
  });

  return matchFound;
}

function updateNoResultsMessage(matchFound, searchTerm, table) {
  const existingMessage = document.querySelector('.no-results-message');

  if (matchFound || !searchTerm) {
    if (existingMessage) existingMessage.remove();
    return;
  }

  if (existingMessage) return;

  addNoResultsMessage(searchTerm, table);
}

function addNoResultsMessage(searchTerm, table) {
  const message = document.createElement('div');
  message.className = 'no-results-message notice';
  message.textContent = `No expenses matching "${searchTerm}" found.`;
  table.parentNode.insertBefore(message, table.nextSibling);
}

function highlightMatches(row, searchTerm) {
  clearHighlights(row);

  row.querySelectorAll('td').forEach(cell => {
    if (cell.querySelector('button, .dropdown, form')) return;

    const text = cell.textContent;
    const regex = new RegExp(`(${escapeRegExp(searchTerm)})`, 'gi');

    if (regex.test(text)) {
      cell.innerHTML = text.replace(regex, '<span class="highlight">$1</span>');
    }
  });
}

function clearHighlights(row) {
  row.querySelectorAll('td:not(:has(button, .dropdown, form))').forEach(cell => {
    if (cell.querySelector('.highlight')) {
      cell.textContent = cell.textContent;
    }
  });
}

function escapeRegExp(string) {
  return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

function enhanceCategorySelection() {
  document.querySelectorAll('select[name="category"]').forEach(setupCategorySelect);
}

function setupCategorySelect(select) {
  const recentChoices = getRecentCategories();

  if (recentChoices.length > 0) {
    addRecentCategories(select, recentChoices);
  }

  select.addEventListener('change', function() {
    trackCategoryUsage(this.value);
  });
}

function getRecentCategories() {
  return JSON.parse(localStorage.getItem('recentCategories') || '[]');
}

function addRecentCategories(select, recentChoices) {
  const optgroup = document.createElement('optgroup');
  optgroup.label = 'Recently Used';

  recentChoices.slice(0, 3).forEach(category => {
    addCategoryOption(optgroup, category);
  });

  select.insertBefore(optgroup, select.firstChild);
}

function addCategoryOption(optgroup, category) {
  const option = document.createElement('option');
  option.value = category;
  option.textContent = category;
  optgroup.appendChild(option);
}

function trackCategoryUsage(categoryValue) {
  if (!categoryValue) return;

  let recent = getRecentCategories();
  recent = [categoryValue, ...recent.filter(item => item !== categoryValue)].slice(0, 5);
  localStorage.setItem('recentCategories', JSON.stringify(recent));
}

function enhanceTrustSignals() {
  document.querySelectorAll('table').forEach(addTableLastUpdated);
  addVerifiedDataPoints();
  addTrustStyles();
}

function addTrustStyles() {
  addStyleIfMissing('trust-styles', `
    .verified-icon {
      color: var(--success-color);
      vertical-align: middle;
      margin-left: 5px;
    }

    .verified-data {
      position: relative;
    }

    .verified-data:after {
      content: "";
      display: inline-block;
      width: 8px;
      height: 8px;
      background-color: var(--success-color);
      border-radius: 50%;
      margin-left: 5px;
      opacity: 0.6;
    }
  `);
}

function addTableLastUpdated(table) {
  if (table.querySelector('caption')) return;

  const caption = createLastUpdatedCaption();
  table.appendChild(caption);
}

function createLastUpdatedCaption() {
  const caption = document.createElement('caption');
  const now = new Date();

  caption.innerHTML = `<small class="text-muted">Last updated: ${now.toLocaleTimeString()}
                      <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24"
                      fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                      stroke-linejoin="round" class="verified-icon"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                      <polyline points="22 4 12 14.01 9 11.01"></polyline></svg></small>`;

  caption.style.textAlign = 'right';
  caption.style.captionSide = 'bottom';
  caption.style.marginRight = 'var(--spacing-sm)';

  return caption;
}

function addVerifiedDataPoints() {
  document.querySelectorAll('.expense-amount, .category-total').forEach(element => {
    element.setAttribute('data-tooltip', 'Verified data point');
    element.classList.add('verified-data');
  });
}

function initializeProgressiveEngagement() {
  const interactionCount = parseInt(localStorage.getItem('interactionCount') || '0');
  setupEngagementTracking(interactionCount);
}

function setupEngagementTracking(currentCount) {
  const interactiveElements = document.querySelectorAll('button:not(.close), a.button, tbody tr, .category-item');

  interactiveElements.forEach(element => {
    element.addEventListener('click', () => trackUserEngagement(currentCount));
  });
}

function trackUserEngagement(interactionCount) {
  const newCount = interactionCount + 1;
  localStorage.setItem('interactionCount', newCount);

  if (newCount === 5 && !document.querySelector('.feature-suggestion')) {
    showFeatureSuggestion();
  }
}

function showFeatureSuggestion() {
  const suggestion = createSuggestionElement();
  addSuggestionToPage(suggestion);
}

function createSuggestionElement() {
  const suggestion = document.createElement('div');
  suggestion.className = 'alert feature-suggestion';
  suggestion.style.backgroundColor = '#e3f2fd';
  suggestion.style.borderLeft = '4px solid var(--primary-color)';
  suggestion.style.marginBottom = '20px';

  suggestion.innerHTML = `<strong>Pro Tip:</strong> Try using our filters to analyze your expense patterns by month or category.
                         <button class="dismiss-suggestion" style="float: right; background: none; border: none; cursor: pointer; color: #666;">×</button>`;

  return suggestion;
}

function addSuggestionToPage(suggestion) {
  const main = document.querySelector('main');
  if (!main || !main.firstChild) return;

  main.insertBefore(suggestion, main.firstChild);

  document.querySelector('.dismiss-suggestion').addEventListener('click', () => {
    suggestion.remove();
  });
}

function getStringHash(str) {
  let hash = 0;

  for (let i = 0; i < str.length; i++) {
    hash = ((hash << 5) - hash) + str.charCodeAt(i);
    hash |= 0;
  }

  return Math.abs(hash);
}

function addStyleIfMissing(id, cssText) {
  if (document.querySelector(`#${id}`)) return;

  const style = document.createElement('style');
  style.id = id;
  style.textContent = cssText;
  document.head.appendChild(style);
}
