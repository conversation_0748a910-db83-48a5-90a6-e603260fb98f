<?php

/**
 * Standardized pagination component
 *
 * Parameters:
 * @param int $currentPage Current page number
 * @param int $totalPages Total number of pages
 * @param string $baseUrl Base URL for pagination links
 * @param array $queryParams Additional query parameters to include in pagination links
 * @param int $maxPageLinks Maximum number of page links to display (default: 5)
 */

// Set defaults
$currentPage = $currentPage ?? 1;
$totalPages = $totalPages ?? 1;
$baseUrl = $baseUrl ?? '';
$queryParams = $queryParams ?? [];
$maxPageLinks = $maxPageLinks ?? 5;

// Don't show pagination if there's only one page
if ($totalPages <= 1) {
    return;
}

// Calculate the range of page links to display
$halfMaxLinks = floor($maxPageLinks / 2);
$startPage = max(1, $currentPage - $halfMaxLinks);
$endPage = min($totalPages, $startPage + $maxPageLinks - 1);

// Adjust start page if we're near the end
if ($endPage - $startPage + 1 < $maxPageLinks) {
    $startPage = max(1, $endPage - $maxPageLinks + 1);
}

// Build the query string for pagination links
$queryString = '';
if (!empty($queryParams)) {
    $filteredParams = array_filter($queryParams, function ($key) {
        return $key !== 'page';
    }, ARRAY_FILTER_USE_KEY);

    if (!empty($filteredParams)) {
        $queryParts = [];
        foreach ($filteredParams as $key => $value) {
            $queryParts[] = urlencode($key) . '=' . urlencode($value);
        }
        $queryString = '&' . implode('&', $queryParts);
    }
}

// Helper function to build pagination URLs
$buildUrl = function ($page) use ($baseUrl, $queryString) {
    return $baseUrl . '?page=' . $page . $queryString;
};
?>

<nav class="pagination" aria-label="Pagination">
    <ul class="pagination-list">
        <!-- First page link -->
        <?php if ($currentPage > 1) : ?>
            <li class="pagination-item">
                <a href="<?= htmlspecialchars($buildUrl(1)) ?>" class="pagination-link" aria-label="Go to first page">
                    <span aria-hidden="true">&laquo;</span>
                </a>
            </li>
        <?php else : ?>
            <li class="pagination-item disabled">
                <span class="pagination-link" aria-hidden="true">&laquo;</span>
            </li>
        <?php endif; ?>

        <!-- Previous page link -->
        <?php if ($currentPage > 1) : ?>
            <li class="pagination-item">
                <a href="<?= htmlspecialchars($buildUrl($currentPage - 1)) ?>" class="pagination-link" aria-label="Go to previous page">
                    <span aria-hidden="true">&lsaquo;</span>
                </a>
            </li>
        <?php else : ?>
            <li class="pagination-item disabled">
                <span class="pagination-link" aria-hidden="true">&lsaquo;</span>
            </li>
        <?php endif; ?>

        <!-- Page number links -->
        <?php for ($i = $startPage; $i <= $endPage; $i++) : ?>
            <?php if ($i == $currentPage) : ?>
                <li class="pagination-item active">
                    <span class="pagination-link" aria-current="page"><?= $i ?></span>
                </li>
            <?php else : ?>
                <li class="pagination-item">
                    <a href="<?= htmlspecialchars($buildUrl($i)) ?>" class="pagination-link"><?= $i ?></a>
                </li>
            <?php endif; ?>
        <?php endfor; ?>

        <!-- Next page link -->
        <?php if ($currentPage < $totalPages) : ?>
            <li class="pagination-item">
                <a href="<?= htmlspecialchars($buildUrl($currentPage + 1)) ?>" class="pagination-link" aria-label="Go to next page">
                    <span aria-hidden="true">&rsaquo;</span>
                </a>
            </li>
        <?php else : ?>
            <li class="pagination-item disabled">
                <span class="pagination-link" aria-hidden="true">&rsaquo;</span>
            </li>
        <?php endif; ?>

        <!-- Last page link -->
        <?php if ($currentPage < $totalPages) : ?>
            <li class="pagination-item">
                <a href="<?= htmlspecialchars($buildUrl($totalPages)) ?>" class="pagination-link" aria-label="Go to last page">
                    <span aria-hidden="true">&raquo;</span>
                </a>
            </li>
        <?php else : ?>
            <li class="pagination-item disabled">
                <span class="pagination-link" aria-hidden="true">&raquo;</span>
            </li>
        <?php endif; ?>
    </ul>
    
    <!-- Page info -->
    <div class="pagination-info">
        Page <?= $currentPage ?> of <?= $totalPages ?>
    </div>
</nav>
