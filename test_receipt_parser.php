<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\Receipt;

// Test receipt content
$content = <<<EOT
Cinema: MovieMax
Date: 2023-11-01
Items:
  - Movie Ticket: $12.00
  - Popcorn: $5.50
  - Soda: $3.00
Total: $20.50
Payment Method: Cash
EOT;

// Parse the receipt content
$parsedData = Receipt::parseContent($content);

// Output the result
echo "Parsed Receipt Data:\n";
echo "-------------------\n";
echo "Merchant: " . $parsedData['merchant'] . "\n";
echo "Date: " . $parsedData['date'] . "\n";
echo "Amount: $" . number_format($parsedData['amount'], 2) . "\n";
echo "Payment Method: " . $parsedData['payment_method'] . "\n";
echo "Category: " . $parsedData['category'] . "\n";
echo "Items:\n";

if (!empty($parsedData['items'])) {
    foreach ($parsedData['items'] as $item) {
        echo "  - " . $item['name'] . ": $" . number_format($item['price'], 2) . "\n";
    }
} else {
    echo "  No items found\n";
}

// Generate a downloadable receipt
$receipt = Receipt::generateDownloadableReceipt($parsedData);
echo "\nGenerated Receipt:\n";
echo "-------------------\n";
echo $receipt . "\n";
