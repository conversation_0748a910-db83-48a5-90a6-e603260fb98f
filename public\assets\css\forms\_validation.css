/* ===== FORM VALIDATION ===== */
input::placeholder,
select::placeholder,
textarea::placeholder {
  transition: all 0.2s ease;
  opacity: 0.6;
}

input:focus::placeholder,
select:focus::placeholder,
textarea:focus::placeholder {
  opacity: 0.4;
  transform: translateX(5px);
}

/* Error state */
.form-group.error input,
.form-group.error select,
.form-group.error textarea {
  border-color: var(--danger-color);
  background-color: #fff5f5;
}

.form-group.error label {
  color: var(--danger-color);
}

.form-group.error .form-hint {
  color: var(--danger-color);
}

.form-error-message {
  display: none;
  color: var(--danger-color);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-xs);
  animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
}

@keyframes shake {
  10%, 90% { transform: translate3d(-1px, 0, 0); }
  20%, 80% { transform: translate3d(2px, 0, 0); }
  30%, 50%, 70% { transform: translate3d(-4px, 0, 0); }
  40%, 60% { transform: translate3d(4px, 0, 0); }
}

.form-group.error .form-error-message {
  display: block;
}

/* Success state */
.form-group.success input,
.form-group.success select,
.form-group.success textarea {
  border-color: var(--success-color);
  background-color: rgba(40, 167, 69, 0.03);
}

.form-group.success label {
  color: var(--success-color);
}