document.addEventListener('DOMContentLoaded', initializeCategoryPage);

function initializeCategoryPage()
{
    setupDeleteConfirmation();
    initializeCategoryChart();
    initializeMonthlyTrendChart();
    enhanceCategoryCards();
}

function setupDeleteConfirmation()
{
    document.querySelectorAll('.delete-category-form').forEach(form => {
        form.addEventListener('submit', e => {
            e.preventDefault();
            const hasExpenses = form.querySelector('button').getAttribute('data-has-expenses') === 'true';
            const message = hasExpenses
                ? 'Are you sure you want to delete this category? This will affect existing expenses.'
                : 'Are you sure you want to delete this category?';

            if (confirm(message)) {
                form.submit();
            }
        });
    });
}

function initializeCategoryChart()
{
    const chartElement = document.getElementById('categoryChart');
    if (!chartElement) {
        return;
    }

    if (document.querySelector('.category-statistics')) {
        initializeCategoryBreakdownChart(chartElement);
    } else if (chartElement.hasAttribute('data-chart')) {
        initializeDashboardCategoryChart(chartElement);
    }
}

function initializeCategoryBreakdownChart(chartElement)
{
    const ctx = chartElement.getContext('2d');
    const categoryItems = document.querySelectorAll('.distribution-item');
    if (categoryItems.length === 0) {
        return;
    }

    const chartData = extractCategoryData(categoryItems);
    const colors = generateColorPalette(chartData.labels.length);

    applyCategoryColors(categoryItems, colors);
    createCategoryChart(ctx, chartData, colors);
}

function extractCategoryData(categoryItems)
{
    const labels = [];
    const data = [];
    const categoryIds = [];

    categoryItems.forEach(item => {
        const name = item.querySelector('.category-name').textContent;
        const amountText = item.querySelector('.amount').textContent;
        const amount = parseFloat(amountText.replace(/[^0-9.-]+/g, ''));
        const categoryId = item.getAttribute('data-category-id');

        labels.push(name);
        data.push(amount);
        categoryIds.push(categoryId);
    });

    return { labels, data, categoryIds };
}

function applyCategoryColors(categoryItems, colors)
{
    categoryItems.forEach((item, index) => {
        const indicator = item.querySelector('.color-indicator');
        if (indicator) {
            indicator.style.backgroundColor = colors[index];
        }
    });
}

function createCategoryChart(ctx, chartData, colors)
{
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: chartData.labels,
            datasets: [{
                data: chartData.data,
                backgroundColor: colors,
                borderColor: colors.map(color => color.replace('0.7', '1')),
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: true,
            plugins: {
                legend: { display: false },
                tooltip: {
                    callbacks: {
                        label: context => formatTooltipLabel(context)
                    }
                }
            },
            onClick: (_, elements) => handleChartClick(elements, chartData.categoryIds)
        }
    });
}

function formatTooltipLabel(context)
{
    const label = context.label || '';
    const value = context.raw;
    const total = context.dataset.data.reduce((a, b) => a + b, 0);
    const percentage = Math.round((value / total) * 100);
    return `${label}: $${value.toFixed(2)} (${percentage} % )`;
}

function handleChartClick(elements, categoryIds)
{
    if (elements.length === 0) {
        return;
    }

    const index = elements[0].index;
    const categoryId = categoryIds[index];
    if (categoryId) {
        window.location.href = ` / expenses ? category_id = ${categoryId}`;
    }
}

function initializeDashboardCategoryChart(chartElement)
{
    const ctx = chartElement.getContext('2d');
    const chartData = JSON.parse(chartElement.getAttribute('data-chart'));
    const backgroundColors = generateChartColors(chartData.labels);

    createDashboardChart(ctx, chartData, backgroundColors);
}

function generateChartColors(labels)
{
    return labels.map((_, index) => {
        const hue = (index * 137) % 360;
        return `hsla(${hue}, 70 % , 60 % , 0.7)`;
    });
}

function createDashboardChart(ctx, chartData, backgroundColors)
{
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: chartData.labels,
            datasets: [{
                data: chartData.data,
                backgroundColor: backgroundColors,
                borderColor: backgroundColors.map(color => color.replace('0.7', '1')),
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'right',
                    labels: {
                        boxWidth: 12,
                        font: { size: 11 }
                    }
                },
                tooltip: {
                    callbacks: {
                        label: context => formatTooltipLabel(context)
                    }
                }
            }
        }
    });
}

function initializeMonthlyTrendChart()
{
    const trendCtx = document.getElementById('monthlyTrendChart');
    if (!trendCtx || typeof monthlyData === 'undefined') {
        return;
    }

    new Chart(trendCtx, {
        type: 'line',
        data: monthlyData,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: value => '$' + value
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: context => '$' + context.raw.toFixed(2)
                    }
                }
            }
        }
    });
}

function enhanceCategoryCards()
{
    document.querySelectorAll('.category-card').forEach(card => {
        card.addEventListener('click', e => {
            if (e.target.closest('a, button, form')) {
                return;
            }

            const categoryId = card.getAttribute('data-category-id');
            if (categoryId) {
                window.location.href = ` / expenses ? category_id = ${categoryId}`;
            }
        });

        card.style.cursor = 'pointer';
    });
}

function generateColorPalette(count)
{
    const baseColors = [
        'hsla(210, 70%, 60%, 0.7)', // Blue
        'hsla(150, 70%, 50%, 0.7)', // Green
        'hsla(0, 70%, 60%, 0.7)',   // Red
        'hsla(40, 90%, 60%, 0.7)',  // Orange
        'hsla(280, 70%, 60%, 0.7)', // Purple
        'hsla(180, 70%, 50%, 0.7)', // Teal
        'hsla(320, 70%, 60%, 0.7)', // Pink
        'hsla(60, 90%, 60%, 0.7)',  // Yellow
        'hsla(240, 70%, 70%, 0.7)'  // Indigo
    ];

    if (count <= baseColors.length) {
        return baseColors.slice(0, count);
    }

    const colors = [...baseColors];

    for (let i = baseColors.length; i < count; i++) {
        const hue = (i * 137) % 360;
        const saturation = 70 + (i % 2) * 10;
        const lightness = 50 + (i % 3) * 5;
        colors.push(`hsla(${hue}, ${saturation} % , ${lightness} % , 0.7)`);
    }

    return colors;
}
