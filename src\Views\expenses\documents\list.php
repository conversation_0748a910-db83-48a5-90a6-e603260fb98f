<?php

/**
 * Document listing for a specific category
 * Used by the listDocuments() controller action
 */

// Include shared helper functions
include_once __DIR__ . '/../../../shared/helpers.php';

// Ensure we have the required data
if (empty($category) || empty($files)) {
    echo '<div class="notice">
            <p>No documents found in this category.</p>
            <a href="/categories" class="button secondary">Back to Categories</a>
          </div>';
    return;
}
?>

<?php
// Set page header parameters
$title = 'Category Documents: ' . htmlspecialchars($category['name']);
$backUrl = '/expenses';
$backText = 'Back to Expenses';
include __DIR__ . '/../../../shared/components/page_header.php';
?>

<?php include __DIR__ . '/../../../shared/messages.php'; ?>

<div class="documents-grid">
    <?php foreach ($files as $file) : ?>
        <div class="document-card">
            <div class="document-icon">
                <?php
                $extension = strtolower(pathinfo($file['original_name'] ?? '', PATHINFO_EXTENSION));
                $icon = match ($extension) {
                    'pdf' => 'file-pdf',
                    'txt' => 'file-text',
                    'csv' => 'file-csv',
                    'log' => 'file-text',
                    default => 'file-generic'
                };
                $size = 'lg';
                include __DIR__ . '/../../../shared/components/icon.php';
    ?>
            </div>

            <div class="document-info">
                <?php $displayName = $file['original_name'] ?? basename($file['filename'] ?? ''); ?>
                <h4 class="document-name"><?= htmlspecialchars($displayName) ?></h4>

                <div class="document-meta">
                    <?php if (!empty($file['expense_id'])) : ?>
                        <p>
                            <strong>Expense:</strong>
                            <a href="/expenses/<?= htmlspecialchars($file['expense_id']) ?>">
                            <?= htmlspecialchars($file['expense_description'] ?? 'View') ?>
                        </a></p>
                    <?php endif; ?>
                    <?php if (!empty($file['size'])) : ?>
                        <p><strong>Size:</strong> <?= formatFileSize((int)$file['size']) ?></p>
                    <?php endif; ?>
                    <?php if (!empty($file['created_at'])) : ?>
                        <p><strong>Added:</strong> <?= formatDate($file['created_at']) ?></p>
                    <?php endif; ?>
                </div>

                <div class="document-actions">
                    <?php if (!empty($file['expense_id'])) : ?>
                        <?php
                        // Define action buttons
                        $actions = [
                            [
                                'url' => "/expenses/" . htmlspecialchars($file['expense_id']) . "/document",
                                'text' => "View",
                                'icon' => "view",
                                'size' => "small"
                            ],
                            [
                                'url' => "/expenses/" . htmlspecialchars($file['expense_id']) . "/document/download",
                                'text' => "Download",
                                'icon' => "download",
                                'size' => "small"
                            ]
                        ];

                        // Render each action button
                        foreach ($actions as $action) {
                            $url = $action['url'];
                            $text = $action['text'];
                            $icon = $action['icon'];
                            $size = $action['size'];
                            include __DIR__ . '/../../../shared/components/action_button.php';
                        }
                        ?>
                    <?php else : ?>
                        <span class="text-muted">No direct access</span>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    <?php endforeach; ?>
</div>

<div class="navigation-links">
    <?php
    // Define navigation buttons
    $actions = [
        [
            'url' => "/expenses",
            'text' => "Back to Expenses",
            'icon' => "arrow-back",
            'type' => "secondary"
        ],
        [
            'url' => "/categories",
            'text' => "Back to Categories",
            'icon' => "arrow-back",
            'type' => "secondary"
        ]
    ];

    // Render each navigation button
    foreach ($actions as $action) {
        $url = $action['url'];
        $text = $action['text'];
        $icon = $action['icon'];
        $type = $action['type'];
        include __DIR__ . '/../../../shared/components/action_button.php';
    }
    ?>
</div>


