<?php

/**
 * Receipt upload component
 * Provides a drag-and-drop area for receipt uploads
 */

?>

<div class="receipt-upload-container hidden">
    <div class="receipt-upload-area" id="receipt-upload-area">
        <div class="upload-icon">
            <svg class="svg-icon svg-icon--xl" aria-hidden="true" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M19.35 10.04C18.67 6.59 15.64 4 12 4 9.11 4 6.6 5.64 5.35 8.04 2.34 8.36 0 10.91 0 14c0 3.31 2.69 6 6 6h13c2.76 0 5-2.24 5-5 0-2.64-2.05-4.78-4.65-4.96zM14 13v4h-4v-4H7l5-5 5 5h-3z"/>
            </svg>
        </div>
        <h3>Drag & Drop Receipt</h3>
        <p>or click to browse files</p>
    </div>
    
    <div class="receipt-preview hidden" id="receipt-preview">
        <div class="preview-header">
            <h3>Receipt Preview</h3>
            <button type="button" id="clear-receipt" class="button secondary small">Clear</button>
        </div>
        <div class="preview-content" id="preview-content"></div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // This is a placeholder for future receipt upload functionality
    // The actual implementation would include drag-and-drop handling,
    // file preview, and extraction of receipt data
    
    const uploadArea = document.getElementById('receipt-upload-area');
    const fileInput = document.getElementById('document');
    
    if (uploadArea && fileInput) {
        uploadArea.addEventListener('click', function() {
            fileInput.click();
        });
    }
});
</script>
