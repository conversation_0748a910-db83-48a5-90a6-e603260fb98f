<?php

// _actions.php
// Renders toolbar with view tabs and action buttons.
// Expects:
//   - $expense: array with expense data (must include 'id')
//   - $currentView: string, either 'details' or 'document'

// Make sure we have the expense data
if (!isset($expense) && isset($viewData['expense'])) {
    error_log("[Debug] _actions.php - Getting expense from viewData");
    $expense = $viewData['expense'];
}

// Debug expense data
if (isset($expense) && isset($expense['id'])) {
    error_log("[Debug] _actions.php - Expense ID: " . $expense['id']);
} else {
    error_log("[Warning] _actions.php - No expense ID available");
    // Provide a fallback to prevent errors
    if (!isset($expense) || !is_array($expense)) {
        $expense = ['id' => 0];
    } elseif (!isset($expense['id'])) {
        $expense['id'] = 0;
    }
}

$currentView = isset($currentView) && in_array($currentView, ['details','document'])
    ? $currentView
    : 'details';
$id = htmlspecialchars($expense['id'], ENT_QUOTES, 'UTF-8');
?>
<div class="action-group u-spacing-inline-md" role="tablist" aria-label="Expense view tabs">
    <a href="/expenses/<?= $id ?>" 
       class="button tab-button <?= $currentView === 'details' ? 'active' : '' ?>" 
       role="tab" 
       aria-selected="<?= $currentView === 'details' ? 'true' : 'false' ?>"
       aria-label="View details">
        <span class="sr-only">Details</span>Details
    </a>
    <a href="/expenses/<?= $id ?>/document" 
       class="button tab-button <?= $currentView === 'document' ? 'active' : '' ?>" 
       role="tab" 
       aria-selected="<?= $currentView === 'document' ? 'true' : 'false' ?>"
       aria-label="View document">
        <span class="sr-only">Document</span>Document
    </a>
</div>

<div class="action-group u-spacing-inline-md" role="toolbar" aria-label="Expense actions">
    <a href="/expenses/<?= $id ?>/edit" class="button secondary" aria-label="Edit expense">
        <span class="sr-only">Edit</span>Edit
    </a>
    <a href="/expenses/<?= $id ?>/delete" class="button danger" aria-label="Delete expense">
        <span class="sr-only">Delete</span>Delete
    </a>
    <a href="/expenses" class="button secondary" aria-label="Back to list">
        <span class="sr-only">Back to list</span>Back to List
    </a>
    <a href="#" onclick="window.print(); return false;" class="button secondary" aria-label="Print">
        <span class="sr-only">Print</span>Print
    </a>
    <a href="/expenses/<?= $id ?>/document/download" class="button info" aria-label="Download document">
        <span class="sr-only">Download</span>Download
    </a>
</div>
