<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\PaymentMethod;

class PaymentMethodService extends BaseService
{
    protected function getModelClass(): string
    {
        return PaymentMethod::class;
    }

    public function getPaymentMethodById(int $id): ?array
    {
        return $this->find($id);
    }

    public function getAllPaymentMethods(): array
    {
        try {
            return $this->all();
        } catch (\Throwable $e) {
            // log error
            error_log("PaymentMethodService::getAllPaymentMethods() - Error: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Override the all method to ensure we return complete payment method objects
     */
    public function all(): array
    {
        $methods = parent::all();

        // Log the methods array for debugging
        error_log("PaymentMethodService::all() - Methods: " . json_encode($methods));

        // If the methods array only contains IDs, convert it to a proper array of objects
        if (!empty($methods) && is_array($methods) && !isset($methods[0]['method'])) {
            error_log("PaymentMethodService::all() - Converting IDs to objects");
            $result = [];
            foreach ($methods as $methodId) {
                if (is_numeric($methodId)) {
                    $method = $this->find((int)$methodId);
                    error_log("PaymentMethodService::all() - Finding method ID $methodId: " . json_encode($method));
                    if ($method) {
                        $result[] = $method;
                    } else {
                        // If we can't find the method, create a placeholder
                        $placeholder = [
                            'id' => $methodId,
                            'method' => 'Payment Method ' . $methodId
                        ];
                        error_log("PaymentMethodService::all() - Using placeholder: " . json_encode($placeholder));
                        $result[] = $placeholder;
                    }
                }
            }
            error_log("PaymentMethodService::all() - Result: " . json_encode($result));
            return $result;
        }

        return $methods;
    }
}
