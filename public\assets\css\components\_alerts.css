/* ===== ALERT COMPONENTS ===== */
.notice {
    padding: var(--spacing-md);
    background-color: rgba(0, 0, 0, 0.04);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-md);
    text-align: center;
    color: var(--text-muted);
    border-left: 3px solid #aaa;
    font-size: var(--font-size-sm);
  }

  .alert {
    padding: var(--spacing) var(--spacing-md);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-md);
    font-weight: var(--font-weight-medium);
    position: relative;
    padding-left: 3rem;
    line-height: 1.5;
    display: flex;
    align-items: center;
  }

  .alert::before {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.2rem;
    font-weight: bold;
  }

  .alert-success {
    background-color: hsl(140, 40%, 94%);
    color: hsl(140, 40%, 25%);
    border: 1px solid hsl(140, 40%, 88%);
  }

  .alert-success::before {
    content: "✓";
    color: var(--success-color);
  }

  .alert-danger {
    background-color: hsl(0, 65%, 95%);
    color: hsl(0, 65%, 30%);
    border: 1px solid hsl(0, 65%, 90%);
  }

  .alert-danger::before {
    content: "!";
    color: var(--danger-color);
  }

  .alert-warning {
    background-color: hsl(45, 100%, 94%);
    color: hsl(45, 75%, 30%);
    border: 1px solid hsl(45, 100%, 86%);
  }

  .alert-warning::before {
    content: "⚠️";
    font-size: 1rem;
  }

  .alert-info {
    background-color: hsl(210, 100%, 95%);
    color: hsl(210, 100%, 30%);
    border: 1px solid hsl(210, 100%, 90%);
  }

  .alert-info::before {
    content: "ℹ️";
    font-size: 1rem;
  }

  .alert ul {
    margin: var(--spacing-sm) 0 0;
    padding-left: var(--spacing-md);
  }