/* ===== DESIGN TOKENS ===== */
/* Updated: Refined core color palette with richer, high-contrast tones for improved visual hierarchy */

:root {
  /* Core Design Tokens - Enhanced for Visual Impact */
  --card-box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.04); /* Subtle depth for card components */
  --transition-default: all 0.25s cubic-bezier(0.4, 0, 0.2, 1); /* Standard easing for smooth interactions */

  /* Color Psychology System - Enhanced Contrast & Richness */
  --primary-hue: 210; /* Blue - Trust, Security, Stability */
  --color-primary: hsl(var(--primary-hue), 65%, 48%); /* Richer, higher contrast primary */
  --primary-color: var(--color-primary); /* Legacy compatibility */
  --primary-light: hsl(var(--primary-hue), 60%, 62%);
  --primary-dark: hsl(var(--primary-hue), 70%, 35%);
  
  --color-secondary: hsl(154, 75%, 38%); /* Richer green - Growth, Success */
  --secondary-color: var(--color-secondary); /* Legacy compatibility */
  
  --color-accent: hsl(280, 45%, 58%); /* Enhanced purple - Focus, Creativity with improved contrast */

  /* Color Variables with RGB Values for Opacity Control */
  --primary-rgb: 58, 123, 213;
  --secondary-rgb: 108, 117, 125;
  --success-rgb: 40, 167, 69;
  --danger-rgb: 220, 53, 69;
  --warning-rgb: 255, 193, 7;
  --info-rgb: 23, 162, 184;
  --light-rgb: 248, 249, 250;
  --dark-rgb: 52, 58, 64;
  --grey-rgb: 173, 181, 189;

  /* Accent Colors - Psychological Impact - Enhanced Vibrancy */
  --accent-calm: hsl(195, 90%, 48%); /* Calm, Clarity - richer tone */
  --accent-energy: hsl(35, 95%, 50%); /* Energy, Optimism - enhanced vibrancy */
  --accent-focus: var(--color-accent); /* Focus, Creativity - uses enhanced accent color */
  --color-accent-teal: hsl(180, 75%, 40%); /* Complementary teal - deeper tone */
  --color-accent-slate: hsl(215, 35%, 40%); /* Complementary slate - richer contrast */

  /* Interaction & Motion Tokens */
  --hover-lift: 0 4px 12px rgba(0, 0, 0, 0.15);
  --focus-ring: 0 0 0 3px rgba(var(--primary-rgb), 0.2);
  --active-press: 0 2px 4px rgba(0, 0, 0, 0.2);

  /* Fine Motion Timing */
  --timing-gentle: cubic-bezier(0.4, 0, 0.2, 1);
  --timing-bounce-soft: cubic-bezier(0.68, -0.55, 0.265, 1.55);

  /* Extra Spacing */
  --space-xxs: 0.125rem;

  /* Neutrals - Reduced Cognitive Load - WCAG AA Optimized */
  --dark-color: hsl(210, 29%, 20%);
  --light-color: hsl(210, 17%, 98%);
  --grey-100: hsl(210, 20%, 96%);
  --grey-200: hsl(210, 16%, 93%);
  --grey-300: hsl(210, 14%, 87%);
  --grey-400: hsl(210, 14%, 78%);
  --grey-500: hsl(210, 11%, 65%);
  --grey-600: hsl(210, 7%, 50%);
  --grey-700: hsl(210, 9%, 35%);
  --grey-800: hsl(210, 10%, 25%);
  --grey-900: hsl(210, 11%, 18%);

  /* Feedback colors - Intuitive Emotional Response - WCAG AA Compliant */
  --success-color: hsl(145, 68%, 35%); /* Achievement - darker for better contrast */
  --success-light: hsl(145, 68%, 95%);
  --success-border: hsl(145, 68%, 75%);
  --success-dark: hsl(145, 68%, 25%);

  --warning-color: hsl(37, 90%, 45%); /* Caution - darker for better contrast */
  --warning-light: hsl(37, 90%, 95%);
  --warning-border: hsl(37, 90%, 75%);
  --warning-dark: hsl(37, 90%, 35%);

  --danger-color: hsl(6, 75%, 48%); /* Alert - darker for better contrast */
  --danger-light: hsl(6, 75%, 95%);
  --danger-border: hsl(6, 75%, 75%);
  --danger-dark: hsl(6, 75%, 38%);

  --info-color: hsl(195, 85%, 35%); /* Information - darker for better contrast */
  --info-light: hsl(195, 85%, 95%);
  --info-border: hsl(195, 85%, 75%);
  --info-dark: hsl(195, 85%, 25%);

  /* Financial Data Colors - UX Color Coding */
  --color-income: hsl(145, 68%, 35%); /* Green for positive income */
  --color-expense: hsl(6, 75%, 48%); /* Red for expenses/outgoing */

  /* Text colors - Optimized for Readability - WCAG AA Compliant */
  --text-color: hsl(210, 15%, 18%);
  --text-muted: hsl(210, 10%, 40%);
  --text-light: hsl(0, 0%, 100%);

  /* Typography - Font Psychology */
  --font-main: 'Nunito', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', sans-serif;
  --font-mono: 'Menlo', 'Monaco', 'Consolas', 'Liberation Mono', 'Courier New', monospace;
  --font-size-xs: 0.75rem; /* 12px */
  --font-size-sm: 0.875rem; /* 14px */
  --font-size: 1rem;       /* 16px */
  --font-size-md: 1.125rem; /* 18px */
  --font-size-lg: 1.25rem;  /* 20px */
  --font-size-xl: 1.5rem;   /* 24px */
  --font-size-xxl: 2rem;    /* 32px */

  /* Monospace Font Sizes - For Financial Data */
  --font-mono-xs: 0.75rem;  /* 12px */
  --font-mono-sm: 0.875rem; /* 14px */
  --font-mono: 1rem;        /* 16px */
  --font-mono-md: 1.125rem; /* 18px */
  --font-mono-lg: 1.25rem;  /* 20px */

  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --line-height-tight: 1.2;
  --line-height: 1.5;
  --line-height-loose: 1.8;

  /* Layout - Spatial Psychology */
  --radius-xs: 2px;
  --radius-sm: 4px;
  --radius: 6px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;

  /* Legacy border-radius tokens for backward compatibility */
  --border-radius-sm: var(--radius-sm);
  --border-radius: var(--radius-md);
  --border-radius-lg: var(--radius-lg);
  --box-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.08), 0 1px 2px rgba(0, 0, 0, 0.05);
  --box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1), 0 1px 4px rgba(0, 0, 0, 0.06);
  --box-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --box-shadow-raised: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  /* Transitions - Motion Psychology */
  --transition-fast: all 0.15s ease;
  --transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-bounce: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --transition-smooth: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);

  /* Spacing - Balanced Visual Hierarchy */
  --spacing-xs: 0.25rem; /* 4px */
  --spacing-sm: 0.5rem;  /* 8px */
  --spacing: 1rem;       /* 16px */
  --spacing-md: 1.5rem;  /* 24px */
  --spacing-lg: 2rem;    /* 32px */
  --spacing-xl: 3rem;    /* 48px */

  /* Action Button Variables */
  --action-button-icon-only-size: 2.75rem; /* 44px minimum touch target */
  --action-button-icon-size: 1.5rem;       /* Icon size within button */

  /* Fluid Sizing Variables */
  --action-button-fluid-size: clamp(2.75rem, 6vw, 3.5rem);
  --action-icon-fluid-size: clamp(1.5rem, 4vw, 2rem);
  --table-action-button-size: clamp(1.5rem, 3vw, 2rem);
  --table-action-icon-size: clamp(0.75rem, 2vw, 1rem);
  --dropdown-fluid-width: clamp(180px, 30vw, 300px);
  --table-mobile-padding: clamp(8px, 4vw, 16px);
  --table-mobile-label-width: clamp(100px, 25vw, 140px);

  /* Table Typography and Spacing */
  font-size: 1rem;
  --table-font-size: clamp(0.875rem, 1.2vw, 1rem);
  padding: var(--spacing);
  --table-cell-padding: clamp(0.5rem, 1vw, 1rem);

  /* Layout Constants */
  --header-height: 60px;
  --sidebar-width: 240px;
  --content-max-width: 1200px;
  --container-max-width: 1200px;

  /* Document Viewer Constants */
  --card-bg: white;
  --border-color: var(--grey-300);

  /* Z-index Layers */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;

  /* Modal Overlay */
  --modal-overlay: rgba(0, 0, 0, 0.5);
}

/* Dark Mode Variables - For Future Implementation */
:root.dark-theme {
  --primary-color: hsl(var(--primary-hue), 50%, 58%);
  --primary-light: hsl(var(--primary-hue), 55%, 70%);
  --primary-dark: hsl(var(--primary-hue), 50%, 48%);
  --light-color: hsl(210, 29%, 15%);
  --dark-color: hsl(210, 17%, 98%);
  --text-color: hsl(210, 15%, 90%);
  --text-muted: hsl(210, 10%, 70%);
  --text-light: hsl(0, 0%, 100%);
  --grey-100: hsl(210, 20%, 20%);
  --grey-200: hsl(210, 16%, 25%);
  --grey-300: hsl(210, 14%, 30%);
  --grey-400: hsl(210, 14%, 35%);
  --grey-500: hsl(210, 11%, 40%);
  --grey-600: hsl(210, 7%, 56%);
  --grey-700: hsl(210, 9%, 70%);
  --grey-800: hsl(210, 10%, 80%);
  --grey-900: hsl(210, 11%, 90%);

}
