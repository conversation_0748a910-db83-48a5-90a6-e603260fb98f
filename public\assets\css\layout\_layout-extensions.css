/* ===== LAYOUT EXTENSIONS ===== */

/* Container */
.container {
  width: 100%;
  max-width: var(--content-max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  transition: var(--transition);
}

/* Main Content Area */
main.container {
  padding-top: var(--spacing-lg);
  padding-bottom: var(--spacing-xl);
  min-height: calc(100vh - var(--header-height) - 60px); /* Ensures footer stays at bottom */
  width: calc(100% - var(--sidebar-width));
  transition: var(--transition);
}

/* Page Sections */
.page-section {
  margin-bottom: var(--spacing-xl);
  animation: fadeIn 0.5s ease-out;
}

/* Grid System */
.grid {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: var(--spacing-lg);
  width: 100%;
}

/* Grid Column Spans */
.col-1 { grid-column: span 1; }
.col-2 { grid-column: span 2; }
.col-3 { grid-column: span 3; }
.col-4 { grid-column: span 4; }
.col-5 { grid-column: span 5; }
.col-6 { grid-column: span 6; }
.col-7 { grid-column: span 7; }
.col-8 { grid-column: span 8; }
.col-9 { grid-column: span 9; }
.col-10 { grid-column: span 10; }
.col-11 { grid-column: span 11; }
.col-12 { grid-column: span 12; }

/* Flex Layouts */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.justify-between {
  justify-content: space-between;
}

.justify-center {
  justify-content: center;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.flex-wrap {
  flex-wrap: wrap;
}

.gap-sm {
  gap: var(--spacing-sm);
}

.gap {
  gap: var(--spacing);
}

.gap-lg {
  gap: var(--spacing-lg);
}

/* Page Header - Consistent Pattern */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--grey-300);
}

.header-content h2 {
  margin-bottom: 0;
  border-bottom: none;
  padding-bottom: 0;
}

.header-actions {
  display: flex;
  gap: var(--spacing-sm);
}

/* Responsive Adjustments */
@media (max-width: 1024px) {
  .grid {
    gap: var(--spacing);
  }

  .col-md-6 { grid-column: span 6; }
  .col-md-12 { grid-column: span 12; }
}

@media (max-width: 768px) {
  main.container {
    margin-left: 0;
    width: 100%;
    padding-top: calc(var(--header-height) + var(--spacing));
  }

  .container {
    padding-left: var(--spacing);
    padding-right: var(--spacing);
  }

  .grid {
    gap: var(--spacing-sm);
  }

  .col-sm-12 { grid-column: span 12; }

  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .header-actions {
    width: 100%;
    justify-content: flex-start;
    margin-top: var(--spacing-sm);
  }
}
