/* ===== EXTENDED FORM INPUTS ===== */
/* Based on Cognitive Load & Visual Feedback Principles */

/* Base Input Styling with Psychological Cues */
input[type="text"],
input[type="number"],
input[type="date"],
input[type="password"],
input[type="email"],
input[type="search"],
input[type="tel"],
input[type="url"],
select,
textarea {
  width: 100%;
  padding: 0.9rem 1rem;
  border: 2px solid var(--grey-300);
  border-radius: var(--border-radius);
  transition: var(--transition);
  background-color: var(--grey-100);
  font-family: var(--font-main);
  font-size: var(--font-size);
  color: var(--text-color);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.03);
}

/* Focus State - Clear Visual Feedback */
input:focus,
select:focus,
textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  background-color: var(--light-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.15);
}

/* Placeholder Animation - Subtle Direction */
input::placeholder,
textarea::placeholder {
  color: var(--text-muted);
  transition: transform 0.2s, opacity 0.2s;
}

input:focus::placeholder,
textarea:focus::placeholder {
  opacity: 0.7;
  transform: translateX(10px);
}

/* Disabled State - Clear Visual Cue */
input:disabled,
select:disabled,
textarea:disabled {
  background-color: var(--grey-200);
  border-color: var(--grey-300);
  color: var(--grey-600);
  cursor: not-allowed;
  opacity: 0.7;
}

/* Error State - Attention-Grabbing */
input.error,
select.error,
textarea.error {
  border-color: var(--danger-color);
  background-color: rgba(var(--danger-rgb), 0.05);
}

input.error:focus,
select.error:focus,
textarea.error:focus {
  box-shadow: 0 0 0 3px rgba(var(--danger-rgb), 0.15);
}

/* Success State - Positive Reinforcement */
input.success,
select.success,
textarea.success {
  border-color: var(--success-color);
  background-color: rgba(var(--success-rgb), 0.05);
}

input.success:focus,
select.success:focus,
textarea.success:focus {
  box-shadow: 0 0 0 3px rgba(var(--success-rgb), 0.15);
}

/* Select Styling - Improved Affordance */
select {
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%236c757d' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 16px;
  padding-right: 2.5rem;
}

select:focus {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%233a7bd5' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
}

/* Textarea Styling - Improved Usability */
textarea {
  min-height: 120px;
  resize: vertical;
  line-height: 1.5;
}

/* Checkbox & Radio - Custom Styling */
input[type="checkbox"],
input[type="radio"] {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
}

input[type="checkbox"] + label,
input[type="radio"] + label {
  position: relative;
  padding-left: 30px;
  cursor: pointer;
  display: inline-block;
  line-height: 20px;
  font-size: var(--font-size);
  user-select: none;
}

input[type="checkbox"] + label:before,
input[type="radio"] + label:before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 20px;
  height: 20px;
  border: 2px solid var(--grey-400);
  background-color: var(--light-color);
  transition: var(--transition);
}

input[type="checkbox"] + label:before {
  border-radius: 4px;
}

input[type="radio"] + label:before {
  border-radius: 50%;
}

input[type="checkbox"]:checked + label:before,
input[type="radio"]:checked + label:before {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

input[type="checkbox"]:checked + label:after {
  content: '';
  position: absolute;
  left: 7px;
  top: 3px;
  width: 6px;
  height: 12px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

input[type="radio"]:checked + label:after {
  content: '';
  position: absolute;
  left: 6px;
  top: 6px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: white;
}

input[type="checkbox"]:focus + label:before,
input[type="radio"]:focus + label:before {
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.15);
}

/* Input Groups - Visual Grouping */
.input-group {
  display: flex;
  position: relative;
}

.input-group input {
  border-radius: 0;
  position: relative;
  flex: 1 1 auto;
  width: 1%;
}

.input-group input:not(:last-child) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.input-group input:not(:first-child) {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  margin-left: -1px;
}

.input-group-prepend,
.input-group-append {
  display: flex;
}

.input-group-text {
  display: flex;
  align-items: center;
  padding: 0.9rem 1rem;
  background-color: var(--grey-200);
  border: 2px solid var(--grey-300);
  color: var(--text-muted);
  font-size: var(--font-size);
  white-space: nowrap;
}

.input-group-prepend .input-group-text {
  border-top-left-radius: var(--border-radius);
  border-bottom-left-radius: var(--border-radius);
  border-right: 0;
}

.input-group-append .input-group-text {
  border-top-right-radius: var(--border-radius);
  border-bottom-right-radius: var(--border-radius);
  border-left: 0;
}

/* Floating Labels - Reduced Cognitive Load */
.form-floating {
  position: relative;
}

.form-floating input,
.form-floating select,
.form-floating textarea {
  height: calc(3.5rem + 2px);
  padding: 1.625rem 1rem 0.625rem;
}

.form-floating label {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  padding: 1rem 1rem;
  pointer-events: none;
  border: 1px solid transparent;
  transform-origin: 0 0;
  transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
  color: var(--text-muted);
}

.form-floating input:focus ~ label,
.form-floating input:not(:placeholder-shown) ~ label,
.form-floating select:focus ~ label,
.form-floating select:not([value=""]):not(:focus) ~ label,
.form-floating textarea:focus ~ label,
.form-floating textarea:not(:placeholder-shown) ~ label {
  opacity: 0.65;
  transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  input[type="text"],
  input[type="number"],
  input[type="date"],
  input[type="password"],
  input[type="email"],
  input[type="search"],
  input[type="tel"],
  input[type="url"],
  select,
  textarea {
    padding: 0.75rem 0.9rem;
    font-size: var(--font-size-sm);
  }

  .form-floating input,
  .form-floating select,
  .form-floating textarea {
    height: calc(3.2rem + 2px);
    padding: 1.5rem 0.9rem 0.5rem;
  }

  .form-floating label {
    padding: 0.9rem 0.9rem;
  }
}
