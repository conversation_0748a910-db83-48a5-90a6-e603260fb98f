function initializeModal(modal)
{
    if (!modal) {
        return;
    }

    setupCloseButton(modal);
    setupOutsideClickHandler(modal);
    setupEscapeKeyHandler(modal);
    setupFocusTrap(modal);
}

function setupCloseButton(modal)
{
    const closeButton = modal.querySelector('.close');
    if (closeButton) {
        closeButton.addEventListener('click', () => closeModal(modal));
    }
}

function setupOutsideClickHandler(modal)
{
    modal.addEventListener('click', event => {
        if (event.target === modal) {
            closeModal(modal);
        }
    });
}

function setupEscapeKeyHandler(modal)
{
    document.addEventListener('keydown', event => {
        if (event.key === 'Escape' && modal.style.display === 'block') {
            closeModal(modal);
        }
    });
}

function setupFocusTrap(modal)
{
    const modalContent = modal.querySelector('.modal-content');
    if (modalContent) {
        trapFocus(modalContent);
    }
}

function openModal(modal)
{
    if (!modal) {
        return;
    }

    savePreviousFocus(modal);
    showModal(modal);
    preventBodyScrolling();
    setInitialFocus(modal);
}

function savePreviousFocus(modal)
{
    modal.setAttribute('data-previous-focus', document.activeElement.id || '');
}

function showModal(modal)
{
    modal.style.display = 'block';
}

function preventBodyScrolling()
{
    document.body.classList.add('modal-open');
}

function setInitialFocus(modal)
{
    const focusableElements = getFocusableElements(modal);
    if (focusableElements.length > 0) {
        focusableElements[0].focus();
    }
}

function closeModal(modal)
{
    if (!modal) {
        return;
    }

    hideModal(modal);
    restoreBodyScrolling();
    restorePreviousFocus(modal);
}

function hideModal(modal)
{
    modal.style.display = 'none';
}

function restoreBodyScrolling()
{
    document.body.classList.remove('modal-open');
}

function restorePreviousFocus(modal)
{
    const previousFocusId = modal.getAttribute('data-previous-focus');
    if (!previousFocusId) {
        return;
    }

    const previousFocusElement = document.getElementById(previousFocusId);
    if (previousFocusElement) {
        previousFocusElement.focus();
    }
}

function trapFocus(element)
{
    const focusableElements = getFocusableElements(element);
    if (focusableElements.length === 0) {
        return;
    }

    const firstFocusableElement = focusableElements[0];
    const lastFocusableElement = focusableElements[focusableElements.length - 1];

    element.addEventListener('keydown', event => {
        if (event.key !== 'Tab') {
            return;
        }

        handleTabNavigation(event, firstFocusableElement, lastFocusableElement);
    });
}

function getFocusableElements(element)
{
    return element.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    );
}

function handleTabNavigation(event, firstFocusableElement, lastFocusableElement)
{
    if (event.shiftKey && document.activeElement === firstFocusableElement) {
        event.preventDefault();
        lastFocusableElement.focus();
    } else if (!event.shiftKey && document.activeElement === lastFocusableElement) {
        event.preventDefault();
        firstFocusableElement.focus();
    }
}

export { initializeModal, openModal, closeModal };
