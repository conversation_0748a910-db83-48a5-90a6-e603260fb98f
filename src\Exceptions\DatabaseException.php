<?php

declare(strict_types=1);

namespace App\Exceptions;

use Throwable;

class DatabaseException extends BaseException
{
    private string $sqlState;

    /**
     * Constructor for DatabaseException
     *
     * @param string $message Error message
     * @param int|string $code Error code or SQL state
     * @param Throwable|null $previous Previous exception
     */
    public function __construct(string $message, int|string $code = 0, ?Throwable $previous = null)
    {
        // Store the original SQL state code if it's a string
        $this->sqlState = is_string($code) ? $code : '';

        // Convert string error codes to integers to avoid type errors
        // Use a hash of the string to create a unique numeric code
        $numericCode = is_string($code) ? crc32($code) : $code;

        parent::__construct($message, $numericCode, $previous);
    }

    public function getSqlState(): string
    {
        return $this->sqlState;
    }

    public function __toString(): string
    {
        $sqlStateInfo = $this->sqlState ? " [SQL State: {$this->sqlState}]" : '';
        return sprintf(
            '%s: [%d]%s: %s',
            static::class,
            $this->code,
            $sqlStateInfo,
            $this->message
        );
    }
}
