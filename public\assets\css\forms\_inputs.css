/* ===== FORM INPUTS ===== */
input[type="text"],
input[type="number"],
input[type="date"],
input[type="password"],
input[type="email"],
input[type="search"],
input[type="tel"],
input[type="url"],
select,
textarea {
  width: 100%;
  padding: 0.9rem 1rem;
  border: 2px solid var(--grey-300);
  border-radius: var(--border-radius);
  transition: all 0.25s ease;
  background-color: var(--grey-100);
  font-family: var(--font-main);
  font-size: var(--font-size);
  color: var(--text-color);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.03);
}

input:hover,
select:hover,
textarea:hover {
  border-color: var(--grey-500);
  background-color: white;
}

input:focus,
select:focus,
textarea:focus {
  border-color: var(--primary-color);
  outline: none;
  box-shadow: 0 0 0 3px rgba(58, 123, 213, 0.15);
  background-color: white;
}

/* Placeholder animation */
input::placeholder,
textarea::placeholder {
  transition: var(--transition-fast);
  opacity: 0.7;
}

input:focus::placeholder,
textarea:focus::placeholder {
  opacity: 0.5;
  transform: translateY(-5px);
}

/* Required field indicator */
.required-field::after {
  content: "*";
  color: var(--danger-color);
  margin-left: var(--spacing-xs);
}