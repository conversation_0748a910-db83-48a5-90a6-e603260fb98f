<?php

declare(strict_types=1);

namespace App\Models;

class EmailLog extends BaseModel
{
    protected static function getTableName(): string
    {
        return 'email_logs';
    }

    protected static function getColumnList(): string
    {
        return "id, recipient, subject, status, error, sent_at";
    }

    public static function log(string $recipient, string $subject, bool $success, ?string $error = null): int
    {
        return self::transaction(function () use ($recipient, $subject, $success, $error) {
            $sql = sprintf(
                "INSERT INTO %s (recipient, subject, status, error, sent_at) 
                 VALUES (?, ?, ?, ?, NOW())",
                self::getTableName()
            );

            $status = $success ? 'sent' : 'failed';

            self::executeSql($sql, [$recipient, $subject, $status, $error]);
            return (int)self::getDb()->lastInsertId();
        });
    }

    public static function getRecent(int $limit = 50): array
    {
        $sql = sprintf(
            "SELECT %s FROM %s ORDER BY sent_at DESC LIMIT ?",
            self::getColumnList(),
            self::getTableName()
        );

        return self::fetchMany($sql, [$limit]);
    }

    public static function getByRecipient(string $recipient, int $limit = 50): array
    {
        $sql = sprintf(
            "SELECT %s FROM %s WHERE recipient = ? ORDER BY sent_at DESC LIMIT ?",
            self::getColumnList(),
            self::getTableName()
        );

        return self::fetchMany($sql, [$recipient, $limit]);
    }

    public static function getFailures(int $limit = 50): array
    {
        $sql = sprintf(
            "SELECT %s FROM %s WHERE status = 'failed' ORDER BY sent_at DESC LIMIT ?",
            self::getColumnList(),
            self::getTableName()
        );

        return self::fetchMany($sql, [$limit]);
    }

    public static function getStats(): array
    {
        $sql = "SELECT 
                    COUNT(*) as total,
                    SUM(CASE WHEN status = 'sent' THEN 1 ELSE 0 END) as sent,
                    SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed,
                    DATE(sent_at) as date
                FROM " . self::getTableName() . "
                GROUP BY DATE(sent_at)
                ORDER BY date DESC
                LIMIT 30";

        return self::fetchMany($sql);
    }
}
