/* Premium Theme for Personal Expense Tracker
   A modern, clean design system with enhanced visual elements
   ========================================================== */

:root {
    /* Color System */
    --primary-color: #6366f1;
    --primary-light: #818cf8;
    --primary-dark: #4f46e5;
    --secondary-color: #10b981;
    --secondary-light: #34d399;
    --secondary-dark: #059669;
    --danger-color: #ef4444;
    --danger-light: #f87171;
    --warning-color: #f59e0b;
    --info-color: #3b82f6;

    /* Text colors */
    --text-dark: #111827;
    --text-medium: #374151;
    --text-light: #6b7280;
    --text-muted: #9ca3af;

    /* Background colors */
    --bg-white: #ffffff;
    --bg-light: #f9fafb;
    --bg-gray-50: #f9fafb;
    --bg-gray-100: #f3f4f6;
    --bg-gray-200: #e5e7eb;

    /* Border colors */
    --border-light: #e5e7eb;
    --border-medium: #d1d5db;

    /* Shadow styles */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Border radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.375rem;
    --radius-lg: 0.5rem;
    --radius-xl: 0.75rem;
    --radius-2xl: 1rem;

    /* Transitions */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Base styles */
body {
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    color: var(--text-medium);
    background-color: var(--bg-gray-50);
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

h1, h2, h3, h4, h5, h6 {
    font-family: 'Poppins', sans-serif;
    color: var(--text-dark);
    font-weight: 600;
    line-height: 1.3;
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--primary-dark);
}

/* Container */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

/* Card styles */
.card {
    background-color: var(--bg-white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--border-light);
    overflow: hidden;
    transition: box-shadow var(--transition-fast), transform var(--transition-fast);
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.card-header {
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid var(--border-light);
    background-color: var(--bg-light);
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    padding: 1.25rem 1.5rem;
    border-top: 1px solid var(--border-light);
    background-color: var(--bg-light);
}

/* Button styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.625rem 1.25rem;
    border-radius: var(--radius-md);
    font-weight: 500;
    font-size: 0.875rem;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    border: none;
    outline: none;
}

.btn i, .btn svg {
    font-size: 1rem;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
    box-shadow: 0 2px 4px rgba(99, 102, 241, 0.2);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(99, 102, 241, 0.3);
}

.btn-secondary {
    background-color: var(--bg-white);
    color: var(--text-medium);
    border: 1px solid var(--border-medium);
}

.btn-secondary:hover {
    background-color: var(--bg-gray-50);
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
    box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2);
}

.btn-danger:hover {
    background-color: #dc2626;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(239, 68, 68, 0.3);
}

.btn-success {
    background-color: var(--secondary-color);
    color: white;
    box-shadow: 0 2px 4px rgba(16, 185, 129, 0.2);
}

.btn-success:hover {
    background-color: var(--secondary-dark);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(16, 185, 129, 0.3);
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
}

/* Form elements */
.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-dark);
    font-size: 0.875rem;
}

.form-control {
    display: block;
    width: 100%;
    padding: 0.625rem 0.875rem;
    font-size: 0.875rem;
    line-height: 1.5;
    color: var(--text-dark);
    background-color: var(--bg-white);
    background-clip: padding-box;
    border: 1px solid var(--border-medium);
    border-radius: var(--radius-md);
    transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.form-control:focus {
    border-color: var(--primary-light);
    outline: 0;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.15);
}

/* Table styles */
.table {
    width: 100%;
    margin-bottom: 1rem;
    color: var(--text-medium);
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 0.75rem;
    vertical-align: top;
    border-top: 1px solid var(--border-light);
}

.table thead th {
    vertical-align: bottom;
    border-bottom: 2px solid var(--border-light);
    color: var(--text-dark);
    font-weight: 600;
    background-color: var(--bg-light);
}

.table tbody tr:hover {
    background-color: var(--bg-gray-50);
}

/* Badge styles */
.badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 500;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: var(--radius-full);
}

.badge-primary {
    background-color: rgba(99, 102, 241, 0.1);
    color: var(--primary-color);
    border: 1px solid rgba(99, 102, 241, 0.2);
}

.badge-success {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--secondary-color);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.badge-danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
    border: 1px solid rgba(239, 68, 68, 0.2);
}

.badge-warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
    border: 1px solid rgba(245, 158, 11, 0.2);
}

.badge-info {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--info-color);
    border: 1px solid rgba(59, 130, 246, 0.2);
}

/* Alert styles */
.alert {
    position: relative;
    padding: 1rem 1.25rem;
    margin-bottom: 1rem;
    border: 1px solid transparent;
    border-radius: var(--radius-md);
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
}

.alert-icon {
    font-size: 1.25rem;
    flex-shrink: 0;
}

.alert-content {
    flex: 1;
}

.alert-heading {
    color: inherit;
    margin-top: 0;
    margin-bottom: 0.5rem;
}

.alert-link {
    font-weight: 600;
}

.alert-primary {
    background-color: rgba(99, 102, 241, 0.1);
    color: var(--primary-dark);
    border-color: rgba(99, 102, 241, 0.2);
}

.alert-success {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--secondary-dark);
    border-color: rgba(16, 185, 129, 0.2);
}

.alert-danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: #b91c1c;
    border-color: rgba(239, 68, 68, 0.2);
}

.alert-warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: #b45309;
    border-color: rgba(245, 158, 11, 0.2);
}

.alert-info {
    background-color: rgba(59, 130, 246, 0.1);
    color: #1e40af;
    border-color: rgba(59, 130, 246, 0.2);
}

/* Navbar styles */
.navbar {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 0;
}

.navbar-brand {
    display: inline-block;
    padding-top: 0.3125rem;
    padding-bottom: 0.3125rem;
    margin-right: 1rem;
    font-size: 1.25rem;
    font-weight: 600;
    line-height: inherit;
    white-space: nowrap;
    color: var(--text-dark);
    text-decoration: none;
}

.navbar-nav {
    display: flex;
    flex-direction: row;
    padding-left: 0;
    margin-bottom: 0;
    list-style: none;
    gap: 1rem;
}

.nav-link {
    display: block;
    padding: 0.5rem 0;
    color: var(--text-medium);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.nav-link:hover,
.nav-link:focus {
    color: var(--primary-color);
}

.nav-link.active {
    color: var(--primary-color);
    font-weight: 500;
}

/* Utility classes */
.text-primary { color: var(--primary-color) !important; }
.text-secondary { color: var(--secondary-color) !important; }
.text-success { color: var(--secondary-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-info { color: var(--info-color) !important; }
.text-dark { color: var(--text-dark) !important; }
.text-medium { color: var(--text-medium) !important; }
.text-light { color: var(--text-light) !important; }
.text-muted { color: var(--text-muted) !important; }

.bg-primary { background-color: var(--primary-color) !important; }
.bg-secondary { background-color: var(--secondary-color) !important; }
.bg-success { background-color: var(--secondary-color) !important; }
.bg-danger { background-color: var(--danger-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-info { background-color: var(--info-color) !important; }
.bg-light { background-color: var(--bg-light) !important; }
.bg-white { background-color: var(--bg-white) !important; }
.bg-gray-50 { background-color: var(--bg-gray-50) !important; }
.bg-gray-100 { background-color: var(--bg-gray-100) !important; }
.bg-gray-200 { background-color: var(--bg-gray-200) !important; }

.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow-md { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }
.shadow-xl { box-shadow: var(--shadow-xl) !important; }

.rounded-sm { border-radius: var(--radius-sm) !important; }
.rounded-md { border-radius: var(--radius-md) !important; }
.rounded-lg { border-radius: var(--radius-lg) !important; }
.rounded-xl { border-radius: var(--radius-xl) !important; }
.rounded-2xl { border-radius: var(--radius-2xl) !important; }
.rounded-full { border-radius: 9999px !important; }

.border { border: 1px solid var(--border-light) !important; }
.border-top { border-top: 1px solid var(--border-light) !important; }
.border-right { border-right: 1px solid var(--border-light) !important; }
.border-bottom { border-bottom: 1px solid var(--border-light) !important; }
.border-left { border-left: 1px solid var(--border-light) !important; }

.d-flex { display: flex !important; }
.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.justify-content-start { justify-content: flex-start !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-around { justify-content: space-around !important; }
.align-items-start { align-items: flex-start !important; }
.align-items-end { align-items: flex-end !important; }
.align-items-center { align-items: center !important; }
.align-items-baseline { align-items: baseline !important; }
.align-items-stretch { align-items: stretch !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }
.flex-grow-1 { flex-grow: 1 !important; }
.flex-shrink-0 { flex-shrink: 0 !important; }
.flex-shrink-1 { flex-shrink: 1 !important; }

.gap-1 { gap: 0.25rem !important; }
.gap-2 { gap: 0.5rem !important; }
.gap-3 { gap: 0.75rem !important; }
.gap-4 { gap: 1rem !important; }
.gap-5 { gap: 1.25rem !important; }
.gap-6 { gap: 1.5rem !important; }
.gap-8 { gap: 2rem !important; }
.gap-10 { gap: 2.5rem !important; }
.gap-12 { gap: 3rem !important; }

.m-0 { margin: 0 !important; }
.m-1 { margin: 0.25rem !important; }
.m-2 { margin: 0.5rem !important; }
.m-3 { margin: 0.75rem !important; }
.m-4 { margin: 1rem !important; }
.m-5 { margin: 1.25rem !important; }
.m-6 { margin: 1.5rem !important; }
.m-8 { margin: 2rem !important; }
.m-10 { margin: 2.5rem !important; }
.m-12 { margin: 3rem !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: 0.25rem !important; }
.mt-2 { margin-top: 0.5rem !important; }
.mt-3 { margin-top: 0.75rem !important; }
.mt-4 { margin-top: 1rem !important; }
.mt-5 { margin-top: 1.25rem !important; }
.mt-6 { margin-top: 1.5rem !important; }
.mt-8 { margin-top: 2rem !important; }
.mt-10 { margin-top: 2.5rem !important; }
.mt-12 { margin-top: 3rem !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: 0.25rem !important; }
.mb-2 { margin-bottom: 0.5rem !important; }
.mb-3 { margin-bottom: 0.75rem !important; }
.mb-4 { margin-bottom: 1rem !important; }
.mb-5 { margin-bottom: 1.25rem !important; }
.mb-6 { margin-bottom: 1.5rem !important; }
.mb-8 { margin-bottom: 2rem !important; }
.mb-10 { margin-bottom: 2.5rem !important; }
.mb-12 { margin-bottom: 3rem !important; }

.ml-0 { margin-left: 0 !important; }
.ml-1 { margin-left: 0.25rem !important; }
.ml-2 { margin-left: 0.5rem !important; }
.ml-3 { margin-left: 0.75rem !important; }
.ml-4 { margin-left: 1rem !important; }
.ml-5 { margin-left: 1.25rem !important; }
.ml-6 { margin-left: 1.5rem !important; }
.ml-8 { margin-left: 2rem !important; }
.ml-10 { margin-left: 2.5rem !important; }
.ml-12 { margin-left: 3rem !important; }

.mr-0 { margin-right: 0 !important; }
.mr-1 { margin-right: 0.25rem !important; }
.mr-2 { margin-right: 0.5rem !important; }
.mr-3 { margin-right: 0.75rem !important; }
.mr-4 { margin-right: 1rem !important; }
.mr-5 { margin-right: 1.25rem !important; }
.mr-6 { margin-right: 1.5rem !important; }
.mr-8 { margin-right: 2rem !important; }
.mr-10 { margin-right: 2.5rem !important; }
.mr-12 { margin-right: 3rem !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: 0.25rem !important; }
.p-2 { padding: 0.5rem !important; }
.p-3 { padding: 0.75rem !important; }
.p-4 { padding: 1rem !important; }
.p-5 { padding: 1.25rem !important; }
.p-6 { padding: 1.5rem !important; }
.p-8 { padding: 2rem !important; }
.p-10 { padding: 2.5rem !important; }
.p-12 { padding: 3rem !important; }

.pt-0 { padding-top: 0 !important; }
.pt-1 { padding-top: 0.25rem !important; }
.pt-2 { padding-top: 0.5rem !important; }
.pt-3 { padding-top: 0.75rem !important; }
.pt-4 { padding-top: 1rem !important; }
.pt-5 { padding-top: 1.25rem !important; }
.pt-6 { padding-top: 1.5rem !important; }
.pt-8 { padding-top: 2rem !important; }
.pt-10 { padding-top: 2.5rem !important; }
.pt-12 { padding-top: 3rem !important; }

.pb-0 { padding-bottom: 0 !important; }
.pb-1 { padding-bottom: 0.25rem !important; }
.pb-2 { padding-bottom: 0.5rem !important; }
.pb-3 { padding-bottom: 0.75rem !important; }
.pb-4 { padding-bottom: 1rem !important; }
.pb-5 { padding-bottom: 1.25rem !important; }
.pb-6 { padding-bottom: 1.5rem !important; }
.pb-8 { padding-bottom: 2rem !important; }
.pb-10 { padding-bottom: 2.5rem !important; }
.pb-12 { padding-bottom: 3rem !important; }

.pl-0 { padding-left: 0 !important; }
.pl-1 { padding-left: 0.25rem !important; }
.pl-2 { padding-left: 0.5rem !important; }
.pl-3 { padding-left: 0.75rem !important; }
.pl-4 { padding-left: 1rem !important; }
.pl-5 { padding-left: 1.25rem !important; }
.pl-6 { padding-left: 1.5rem !important; }
.pl-8 { padding-left: 2rem !important; }
.pl-10 { padding-left: 2.5rem !important; }
.pl-12 { padding-left: 3rem !important; }

.pr-0 { padding-right: 0 !important; }
.pr-1 { padding-right: 0.25rem !important; }
.pr-2 { padding-right: 0.5rem !important; }
.pr-3 { padding-right: 0.75rem !important; }
.pr-4 { padding-right: 1rem !important; }
.pr-5 { padding-right: 1.25rem !important; }
.pr-6 { padding-right: 1.5rem !important; }
.pr-8 { padding-right: 2rem !important; }
.pr-10 { padding-right: 2.5rem !important; }
.pr-12 { padding-right: 3rem !important; }

.w-100 { width: 100% !important; }
.h-100 { height: 100% !important; }

.text-center { text-align: center !important; }
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }

.font-weight-normal { font-weight: 400 !important; }
.font-weight-medium { font-weight: 500 !important; }
.font-weight-semibold { font-weight: 600 !important; }
.font-weight-bold { font-weight: 700 !important; }

.text-uppercase { text-transform: uppercase !important; }
.text-lowercase { text-transform: lowercase !important; }
.text-capitalize { text-transform: capitalize !important; }

.font-size-xs { font-size: 0.75rem !important; }
.font-size-sm { font-size: 0.875rem !important; }
.font-size-md { font-size: 1rem !important; }
.font-size-lg { font-size: 1.125rem !important; }
.font-size-xl { font-size: 1.25rem !important; }
.font-size-2xl { font-size: 1.5rem !important; }
.font-size-3xl { font-size: 1.875rem !important; }
.font-size-4xl { font-size: 2.25rem !important; }

.line-height-tight { line-height: 1.25 !important; }
.line-height-normal { line-height: 1.5 !important; }
.line-height-relaxed { line-height: 1.75 !important; }

.position-relative { position: relative !important; }
.position-absolute { position: absolute !important; }
.position-fixed { position: fixed !important; }
.position-sticky { position: sticky !important; }

.top-0 { top: 0 !important; }
.right-0 { right: 0 !important; }
.bottom-0 { bottom: 0 !important; }
.left-0 { left: 0 !important; }

.z-0 { z-index: 0 !important; }
.z-10 { z-index: 10 !important; }
.z-20 { z-index: 20 !important; }
.z-30 { z-index: 30 !important; }
.z-40 { z-index: 40 !important; }
.z-50 { z-index: 50 !important; }

.invisible { visibility: hidden !important; }
.visible { visibility: visible !important; }

.overflow-auto { overflow: auto !important; }
.overflow-hidden { overflow: hidden !important; }
.overflow-visible { overflow: visible !important; }
.overflow-scroll { overflow: scroll !important; }

.d-none { display: none !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-block { display: block !important; }
.d-grid { display: grid !important; }

/* Responsive utilities */
@media (min-width: 576px) {
    .d-sm-none { display: none !important; }
    .d-sm-inline { display: inline !important; }
    .d-sm-inline-block { display: inline-block !important; }
    .d-sm-block { display: block !important; }
    .d-sm-flex { display: flex !important; }
    .d-sm-grid { display: grid !important; }
}

@media (min-width: 768px) {
    .d-md-none { display: none !important; }
    .d-md-inline { display: inline !important; }
    .d-md-inline-block { display: inline-block !important; }
    .d-md-block { display: block !important; }
    .d-md-flex { display: flex !important; }
    .d-md-grid { display: grid !important; }
}

@media (min-width: 992px) {
    .d-lg-none { display: none !important; }
    .d-lg-inline { display: inline !important; }
    .d-lg-inline-block { display: inline-block !important; }
    .d-lg-block { display: block !important; }
    .d-lg-flex { display: flex !important; }
    .d-lg-grid { display: grid !important; }
}

@media (min-width: 1200px) {
    .d-xl-none { display: none !important; }
    .d-xl-inline { display: inline !important; }
    .d-xl-inline-block { display: inline-block !important; }
    .d-xl-block { display: block !important; }
    .d-xl-flex { display: flex !important; }
    .d-xl-grid { display: grid !important; }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideInDown {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideInLeft {
    from {
        transform: translateX(-20px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(20px);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.animate-fade-in { animation: fadeIn var(--transition-normal); }
.animate-slide-up { animation: slideInUp var(--transition-normal); }
.animate-slide-down { animation: slideInDown var(--transition-normal); }
.animate-slide-left { animation: slideInLeft var(--transition-normal); }
.animate-slide-right { animation: slideInRight var(--transition-normal); }
.animate-pulse { animation: pulse 2s infinite; }

/* Print styles */
@media print {
    .no-print { display: none !important; }

    body {
        background-color: white;
    }

    .container {
        max-width: 100%;
        padding: 0;
    }

    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }

    a {
        text-decoration: none !important;
        color: var(--text-dark) !important;
    }
}