<?php

declare(strict_types=1);

namespace App\Models;

use App\Exceptions\InvalidArgumentException;

class EmailTemplate extends BaseModel
{
    protected static function getTableName(): string
    {
        return 'email_templates';
    }

    protected static function getColumnList(): string
    {
        return "id, name, subject, content, created_at, updated_at";
    }

    public static function find(int $id): ?array
    {
        $sql = sprintf(
            "SELECT %s FROM %s WHERE id = ?",
            self::getColumnList(),
            self::getTableName()
        );

        return self::fetchOne($sql, [$id]);
    }

    public static function findByName(string $name): ?array
    {
        $sql = sprintf(
            "SELECT %s FROM %s WHERE name = ?",
            self::getColumnList(),
            self::getTableName()
        );

        return self::fetchOne($sql, [$name]);
    }

    public static function all(): array
    {
        $sql = sprintf(
            "SELECT %s FROM %s ORDER BY name ASC",
            self::getColumnList(),
            self::getTableName()
        );

        return self::fetchMany($sql);
    }

    public static function createTemplate(string $name, string $subject, string $content): int
    {
        self::validateName($name);
        self::validateSubject($subject);
        self::validateContent($content);

        $data = [
            'name' => $name,
            'subject' => $subject,
            'content' => $content,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        return self::create($data);
    }

    public static function create(array $data): int
    {
        if (isset($data['name'])) {
            self::validateName($data['name']);
        }

        if (isset($data['subject'])) {
            self::validateSubject($data['subject']);
        }

        if (isset($data['content'])) {
            self::validateContent($data['content']);
        }

        return self::transaction(function () use ($data) {
            $sql = sprintf(
                "INSERT INTO %s (name, subject, content, created_at, updated_at)
                 VALUES (:name, :subject, :content, NOW(), NOW())",
                self::getTableName()
            );

            self::executeSql($sql, $data);
            return (int)self::getDb()->lastInsertId();
        });
    }

    public static function updateTemplate(
        int $id,
        ?string $name = null,
        ?string $subject = null,
        ?string $content = null
    ): bool {
        $template = self::find($id);
        if (!$template) {
            return false;
        }

        $data = [];

        if ($name !== null) {
            self::validateName($name);
            $data['name'] = $name;
        }

        if ($subject !== null) {
            self::validateSubject($subject);
            $data['subject'] = $subject;
        }

        if ($content !== null) {
            self::validateContent($content);
            $data['content'] = $content;
        }

        if (empty($data)) {
            return false;
        }

        $data['updated_at'] = date('Y-m-d H:i:s');

        return self::update($id, $data);
    }

    public static function update(int $id, array $data): bool
    {
        if (isset($data['name'])) {
            self::validateName($data['name']);
        }

        if (isset($data['subject'])) {
            self::validateSubject($data['subject']);
        }

        if (isset($data['content'])) {
            self::validateContent($data['content']);
        }

        $data['updated_at'] = date('Y-m-d H:i:s');

        $template = self::find($id);
        if (!$template) {
            return false;
        }

        return parent::update($id, $data);
    }

    public static function delete(int $id): bool
    {
        $template = self::find($id);
        if (!$template) {
            return false;
        }

        return parent::delete($id);
    }

    /**
     * Render an email template with variables
     *
     * @param array $template The template data
     * @param array $variables Variables to replace in the template
     * @return string The rendered template
     */
    public static function render(array $template, array $variables = []): string
    {
        $content = $template['content'];

        foreach ($variables as $key => $value) {
            // Validate the key to prevent injection
            if (!preg_match('/^[a-zA-Z0-9_]+$/', $key)) {
                continue;
            }

            // Convert value to string and escape HTML if it's not already HTML content
            $stringValue = (string)$value;

            // If the value doesn't contain HTML tags, escape it
            if ($stringValue === strip_tags($stringValue)) {
                $stringValue = htmlspecialchars($stringValue, ENT_QUOTES, 'UTF-8');
            }

            // Replace the placeholder with the value
            $content = str_replace('{{' . $key . '}}', $stringValue, $content);
        }

        return $content;
    }

    private static function validateName(string $name): void
    {
        $trimmed = trim($name);
        if (empty($trimmed) || strlen($trimmed) > 100) {
            throw new InvalidArgumentException("Template name must be between 1 and 100 characters");
        }
    }

    private static function validateSubject(string $subject): void
    {
        $trimmed = trim($subject);
        if (empty($trimmed) || strlen($trimmed) > 255) {
            throw new InvalidArgumentException("Subject must be between 1 and 255 characters");
        }
    }

    private static function validateContent(string $content): void
    {
        if (empty(trim($content))) {
            throw new InvalidArgumentException("Template content cannot be empty");
        }
    }
}
