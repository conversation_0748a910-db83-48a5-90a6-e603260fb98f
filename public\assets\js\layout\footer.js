function initializeFooter()
{
    setupModalContainer();
    setupToastContainer();
}

function setupModalContainer()
{
    const modalContainer = document.getElementById('modal-container');
    if (!modalContainer) {
        return;
    }

    setupModalCloseEvents(modalContainer);
}

function setupModalCloseEvents(modalContainer)
{
    modalContainer.addEventListener('click', e => {
        if (e.target === modalContainer) {
            closeModalContainer();
        }
    });

    document.addEventListener('keydown', e => {
        if (e.key === 'Escape' && modalContainer.getAttribute('aria-hidden') === 'false') {
            closeModalContainer();
        }
    });
}

function openModalContainer(content)
{
    const modalContainer = document.getElementById('modal-container');
    if (!modalContainer) {
        return;
    }

    clearAndAddContent(modalContainer, content);
    showModalContainer(modalContainer);
    preventBodyScrolling();
}

function clearAndAddContent(container, content)
{
    container.innerHTML = '';
    container.appendChild(content);
}

function showModalContainer(modalContainer)
{
    modalContainer.setAttribute('aria-hidden', 'false');
}

function preventBodyScrolling()
{
    document.body.classList.add('modal-open');
}

function closeModalContainer()
{
    const modalContainer = document.getElementById('modal-container');
    if (!modalContainer) {
        return;
    }

    hideModalContainer(modalContainer);
    restoreBodyScrolling();
}

function hideModalContainer(modalContainer)
{
    modalContainer.setAttribute('aria-hidden', 'true');
}

function restoreBodyScrolling()
{
    document.body.classList.remove('modal-open');
}

function setupToastContainer()
{
    const toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        return;
    }

    toastContainer.setAttribute('aria-live', 'polite');
}

function showToast(message, type = 'info', duration = 3000)
{
    const toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        return;
    }

    const toast = createToastElement(message, type);
    addToastToContainer(toast, toastContainer);
    animateToastEntry(toast);
    scheduleToastRemoval(toast, duration);
}

function createToastElement(message, type)
{
    const toast = document.createElement('div');
    toast.className = `toast toast - ${type}`;
    toast.textContent = message;
    return toast;
}

function addToastToContainer(toast, container)
{
    container.appendChild(toast);
}

function animateToastEntry(toast)
{
    setTimeout(() => {
        toast.classList.add('show');
    }, 10);
}

function scheduleToastRemoval(toast, duration)
{
    setTimeout(() => {
        toast.classList.remove('show');

        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }, duration);
}

export { initializeFooter, openModalContainer, closeModalContainer, showToast };
