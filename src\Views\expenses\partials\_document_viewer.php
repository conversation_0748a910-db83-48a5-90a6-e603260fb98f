<div class="card expense-card document-card">
    <div class="card-header">
        <h4 class="card-title">
            <svg class="svg-icon" aria-hidden="true" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M14 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6zm1 17H9v-2h6v2zm0-4H9v-2h6v2zm-3-9V3.5L18.5 9H12z"/>
            </svg>
            Document Preview
        </h4>
    </div>
    <div class="card-body">
        <section role="region" aria-label="Document preview" class="document-preview">
            <h3 class="u-type-scale-medium">Document Preview</h3>
            <?php if (!empty($document['isImage']) && $document['isImage']) : ?>
                <div class="image-preview">
                    <img src="data:<?= htmlspecialchars($document['fileInfo']['mime_type']) ?>;base64,<?= base64_encode($document['content']) ?>"
                         alt="Document Image Preview" class="document-image-preview" style="object-fit: contain; transition: box-shadow var(--motion-duration-fast) var(--motion-easing-standard);" onmouseover="this.style.boxShadow='var(--hover-lift)'" onmouseout="this.style.boxShadow='none'">
                </div>
            <?php elseif (!empty($document['isPdf']) && $document['isPdf']) : ?>
                <div class="pdf-preview text-center">
                    <svg class="svg-icon pdf-icon" aria-hidden="true" viewBox="0 0 24 24">
                        <path d="M20 2H8c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-8.5 7.5c0 .83-.67 1.5-1.5 1.5H9v2H7.5V7H10c.83 0 1.5.67 1.5 1.5v1zm5 2c0 .83-.67 1.5-1.5 1.5h-2.5V7H15c.83 0 1.5.67 1.5 1.5v3zM4 6h2v12H4V6z"/>
                    </svg>
                    <p>PDF documents cannot be displayed in the browser.</p>
                    <a href="/expenses/<?= htmlspecialchars($expense['id']) ?>/document/download"
                       class="button primary">Download PDF</a>
                </div>
            <?php elseif (!empty($document['isText']) && $document['isText']) : ?>
                <div class="text-preview">
                    <pre class="document-text-preview"><?= htmlspecialchars($document['content']) ?></pre>
                </div>
            <?php else : ?>
                <div class="unknown-preview text-center">
                    <svg class="svg-icon unknown-icon" aria-hidden="true" viewBox="0 0 24 24">
                        <path d="M14 2H6c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6zm-2 15c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1zm1-5h-2V7h2v5z"/>
                    </svg>
                    <p>This document type cannot be previewed.</p>
                    <a href="/expenses/<?= htmlspecialchars($expense['id']) ?>/document/download"
                       class="button primary">Download Document</a>
                </div>
            <?php endif; ?>
        </div>

        <div class="document-actions mt-md">
            <a href="/expenses/<?= htmlspecialchars($expense['id']) ?>/document/download"
               class="button secondary">Download Original Document</a>
        </section>

        <section role="region" aria-label="Receipt preview" class="receipt-preview-section u-spacing-stack-md">
            <h3 class="u-type-scale-medium">Receipt Preview</h3>
            <?php if (!empty($document['receiptData']['html'])) : ?>
                <div class="receipt-viewer">
                    <div class="receipt-paper">
                        <details>
                            <summary style="cursor: pointer; color: var(--color-primary); text-decoration: underline;">View Receipt Details</summary>
                            <?= $document['receiptData']['html'] ?>
                        </details>
                    </div>
                    <div class="receipt-shadow"></div>
                </div>
                <div class="receipt-actions mt-md">
                    <a href="/expenses/<?= htmlspecialchars($expense['id']) ?>/receipt/download"
                       class="button info">Download Receipt</a>
                </div>
            <?php else : ?>
                <div class="no-receipt text-center">
                    <svg class="svg-icon mb-sm warn-icon" aria-hidden="true" viewBox="0 0 24 24">
                        <path d="M13 7h-2v4H7v2h4v4h2v-4h4v-2h-4V7zm-1-5C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2z"/>
                    </svg>
                    <p class="mb-md">No receipt has been extracted yet.</p>
                    <form method="POST" action="/expenses/<?= htmlspecialchars($expense['id']) ?>/receipt/process">
                        <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($csrf_token) ?>">
                        <button type="submit" class="button primary"
                                onclick="return confirm('Analyze the document to extract receipt data?');">
                            Extract Data
                        </button>
                    </form>
                </div>
            <?php endif; ?>
        </section>
    </div>
</div>
