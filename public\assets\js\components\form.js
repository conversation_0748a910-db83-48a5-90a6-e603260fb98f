function initializeForms()
{
    const forms = document.querySelectorAll('form:not([data-no-enhance])');

    forms.forEach(form => {
        form.addEventListener('submit', validateForm);
        setupInputValidation(form);
        form.addEventListener('submit', addLoadingState);
    });
}

function setupInputValidation(form)
{
    const inputs = form.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('blur', validateInput);
        input.addEventListener('input', clearInputError);
    });
}

function validateForm(event)
{
    const form = event.target;
    const inputs = form.querySelectorAll('input, select, textarea');
    let isValid = true;

    inputs.forEach(input => {
        if (!validateInput({ target: input })) {
            isValid = false;
        }
    });

    if (!isValid) {
        event.preventDefault();
        focusFirstInvalidInput(form);
    }
}

function focusFirstInvalidInput(form)
{
    const firstInvalidInput = form.querySelector('.is-invalid');
    if (firstInvalidInput) {
        firstInvalidInput.focus();
    }
}

function validateInput(event)
{
    const input = event.target;
    const value = input.value.trim();

    if (isExemptFromValidation(input, value)) {
        clearInputError({ target: input });
        return true;
    }

    if (isRequiredButEmpty(input, value)) {
        setInputError(input, 'This field is required');
        return false;
    }

    if (!validateByInputType(input, value)) {
        return false;
    }

    clearInputError({ target: input });
    return true;
}

function isExemptFromValidation(input, value)
{
    return !input.hasAttribute('required') && value === '';
}

function isRequiredButEmpty(input, value)
{
    return input.hasAttribute('required') && value === '';
}

function validateByInputType(input, value)
{
    if (input.type === 'email' && value !== '') {
        return validateEmail(input, value);
    }

    if (input.type === 'password' && input.hasAttribute('data-min-length') && value !== '') {
        return validatePassword(input, value);
    }

    if (input.hasAttribute('data-confirm-password')) {
        return validatePasswordConfirmation(input, value);
    }

    if (input.type === 'number' && value !== '') {
        return validateNumber(input, value);
    }

    return true;
}

function validateEmail(input, value)
{
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(value)) {
        setInputError(input, 'Please enter a valid email address');
        return false;
    }
    return true;
}

function validatePassword(input, value)
{
    const minLength = parseInt(input.getAttribute('data-min-length'));
    if (value.length < minLength) {
        setInputError(input, `Password must be at least ${minLength} characters`);
        return false;
    }
    return true;
}

function validatePasswordConfirmation(input, value)
{
    const passwordInput = document.getElementById(input.getAttribute('data-confirm-password'));
    if (passwordInput && value !== passwordInput.value) {
        setInputError(input, 'Passwords do not match');
        return false;
    }
    return true;
}

function validateNumber(input, value)
{
    const min = input.hasAttribute('min') ? parseFloat(input.getAttribute('min')) : null;
    const max = input.hasAttribute('max') ? parseFloat(input.getAttribute('max')) : null;
    const numValue = parseFloat(value);

    if (min !== null && numValue < min) {
        setInputError(input, `Value must be at least ${min}`);
        return false;
    }

    if (max !== null && numValue > max) {
        setInputError(input, `Value must be at most ${max}`);
        return false;
    }

    return true;
}

function setInputError(input, message)
{
    const formGroup = input.closest('.form-group');
    if (!formGroup) {
        return;
    }

    input.classList.add('is-invalid');

    let errorElement = formGroup.querySelector('.invalid-feedback');
    if (!errorElement) {
        errorElement = createErrorElement(formGroup);
    }

    errorElement.textContent = message;
}

function createErrorElement(formGroup)
{
    const errorElement = document.createElement('div');
    errorElement.className = 'invalid-feedback';
    formGroup.appendChild(errorElement);
    return errorElement;
}

function clearInputError(event)
{
    const input = event.target;
    const formGroup = input.closest('.form-group');
    if (!formGroup) {
        return;
    }

    input.classList.remove('is-invalid');

    const errorElement = formGroup.querySelector('.invalid-feedback');
    if (errorElement) {
        errorElement.textContent = '';
    }
}

function addLoadingState(event)
{
    const form = event.target;

    if (form.classList.contains('is-submitting')) {
        return;
    }

    form.classList.add('is-submitting');
    updateSubmitButton(form);
}

function updateSubmitButton(form)
{
    const submitButton = form.querySelector('[type="submit"]');
    if (!submitButton) {
        return;
    }

    submitButton.classList.add('is-loading');

    const originalText = submitButton.textContent;
    submitButton.setAttribute('data-original-text', originalText);

    const loadingText = submitButton.getAttribute('data-loading-text') || 'Submitting...';
    submitButton.textContent = loadingText;

    addLoadingSpinner(submitButton);
    submitButton.disabled = true;
}

function addLoadingSpinner(button)
{
    const spinner = document.createElement('span');
    spinner.className = 'spinner';
    button.appendChild(spinner);
}

export { initializeForms, validateForm, validateInput, setInputError, clearInputError };
