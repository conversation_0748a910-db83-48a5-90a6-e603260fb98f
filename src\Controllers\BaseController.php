<?php

declare(strict_types=1);

namespace App\Controllers;

use App\Core\Response;
use App\Core\Security\Validator;
use App\Core\Security\CSRF;
use App\Core\Security\Session;
use App\Core\LogManager;
use App\Utils\ArrayHelper;
use InvalidArgumentException;

abstract class BaseController
{
    protected array $scripts = [];
    protected array $stylesheets = [];

    // View Rendering Methods
    protected function view(string $viewPath, array $data = []): Response
    {
        error_log("[Debug] BaseController::view - View path: {$viewPath}");
        error_log("[Debug] BaseController::view - Data keys: " . json_encode(array_keys($data)));

        // Make sure we have the expense data if it's an expense view
        if (strpos($viewPath, 'expenses/show') !== false && !isset($data['expense'])) {
            error_log("[Warning] BaseController::view - Missing expense data for expense view");
        }

        $data = $this->prepareViewData($data);
        $viewFile = $this->resolveViewPath($viewPath);
        $layoutFile = $this->resolveLayoutPath();

        $content = $this->renderPhpFile($viewFile, $data);
        $layoutData = $this->prepareLayoutData($content, $data);

        $htmlContent = $this->renderPhpFile($layoutFile, $layoutData);
        $this->clearSessionFlash();

        return Response::html($htmlContent);
    }

    private function prepareViewData(array $data): array
    {
        error_log("[Debug] BaseController::prepareViewData - Preparing view data");

        // Add scripts and stylesheets
        $data['scripts'] = $this->scripts;
        $data['stylesheets'] = $this->stylesheets;

        // Add CSRF token if not set
        if (!isset($data['csrf_token'])) {
            $data['csrf_token'] = $this->getCsrfToken();
        }

        // Make sure viewData is set for templates that expect it
        if (!isset($data['viewData']) && !empty($data)) {
            error_log("[Debug] BaseController::prepareViewData - Setting viewData");
            $data['viewData'] = $data;
        }

        // Merge session flash data
        $data['errors'] = ArrayHelper::ensureArray(Session::getFlash('errors'));
        $data['old']    = ArrayHelper::ensureArray(Session::getFlash('old'));

        // Add type guards for known array fields
        foreach (['categories','merchants','paymentMethods','paymentMethodsWithKeys'] as $key) {
            $data[$key] = ArrayHelper::ensureArray($data[$key] ?? null);
        }

        return $data;
    }

    private function resolveViewPath(string $viewPath): string
    {
        $viewFile = __DIR__ . "/../../src/Views/{$viewPath}.php";

        if (!file_exists($viewFile)) {
            throw new \RuntimeException("View file not found: {$viewFile}");
        }

        return $viewFile;
    }

    private function resolveLayoutPath(): string
    {
        return __DIR__ . "/../../src/Views/layouts/main.php";
    }

    private function prepareLayoutData(string $content, array $data): array
    {
        return [
            'content' => $content,
            'scripts' => $this->scripts,
            'stylesheets' => $this->stylesheets,
            'title' => $data['title'] ?? 'Personal Expense Tracker'
        ];
    }

    private function renderPhpFile(string $file, array $data = []): string
    {
        if (!file_exists($file)) {
            error_log("File does not exist: {$file}");
            throw new \RuntimeException("File not found: {$file}");
        }

        // Debug logging disabled

        // Preserve the array keys for paymentMethods
        if (isset($data['paymentMethods']) && is_array($data['paymentMethods'])) {
            $paymentMethodsWithKeys = [];
            foreach ($data['paymentMethods'] as $key => $value) {
                $paymentMethodsWithKeys["pm_$key"] = $value;
            }
            $data['paymentMethodsWithKeys'] = $paymentMethodsWithKeys;
        }

        // Extract variables from the data array
        extract($data);
        ob_start();

        try {
            require $file;

            return ob_get_clean() ?: '';
        } catch (\Throwable $e) {
            ob_end_clean();
            throw new \RuntimeException("Error rendering file {$file}: " . $e->getMessage(), 0, $e);
        }
    }

    protected function direct(string $viewPath): Response
    {
        $viewFile = $this->resolveViewPath($viewPath);
        $content = $this->renderPhpFile($viewFile, []);

        return Response::html($content);
    }

    // Response Generation Methods
    protected function json(array $data, int $statusCode = 200): Response
    {
        return Response::json($data, $statusCode);
    }

    protected function error(string $message = 'An error occurred', int $statusCode = 500): Response
    {
        return Response::error($statusCode, $message);
    }

    protected function notFound(string $message = 'Resource not found'): Response
    {
        return Response::notFound($message);
    }

    protected function redirect(string $path, ?string $message = null, string $type = 'success'): Response
    {
        if ($message) {
            Session::set($type, $message);
        }

        // Create the redirect response
        $response = Response::redirect($path);

        // Return the response instead of sending it and exiting
        return $response;
    }

    protected function redirectWithErrors(string $path, string|array $message, array $input = []): Response
    {
        error_log("[Debug] BaseController::redirectWithErrors - Path: " . $path);
        error_log("[Debug] BaseController::redirectWithErrors - Message: " . (is_array($message) ? json_encode($message) : $message));
        error_log("[Debug] BaseController::redirectWithErrors - Input: " . json_encode($input));

        $errors = is_array($message) ? $message : [$message];
        $errorId = bin2hex(random_bytes(4));

        if (!is_iterable($errors)) {
            $errors = [$errors];
        }

        $this->storeErrorsInSession($errorId, $errors);
        $this->logValidationError($errorId, $errors, $path);
        $this->storeInputInSession($input);

        // Create the redirect response
        $response = Response::redirect($path);

        // Return the response instead of sending it and exiting
        return $response;
    }

    private function storeErrorsInSession(string $errorId, array $errors): void
    {
        error_log("[Debug] BaseController::storeErrorsInSession - Error ID: " . $errorId);
        error_log("[Debug] BaseController::storeErrorsInSession - Errors: " . json_encode($errors));

        Session::set('error_id', $errorId);
        Session::set('errors', $errors);

        error_log("[Debug] BaseController::storeErrorsInSession - Stored in session");
    }

    private function logValidationError(string $errorId, array $errors, string $path): void
    {
        LogManager::getLogger()?->warning("Form validation error (ID: {$errorId})", [
            'errors' => $errors,
            'path' => $path
        ]);
    }

    private function storeInputInSession(array $input): void
    {
        error_log("[Debug] BaseController::storeInputInSession - Input before: " . json_encode($input));

        if (empty($input)) {
            error_log("[Debug] BaseController::storeInputInSession - Input is empty, not storing");
            return;
        }

        unset($input['password'], $input['password_confirmation']);

        error_log("[Debug] BaseController::storeInputInSession - Input after: " . json_encode($input));

        Session::set('old', $input);
        error_log("[Debug] BaseController::storeInputInSession - Stored in session");
    }

    // Validation Methods
    protected function validate(array $data, array $rules): array
    {
        return (new Validator($rules))->validate($data);
    }

    protected function validateCsrf(?string $token): void
    {
        if (!$token || !CSRF::validateToken($token)) {
            throw new InvalidArgumentException('Invalid security token. Please try again.');
        }
    }

    // Session and Authentication Methods
    protected function getCsrfToken(): string
    {
        return CSRF::generateToken();
    }

    protected function getUserId(): int
    {
        if (!Session::has('user_id')) {
            throw new InvalidArgumentException('User not authenticated');
        }

        return (int)Session::get('user_id');
    }

    protected function setUserId(int $userId): void
    {
        Session::set('user_id', $userId);
    }

    protected function isAuthenticated(): bool
    {
        error_log("BaseController::isAuthenticated() called");

        // Check if user is authenticated via session
        if (!isset($_SESSION['user_id'])) {
            error_log("No user_id in session, checking for remember token");

            // Try to authenticate with remember me cookie if available
            if (isset($_COOKIE['remember_token'])) {
                error_log("Remember token found in cookie: " . substr($_COOKIE['remember_token'], 0, 8) . "...");

                // Attempt to authenticate with the token
                $token = $_COOKIE['remember_token'];
                $tokenData = \App\Models\User::findRememberToken($token);

                if (!$tokenData) {
                    error_log("Token not found in database");
                    $this->clearRememberTokenCookie();
                    return false;
                }

                error_log("Token found in database: " . json_encode($tokenData));

                if (time() < strtotime($tokenData['expires_at'])) {
                    error_log("Token is still valid, expires at: " . $tokenData['expires_at']);
                    $user = \App\Models\User::findById($tokenData['user_id']);

                    if (!$user) {
                        error_log("User not found for token");
                        $this->clearRememberTokenCookie();
                        return false;
                    }

                    error_log("User found: " . json_encode($user));

                    if ($user['user_status_id'] === \App\Models\User::STATUS_ACTIVE) {
                        error_log("User is active, starting session");

                        // Start session for the user
                        $_SESSION['user_id'] = $user['id'];
                        $_SESSION['user_email'] = $user['email'];
                        $_SESSION['user_name'] = $user['name'];
                        $_SESSION['remember_me'] = true;
                        $_SESSION['authenticated'] = true;
                        $_SESSION['last_activity'] = time();

                        // Regenerate session ID for security
                        session_regenerate_id(true);

                        error_log("User authenticated via remember token: " . $user['id']);
                        error_log("New session data: " . json_encode($_SESSION));
                        return true;
                    } else {
                        error_log("User is not active");
                    }
                } else {
                    error_log("Token has expired: " . $tokenData['expires_at']);
                }

                // If we get here, the token is invalid
                error_log("Token is invalid, clearing cookie");
                $this->clearRememberTokenCookie();
                return false;
            } else {
                error_log("No remember token found in cookie");
            }

            error_log("Authentication failed: no session and no valid remember token");
            return false;
        }

        error_log("User ID found in session: " . $_SESSION['user_id']);

        // For non-persistent sessions (no remember me), check for session timeout
        if (!isset($_SESSION['remember_me']) || $_SESSION['remember_me'] === false) {
            error_log("Non-persistent session, checking for timeout");

            $lastActivity = $_SESSION['last_activity'] ?? 0;
            $timeSinceLastActivity = time() - $lastActivity;
            $sessionTimeout = 1800; // 30 minutes

            error_log("Last activity: {$lastActivity}, time since: {$timeSinceLastActivity}s");
            error_log("Session timeout setting: {$sessionTimeout}s");

            // Session timeout after 30 minutes of inactivity
            if ($timeSinceLastActivity > $sessionTimeout) {
                error_log("Session timeout after {$timeSinceLastActivity}s (max: {$sessionTimeout}s)");
                $this->logoutUser();
                return false;
            }

            // Update last activity time
            $_SESSION['last_activity'] = time();
            error_log("Updated last activity time to: " . time());
        } else {
            error_log("Persistent session (remember_me is set), no timeout check needed");
        }

        error_log("Authentication successful");
        return true;
    }

    /**
     * Clear the remember token cookie
     */
    protected function clearRememberTokenCookie(): void
    {
        setcookie(
            'remember_token',
            '',
            [
                'expires' => time() - 3600,
                'path' => '/',
                'domain' => '',
                'secure' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on',
                'httponly' => true,
                'samesite' => 'Lax'
            ]
        );
    }

    protected function logoutUser(): ?Response
    {
        session_unset();
        session_destroy();

        if (isset($_COOKIE[session_name()])) {
            $params = session_get_cookie_params();
            setcookie(
                session_name(),
                '',
                [
                    'expires' => time() - 3600,
                    'path' => $params['path'],
                    'domain' => $params['domain'],
                    'secure' => $params['secure'],
                    'httponly' => $params['httponly'],
                    'samesite' => $params['samesite'] ?? 'Lax'
                ]
            );
        }

        if (isset($_COOKIE['remember_token'])) {
            setcookie(
                'remember_token',
                '',
                [
                    'expires' => time() - 3600,
                    'path' => '/',
                    'domain' => '',
                    'secure' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on',
                    'httponly' => true,
                    'samesite' => 'Lax'
                ]
            );
        }

        return null;
    }

    private function clearSessionFlash(): void
    {
        Session::remove('success');
        Session::remove('errors');
        Session::remove('old');
    }

    // Input Handling Methods
    protected function getPostValue(string $key, string $default = ''): string
    {
        return $this->sanitizeStringValue($_POST[$key] ?? $default, $default);
    }

    protected function getQueryValue(string $key, string $default = ''): string
    {
        return $this->sanitizeStringValue($_GET[$key] ?? $default, $default);
    }

    private function sanitizeStringValue($value, string $default): string
    {
        return is_string($value) ? htmlspecialchars($value, ENT_QUOTES, 'UTF-8') : $default;
    }

    protected function getPostInt(string $key, int $default = 0): int
    {
        return $this->validateIntValue($_POST[$key] ?? null, $default);
    }

    protected function getQueryInt(string $key, int $default = 0): int
    {
        return $this->validateIntValue($_GET[$key] ?? null, $default);
    }

    private function validateIntValue($value, int $default): int
    {
        return filter_var($value, FILTER_VALIDATE_INT) !== false ? (int)$value : $default;
    }

    protected function getPostFloat(string $key, float $default = 0.0): float
    {
        return $this->validateFloatValue($_POST[$key] ?? null, $default);
    }

    protected function getQueryFloat(string $key, float $default = 0.0): float
    {
        return $this->validateFloatValue($_GET[$key] ?? null, $default);
    }

    private function validateFloatValue($value, float $default): float
    {
        return filter_var($value, FILTER_VALIDATE_FLOAT) !== false ? (float)$value : $default;
    }

    // Asset Management Methods
    protected function addScript(string $path): void
    {
        $this->scripts[] = $path;
    }

    protected function addStylesheet(string $path): void
    {
        $this->stylesheets[] = $path;
    }
}
