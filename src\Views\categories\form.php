<h2><?= isset($category) ? 'Edit' : 'Create' ?> Category</h2>

<div class="actions">
    <a href="/categories" class="button secondary">Back to Categories</a>
</div>

<div class="form-container">
    <form method="POST" action="<?= isset($category) ? "/categories/{$category['id']}" : "/categories/store" ?>">
        <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($csrf_token) ?>">

        <?php if (isset($_SESSION['errors'])) : ?>
            <div class="alert alert-danger" role="alert">
                <?php if (is_array($_SESSION['errors'])) : ?>
                    <ul class="error-list">
                        <?php foreach ($_SESSION['errors'] as $error) : ?>
                            <li><?= htmlspecialchars($error) ?></li>
                        <?php endforeach; ?>
                    </ul>
                <?php else : ?>
                    <?= htmlspecialchars($_SESSION['errors']) ?>
                <?php endif; ?>
            </div>
        <?php endif; ?>

        <!-- Category Name Field -->
        <div class="form-group">
            <label for="name">Category Name</label>
            <input type="text" name="name" id="name"
                   value="<?= htmlspecialchars($_SESSION['old']['name'] ?? ($category['name'] ?? '')) ?>"
                   required placeholder="e.g., Groceries, Utilities, Entertainment"
                   maxlength="50">
            <small class="form-hint">Choose a descriptive name for this expense category (max 50 characters)</small>
        </div>

        <!-- Category Description Field -->
        <div class="form-group">
            <label for="description">Description (Optional)</label>
            <textarea 
                name="description" 
                id="description" 
                rows="3"
                placeholder="Brief description of what expenses belong in this category"
            ><?= htmlspecialchars($_SESSION['old']['description'] ?? ($category['description'] ?? '')) ?></textarea>
            <small class="form-hint">Provide additional details about what types of expenses should be 
                included in this category</small>
        </div>



        <!-- Form Actions -->
        <div class="form-actions">
            <button type="submit" class="button primary">
                <i class="icon-save"></i> <?= isset($category) ? 'Update' : 'Create' ?> Category
            </button>
            <a href="/categories" class="button secondary">
                <i class="icon-cancel"></i> Cancel
            </a>
        </div>
    </form>
</div>

<!-- Category styles are now included in main.css -->
