<?php
// Get old input values
$oldName = $old['name'] ?? '';
$oldEmail = $old['email'] ?? '';

// Prepare content for the layout
$title = 'Register';
ob_start();
?>

<form method="POST" action="/store">
    <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($csrf_token) ?>">

    <!-- Name Field -->
    <div class="form-group">
        <label for="name">Full Name</label>
        <input type="text" name="name" id="name"
               value="<?= htmlspecialchars($oldName) ?>"
               required autofocus>
    </div>

    <!-- Email Field -->
    <div class="form-group">
        <label for="email">Email Address</label>
        <input type="email" name="email" id="email"
               value="<?= htmlspecialchars($oldEmail) ?>"
               required aria-describedby="email-help">
        <small id="email-help" class="form-hint">We'll never share your email with anyone else</small>
    </div>

    <!-- Password Field -->
    <div class="form-group">
        <label for="password">Password</label>
        <input type="password" name="password" id="password" required
               aria-describedby="password-help">
        <small id="password-help" class="form-hint">
            Must be at least 8 characters with a number and special character
        </small>
    </div>

    <!-- Confirm Password Field -->
    <div class="form-group">
        <label for="password_confirm">Confirm Password</label>
        <input type="password" name="password_confirm" id="password_confirm" required>
    </div>

    <!-- Terms Agreement -->
    <div class="form-group checkbox-group">
        <input type="checkbox" name="terms" id="terms" value="1" required>
        <label for="terms">I agree to the <a href="/terms" target="_blank">Terms and Conditions</a></label>
    </div>

    <!-- Form Actions -->
    <div class="form-actions">
        <button type="submit" class="button">Register</button>
    </div>
</form>

<div class="form-links">
    <a href="/login">Already have an account? Login</a>
</div>

<?php
$content = ob_get_clean();
require __DIR__ . '/_layout.php';
?>
