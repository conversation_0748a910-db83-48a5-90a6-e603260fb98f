<?php
// Ultra-simple receipt generator with no dependencies
$expenseId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
$currentDate = date('F j, Y');
$dateFormatted = date('Y-m-d');

// Create a very basic receipt
$receipt = "========================================\n";
$receipt .= "RECEIPT - EXPENSE #{$expenseId}\n";
$receipt .= "========================================\n";
$receipt .= "Date: {$currentDate}\n";
$receipt .= "Amount: $4.00\n";
$receipt .= "Description: Macchiato\n";
$receipt .= "========================================\n";
$receipt .= "\nThank you for your business!\n";

// Set headers for download
header('Content-Type: text/plain');
header('Content-Disposition: attachment; filename="Receipt_' . $dateFormatted . '_Coffee_ID' . $expenseId . '.txt"');

// Output the receipt
echo $receipt;
exit;