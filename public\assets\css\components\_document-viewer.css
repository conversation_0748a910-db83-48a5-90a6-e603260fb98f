/* ===== DOCUMENT VIEWER COMPONENTS ===== */

/* Enhanced Document Summary - Specific to show.php */
#tab-document .document-summary-container {
  margin-bottom: var(--spacing-lg);
}

/* Document Header Card - Specific to show.php */
#tab-document .document-header-card {
  display: flex;
  flex-direction: column;
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-sm);
  overflow: hidden;
  margin-bottom: var(--spacing-lg);
  border: 1px solid var(--border-color);
  transition: var(--transition-medium);
}

#tab-document .document-header-card:hover {
  box-shadow: var(--box-shadow);
  transform: translateY(-2px);
}

#tab-document .document-header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  gap: var(--spacing-lg);
}

#tab-document .document-preview-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
  flex: 1;
}

#tab-document .document-type-icon {
  flex-shrink: 0;
  width: 64px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--grey-100);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-sm);
}

#tab-document .document-type-icon .document-icon {
  width: 40px;
  height: 40px;
  display: block;
  margin: 0;
}

#tab-document .document-icon.pdf {
  color: #e53935;
}

#tab-document .document-icon.image {
  color: #43a047;
}

#tab-document .document-info {
  flex: 1;
}

#tab-document .document-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-sm);
  color: var(--text-color);
  word-break: break-word;
}

#tab-document .document-meta {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing) var(--spacing-lg);
  color: var(--text-muted);
  font-size: var(--font-size-sm);
}

#tab-document .meta-item {
  display: inline-flex;
  align-items: center;
}

/* Status Badge - Specific to show.php */
#tab-document .status-badge {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-pill);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  white-space: nowrap;
  box-shadow: var(--box-shadow-sm);
}

#tab-document .status-badge.success {
  background-color: var(--success-light);
  color: var(--success-dark);
}

#tab-document .status-badge.pending {
  background-color: var(--warning-light);
  color: var(--warning-dark);
}

#tab-document .status-badge svg {
  width: 18px;
  height: 18px;
  display: block;
  margin: 0;
}

/* Document Thumbnail - Specific to show.php */
#tab-document .document-thumbnail {
  padding: 0 var(--spacing-lg) var(--spacing-lg);
  width: 100%;
}

#tab-document .thumbnail-container {
  width: 100%;
  max-height: 200px;
  overflow: hidden;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-sm);
  border: 1px solid var(--border-color);
}

#tab-document .thumbnail-image {
  width: 100%;
  height: auto;
  object-fit: cover;
  display: block;
}

/* Legacy styles for backward compatibility */
.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  width: fit-content;
}

.status-indicator.success {
  background-color: var(--success-light);
  color: var(--success-color);
}

.status-indicator.pending {
  background-color: var(--warning-light);
  color: var(--warning-color);
}

.status-indicator .svg-icon {
  width: 18px;
  height: 18px;
  display: block;
  margin: 0;
}

.document-preview {
  display: flex;
  align-items: center;
  gap: var(--spacing);
  margin-bottom: var(--spacing);
  padding: var(--spacing);
  background-color: var(--grey-100);
  border-radius: var(--border-radius);
}

.preview-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  flex-shrink: 0;
}

.preview-icon .document-icon {
  width: 48px;
  height: 48px;
  color: var(--primary-color);
  display: block;
  margin: 0;
}

.document-info h4 {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: var(--font-size-md);
  color: var(--text-color);
}

.document-description {
  margin: 0 0 var(--spacing-xs) 0;
  color: var(--text-muted);
  font-size: var(--font-size);
}

.extraction-summary {
  margin-top: var(--spacing-sm);
  font-size: var(--font-size-sm);
  color: var(--success-color);
}

.document-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing);
}

.action-button {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing);
  border-radius: var(--border-radius);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  transition: var(--transition-fast);
  border: none;
  cursor: pointer;
  font-size: var(--font-size);
}

.action-button.primary {
  background-color: var(--primary-color);
  color: white;
}

.action-button.primary:hover {
  background-color: var(--primary-dark);
}

.action-button.secondary {
  color: var(--text-color);
}

.action-button.secondary:hover {
  background-color: var(--grey-300);
}

.secondary-actions {
  display: flex;
  gap: var(--spacing-sm);
}

/* Receipt Preview */
.receipt-preview {
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  border-radius: var(--border-radius);
  background-color: var(--card-bg);
  box-shadow: var(--box-shadow-sm);
}

.receipt-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color);
}

.receipt-header h4 {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin: 0;
  font-size: var(--font-size-md);
  color: var(--text-color);
}

.view-full-receipt {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--primary-color);
  text-decoration: none;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: var(--transition-fast);
}

.view-full-receipt:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

.receipt-preview-content {
  background-color: white;
  padding: var(--spacing);
  border-radius: var(--border-radius);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.receipt-merchant-preview {
  text-align: center;
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px dashed var(--border-color);
}

.receipt-details-preview {
  padding: var(--spacing-sm) 0;
}

.receipt-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-xs);
}

.receipt-label {
  color: var(--text-muted);
  font-size: var(--font-size-sm);
}

.receipt-value {
  font-weight: var(--font-weight-medium);
}

/* Data Extraction Prompt */
.extraction-prompt {
  display: flex;
  align-items: center;
  gap: var(--spacing);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  border-radius: var(--border-radius);
  background-color: var(--card-bg);
  box-shadow: var(--box-shadow-sm);
}

.prompt-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background-color: var(--primary-light);
  border-radius: 50%;
  color: var(--primary-color);
  flex-shrink: 0;
}

.prompt-content {
  flex: 1;
}

.prompt-content h4 {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: var(--font-size-md);
  color: var(--text-color);
}

.prompt-content p {
  margin: 0 0 var(--spacing) 0;
  color: var(--text-muted);
}

/* Document Placeholder */
.document-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  text-align: center;
  background-color: var(--grey-100);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-lg);
}

.placeholder-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  background-color: var(--grey-200);
  border-radius: 50%;
  margin-bottom: var(--spacing);
  color: var(--text-muted);
}

.info-message {
  margin-bottom: var(--spacing);
  color: var(--text-muted);
}

/* Not Found State */
.not-found {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  text-align: center;
  background-color: var(--grey-100);
  border-radius: var(--border-radius);
}

.not-found-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  background-color: var(--danger-light);
  border-radius: 50%;
  margin-bottom: var(--spacing);
  color: var(--danger-color);
}

/* Help Tooltip */
.help-tooltip {
  position: relative;
  margin-top: var(--spacing-lg);
  display: flex;
  justify-content: flex-end;
}

.help-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background-color: var(--grey-200);
  border: none;
  border-radius: 50%;
  color: var(--text-muted);
  cursor: pointer;
  transition: var(--transition-fast);
}

.help-button:hover {
  background-color: var(--primary-light);
  color: var(--primary-color);
}

.tooltip-content {
  position: absolute;
  top: calc(100% + 10px);
  right: 0;
  width: 300px;
  padding: var(--spacing);
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  z-index: 10;
  display: none;
  border: 1px solid var(--border-color);
}

.tooltip-content.visible {
  display: block;
}

.tooltip-content h4 {
  margin: 0 0 var(--spacing-sm) 0;
  font-size: var(--font-size-md);
  color: var(--text-color);
}

.tooltip-content p {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-muted);
  font-size: var(--font-size-sm);
}

.tooltip-content ul {
  margin: 0 0 var(--spacing-sm) 0;
  padding-left: var(--spacing);
}

.tooltip-content li {
  margin-bottom: var(--spacing-xs);
  color: var(--text-muted);
  font-size: var(--font-size-sm);
}

/* Animation */
.pulse-animation {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--primary-rgb), 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(var(--primary-rgb), 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--primary-rgb), 0);
  }
}

/* Card Effect */
.card-effect {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-sm);
  transition: var(--transition);
}

.card-effect:hover {
  box-shadow: var(--box-shadow);
  transform: translateY(-2px);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .document-actions {
    flex-direction: column;
  }

  .secondary-actions {
    flex-direction: column;
  }

  .document-preview {
    flex-direction: column;
    text-align: center;
  }

  .extraction-prompt {
    flex-direction: column;
    text-align: center;
  }

  .receipt-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  /* Enhanced document section responsive styles - Specific to show.php */
  #tab-document .document-header-content {
    flex-direction: column;
    align-items: flex-start;
  }

  #tab-document .document-preview-container {
    width: 100%;
    margin-bottom: var(--spacing);
  }

  #tab-document .status-badge {
    align-self: flex-start;
    margin-top: var(--spacing-sm);
  }

  #tab-document .document-meta {
    flex-direction: column;
    gap: var(--spacing-xs);
    align-items: flex-start;
  }

  #tab-document .action-buttons-grid {
    grid-template-columns: 1fr;
  }
}