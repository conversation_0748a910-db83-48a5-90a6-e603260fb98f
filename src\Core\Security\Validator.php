<?php

declare(strict_types=1);

namespace App\Core\Security;

use DateTime;
use InvalidArgumentException;

final class Validator
{
    private const MESSAGES = [
        'required' => 'The %s field is required',
        'email' => 'Invalid email address',
        'numeric' => '%s must be a number',
        'positive' => '%s must be greater than zero',
        'min' => '%s must be at least %d characters',
        'max' => '%s must not exceed %d characters',
        'date' => 'Invalid date for %s',
        'string' => '%s must be text',
        'boolean' => '%s must be 0 or 1',
        'contains_digit' => '%s must contain at least one number',
        'contains_special' => '%s must contain at least one special character',
        'matches' => '%s must match %s'
    ];

    private array $errors = [];

    public function __construct(private readonly array $rules)
    {
    }

    public function validate(array $data): array
    {
        $this->errors = [];
        $validated = [];
        $fields = array_keys($this->rules);

        foreach ($fields as $field) {
            $value = $data[$field] ?? null;
            $fieldRules = $this->rules[$field];

            try {
                $validated[$field] = $this->processField($field, $value, $fieldRules, $data);
            } catch (InvalidArgumentException $e) {
                $this->errors[$field] = $e->getMessage();
            }
        }

        if (!empty($this->errors)) {
            throw new InvalidArgumentException($this->formatErrors());
        }

        return $validated;
    }

    public function getErrors(): array
    {
        return $this->errors;
    }

    public function getRules(): array
    {
        return $this->rules;
    }

    private function formatErrors(): string
    {
        return json_encode($this->errors);
    }

    private function processField(string $field, mixed $value, array $rules, array $data): mixed
    {
        $ruleParams = array_map([$this, 'parseRule'], $rules);

        foreach ($ruleParams as [$ruleName, $parameter]) {
            if ($this->shouldSkipRule($value, $ruleName)) {
                continue;
            }

            if ($error = $this->checkRule($ruleName, $field, $value, $parameter, $data)) {
                throw new InvalidArgumentException($error);
            }
        }

        return $this->sanitizeValue($value, array_column($ruleParams, 0));
    }

    private function shouldSkipRule(mixed $value, string $ruleName): bool
    {
        return $value === null && $ruleName !== 'required';
    }

    private function parseRule(string $rule): array
    {
        if (!str_contains($rule, ':')) {
            return [$rule, null];
        }

        [$ruleName, $parameter] = explode(':', $rule, 2);

        if (!is_numeric($parameter)) {
            return [$ruleName, $parameter];
        }

        $numericValue = $parameter + 0;
        return [$ruleName, is_float($numericValue) ? (float) $parameter : (int) $parameter];
    }

    private function checkRule(string $rule, string $field, mixed $value, mixed $parameter, array $data): ?string
    {
        $ruleMap = [
            'required' => fn() => $this->checkRequired($value, $field),
            'email' => fn() => $this->checkEmail($value),
            'numeric' => fn() => $this->checkNumeric($value, $field),
            'positive' => fn() => $this->checkPositive($value, $field),
            'min' => fn() => $this->checkMinLength($value, $parameter, $field),
            'max' => fn() => $this->checkMaxLength($value, $parameter, $field),
            'date' => fn() => $this->checkDate($value, $field),
            'string' => fn() => $this->checkString($value, $field),
            'boolean' => fn() => $this->checkBoolean($value, $field),
            'contains_digit' => fn() => $this->checkContainsDigit($value, $field),
            'contains_special' => fn() => $this->checkContainsSpecial($value, $field),
            'matches' => fn() => $this->checkMatches($value, $parameter, $field, $data),
        ];

        return isset($ruleMap[$rule]) ? $ruleMap[$rule]() : null;
    }

    private function checkRequired(mixed $value, string $field): ?string
    {
        $isEmpty = empty($value) && $value !== 0 && $value !== '0';
        return $isEmpty ? sprintf(self::MESSAGES['required'], $field) : null;
    }

    private function checkEmail(mixed $value): ?string
    {
        return !filter_var($value, FILTER_VALIDATE_EMAIL) ? self::MESSAGES['email'] : null;
    }

    private function checkNumeric(mixed $value, string $field): ?string
    {
        return !is_numeric($value) ? sprintf(self::MESSAGES['numeric'], $field) : null;
    }

    private function checkPositive(mixed $value, string $field): ?string
    {
        if (!is_numeric($value)) {
            return sprintf(self::MESSAGES['numeric'], $field);
        }

        return ((float) $value <= 0) ? sprintf(self::MESSAGES['positive'], $field) : null;
    }

    private function checkMinLength(mixed $value, mixed $min, string $field): ?string
    {
        if ($min === null || !is_string($value) || !is_numeric($min)) {
            return null;
        }

        return (strlen($value) < (int)$min) ? sprintf(self::MESSAGES['min'], $field, $min) : null;
    }

    private function checkMaxLength(mixed $value, mixed $max, string $field): ?string
    {
        if ($max === null || !is_string($value) || !is_numeric($max)) {
            return null;
        }

        return (strlen($value) > (int)$max) ? sprintf(self::MESSAGES['max'], $field, $max) : null;
    }

    private function checkDate(mixed $value, string $field): ?string
    {
        return !$this->isValidDate($value) ? sprintf(self::MESSAGES['date'], $field) : null;
    }

    private function checkString(mixed $value, string $field): ?string
    {
        return !is_string($value) ? sprintf(self::MESSAGES['string'], $field) : null;
    }

    private function checkBoolean(mixed $value, string $field): ?string
    {
        $validValues = [0, 1, '0', '1', true, false];
        return !in_array($value, $validValues, true) ? sprintf(self::MESSAGES['boolean'], $field) : null;
    }

    private function checkContainsDigit(mixed $value, string $field): ?string
    {
        if (!is_string($value)) {
            return null;
        }

        return !preg_match('/[0-9]/', $value) ? sprintf(self::MESSAGES['contains_digit'], $field) : null;
    }

    private function checkContainsSpecial(mixed $value, string $field): ?string
    {
        if (!is_string($value)) {
            return null;
        }

        return !preg_match('/[^a-zA-Z0-9]/', $value) ? sprintf(self::MESSAGES['contains_special'], $field) : null;
    }

    private function checkMatches(mixed $value, mixed $parameter, string $field, array $data): ?string
    {
        if (!is_string($parameter) || !isset($data[$parameter])) {
            return null;
        }

        return $value !== $data[$parameter] ? sprintf(self::MESSAGES['matches'], $field, $parameter) : null;
    }

    private function isValidDate(mixed $value): bool
    {
        if (!is_string($value) || trim($value) === '') {
            return false;
        }

        $date = DateTime::createFromFormat('Y-m-d', $value);
        return $date && $date->format('Y-m-d') === $value;
    }

    private function sanitizeValue(mixed $value, array $rules): mixed
    {
        if ($value === null) {
            return null;
        }

        $sanitizers = [
            'boolean' => fn() => $this->sanitizeBoolean($value),
            'numeric' => fn() => is_numeric($value) ? (is_float($value + 0) ? (float)$value : (int)$value) : $value,
            'positive' => fn() => is_numeric($value) && (float)$value > 0 ? (float)$value : $value,
            'date' => fn() => $this->isValidDate($value) ? (new DateTime($value))->format('Y-m-d') : $value,
            'email' => fn() => filter_var($value, FILTER_VALIDATE_EMAIL) ? strtolower(trim($value)) : $value,
            'string' => fn() => is_string($value) ? trim($value) : $value,
        ];

        foreach ($rules as $rule) {
            if (isset($sanitizers[$rule])) {
                $value = $sanitizers[$rule]();
            }
        }

        return $value;
    }

    private function sanitizeBoolean(mixed $value): bool
    {
        $trueValues = ['1', 1, true];

        if (in_array($value, $trueValues, true)) {
            return true;
        }

        return false;
    }
}
