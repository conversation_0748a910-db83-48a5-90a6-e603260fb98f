/* ===== MESSAGES COMPONENT ===== */

.alert {
  padding: var(--spacing);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-md);
  position: relative;
}

.alert p {
  margin: 0;
}

.alert-success {
  background-color: var(--success-light);
  border: 1px solid var(--success-border);
  color: var(--success-dark);
}

.alert-danger {
  background-color: var(--danger-light);
  border: 1px solid var(--danger-border);
  color: var(--danger-dark);
}

.alert-warning {
  background-color: var(--warning-light);
  border: 1px solid var(--warning-border);
  color: var(--warning-dark);
}

.alert-info {
  background-color: var(--info-light);
  border: 1px solid var(--info-border);
  color: var(--info-dark);
}

/* Alert with Icon */
.alert-with-icon {
  display: flex;
  align-items: flex-start;
}

.alert-icon {
  margin-right: var(--spacing);
  flex-shrink: 0;
}

.alert-content {
  flex: 1;
}

/* Dismissible Alert */
.alert-dismissible {
  padding-right: 3rem;
}

.alert-dismiss {
  position: absolute;
  top: var(--spacing);
  right: var(--spacing);
  background: none;
  border: none;
  color: inherit;
  opacity: 0.7;
  cursor: pointer;
  font-size: var(--font-size-lg);
  line-height: 1;
  padding: 0;
  width: 1.5rem;
  height: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.alert-dismiss:hover {
  opacity: 1;
}
