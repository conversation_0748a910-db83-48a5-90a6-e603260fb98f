<?php
/**
 * Expense Detail View
 *
 * Displays detailed information about a specific expense, including:
 * - Basic expense details (description, amount, date, etc.)
 * - Notes (if present)
 * - Document viewer (when viewing document tab)
 * - Action buttons for edit, delete, print, download
 *
 * @var array $viewData Contains expense data and related information
 * @var object $expense Direct expense object (fallback if not in viewData)
 */

include __DIR__ . '/../shared/messages.php';

// If expense is not in viewData but is directly available, use it
if (!isset($viewData['expense']) && isset($expense)) {
    $viewData['expense'] = $expense;
}
?>

<section class="expense-detail container u-spacing-stack-lg">
    <h1 class="page-title">Expense Details</h1>
    
    <div class="expense__container">
    <?php if (!empty($viewData['expense'])) : ?>
        <?php
        // Compute current view based on presence of document data
        $currentView = isset($viewData['document']) ? 'document' : 'details';
        ?>
        
        <?php include __DIR__ . '/partials/_header.php'; ?>
        
        <?php include __DIR__ . '/partials/_actions.php'; ?>
        
        <div class="expense__content u-spacing-stack-lg">
            <?php if ($currentView === 'details') : ?>
                <?php include __DIR__ . '/partials/_detail_card.php'; ?>
                <?php include __DIR__ . '/partials/_notes_card.php'; ?>
            <?php else : ?>
                <?php include __DIR__ . '/partials/_document_viewer.php'; ?>
            <?php endif; ?>
        </div>

    <?php else : ?>
        <div class="not-found">
            <div class="card">
                <div class="card-body text-center py-lg">
                    <svg class="svg-icon mb-md text-light" style="width: 4rem; height: 4rem;" aria-hidden="true" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"/>
                    </svg>
                    <h3 class="mb-md text-dark">Expense Not Found</h3>
                    <p class="mb-lg text-medium">The expense you're looking for doesn't exist or has been removed.</p>
                    <a href="/expenses" class="button secondary">
                        <svg class="svg-icon" aria-hidden="true" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
                        </svg>
                        Back to Expenses List
                    </a>
                </div>
            </div>
        </div>
    <?php endif; ?>
    </div>
</section>
