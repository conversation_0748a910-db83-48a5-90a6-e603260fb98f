<?php
// Expense metadata group
?>
<ul class="meta-list u-spacing-stack-sm" role="group" aria-label="Expense metadata">
    <?php if (!empty($expense['date'])) : ?>
    <li class="meta-item">
        <span class="meta-label">
            <svg class="svg-icon u-spacing-inline-xs" aria-hidden="true" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M19 4h-1V2h-2v2H8V2H6v2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 16H5V9h14v11z"/>
            </svg>
            Date:
        </span>
        <span class="meta-value">
            <time datetime="<?= htmlspecialchars($expense['date']) ?>">
                <?= date('F j, Y', strtotime($expense['date'])) ?>
            </time>
        </span>
    </li>
    <?php endif; ?>

    <?php if (!empty($category['name'])) : ?>
    <li class="meta-item">
        <span class="meta-label">Category:</span>
        <span class="meta-value category-badge">
            <?= htmlspecialchars($category['name']) ?>
        </span>
    </li>
    <?php endif; ?>

    <?php if (!empty($merchant_name)) : ?>
    <li class="meta-item">
        <span class="meta-label">Merchant:</span>
        <span class="meta-value"><?= htmlspecialchars($merchant_name) ?></span>
    </li>
    <?php endif; ?>

    <?php if (!empty($paymentMethod['name'])) : ?>
    <li class="meta-item">
        <span class="meta-label">Payment Method:</span>
        <span class="meta-value"><?= htmlspecialchars($paymentMethod['name']) ?></span>
    </li>
    <?php endif; ?>
</ul>
