<?php

declare(strict_types=1);

namespace App\Core\Security;

final class CSRF
{
    private const TOKEN_LENGTH = 32;
    private const TOKEN_KEY = 'csrf_token';
    private const TOKEN_TIMESTAMP_KEY = 'csrf_token_timestamp';
    private const TOKEN_EXPIRY_MINUTES = 60; // 1 hour

    public static function generateToken(): string
    {
        if (!self::hasValidToken()) {
            $token = bin2hex(random_bytes(self::TOKEN_LENGTH));
            Session::set(self::TOKEN_KEY, $token);
            Session::set(self::TOKEN_TIMESTAMP_KEY, time());
        }

        return Session::get(self::TOKEN_KEY);
    }

    public static function validateToken(string $token): bool
    {
        if (!Session::has(self::TOKEN_KEY) || !Session::has(self::TOKEN_TIMESTAMP_KEY)) {
            return false;
        }

        // Check if token has expired
        $tokenTimestamp = Session::get(self::TOKEN_TIMESTAMP_KEY);
        $expiryTime = $tokenTimestamp + (self::TOKEN_EXPIRY_MINUTES * 60);
        
        if (time() > $expiryTime) {
            self::removeToken();
            return false;
        }

        $storedToken = Session::get(self::TOKEN_KEY);
        $isValid = hash_equals($storedToken, $token);
        
        // Log CSRF validation attempts
        if (!$isValid) {
            $securityLogger = new SecurityLogger();
            $securityLogger->logCSRFViolation($storedToken, $token);
        }
        
        return $isValid;
    }

    public static function removeToken(): void
    {
        Session::remove(self::TOKEN_KEY);
        Session::remove(self::TOKEN_TIMESTAMP_KEY);
    }

    public static function regenerateToken(): string
    {
        self::removeToken();
        return self::generateToken();
    }

    public static function hasValidToken(): bool
    {
        if (!Session::has(self::TOKEN_KEY) || !Session::has(self::TOKEN_TIMESTAMP_KEY)) {
            return false;
        }

        $tokenTimestamp = Session::get(self::TOKEN_TIMESTAMP_KEY);
        $expiryTime = $tokenTimestamp + (self::TOKEN_EXPIRY_MINUTES * 60);
        
        return time() <= $expiryTime;
    }

    public static function getTokenExpiryTime(): int
    {
        if (!Session::has(self::TOKEN_TIMESTAMP_KEY)) {
            return 0;
        }
        
        $tokenTimestamp = Session::get(self::TOKEN_TIMESTAMP_KEY);
        return $tokenTimestamp + (self::TOKEN_EXPIRY_MINUTES * 60);
    }

    public static function getRemainingTime(): int
    {
        $expiryTime = self::getTokenExpiryTime();
        return max(0, $expiryTime - time());
    }
}
