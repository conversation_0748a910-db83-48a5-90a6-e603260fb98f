<?php

/**
 * Standardized form field component
 *
 * Parameters:
 * @param string $type Input type (text, email, password, etc.)
 * @param string $name Field name
 * @param string $label Field label
 * @param string $value Field value
 * @param bool $required Whether field is required
 * @param string $id Field ID (defaults to name)
 * @param string $placeholder Field placeholder
 * @param string $helpText Help text to display below field
 * @param string $errorMessage Error message to display
 * @param array $options Options for select fields
 * @param string $class Additional CSS classes
 * @param array $attributes Additional HTML attributes

 */

// Set defaults
$type ??= 'text';
$id ??= $name;
$value ??= $_SESSION['old'][$name] ?? '';
$required ??= false;
$placeholder ??= '';
$helpText ??= '';
$errorMessage ??= '';
$options ??= [];
$class ??= '';
$attributes ??= [];

// Get error message from session if not provided
if (empty($errorMessage) && isset($_SESSION['errors'][$name])) {
    $errorMessage = $_SESSION['errors'][$name];
}

// Determine field state
$hasError = !empty($errorMessage);
$fieldClass = 'form-field' . ($hasError ? ' has-error' : '') . ($class ? ' ' . $class : '');

// Set field attributes
$fieldAttributes = array_merge([
    'id' => $id,
    'name' => $name,
    'placeholder' => $placeholder,
    'aria-describedby' => $helpText ? "{$id}-help" : null,
    'aria-invalid' => $hasError ? 'true' : null,
    'required' => $required ? true : null
], $attributes);

// Filter out null attributes
$fieldAttributes = array_filter($fieldAttributes, fn($value) => $value !== null);

// Build attributes string
$attributesStr = '';
foreach ($fieldAttributes as $key => $value) {
    if ($value === true) {
        $attributesStr .= ' ' . htmlspecialchars($key);
    } else {
        $attributesStr .= ' ' . htmlspecialchars($key) . '="' . htmlspecialchars($value) . '"';
    }
}
?>

<div class="<?= $fieldClass ?>">
    <label for="<?= htmlspecialchars($id) ?>">
        <?= htmlspecialchars($label) ?>
        <?php if ($required) : ?>
            <span class="required">*</span>
        <?php endif; ?>
    </label>

    <?php if ($type === 'textarea') : ?>
        <textarea<?= $attributesStr ?>><?= htmlspecialchars($value) ?></textarea>
    <?php elseif ($type === 'select') : ?>
        <select<?= $attributesStr ?>>
            <?php foreach ($options as $optionValue => $optionLabel) : ?>
                <option value="<?= htmlspecialchars($optionValue) ?>"<?= $optionValue == $value ? ' selected' : '' ?>>
                    <?= htmlspecialchars($optionLabel) ?>
                </option>
            <?php endforeach; ?>
        </select>
    <?php elseif ($type === 'checkbox') : ?>
        <div class="checkbox-wrapper">
            <input type="checkbox" <?= $attributesStr ?> value="1" <?= $value ? 'checked' : '' ?>>
            <span class="checkbox-label"><?= htmlspecialchars($label) ?></span>
        </div>
    <?php elseif ($type === 'radio') : ?>
        <div class="radio-group">
            <?php foreach ($options as $optionValue => $optionLabel) : ?>
                <div class="radio-wrapper">
                    <input
                        type="radio"
                        name="<?= htmlspecialchars($name) ?>"
                        id="<?= htmlspecialchars($id . '_' . $optionValue) ?>"
                        value="<?= htmlspecialchars($optionValue) ?>"
                        <?= $optionValue == $value ? 'checked' : '' ?>
                    >
                    <label for="<?= htmlspecialchars($id . '_' . $optionValue) ?>">
                        <?= htmlspecialchars($optionLabel) ?>
                    </label>
                </div>
            <?php endforeach; ?>
        </div>
    <?php elseif ($type === 'file') : ?>
        <div class="file-upload">
            <input type="file" <?= $attributesStr ?>>
            <div class="file-upload-info">
                <span class="file-name">No file selected</span>
                <button type="button" class="button secondary">Browse</button>
            </div>
        </div>
    <?php else : ?>
        <input type="<?= htmlspecialchars($type) ?>" <?= $attributesStr ?> value="<?= htmlspecialchars($value) ?>">
    <?php endif; ?>

    <?php if ($helpText) : ?>
        <div class="help-text" id="<?= htmlspecialchars($id) ?>-help">
            <?= htmlspecialchars($helpText) ?>
        </div>
    <?php endif; ?>

    <?php if ($hasError) : ?>
        <div class="error-message">
            <?= htmlspecialchars($errorMessage) ?>
        </div>
    <?php endif; ?>
</div>
