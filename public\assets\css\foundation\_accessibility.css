/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
      scroll-behavior: auto !important;
    }

    .button:hover {
      transform: none !important;
    }

    .progress::after {
      animation: none !important;
    }

    .category-item:hover {
      transform: none !important;
    }

    table.interactive tr:hover {
      transform: none !important;
    }

    input:focus::placeholder,
    textarea:focus::placeholder {
      transform: none !important;
    }
  }

/* High contrast mode */
@media (forced-colors: active) {
  .button,
  .nav-link.active,
  .progress {
    forced-color-adjust: none;
  }
}

/* Focus States */
:focus {
  outline: 2px solid var(--primary-light);
  outline-offset: 2px;
}

:focus:not(:focus-visible) {
  outline: none;
}

:focus-visible {
  outline: 3px solid hsla(var(--primary-hue), 70%, 60%, 0.4);
  outline-offset: 2px;
}

/* Screen reader only text */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}