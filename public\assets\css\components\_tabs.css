/* ===== TABS COMPONENT ===== */

/* Content Tabs */
.content-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: var(--spacing-lg);
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.tab-button {
  padding: var(--spacing) var(--spacing-lg);
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  cursor: pointer;
  font-weight: var(--font-weight-medium);
  color: var(--text-muted);
  transition: var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  white-space: nowrap;
}

.tab-button:hover {
  color: var(--text-color);
  background-color: var(--grey-100);
}

.tab-button.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

.tab-button .svg-icon {
  width: 20px;
  height: 20px;
  display: block;
  margin: 0;
}

/* Tab Content */
.tab-content {
  display: none;
  animation: fadeIn 0.3s ease-in-out;
}

.tab-content.active {
  display: block;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Content Grid */
.content-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-lg);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .content-tabs {
    padding-bottom: var(--spacing-xs);
  }

  .tab-button {
    padding: var(--spacing-sm) var(--spacing);
  }
}