<?php

/**
 * Standardized pagination partial for consistent pagination display
 * Used across all views that require pagination
 *
 * @var array $pagination Pagination data containing currentPage and totalPages
 */

?>

<?php if (isset($pagination) && $pagination['totalPages'] > 1) :
    ?>
<nav aria-label="Pagination" class="pagination">
    <div class="pagination-controls">
        <?php
        // Build the base URL with any existing query parameters
        $baseUrl = $_SERVER['REQUEST_URI'];
        $baseUrl = strtok($baseUrl, '?');
// Remove existing query string
        $queryParams = $_GET;
// Remove page from existing query params as we'll add it separately
        if (isset($queryParams['page'])) {
            unset($queryParams['page']);
        }

        // Build query string from remaining parameters
        $queryString = '';
        if (!empty($queryParams)) {
            $queryString = '?' . http_build_query($queryParams) . '&';
        } else {
            $queryString = '?';
        }
        ?>
        
        <?php if ($pagination['currentPage'] > 1) :
            ?>
            <a href="<?= $baseUrl . $queryString ?>page=<?= ($pagination['currentPage'] - 1) ?>" 
               class="button pagination-button" 
               aria-label="Go to previous page">
                <svg class="svg-icon" aria-hidden="true" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12l4.58-4.59z"/>
                </svg>
                Previous
            </a>
            <?php
        else :
            ?>
            <span class="pagination-button disabled">
                <svg class="svg-icon" aria-hidden="true" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12l4.58-4.59z"/>
                </svg>
                Previous
            </span>
            <?php
        endif; ?>
        
        <span class="pagination-info" aria-current="page">
            Page <?= $pagination['currentPage'] ?> of <?= $pagination['totalPages'] ?>
        </span>
        
        <?php if ($pagination['currentPage'] < $pagination['totalPages']) :
            ?>
            <a href="<?= $baseUrl . $queryString ?>page=<?= ($pagination['currentPage'] + 1) ?>" 
               class="button pagination-button" 
               aria-label="Go to next page">
                Next
                <svg class="svg-icon" aria-hidden="true" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6-6-6z"/>
                </svg>
            </a>
            <?php
        else :
            ?>
            <span class="pagination-button disabled">
                Next
                <svg class="svg-icon" aria-hidden="true" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6-6-6z"/>
                </svg>
            </span>
            <?php
        endif; ?>
    </div>
</nav>
    <?php
endif; ?>
