/* Receipt Styling */
body {
    font-family: "Courier New", Courier, monospace;
    font-size: 12px;
    line-height: 1.5;
    padding: 20px;
    max-width: 80mm; /* Standard receipt width */
    margin: 0 auto;
    background-color: #f9f9f9;
}

.receipt-container {
    background-color: white;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.receipt-divider {
    border: 1px dashed #000;
    margin: 10px 0;
    border-top: 1px dashed #000;
    border-bottom: none;
    border-left: none;
    border-right: none;
}

.receipt-header {
    text-align: center;
    font-weight: bold;
    margin-bottom: 10px;
}

.receipt-footer {
    text-align: center;
    margin-top: 20px;
    font-style: italic;
}

.receipt-item-header, .receipt-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 3px;
}

.item-name {
    text-align: left;
    flex-grow: 1;
}

.item-price {
    text-align: right;
    width: 70px;
    min-width: 70px;
    flex-shrink: 0;
}

/* PDF specific styles */
.pdf-container {
    font-family: "Courier New", Courier, monospace;
    line-height: 1.5;
    padding: 20px;
    max-width: 800px;
    margin: 0 auto;
}

.pdf-header {
    text-align: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ddd;
}

.pdf-content {
    padding: 20px;
    border: 1px solid #eee;
    border-radius: 4px;
    background-color: #fff;
}

.print-button {
    position: fixed;
    top: 10px;
    right: 10px;
    padding: 10px 15px;
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    z-index: 1000;
    font-family: Arial, sans-serif;
}

.print-button:hover {
    background: #45a049;
}

/* Print media queries */
@media print {
    body { 
        margin: 0; 
        padding: 0;
        background-color: white;
    }
    .print-button { 
        display: none; 
    }
    .receipt-container {
        box-shadow: none;
        border: none;
    }
}

/* Responsive design */
@media screen and (max-width: 600px) {
    body {
        max-width: 100%;
        padding: 10px;
    }
    
    .receipt-container {
        padding: 15px;
    }
}