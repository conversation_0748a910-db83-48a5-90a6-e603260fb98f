/* ===== Category Grid ===== */
.category-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: var(--spacing-md);
    margin-top: var(--spacing);
  }

  .category-item {
    background-color: var(--light-color);
    padding: var(--spacing-md);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    border-top: 3px solid var(--primary-light);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  .category-item:hover {
    transform: translateY(-5px);
    box-shadow: var(--box-shadow-lg);
  }

  .category-item h4 {
    margin-bottom: var(--spacing-sm);
    color: var(--primary-color);
  }

  @media (max-width: 992px) {
    .category-list {
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
  }