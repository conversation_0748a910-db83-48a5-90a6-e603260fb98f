/* ===== PAGE HEADER COMPONENT ===== */

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing);
  border-bottom: 1px solid var(--grey-200);
}

.header-content {
  flex: 1;
}

.header-content h2 {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--dark-color);
}

.header-content .subtitle {
  margin: var(--spacing-xs) 0 0;
  color: var(--text-muted);
  font-size: var(--font-size-sm);
}

.header-actions {
  display: flex;
  gap: var(--spacing-sm);
  align-items: center;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .header-actions {
    margin-top: var(--spacing);
    width: 100%;
    justify-content: flex-start;
  }
}
