<?php

namespace App\Utils;

class ArrayHelper
{
    /**
     * Ensures the given value is returned as an array.
     *
     * @param mixed $value The value to convert to an array
     * @return array The value as an array
     */
    public static function ensureArray($value): array
    {
        if (is_iterable($value)) {
            return (array)$value;
        }

        return $value === null ? [] : [$value];
    }
}
