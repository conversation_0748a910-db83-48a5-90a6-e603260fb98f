<?php

declare(strict_types=1);

namespace App\Models;

use App\Exceptions\InvalidArgumentException;
use App\Exceptions\DatabaseException;

class Merchant extends BaseModel
{
    protected static function getTableName(): string
    {
        return 'merchants';
    }

    public static function all(?int $userId = null): array
    {
        $conditions = [];
        $params = [];

        if ($userId !== null) {
            $conditions[] = 'user_id = ?';
            $params[] = $userId;
        }

        $whereClause = empty($conditions) ? '' : 'WHERE ' . implode(' AND ', $conditions);

        // Use explicit column selection instead of *
        $sql = "SELECT
                id, name, user_id, created_at, updated_at
            FROM merchants
            $whereClause
            ORDER BY name ASC";

        return self::fetchMany($sql, $params);
    }

    public static function find(int $id, ?int $userId = null): ?array
    {
        $conditions = ['id = ?'];
        $params = [$id];

        // Most restrictive filter first (user_id)
        if ($userId !== null) {
            $conditions[] = 'user_id = ?';
            $params[] = $userId;
        }

        $whereClause = 'WHERE ' . implode(' AND ', $conditions);

        // Use explicit column selection instead of *
        $sql = "SELECT
                id, name, user_id, created_at, updated_at
            FROM merchants
            $whereClause
            LIMIT 1";

        return self::fetchOne($sql, $params);
    }

    public static function findByName(string $name, ?int $userId = null): ?array
    {
        $conditions = ['name = ?'];
        $params = [$name];

        // Most restrictive filter first (user_id)
        if ($userId !== null) {
            $conditions[] = 'user_id = ?';
            $params[] = $userId;
        }

        $whereClause = 'WHERE ' . implode(' AND ', $conditions);

        // Use explicit column selection instead of *
        $sql = "SELECT
                id, name, user_id, created_at, updated_at
            FROM merchants
            $whereClause
            LIMIT 1";

        return self::fetchOne($sql, $params);
    }

    public static function create(array $data): int
    {
        self::validateMerchantData($data);

        $sql = sprintf(
            "INSERT INTO %s (name, user_id, created_at, updated_at) VALUES (?, ?, NOW(), NOW())",
            self::getTableName()
        );

        self::executeSql($sql, [$data['name'], $data['user_id']]);
        return (int)self::getDb()->lastInsertId();
    }

    public static function update(int $id, array $data, ?int $userId = null): bool
    {
        self::validateMerchantData($data);

        $conditions = ['id = ?'];
        $params = [];

        if ($userId !== null) {
            $conditions[] = 'user_id = ?';
            $params[] = $userId;
        }

        $whereClause = 'WHERE ' . implode(' AND ', $conditions);

        $sql = sprintf(
            "UPDATE %s SET name = ?, updated_at = NOW() %s",
            self::getTableName(),
            $whereClause
        );

        $params = array_merge([$data['name']], $params, [$id]);

        return self::executeSql($sql, $params);
    }

    public static function delete(int $id, ?int $userId = null): bool
    {
        $conditions = ['id = ?'];
        $params = [$id];

        if ($userId !== null) {
            $conditions[] = 'user_id = ?';
            $params[] = $userId;
        }

        $whereClause = 'WHERE ' . implode(' AND ', $conditions);

        $sql = sprintf(
            "DELETE FROM %s %s",
            self::getTableName(),
            $whereClause
        );

        return self::executeSql($sql, $params);
    }

    public static function findOrCreate(string $name, int $userId): int
    {
        $merchant = self::findByName($name, $userId);

        if ($merchant) {
            return (int)$merchant['id'];
        }

        return self::create([
            'name' => $name,
            'user_id' => $userId
        ]);
    }

    private static function validateMerchantData(array $data): void
    {
        if (empty($data['name'])) {
            throw new InvalidArgumentException('Merchant name is required');
        }

        if (strlen($data['name']) > 255) {
            throw new InvalidArgumentException('Merchant name must be less than 255 characters');
        }

        if (!isset($data['user_id']) || !is_numeric($data['user_id'])) {
            throw new InvalidArgumentException('User ID is required and must be numeric');
        }
    }

    public static function findByUserId(int $userId): array
    {
        try {
            $stmt = self::getDb()->prepare("
                SELECT id, name, user_id, created_at, updated_at
                FROM merchants
                WHERE user_id = ?
                ORDER BY name ASC
            ");

            $stmt->execute([$userId]);
            return $stmt->fetchAll(\PDO::FETCH_ASSOC);
        } catch (\PDOException $e) {
            throw new DatabaseException("Error finding merchants by user ID: " . $e->getMessage(), (int)$e->getCode());
        }
    }
}
