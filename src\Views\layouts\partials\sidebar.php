<aside class="sidebar" id="sidebar" role="navigation" aria-label="Main navigation">
    <nav class="sidebar-nav">
        <ul class="nav-list">
            <?php if (isset($_SESSION['user_id'])) : ?>
                <li class="nav-item">
                    <a href="/dashboard" class="nav-link <?= strpos($_SERVER['REQUEST_URI'], '/dashboard') === 0 ? 'active' : '' ?>"
                       aria-current="<?= strpos($_SERVER['REQUEST_URI'], '/dashboard') === 0 ? 'page' : 'false' ?>">
                       <svg class="svg-icon" aria-hidden="true" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3 13h8V3H3v10zm0 8h8v-6H3v6zm10 0h8V11h-8v10zm0-18v6h8V3h-8z"/>
                       </svg>
                        <span class="text">Dashboard</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/expenses" class="nav-link <?= strpos($_SERVER['REQUEST_URI'], '/expenses') === 0 ? 'active' : '' ?>"
                       aria-current="<?= strpos($_SERVER['REQUEST_URI'], '/expenses') === 0 ? 'page' : 'false' ?>">
                       <svg class="svg-icon" aria-hidden="true" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M11.8 10.9c-2.27-.59-3-1.2-3-2.15 0-1.09 1.01-1.85 2.7-1.85 1.78 0 2.44.85 2.5 2.1h2.21c-.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94.42-3.5 1.68-3.5 3.61 0 2.31 1.91 3.46 4.7 4.13 2.5.6 3 1.48 3 2.41 0 .69-.49 1.79-2.7 1.79-2.06 0-2.87-.92-2.98-2.1h-2.2c.12 2.19 1.76 3.42 3.68 3.83V21h3v-2.15c1.95-.37 3.5-1.5 3.5-3.55 0-2.84-2.43-3.81-4.7-4.4z"/>
                       </svg>
                        <span class="text">Expenses</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/categories" class="nav-link <?= strpos($_SERVER['REQUEST_URI'], '/categories') === 0 ? 'active' : '' ?>"
                       aria-current="<?= strpos($_SERVER['REQUEST_URI'], '/categories') === 0 ? 'page' : 'false' ?>">
                       <svg class="svg-icon" aria-hidden="true" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M4 11h6a1 1 0 001-1V4a1 1 0 00-1-1H4a1 1 0 00-1 1v6a1 1 0 001 1zm0 10h6a1 1 0 001-1v-6a1 1 0 00-1-1H4a1 1 0 00-1 1v6a1 1 0 001 1zm10 0h6a1 1 0 001-1v-6a1 1 0 00-1-1h-6a1 1 0 00-1 1v6a1 1 0 001 1zm0-10h6a1 1 0 001-1V4a1 1 0 00-1-1h-6a1 1 0 00-1 1v6a1 1 0 001 1z"/>
                       </svg>
                    <span class="text">Categories</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/reports" class="nav-link <?= strpos($_SERVER['REQUEST_URI'], '/reports') === 0 ? 'active' : '' ?>"
                       aria-current="<?= strpos($_SERVER['REQUEST_URI'], '/reports') === 0 ? 'page' : 'false' ?>">
                       <svg class="svg-icon" aria-hidden="true" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z"/>
                       </svg>
                        <span class="text">Reports</span>
                    </a>
                </li>

                <li class="nav-separator" role="separator" aria-hidden="true"></li>

                <li class="nav-item">
                    <a href="/profile" class="nav-link <?= strpos($_SERVER['REQUEST_URI'], '/profile') === 0 ? 'active' : '' ?>"
                       aria-current="<?= strpos($_SERVER['REQUEST_URI'], '/profile') === 0 ? 'page' : 'false' ?>">
                       <svg class="svg-icon" aria-hidden="true" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M19.14 12.94c.04-.3.06-.61.06-.94 0-.32-.02-.64-.07-.94l2.03-1.58c.18-.14.23-.41.12-.61l-1.92-3.32c-.12-.22-.37-.29-.59-.22l-2.39.96c-.5-.38-1.03-.7-1.62-.94l-.36-2.54c-.04-.24-.24-.41-.48-.41h-3.84c-.24 0-.43.17-.47.41l-.36 2.54c-.59.24-1.13.57-1.62.94l-2.39-.96c-.22-.08-.47 0-.59.22L2.74 8.87c-.11.2-.06.47.12.61l2.03 1.58c-.05.3-.09.63-.09.94s.02.64.07.94l-2.03 1.58c-.18.14-.23.41-.12.61l1.92 3.32c.12.22.37.29.59.22l2.39-.96c.5.38 1.03.7 1.62.94l.36 2.54c.05.24.24.41.48.41h3.84c.24 0 .44-.17.47-.41l.36-2.54c.59-.24 1.13-.56 1.62-.94l2.39.96c.22.08.47 0 .59-.22l1.92-3.32c.12-.22.07-.47-.12-.61l-2.01-1.58zM12 15.6c-1.98 0-3.6-1.62-3.6-3.6s1.62-3.6 3.6-3.6 3.6 1.62 3.6 3.6-1.62 3.6-3.6 3.6z"/>
                       </svg>
                        <span class="text">Settings</span>
                    </a>
                </li>
            <?php else : ?>
                <li class="nav-item">
                    <a href="/login" class="nav-link <?= $_SERVER['REQUEST_URI'] === '/login' ? 'active' : '' ?>"
                       aria-current="<?= $_SERVER['REQUEST_URI'] === '/login' ? 'page' : 'false' ?>">
                       <svg class="svg-icon" aria-hidden="true" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 17c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm6-9h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zM8.9 6c0-1.71 1.39-3.1 3.1-3.1s3.1 1.39 3.1 3.1v2H8.9V6zM18 20H6V10h12v10z"/>
                       </svg>
                        <span class="text">Login</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="/register" class="nav-link <?= $_SERVER['REQUEST_URI'] === '/register' ? 'active' : '' ?>"
                       aria-current="<?= $_SERVER['REQUEST_URI'] === '/register' ? 'page' : 'false' ?>">
                       <svg class="svg-icon" aria-hidden="true" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M19 3H5c-1.11 0-2 .9-2 2v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-2 10h-4v4h-2v-4H7v-2h4V7h2v4h4v2z"/>
                       </svg>
                        <span class="text">Register</span>
                    </a>
                </li>
            <?php endif; ?>
        </ul>
    </nav>

    <?php if (isset($_SESSION['user_id'])) : ?>
    <div class="sidebar-footer">
        <a href="/expenses/create" class="quick-add-button" aria-label="Quick add expense">
            <svg class="svg-icon svg-icon--lg" aria-hidden="true" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z"/>
            </svg>
            <span class="text">Quick Add Expense</span>
        </a>
    </div>
    <?php endif; ?>
</aside>

