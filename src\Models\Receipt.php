<?php

declare(strict_types=1);

namespace App\Models;

class Receipt extends BaseModel
{
    private const MAX_DESCRIPTION_LENGTH = 255;
    private const MERCHANT_PATTERNS = [
        '/(?:Merchant|Store|Restaurant|Vendor|Business):\s*([^\n]+)/',
        '/(?:Thank you for shopping at|Receipt from|Sold by):\s*([^\n]+)/',
        '/(?:Welcome to|Receipt)\s+-\s+([^\n]+)/',
    ];

    protected static function getTableName(): string
    {
        return 'receipts';
    }

    public static function parseContent(string $content): array
    {
        if (empty($content)) {
            return [];
        }

        $merchant = self::extractMerchant($content);
        $date = self::extractDate($content);
        $amount = self::extractAmount($content);
        $items = self::extractItems($content);
        $paymentMethod = self::extractPaymentMethod($content);
        $category = self::determineCategory($merchant);

        return [
            'date' => $date,
            'merchant' => $merchant,
            'amount' => $amount,
            'items' => $items,
            'payment_method' => $paymentMethod,
            'category' => $category,
            'description' => self::generateDescription($content, $merchant),
        ];
    }

    public static function generateDownloadableReceipt(array $receiptData): string
    {
        $formattedDate = date('F j, Y', strtotime($receiptData['date']));
        $formattedTotal = number_format($receiptData['amount'], 2);

        $receipt = "========================================\n" .
                  "RECEIPT - {$receiptData['merchant']}\n" .
                  "========================================\n" .
                  "Date: {$formattedDate}\n" .
                  "Category: {$receiptData['category']}\n" .
                  "Payment Method: {$receiptData['payment_method']}\n\n";

        // Add items if available
        if (!empty($receiptData['items'])) {
            $receipt .= "ITEMS:\n";
            $receipt .= "----------------------------------------\n";

            foreach ($receiptData['items'] as $item) {
                $quantity = isset($item['quantity']) && $item['quantity'] > 1 ? "{$item['quantity']} x " : "";
                $itemPrice = number_format($item['price'], 2);
                $receipt .= "{$quantity}{$item['name']}: \${$itemPrice}\n";
            }

            $receipt .= "----------------------------------------\n";
        }

        $receipt .= "TOTAL: \${$formattedTotal}\n" .
                   "========================================\n" .
                   "Thank you for your business!";

        return $receipt;
    }

    private static function extractMerchant(string $content): string
    {
        $merchantFromPatterns = self::findMerchantByPatterns($content);
        if ($merchantFromPatterns !== null) {
            return $merchantFromPatterns;
        }

        $merchantFromBusinessTypes = self::findMerchantByBusinessTypes($content);
        if ($merchantFromBusinessTypes !== null) {
            return $merchantFromBusinessTypes;
        }

        $merchantFromLines = self::findMerchantFromLines($content);
        return $merchantFromLines ?? 'Unknown Merchant';
    }

    private static function findMerchantByPatterns(string $content): ?string
    {
        foreach (self::MERCHANT_PATTERNS as $pattern) {
            if (preg_match($pattern, $content, $matches)) {
                return !empty($matches[2]) ? trim($matches[2]) : trim($matches[1]);
            }
        }
        return null;
    }

    private static function findMerchantByBusinessTypes(string $content): ?string
    {
        $lines = array_filter(explode("\n", $content), 'trim');
        $businessTypes = ['cinema', 'restaurant', 'cafe', 'store', 'shop', 'market', 'hotel', 'theater', 'theatre'];

        foreach ($lines as $line) {
            $merchantFromLine = self::extractMerchantFromBusinessTypeLine($line, $businessTypes);
            if ($merchantFromLine !== null) {
                return $merchantFromLine;
            }
        }
        return null;
    }

    private static function extractMerchantFromBusinessTypeLine(string $line, array $businessTypes): ?string
    {
        if (!preg_match('/^([^:]+):\s*(.+)$/', $line, $matches)) {
            return null;
        }

        if (in_array(strtolower(trim($matches[1])), $businessTypes)) {
            return trim($matches[2]);
        }
        return null;
    }

    private static function findMerchantFromLines(string $content): ?string
    {
        $lines = array_filter(explode("\n", $content), 'trim');
        
        if (empty($lines)) {
            return null;
        }

        $firstLineMerchant = self::validateMerchantLine(reset($lines));
        if ($firstLineMerchant !== null) {
            return $firstLineMerchant;
        }

        if (count($lines) > 1) {
            return self::validateMerchantLine($lines[1]);
        }

        return null;
    }

    private static function validateMerchantLine(string $line): ?string
    {
        $trimmedLine = trim($line);
        if (strlen($trimmedLine) >= 3 && strlen($trimmedLine) <= 50) {
            return $trimmedLine;
        }
        return null;
    }

    private static function generateDescription(string $content, string $merchant): string
    {
        $lines = array_filter(explode("\n", $content), 'trim');

        if (empty($lines)) {
            return 'Receipt';
        }

        $description = $merchant;

        if (strlen($description) <= self::MAX_DESCRIPTION_LENGTH) {
            return $description;
        }

        return substr($description, 0, self::MAX_DESCRIPTION_LENGTH - 3) . '...';
    }

    private static function extractDate(string $content): string
    {
        $dateFromStandardFormat = self::findDateByStandardFormat($content);
        if ($dateFromStandardFormat !== null) {
            return $dateFromStandardFormat;
        }

        $dateFromAlternativeFormat = self::findDateByAlternativeFormat($content);
        return $dateFromAlternativeFormat ?? date('Y-m-d');
    }

    private static function findDateByStandardFormat(string $content): ?string
    {
        if (preg_match('/(?:Date|Date:)\s*(\d{4}-\d{1,2}-\d{1,2})/', $content, $matches)) {
            return $matches[1];
        }

        if (preg_match('/\b(\d{4}-\d{1,2}-\d{1,2})\b/', $content, $matches)) {
            return $matches[1];
        }

        return null;
    }

    private static function findDateByAlternativeFormat(string $content): ?string
    {
        if (preg_match('/(?:Date|Date:)\s*(\d{1,2}[\/\-\.]\d{1,2}[\/\-\.]\d{2,4})/', $content, $matches)) {
            $date = \DateTime::createFromFormat('m/d/Y', $matches[1]);
            if ($date) {
                return $date->format('Y-m-d');
            }
        }

        if ($date) {
            return $date->format('Y-m-d');
        }

        return null;
    }

    private static function extractAmount(string $content): float
    {
        $patterns = [
            '/Total[:\s]*\$([0-9]+\.[0-9]{2})/',
            '/Amount[:\s]*\$([0-9]+\.[0-9]{2})/',
            '/\$([0-9]+\.[0-9]{2})\s*Total/',
            '/Grand Total[:\s]*\$([0-9]+\.[0-9]{2})/',
            '/Final Amount[:\s]*\$([0-9]+\.[0-9]{2})/',
        ];

        $amountFromPatterns = self::findAmountByPatterns($content, $patterns);
        if ($amountFromPatterns !== null) {
            return $amountFromPatterns;
        }

        $amountFromLines = self::findAmountFromLines($content);
        if ($amountFromLines !== null) {
            return $amountFromLines;
        }

        $amountFromItems = self::calculateAmountFromItems($content);
        return $amountFromItems ?? 0.01;
    }

    private static function findAmountByPatterns(string $content, array $patterns): ?float
    {
        foreach ($patterns as $pattern) {
            if (preg_match($pattern, $content, $matches)) {
                return (float)$matches[1];
            }
        }
        return null;
    }

    private static function findAmountFromLines(string $content): ?float
    {
        $lines = array_filter(explode("\n", $content), 'trim');
        foreach ($lines as $line) {
            if (preg_match('/^Total:\s*\$?(\d+\.\d{2})$/i', $line, $matches)) {
                return (float)$matches[1];
            }
        }
        return null;
    }

    private static function calculateAmountFromItems(string $content): ?float
    {
        $items = self::extractItems($content);
        if (empty($items)) {
            return null;
        }

        $total = 0;
        foreach ($items as $item) {
            $total += (float)($item['price'] ?? 0) * (int)($item['quantity'] ?? 1);
        }
        return $total > 0 ? $total : null;
    }

    private static function extractItems(string $content): array
    {
        $items = [];
        $lines = array_filter(explode("\n", $content), 'trim');
        $inItemsSection = false;

        foreach ($lines as $line) {
            if (self::isItemsSectionStart($line)) {
                $inItemsSection = true;
                continue;
            }

            if ($inItemsSection) {
                $item = self::parseItemLine($line);
                if ($item !== null) {
                    $items[] = $item;
                }

                if (self::isItemsSectionEnd($line)) {
                    $inItemsSection = false;
                }
            }
        }

        return $items;
    }

    private static function isItemsSectionStart(string $line): bool
    {
        return stripos($line, 'Items:') !== false;
    }

    private static function isItemsSectionEnd(string $line): bool
    {
        return stripos($line, 'Total:') !== false || stripos($line, 'Payment') !== false;
    }

    private static function parseItemLine(string $line): ?array
    {
        if (preg_match('/^\s*-\s+(.+?):\s*\$(\d+\.\d{2})/', $line, $matches)) {
            return [
                'name' => trim($matches[1]),
                'price' => (float)$matches[2],
                'quantity' => 1
            ];
        }

        if (preg_match('/^\s*-\s+(.+?):\s*\$?(\d+\.\d{2})/', $line, $matches)) {
            return [
                'name' => trim($matches[1]),
                'price' => (float)$matches[2],
                'quantity' => 1
            ];
        }

        if (preg_match('/^\s*-\s+(\d+)\s*x\s+(.+?):\s*\$?(\d+\.\d{2})/', $line, $matches)) {
            return [
                'name' => trim($matches[2]),
                'price' => (float)$matches[3],
                'quantity' => (int)$matches[1]
            ];
        }

        return null;
    }

    private static function extractPaymentMethod(string $content): string
    {
        $paymentMethods = [
            'Cash' => '/(?:Paid with|Payment Method|Payment Type|Method):\s*Cash/i',
            'Credit Card' => '/(?:Paid with|Payment Method|Payment Type|Method):\s*(?:Credit|Credit Card|Visa|MasterCard|Amex)/i',
            'Debit Card' => '/(?:Paid with|Payment Method|Payment Type|Method):\s*(?:Debit|Debit Card)/i',
            'Check' => '/(?:Paid with|Payment Method|Payment Type|Method):\s*(?:Check|Cheque)/i',
            'PayPal' => '/(?:Paid with|Payment Method|Payment Type|Method):\s*PayPal/i',
            'Bank Transfer' => '/(?:Paid with|Payment Method|Payment Type|Method):\s*(?:Bank Transfer|Wire Transfer|ACH)/i',
        ];

        $methodFromPatterns = self::findPaymentMethodByPatterns($content, $paymentMethods);
        if ($methodFromPatterns !== null) {
            return $methodFromPatterns;
        }

        $methodFromLines = self::findPaymentMethodFromLines($content);
        if ($methodFromLines !== null) {
            return $methodFromLines;
        }

        return self::detectCashPayment($content) ? 'Cash' : 'Unknown';
    }

    private static function findPaymentMethodByPatterns(string $content, array $paymentMethods): ?string
    {
        foreach ($paymentMethods as $method => $pattern) {
            if (preg_match($pattern, $content)) {
                return $method;
            }
        }
        return null;
    }

    private static function findPaymentMethodFromLines(string $content): ?string
    {
        $lines = array_filter(explode("\n", $content), 'trim');
        foreach ($lines as $line) {
            if (preg_match('/Payment Method:\s*(.+)$/i', $line, $matches)) {
                return self::normalizePaymentMethod(trim($matches[1]));
            }
        }
        return null;
    }

    private static function normalizePaymentMethod(string $method): string
    {
        $methodMap = [
            'visa' => 'Credit Card',
            'mastercard' => 'Credit Card',
            'amex' => 'Credit Card',
            'american express' => 'Credit Card',
            'debit' => 'Debit Card',
            'cash' => 'Cash',
            'check' => 'Check',
            'cheque' => 'Check',
            'paypal' => 'PayPal',
        ];

        foreach ($methodMap as $key => $value) {
            if (stripos($method, $key) !== false) {
                return $value;
            }
        }

        if (strlen($method) > 0) {
            return ucwords(strtolower($method));
        }

        return 'Unknown';
    }

    private static function detectCashPayment(string $content): bool
    {
        return stripos($content, 'Cash') !== false;
    }

    private static function determineCategory(string $merchant): string
    {
        $merchantCategories = [
            'Food & Dining' => ['restaurant', 'cafe', 'pizza', 'burger', 'food', 'dining', 'kitchen', 'grill', 'bistro'],
            'Shopping' => ['store', 'shop', 'market', 'mall', 'retail', 'boutique'],
            'Entertainment' => ['cinema', 'theater', 'theatre', 'movie', 'entertainment', 'club', 'bar'],
            'Transportation' => ['gas', 'fuel', 'taxi', 'uber', 'lyft', 'transport', 'parking'],
            'Health & Medical' => ['pharmacy', 'hospital', 'clinic', 'medical', 'health', 'doctor'],
            'Utilities' => ['electric', 'water', 'gas', 'internet', 'phone', 'utility'],
        ];

        foreach ($merchantCategories as $category => $keywords) {
            foreach ($keywords as $keyword) {
                if (stripos($merchant, $keyword) !== false) {
                    return $category;
                }
            }
        }

        if (stripos($merchant, 'moviemax') !== false || stripos($merchant, 'cinema') !== false) {
            return 'Entertainment';
        }

        return 'Other';
    }
}
