<h2>Monthly Expense Report</h2>

<?php include __DIR__ . '/components/messages.php'; ?>

<div class="report-filters">
    <form method="GET" action="/reports/monthly">
        <div class="filter-group">
            <label for="year">Year</label>
            <select name="year" id="year" onchange="this.form.submit()">
                <?php foreach ($years as $yr) : ?>
                    <option value="<?= $yr ?>" <?= $yr == $selectedYear ? 'selected' : '' ?>>
                        <?= $yr ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>

        <div class="filter-group">
            <label for="category_id">Category</label>
            <select name="category_id" id="category_id" onchange="this.form.submit()">
                <option value="">All Categories</option>
                <?php foreach ($categories as $category) : ?>
                    <option value="<?= htmlspecialchars($category['id']) ?>" <?= $selectedCategory == $category['id'] ? 'selected' : '' ?>>
                        <?= htmlspecialchars($category['name']) ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>
    </form>
</div>

<?php if (empty($monthlyData)) : ?>
    <div class="notice">
        No expense data available for the selected filters.
    </div>
<?php else : ?>
    <div class="report-summary">
        <div class="summary-stats">
            <div class="stat-item">
                <span class="stat-label">Total for <?= $selectedYear ?></span>
                <span class="stat-value">$<?= number_format($yearlyTotal, 2) ?></span>
            </div>

            <div class="stat-item">
                <span class="stat-label">Monthly Average</span>
                <span class="stat-value">$<?= number_format($monthlyAverage, 2) ?></span>
            </div>

            <div class="stat-item">
                <span class="stat-label">Highest Month</span>
                <span class="stat-value"><?= $highestMonth['month'] ?></span>
                <span class="stat-subvalue">$<?= number_format($highestMonth['amount'], 2) ?></span>
            </div>

            <div class="stat-item">
                <span class="stat-label">Lowest Month</span>
                <span class="stat-value"><?= $lowestMonth['month'] ?></span>
                <span class="stat-subvalue">$<?= number_format($lowestMonth['amount'], 2) ?></span>
            </div>
        </div>
    </div>

    <div class="chart-container">
        <canvas id="monthlyChart" width="400" height="200"></canvas>
    </div>

    <div class="table-responsive">
        <table aria-label="Monthly Expenses">
            <thead>
                <tr>
                    <th scope="col">Month</th>
                    <th scope="col">Total Amount</th>
                    <th scope="col">% of Yearly Total</th>
                    <th scope="col">Number of Expenses</th>
                    <th scope="col">Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($monthlyData as $month) : ?>
                    <tr>
                        <td><?= $month['month_name'] ?></td>
                        <td>$<?= number_format($month['total_amount'], 2) ?></td>
                        <td><?= number_format($month['percentage'], 1) ?>%</td>
                        <td><?= $month['count'] ?></td>
                        <td>
                            <a href="/expenses?month=<?= $month['month'] ?>&year=<?= $selectedYear ?><?= $selectedCategory ? '&category_id=' . $selectedCategory : '' ?>" class="button small">
                                View Expenses
                            </a>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
            <tfoot>
                <tr>
                    <th>Total</th>
                    <th>$<?= number_format($yearlyTotal, 2) ?></th>
                    <th>100%</th>
                    <th><?= array_sum(array_column($monthlyData, 'count')) ?></th>
                    <th></th>
                </tr>
            </tfoot>
        </table>
    </div>

    <div class="download-section">
        <h3>Download Report</h3>
        <div class="download-options">
            <a href="/reports/export?type=monthly&year=<?= $selectedYear ?><?= $selectedCategory ? '&category_id=' . $selectedCategory : '' ?>&format=csv" class="button">
                Export as CSV
            </a>
            <a href="/reports/export?type=monthly&year=<?= $selectedYear ?><?= $selectedCategory ? '&category_id=' . $selectedCategory : '' ?>&format=pdf" class="button">
                Export as PDF
            </a>
        </div>
    </div>
<?php endif; ?>

<div class="navigation-links">
    <a href="/reports" class="button secondary">Back to Reports</a>
</div>

<?php if (!empty($monthlyData)) : ?>
<!-- Hidden data elements for JavaScript -->
<script id="chart-months-data" type="application/json">
    <?= json_encode(array_column($monthlyData, 'month_name')) ?>
</script>
<script id="chart-amounts-data" type="application/json">
    <?= json_encode(array_column($monthlyData, 'total_amount')) ?>
</script>
<?php endif; ?>

<?php
// Add reports-specific scripts
$scripts ??= [];
$scripts[] = 'https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js';
$scripts[] = '/assets/js/pages/reports.js';
?>
