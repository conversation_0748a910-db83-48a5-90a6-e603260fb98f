/**
 * Session Manager - <PERSON>les browser session tracking and validation
 *
 * This script helps detect when a user has closed all tabs and reopened the browser,
 * particularly for cases where "Remember Me" is not checked.
 *
 * It also periodically checks if the session is still valid and redirects to the
 * home page if it's not.
 */
document.addEventListener('DOMContentLoaded', function () {
    console.log('Session manager initialized');

    // Only run on authenticated pages
    // Skip on login, register, and home pages
    const currentPath = window.location.pathname;
    const publicPaths = ['/', '/login', '/register', '/password/reset', '/password/new', '/how'];

    if (!publicPaths.includes(currentPath)) {
        // Get remember me setting from the data attribute
        const rememberMe = document.body.getAttribute('data-remember-me');
        console.log('Remember me setting:', rememberMe);

        // For non-persistent sessions (remember me not checked), set up periodic session checks
        if (rememberMe === 'false') {
            console.log('Setting up periodic session checks for non-persistent session');

            // Check session immediately
            checkSession();

            // Set up periodic session checks (every 5 minutes)
            setInterval(checkSession, 5 * 60 * 1000);

            // Add event listener for visibility change (when tab becomes visible again)
            document.addEventListener('visibilitychange', function () {
                if (document.visibilityState === 'visible') {
                    // Check session when tab becomes visible again
                    checkSession();
                }
            });
        } else {
            console.log('Persistent session (remember me checked), no need for periodic checks');
        }

        // Add event listener for user interaction (clicks)
        document.addEventListener('click', function (event) {
            // Only check session for clicks on links that go to protected pages
            if (event.target.tagName === 'A') {
                const href = event.target.getAttribute('href');
                if (href && !href.startsWith('#') && !isPublicPath(href)) {
                    // This is a link to a protected page, check session before navigating
                    event.preventDefault();
                    checkSessionBeforeNavigation(href);
                }
            }
        });
    } else {
        console.log('On public page, no need to check session');
    }
});

/**
 * Check if a path is a public path that doesn't require authentication
 */
function isPublicPath(path)
{
    const publicPaths = ['/', '/login', '/register', '/password/reset', '/password/new', '/how'];
    return publicPaths.some(publicPath => path === publicPath || path.startsWith(publicPath + '?'));
}

/**
 * Check session before navigating to a protected page
 */
function checkSessionBeforeNavigation(destination)
{
    console.log('Checking session before navigating to:', destination);

    fetch('/check-session', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        console.log('Session check response:', data);

        if (data.valid) {
            // Session is valid, proceed with navigation
            console.log('Session valid, navigating to:', destination);
            window.location.href = destination;
        } else {
            // Session is invalid, redirect to login page
            console.log('Session invalid, redirecting to login page');
            window.location.href = '/login';
        }
    })
    .catch(error => {
        console.error('Error checking session:', error);
        // On error, proceed with navigation anyway
        window.location.href = destination;
    });
}

/**
 * Check if the session is still valid
 */
function checkSession()
{
    console.log('Checking session validity...');

    return fetch('/check-session', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(data => {
        console.log('Session check response:', data);

        // If session is invalid and a redirect is specified, perform the redirect
        if (!data.valid && data.redirect) {
            console.log(`Session invalid, redirecting to ${data.redirect}`);
            window.location.href = data.redirect;
            return false;
        }

        return data.valid;
    })
    .catch(error => {
        console.error('Error checking session:', error);
        return true; // Assume session is valid on error
    });
}
