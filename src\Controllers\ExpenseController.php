<?php

declare(strict_types=1);

namespace App\Controllers;

use App\Services\FileService;
use App\Services\ExpenseService;
use App\Services\ExpenseManagerService;
use App\Services\CategoryService;
use App\Services\MerchantService;
use App\Services\ReceiptParserService;
use App\Core\Response;
use App\Core\LogManager;
use App\Core\Security\Validator;
use App\Core\Security\Session;
use App\Utils\ArrayHelper;
use App\Exceptions\InvalidArgumentException;
use App\Exceptions\NotFoundException;
use App\Exceptions\FileUploadException;
use App\Exceptions\AuthorizationException;

class ExpenseController extends ResourceController
{
    private Validator $expenseValidator;
    private const ITEMS_PER_PAGE = 20;

    public function __construct(
        private readonly ExpenseManagerService $expenseManager,
        private readonly ExpenseService $expenseService,
        private readonly FileService $fileService,
        private readonly CategoryService $categoryService,
        private readonly MerchantService $merchantService,
        private readonly ReceiptParserService $receiptParserService
    ) {
        $this->initializeValidators();
    }

    private function initializeValidators(): void
    {
        $this->expenseValidator = new Validator([
            'amount' => ['required', 'numeric', 'positive'],
            'description' => ['required', 'string', 'min:1', 'max:255'],
            'date' => ['required', 'date'],
            'notes' => ['string'],
            'receipt_text' => ['string'],
            'category_id' => ['numeric'], // Allow null, zero or positive
            'user_category_id' => ['numeric'], // Allow null, zero or positive
            'merchant_id' => ['numeric'], // Allow null, zero or positive
            'payment_method_id' => ['numeric'], // Allow null, zero or positive
        ]);

        error_log("[Debug] ExpenseController::initializeValidators - Initialized validators with rules: " . json_encode($this->expenseValidator->getRules()));
    }

    protected function getService()
    {
        return $this->expenseManager;
    }

    protected function getResourceName(): string
    {
        return 'expenses';
    }

    protected function getIndexData(int $userId): array
    {
        error_log("[Debug] getIndexData - User ID: " . $userId);

        $sortParams = $this->validateSortParameters();
        $filters = $this->validateFilterParameters();
        $options = $this->buildOptionsArray($sortParams, $filters);
        
        $data = $this->expenseManager->getExpensesWithCategories($userId, $options);
        $additionalData = $this->getAdditionalIndexData($userId, $filters);
        
        $this->logIndexDataResults($data, $additionalData, $userId);
        
        return $this->buildIndexResponse($data, $filters, $additionalData);
    }
    
    private function validateSortParameters(): array
    {
        $sort = $_GET['sort'] ?? 'date';
        $order = $_GET['order'] ?? 'desc';
        
        $allowedSortFields = ['date', 'description', 'amount', 'category_id', 'category_name'];
        if (!in_array($sort, $allowedSortFields)) {
            $sort = 'date';
        }
        
        $order = strtolower($order) === 'asc' ? 'asc' : 'desc';
        
        return ['sort' => $sort, 'order' => $order];
    }
    
    private function validateFilterParameters(): array
    {
        return [
            'categoryId' => $this->validateCategoryId($_GET['category_id'] ?? null),
            'categoryIds' => $this->validateCategoryIds($_GET['category_ids'] ?? []),
            'date' => $this->validateDate($_GET['date'] ?? null),
            'dateFrom' => $this->validateDate($_GET['date_from'] ?? null),
            'dateTo' => $this->validateDate($_GET['date_to'] ?? null),
            'minAmount' => $this->validateAmount($_GET['minAmount'] ?? null),
            'maxAmount' => $this->validateAmount($_GET['maxAmount'] ?? null)
        ];
    }
    
    private function validateCategoryId($categoryId): ?int
    {
        if ($categoryId === null || $categoryId === '') {
            return null;
        }
        
        return is_numeric($categoryId) ? (int)$categoryId : null;
    }
    
    private function validateCategoryIds($categoryIds): array
    {
        if (!is_array($categoryIds)) {
            return [];
        }
        
        return array_filter(array_map('intval', $categoryIds), function ($id) {
            return $id > 0;
        });
    }
    
    private function validateDate($date): ?string
    {
        if ($date === null || $date === '') {
            return null;
        }
        
        $dateObj = \DateTime::createFromFormat('Y-m-d', $date);
        return ($dateObj && $dateObj->format('Y-m-d') === $date) ? $date : null;
    }
    
    private function validateAmount($amount): ?float
    {
        if ($amount === null || $amount === '') {
            return null;
        }
        
        $amountFloat = filter_var($amount, FILTER_VALIDATE_FLOAT);
        return ($amountFloat !== false && $amountFloat >= 0) ? $amountFloat : null;
    }
    
    private function buildOptionsArray(array $sortParams, array $filters): array
    {
        $options = [
            '_sort' => $sortParams['sort'],
            '_order' => $sortParams['order']
        ];
        
        $filterMappings = [
            'categoryId' => 'category_id',
            'categoryIds' => 'category_ids',
            'date' => 'date',
            'dateFrom' => 'date_from',
            'dateTo' => 'date_to',
            'minAmount' => 'min_amount',
            'maxAmount' => 'max_amount'
        ];
        
        foreach ($filterMappings as $filterKey => $optionKey) {
            $value = $filters[$filterKey];
            if ($value !== null && (!is_array($value) || !empty($value))) {
                $options[$optionKey] = $value;
            }
        }
        
        return $options;
    }
    
    private function getAdditionalIndexData(int $userId, array $filters): array
    {
        $categoryCounts = $this->expenseService->getCategoryCountsForUser($userId);
        $amountBounds = $this->expenseService->getAmountBounds($userId);
        
        $searchParams = [
            'query' => $_GET['query'] ?? '',
            'dateFrom' => $filters['dateFrom'],
            'dateTo' => $filters['dateTo'],
            'categoryIds' => !empty($filters['categoryIds']) ? $filters['categoryIds'] : 
                           ($filters['categoryId'] ? [$filters['categoryId']] : []),
            'minAmount' => $filters['minAmount'] ?? 0,
            'maxAmount' => $filters['maxAmount'] ?? 0,
            'hasDocument' => $_GET['hasDocument'] ?? ''
        ];
        
        $totalCount = $this->expenseService->countSearchResultsWithParams($userId, $searchParams);
        
        return [
            'categoryCounts' => $categoryCounts,
            'amountBounds' => $amountBounds,
            'totalCount' => $totalCount
        ];
    }
    
    private function logIndexDataResults(array $data, array $additionalData, int $userId): void
    {
        error_log("[Debug] getIndexData - Expenses count: " . count($data['expenses'] ?? []));
        error_log("[Debug] getIndexData - Categories count: " . count($data['categories'] ?? []));
        error_log("[Debug] getIndexData - Total count: " . $additionalData['totalCount']);
        error_log("[Debug] getIndexData - Category counts: " . json_encode($additionalData['categoryCounts']));
        error_log("[Debug] getIndexData - Amount bounds: " . json_encode($additionalData['amountBounds']));
        
        if (empty($data['expenses'])) {
            error_log("[Warning] getIndexData - No expenses found for user " . $userId);
        }
    }
    
    private function buildIndexResponse(array $data, array $filters, array $additionalData): array
    {
        return [
            'pageTitle' => 'Expenses',
            'layout' => 'layouts/expense.php',
            'scripts' => [],
            'stylesheets' => [],
            'expenses' => $data['expenses'],
            'categories' => $data['categories'],
            'paymentMethods' => [],
            'merchants' => [],
            'filters' => $filters,
            'totalCount' => $additionalData['totalCount'],
            'categoryCounts' => $additionalData['categoryCounts'],
            'amountBounds' => $additionalData['amountBounds'],
            'csrf_token' => $this->getCsrfToken(),
            'useModernTable' => true
        ];
    }

    protected function getCreateData(int $userId): array
    {
        return $this->prepareFormData($userId, 'Create Expense', [
            'id' => null,
            'user_id' => null,
            'category_id' => null,
            'amount' => null,
            'description' => null,
            'date' => date('Y-m-d'),
            'merchant_id' => null,
            'file_id' => null,
            'notes' => '',
            'payment_method_id' => null,
            'receipt_text' => '',
            'items' => []
        ]);
    }

    protected function getEditData(array $resource, int $userId): array
    {
        return $this->prepareFormData($userId, 'Edit Expense', $resource);
    }

    private function prepareFormData(int $userId, string $pageTitle, array $expense): array
    {
        $old = Session::get('old') ?? [];
        $errors = Session::get('errors') ?? [];
        
        $formData = $this->expenseManager->getFormData($userId);
        $formData = $this->normalizeFormDataArrays($formData);
        $formData = $this->convertFormDataToObjects($formData);
        
        $mergedExpense = $this->mergeOldInputData($expense, $old);
        
        $result = $this->buildFormDataResponse($pageTitle, $mergedExpense, $errors, $formData);
        
        error_log("ExpenseController::prepareFormData() - Result: " . json_encode($result));
        return $result;
    }
    
    private function normalizeFormDataArrays(array $formData): array
    {
        $formData['categories'] = ArrayHelper::ensureArray($formData['categories'] ?? null);
        $formData['merchants'] = ArrayHelper::ensureArray($formData['merchants'] ?? null);
        $formData['paymentMethods'] = ArrayHelper::ensureArray($formData['paymentMethods'] ?? null);
        
        error_log("ExpenseController::prepareFormData() - Form data: " . json_encode($formData));
        return $formData;
    }
    
    private function convertFormDataToObjects(array $formData): array
    {
        $formData['paymentMethods'] = $this->convertPaymentMethodsToObjects($formData['paymentMethods'] ?? []);
        $formData['categories'] = $this->convertCategoriesToObjects($formData['categories'] ?? []);
        $formData['merchants'] = $this->convertMerchantsToObjects($formData['merchants'] ?? []);
        $formData['paymentMethods'] = $this->fixPaymentMethodsFormat($formData['paymentMethods'] ?? []);
        
        return $formData;
    }
    
    private function convertPaymentMethodsToObjects(array $paymentMethods): array
    {
        if (!$this->needsIdToObjectConversion($paymentMethods)) {
            return $paymentMethods;
        }
        
        error_log("ExpenseController::prepareFormData() - Converting payment method IDs to objects");
        
        $defaultPaymentMethods = [
            ['id' => 1, 'method' => 'Cash'],
            ['id' => 2, 'method' => 'Credit Card'],
            ['id' => 3, 'method' => 'Debit Card'],
            ['id' => 4, 'method' => 'Mobile Payment'],
            ['id' => 5, 'method' => 'Bank Transfer'],
            ['id' => 6, 'method' => 'Digital Wallet'],
            ['id' => 7, 'method' => 'Other']
        ];
        
        $methodsWithNames = [];
        foreach ($paymentMethods as $methodId) {
            $methodName = $this->findDefaultMethodName($methodId, $defaultPaymentMethods);
            $methodsWithNames[] = ['id' => $methodId, 'method' => $methodName];
        }
        
        error_log("ExpenseController::prepareFormData() - Converted payment methods: " . json_encode($methodsWithNames));
        return $methodsWithNames;
    }
    
    private function convertCategoriesToObjects(array $categories): array
    {
        if (!$this->needsIdToObjectConversion($categories)) {
            return $categories;
        }
        
        error_log("ExpenseController::prepareFormData() - Converting category IDs to objects");
        
        $defaultCategories = [
            ['id' => 1, 'name' => 'Food & Dining'],
            ['id' => 2, 'name' => 'Transportation'],
            ['id' => 3, 'name' => 'Housing'],
            ['id' => 4, 'name' => 'Utilities'],
            ['id' => 5, 'name' => 'Entertainment'],
            ['id' => 6, 'name' => 'Career & Business'],
            ['id' => 7, 'name' => 'Health & Medical'],
            ['id' => 8, 'name' => 'Personal Care'],
            ['id' => 9, 'name' => 'Education'],
            ['id' => 10, 'name' => 'Shopping'],
            ['id' => 11, 'name' => 'Travel'],
            ['id' => 12, 'name' => 'Gifts & Donations'],
            ['id' => 13, 'name' => 'Bills & Fees'],
            ['id' => 14, 'name' => 'Other']
        ];
        
        $categoriesWithNames = [];
        foreach ($categories as $categoryId) {
            $categoryName = $this->findDefaultCategoryName($categoryId, $defaultCategories);
            $categoriesWithNames[] = ['id' => $categoryId, 'name' => $categoryName];
        }
        
        error_log("ExpenseController::prepareFormData() - Converted categories: " . json_encode($categoriesWithNames));
        return $categoriesWithNames;
    }
    
    private function convertMerchantsToObjects(array $merchants): array
    {
        if (!$this->needsIdToObjectConversion($merchants)) {
            return $merchants;
        }
        
        error_log("ExpenseController::prepareFormData() - Converting merchant IDs to objects");
        
        $merchantsWithNames = [];
        foreach ($merchants as $merchantId) {
            $merchantsWithNames[] = ['id' => $merchantId, 'name' => "Merchant $merchantId"];
        }
        
        error_log("ExpenseController::prepareFormData() - Converted merchants: " . json_encode($merchantsWithNames));
        return $merchantsWithNames;
    }
    
    private function needsIdToObjectConversion(array $items): bool
    {
        return !empty($items) && 
               isset($items[0]) && 
               is_numeric($items[0]) && 
               !is_array($items[0]);
    }
    
    private function findDefaultMethodName(int $methodId, array $defaultMethods): string
    {
        foreach ($defaultMethods as $defaultMethod) {
            if ($defaultMethod['id'] == $methodId) {
                return $defaultMethod['method'];
            }
        }
        return "Payment Method $methodId";
    }
    
    private function findDefaultCategoryName(int $categoryId, array $defaultCategories): string
    {
        foreach ($defaultCategories as $defaultCategory) {
            if ($defaultCategory['id'] == $categoryId) {
                return $defaultCategory['name'];
            }
        }
        return "Category $categoryId";
    }
    
    private function fixPaymentMethodsFormat(array $paymentMethods): array
    {
        if (empty($paymentMethods) || 
            (is_array($paymentMethods[0]) && isset($paymentMethods[0]['method']))) {
            return $paymentMethods;
        }
        
        error_log("ExpenseController::prepareFormData() - Fixing payment methods format");
        
        $fixedPaymentMethods = [];
        foreach ($paymentMethods as $key => $value) {
            if (is_array($value) && isset($value['id']) && isset($value['method'])) {
                $fixedPaymentMethods[] = $value;
            } else {
                $methodId = is_array($value) ? ($value['id'] ?? $key) : $value;
                $methodName = is_array($value) ? 
                    ($value['method'] ?? "Payment Method $methodId") : 
                    "Payment Method $value";
                $fixedPaymentMethods[] = ['id' => $methodId, 'method' => $methodName];
            }
        }
        
        error_log("ExpenseController::prepareFormData() - Fixed payment methods: " . json_encode($fixedPaymentMethods));
        return $fixedPaymentMethods;
    }
    
    private function mergeOldInputData(array $expense, array $old): array
    {
        $mergedExpense = $expense;
        $primitiveFields = ['amount', 'date', 'merchant_id', 'category_id', 'payment_method_id', 'description', 'notes', 'receipt_text'];
        
        foreach ($primitiveFields as $field) {
            if (isset($old[$field])) {
                $mergedExpense[$field] = $old[$field];
            }
        }
        
        return $mergedExpense;
    }
    
    private function buildFormDataResponse(string $pageTitle, array $expense, array $errors, array $formData): array
    {
        return array_merge([
            'pageTitle' => $pageTitle,
            'layout' => 'layouts/expense.php',
            'scripts' => [],
            'stylesheets' => [],
            'expense' => $expense,
            'errors' => $errors,
            'csrf_token' => $this->getCsrfToken()
        ], $formData);
    }

    protected function validateAndPrepareData(array $data, int $userId): array
    {
        error_log("[Debug] ExpenseController::validateAndPrepareData - Input data: " . json_encode($data));

        $data['user_id'] = $userId;

        if (empty($data['merchant_id'])) {
            $data['merchant_id'] = '0';
        }

        if (empty($data['payment_method_id'])) {
            $data['payment_method_id'] = '0';
        }

        $hasFileUpload = isset($_FILES['document']) &&
                         $_FILES['document']['error'] === UPLOAD_ERR_OK &&
                         !empty($_FILES['document']['tmp_name']);

        if ($hasFileUpload) {
            if (empty($data['amount'])) {
                $data['amount'] = '0.01';
            }
            if (empty($data['description'])) {
                $data['description'] = 'Receipt Upload';
            }
            if (empty($data['date'])) {
                $data['date'] = date('Y-m-d');
            }
            // Only set a default category if neither category_id nor user_category_id is provided
            if (
                (empty($data['category_id']) || $data['category_id'] === '0' || $data['category_id'] === 0 || $data['category_id'] === '_new') &&
                (empty($data['user_category_id']) || $data['user_category_id'] === '0' || $data['user_category_id'] === 0)
            ) {
                LogManager::getLogger()?->debug("No category provided for file upload, defaulting to category_id = 1 (Housing)");
                $data['category_id'] = 1;
            }

            // Special handling for '_new' category
            if (isset($data['category_id']) && $data['category_id'] === '_new' && !empty($data['new_category'])) {
                LogManager::getLogger()?->debug("New category name provided: " . $data['new_category']);
                // Here we would normally create a new category, but for now we'll default to Housing
                $data['category_id'] = 1;
            }
        }

        $expenseData = $this->prepareExpenseData($data);
        $expenseData = $this->processLineItems($expenseData, $data);
        $expenseData = $this->processFileUploads($expenseData);

        if (!isset($expenseData['user_category_id'])) {
            $expenseData['user_category_id'] = 0;
        }

        error_log("[Debug] ExpenseController::validateAndPrepareData - Final data: " . json_encode($expenseData));

        return $expenseData;
    }

    private function processLineItems(array $expenseData, array $data): array
    {
        if (!isset($data['items']) || !is_array($data['items'])) {
            $categoryId = 1;
            $expenseData['category_id'] = $categoryId;
            return $expenseData;
        }

        return $this->incorporateItemsIntoNotes($expenseData, $data['items']);
    }

    private function processFileUploads(array $expenseData): array
    {
        if (!$this->shouldProcessFile($expenseData)) {
            return $expenseData;
        }

        $fileData = $_FILES['document'] ?? null;
        if (!$this->isValidFileData($fileData)) {
            return $expenseData;
        }

        return $this->uploadAndProcessFile($fileData, $expenseData);
    }

    private function shouldProcessFile(array $expenseData): bool
    {
        return isset($expenseData['category_id']) && !empty($expenseData['category_id'] ?? 0);
    }

    private function uploadAndProcessFile(array $fileData, array $expenseData): array
    {
        try {
            $categoryId = $expenseData['category_id'] ?? 0;
            if ($categoryId <= 0) {
                return $expenseData;
            }

            $fileId = $this->handleFileUpload(
                $fileData,
                null,
                $categoryId
            );

            if ($fileId) {
                $expenseData['file_id'] = $fileId;
                $expenseData = $this->parseReceiptIfExists($fileId, $expenseData);
            }
        } catch (FileUploadException $e) {
            $this->logFileUploadError($e, $expenseData['category_id']);
        } catch (\Exception $e) {
            LogManager::getLogger()?->error("Error processing file upload", [
                'error' => $e->getMessage(),
                'category_id' => $expenseData['category_id'] ?? 0
            ]);
        }

        return $expenseData;
    }

    private function logFileUploadError(FileUploadException $e, int $categoryId): void
    {
        LogManager::getLogger()?->error("File upload failed", [
            'error' => $e->getMessage(),
            'category_id' => $categoryId
        ]);
    }

    private function isValidFileData(?array $fileData): bool
    {
        if ($fileData === null) {
            return false;
        }

        if (!isset($fileData['tmp_name']) || empty($fileData['tmp_name'])) {
            return false;
        }

        if (isset($fileData['error'])) {
            $isValidError = $fileData['error'] === UPLOAD_ERR_OK || $fileData['error'] === UPLOAD_ERR_NO_FILE;
            if (!$isValidError) {
                return false;
            }
        }

        return true;
    }

    protected function storeResource(array $data, int $userId): int
    {
        error_log("[Debug] ExpenseController::storeResource - Input data: " . json_encode($data));

        $data['user_id'] = $userId;

        error_log("[Debug] ExpenseController::storeResource - Calling createExpense");
        $expenseId = $this->expenseService->createExpense($data);

        error_log("[Debug] ExpenseController::storeResource - Created expense with ID: " . $expenseId);

        return $expenseId;
    }

    protected function findResource(int $id, int $userId): array
    {
        LogManager::getLogger()?->debug("ExpenseController::findResource", [
            'id' => $id,
            'userId' => $userId
        ]);

        try {
            $expense = $this->expenseManager->getExpenseById($id, $userId);
            LogManager::getLogger()?->debug("ExpenseController::findResource - Found expense", [
                'expense' => $expense
            ]);
            return $expense;
        } catch (\Exception $e) {
            LogManager::getLogger()?->error("ExpenseController::findResource - Error", [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    public function store(): Response
    {
        try {
            // Log the raw POST data at the very beginning
            error_log("[Debug] ExpenseController::store - Raw POST data: " . json_encode($_POST));

            // Log the category_id specifically
            error_log("[Debug] ExpenseController::store - Category ID: " . ($_POST['category_id'] ?? 'not set') . " (type: " . gettype($_POST['category_id'] ?? null) . ")");

            $this->validateCsrf($this->getPostValue('csrf_token'));
            $userId = $this->getUserId();

            LogManager::getLogger()?->debug("Raw POST data", ['data' => $_POST]);
            LogManager::getLogger()?->debug("Before validateAndPrepareData", [
                'merchant_id' => $_POST['merchant_id'] ?? 'not set',
                'payment_method_id' => $_POST['payment_method_id'] ?? 'not set'
            ]);

            $expenseData = $this->validateAndPrepareData($_POST, $userId);

            LogManager::getLogger()?->debug("After validateAndPrepareData", [
                'merchant_id' => $expenseData['merchant_id'] ?? 'not set',
                'payment_method_id' => $expenseData['payment_method_id'] ?? 'not set'
            ]);

            // Check if the expense data is nested under a 'data' key
            if (isset($expenseData['data']) && is_array($expenseData['data'])) {
                LogManager::getLogger()?->debug("Expense data is nested, extracting from 'data' key");
                $expenseData = $expenseData['data'];
            }

            // Log the raw POST data for category
            LogManager::getLogger()?->debug("Raw category data from POST", [
                'category_id' => $_POST['category_id'] ?? 'not set',
                'user_category_id' => $_POST['user_category_id'] ?? 'not set'
            ]);

            // Log the category data after validation
            LogManager::getLogger()?->debug("Category data after validation", [
                'category_id' => $expenseData['category_id'] ?? 'not set',
                'user_category_id' => $expenseData['user_category_id'] ?? 'not set'
            ]);

            // Only set a default category if neither category_id nor user_category_id is provided
            if (
                (empty($expenseData['category_id']) || (int)$expenseData['category_id'] === 0) &&
                (empty($expenseData['user_category_id']) || (int)$expenseData['user_category_id'] === 0)
            ) {
                LogManager::getLogger()?->debug("No category provided, defaulting to category_id = 1 (Housing)");
                $expenseData['category_id'] = 1; // Default to first category if not set
            }

            if (empty($expenseData['user_id'])) {
                $expenseData['user_id'] = $userId;
            }

            LogManager::getLogger()?->debug("Final expense data for storage", $expenseData);

            $expenseId = $this->storeResource($expenseData, $userId);
            return $this->redirect("/expenses/{$expenseId}", 'Expense created successfully');
        } catch (InvalidArgumentException | FileUploadException $e) {
            error_log("[Error] ExpenseController::store - Validation error: " . $e->getMessage());
            error_log("[Error] ExpenseController::store - Trace: " . $e->getTraceAsString());

            // Try to decode the error message if it's in JSON format
            $errorMessage = $e->getMessage();
            $decodedError = json_decode($errorMessage, true);

            if (is_array($decodedError)) {
                // If it's a JSON error, use the decoded array
                error_log("[Error] ExpenseController::store - Decoded validation errors: " . json_encode($decodedError));
                return $this->redirectWithErrors('/expenses/new', $decodedError, $_POST);
            } else {
                // Otherwise use the raw message
                return $this->redirectWithErrors('/expenses/new', [$errorMessage], $_POST);
            }
        } catch (\Exception $e) {
            error_log("[Error] ExpenseController::store - Unexpected error: " . $e->getMessage());
            error_log("[Error] ExpenseController::store - Trace: " . $e->getTraceAsString());

            // Try to decode the error message if it's in JSON format
            $errorMessage = $e->getMessage();
            $decodedError = json_decode($errorMessage, true);

            if (is_array($decodedError)) {
                // If it's a JSON error, use the decoded array
                error_log("[Error] ExpenseController::store - Decoded unexpected errors: " . json_encode($decodedError));
                return $this->redirectWithErrors('/expenses/new', $decodedError, $_POST);
            } else {
                // Otherwise use a generic message
                LogManager::logException($e, "Error storing expense");
                return $this->redirectWithErrors('/expenses/new', ['An unexpected error occurred'], $_POST);
            }
        }
    }

    protected function getShowData(array $resource, int $userId): array
    {
        LogManager::getLogger()?->debug("ExpenseController::getShowData - Resource data", [
            'resource' => $resource
        ]);

        // Make sure we have a valid resource with an ID
        if (empty($resource) || !isset($resource['id'])) {
            LogManager::getLogger()?->error("ExpenseController::getShowData - Invalid resource data");
            throw new NotFoundException('Expense not found or invalid data');
        }

        // Prepare the expense view data
        $viewData = $this->prepareExpenseViewData((int)$resource['id'], false);

        // Log the view data for debugging
        LogManager::getLogger()?->debug("ExpenseController::getShowData - View data prepared", [
            'has_expense' => isset($viewData['expense']),
            'expense_id' => $viewData['expense']['id'] ?? 'not set'
        ]);

        return $viewData;
    }

    /**
     * Prepare standardized view data for expense display
     */
    private function prepareExpenseViewData(int $id, bool $includeDocument): array
    {
        $userId = $this->getUserId();

        // Retrieve the expense
        $expense = $this->expenseService->getExpenseById($id, $userId);
        if (empty($expense)) {
            throw new NotFoundException('Expense not found');
        }

        // Debug the resource data
        error_log("ExpenseController::prepareExpenseViewData() - Expense data: " . json_encode($expense));

        $formData = $this->expenseManager->getFormData($userId);

        // Debug the form data
        error_log("ExpenseController::prepareExpenseViewData() - Form data: " . json_encode($formData));

        // Get the specific category for this expense
        $categoryId = $expense['category_id'] ?? null;
        $userCategoryId = $expense['user_category_id'] ?? null;
        $merchantId = $expense['merchant_id'] ?? null;
        $paymentMethodId = $expense['payment_method_id'] ?? null;

        // Debug the extracted IDs
        error_log("ExpenseController::prepareExpenseViewData() - IDs: category=$categoryId, userCategory=$userCategoryId, merchant=$merchantId, paymentMethod=$paymentMethodId");

        $category = null;
        $merchant = null;
        $paymentMethod = null;

        // Check for system category first
        if ($categoryId) {
            foreach ($formData['categories'] as $cat) {
                if ($cat['id'] == $categoryId) {
                    $category = $cat;
                    break;
                }
            }
        } elseif ($userCategoryId) {
            try {
                $userCategory = $this->categoryService->getUserCategoryById($userCategoryId, $userId);
                if ($userCategory) {
                    $category = $userCategory;
                }
            } catch (\Exception $e) {
                error_log("Error fetching user category: " . $e->getMessage());
            }
        }

        // Find the merchant
        if ($merchantId) {
            foreach ($formData['merchants'] as $m) {
                if ($m['id'] == $merchantId) {
                    $merchant = $m;
                    break;
                }
            }

            // If merchant not found in form data or has empty name, fetch directly from service
            if (!$merchant || empty($merchant['name'])) {
                try {
                    $merchantRecord = $this->merchantService->getMerchantById($merchantId);
                    $merchant = $merchantRecord;
                } catch (\Exception $e) {
                    error_log("Error fetching merchant directly: " . $e->getMessage());
                    $merchant = null;
                }
            }
        }

        // Determine merchant name with fuzzy matching fallback
        $merchantName = $merchant['name'] ?? '';
        if (empty($merchantName)) {
            $suggestion = $this->receiptParserService->findBestMerchantMatch($expense['description'] ?? '', $userId);
            if ($suggestion) {
                $merchantName = $suggestion;
            } else {
                $merchantName = 'Unknown Merchant';
            }
        }

        // Ensure expense has merchant_name for receipt formatter
        $expense['merchant_name'] = $merchantName;

        // Find the payment method and standardize format
        if ($paymentMethodId) {
            foreach ($formData['paymentMethods'] as $pm) {
                if ($pm['id'] == $paymentMethodId) {
                    $paymentMethod = [
                        'id' => $pm['id'],
                        'name' => $pm['method'] ?? $pm['name'] ?? 'Unknown Payment Method'
                    ];
                    break;
                }
            }
        }

        // Get file info if there's a file attached
        $fileInfo = null;
        if (!empty($expense['file_id'])) {
            try {
                $fileInfo = $this->fileService->getFileInfo($expense['file_id']);
            } catch (\Exception $e) {
                error_log("Error fetching file info: " . $e->getMessage());
            }
        }

        // Generate receipt HTML for display if we have a file or receipt text
        $receiptHtml = null;
        if (!empty($expense['file_id']) || !empty($expense['receipt_text'])) {
            try {
                $receiptService = new \App\Services\ReceiptService();
                $receiptData = $receiptService->getExpenseDataForReceipt($expense['id'], $userId);
                $receiptText = $receiptService->generateReceiptContent($receiptData, $expense['id']);

                // Create a styled receipt HTML that matches the manual format
                $receiptHtml = $this->formatReceiptAsStyledHtml($receiptText, $expense);
            } catch (\Exception $e) {
                error_log("Error generating receipt HTML for expense view: " . $e->getMessage());
            }
        }

        // Prepare base view data with consistent keys
        $viewData = [
            'pageTitle' => 'View Expense',
            'layout' => 'layouts/expense.php',
            'scripts' => [],
            'stylesheets' => [],
            'expense' => $expense,
            'hasDocument' => !empty($expense['file_id']),
            'category' => $category,
            'merchant' => $merchant,
            'merchant_name' => $merchantName,
            'paymentMethod' => $paymentMethod,
            'receiptHtml' => $receiptHtml,
            'csrf_token' => $this->getCsrfToken(),
            'categories' => $formData['categories'] ?? [],
            'paymentMethods' => $formData['paymentMethods'] ?? [],
            'merchants' => $formData['merchants'] ?? []
        ];

        // Add document data if requested
        if ($includeDocument && !empty($expense['file_id'])) {
            if (empty($fileInfo)) {
                throw new NotFoundException('No document attached');
            }

            try {
                $content = $this->fileService->getFileContent($expense['file_id']);
                $mimeType = $fileInfo['mime_type'] ?? '';

                $viewData['document'] = [
                    'fileInfo' => $fileInfo,
                    'content' => $content,
                    'isImage' => strpos($mimeType, 'image/') === 0,
                    'isPdf' => $mimeType === 'application/pdf',
                    'isText' => strpos($mimeType, 'text/') === 0,
                    'receiptData' => $receiptHtml ? ['html' => $receiptHtml] : null
                ];
            } catch (\Exception $e) {
                LogManager::getLogger()?->error('Error loading document content', [
                    'expense_id' => $id,
                    'error' => $e->getMessage()
                ]);
                throw new NotFoundException('Document could not be loaded');
            }
        }

        // Debug the final result
        error_log("ExpenseController::prepareExpenseViewData() - Final result: " . json_encode([
            'expense_exists' => isset($viewData['expense']),
            'hasDocument' => $viewData['hasDocument'],
            'category_exists' => isset($viewData['category']),
            'merchant_exists' => isset($viewData['merchant']),
            'paymentMethod_exists' => isset($viewData['paymentMethod']),
            'document_exists' => isset($viewData['document']),
            'receiptHtml_exists' => isset($viewData['receiptHtml'])
        ]));

        return $viewData;
    }

    /**
     * Format a receipt text into a styled HTML that matches the manual receipt format
     */
    protected function formatReceiptAsStyledHtml(string $receiptText, array $expense): string
    {
        // Extract data from the receipt text
        $merchantName = $expense['merchant_name'] ?? 'Unknown Merchant';
        $date = date('F j, Y', strtotime($expense['date'] ?? 'now'));
        $categoryName = $expense['category_name'] ?? '';
        $amount = number_format((float)$expense['amount'], 2);
        $description = $expense['description'] ?? '';

        // Extract items if available
        $items = $this->expenseManager->extractItemsFromNotes($expense['notes'] ?? '');

        // Start building the HTML
        $html = '<div class="receipt-merchant">' . htmlspecialchars($merchantName) . '</div>';

        // Add description directly under merchant name (no label)
        if (!empty($description)) {
            $html .= '<div class="receipt-description">' . htmlspecialchars($description) . '</div>';
        }

        $html .= '<div class="receipt-divider"></div>';

        // Add receipt details
        $html .= '<div class="receipt-details">';
        $html .= '<div class="receipt-row"><span class="receipt-label">Date:</span><span class="receipt-value">' . $date . '</span></div>';

        if (!empty($categoryName)) {
            $html .= '<div class="receipt-row"><span class="receipt-label">Category:</span><span class="receipt-value">' . htmlspecialchars($categoryName) . '</span></div>';
        }

        if (!empty($expense['payment_method'])) {
            $html .= '<div class="receipt-row"><span class="receipt-label">Payment:</span><span class="receipt-value">' . htmlspecialchars($expense['payment_method']) . '</span></div>';
        }

        $html .= '</div>';

        // Add items if available
        if (!empty($items)) {
            $html .= '<div class="receipt-divider"></div>';
            $html .= '<div class="receipt-items">';
            $html .= '<div class="receipt-item-header"><span class="item-name">Item</span><span class="item-price" style="width:70px;min-width:70px;flex-shrink:0;text-align:right;">Price</span></div>';

            foreach ($items as $item) {
                $html .= '<div class="receipt-item">';
                $html .= '<span class="item-name">' . htmlspecialchars($item['name']) . '</span>';
                $html .= '<span class="item-price" style="width:70px;min-width:70px;flex-shrink:0;text-align:right;">$' . number_format((float)$item['price'], 2) . '</span>';
                $html .= '</div>';
            }

            $html .= '</div>';
        }

        // Add total with improved spacing
        $html .= '<div class="receipt-divider dashed"></div>';
        $html .= '<div class="receipt-total"><span class="total-label">TOTAL</span><span class="total-amount">$' . $amount . '</span></div>';

        // Add footer
        $html .= '<div class="receipt-footer">';
        $html .= '<div class="receipt-barcode"></div>';
        $html .= '<div class="receipt-thank-you">Thank you for your business!</div>';
        $html .= '</div>';

        return $html;
    }

    /**
     * View document attached to an expense
     */
    public function viewDocument(int $id): Response
    {
        try {
            $viewData = $this->prepareExpenseViewData($id, true);
            return $this->view('expenses/show', $viewData);
        } catch (NotFoundException $e) {
            throw $e;
        } catch (\Exception $e) {
            LogManager::getLogger()?->error('Error loading document', [
                'expense_id' => $id,
                'error' => $e->getMessage()
            ]);
            throw new NotFoundException('Document could not be loaded');
        }
    }

    /**
     * Prepare expense data for storage
     */
    private function prepareExpenseData(array $data): array
    {
        error_log("[Debug] ExpenseController::prepareExpenseData - Input data: " . json_encode($data));

        // Validate the expense data
        try {
            // The validate method will throw an exception if validation fails
            // or return the validated data if successful
            $data = $this->expenseValidator->validate($data);
            error_log("[Debug] ExpenseController::prepareExpenseData - Validated data: " . json_encode($data));
        } catch (InvalidArgumentException $e) {
            error_log("[Error] ExpenseController::prepareExpenseData - Validation error: " . $e->getMessage());
            // Re-throw the exception
            throw $e;
        }

        // Ensure numeric fields are properly formatted
        $data['amount'] = (float)$data['amount'];

        // Log the category data before conversion
        error_log("[Debug] ExpenseController::prepareExpenseData - Category data before conversion: " . json_encode([
            'category_id' => $data['category_id'] ?? 'not set',
            'user_category_id' => $data['user_category_id'] ?? 'not set'
        ]));

        LogManager::getLogger()?->debug("Category data before conversion", [
            'category_id' => $data['category_id'] ?? 'not set',
            'user_category_id' => $data['user_category_id'] ?? 'not set'
        ]);

        // Convert category_id to integer if it exists, otherwise keep it as is
        if (isset($data['category_id'])) {
            $data['category_id'] = (int)$data['category_id'];
            error_log("[Debug] ExpenseController::prepareExpenseData - Converted category_id to int: " . $data['category_id']);
        }

        // Convert user_category_id to integer if it exists, otherwise keep it as is
        if (isset($data['user_category_id'])) {
            $data['user_category_id'] = (int)$data['user_category_id'];
            error_log("[Debug] ExpenseController::prepareExpenseData - Converted user_category_id to int: " . $data['user_category_id']);
        }

        $data['merchant_id'] = !empty($data['merchant_id']) ? (int)$data['merchant_id'] : 0;
        $data['payment_method_id'] = !empty($data['payment_method_id']) ? (int)$data['payment_method_id'] : 0;

        // Log the category data after conversion
        LogManager::getLogger()?->debug("Category data after conversion", [
            'category_id' => $data['category_id'] ?? 'not set',
            'user_category_id' => $data['user_category_id'] ?? 'not set'
        ]);

        // Ensure other fields are properly formatted
        $data['description'] = trim($data['description']);
        $data['notes'] = $data['notes'] ?? '';
        $data['receipt_text'] = $data['receipt_text'] ?? '';

        return $data;
    }

    /**
     * Handle file upload for an expense
     */
    private function handleFileUpload(array $fileData, ?int $expenseId, int $categoryId): ?int
    {
        if (empty($fileData) || $fileData['error'] !== UPLOAD_ERR_OK) {
            return null;
        }

        try {
            // Upload the file
            $fileId = $this->fileService->uploadFile(
                $fileData,
                $this->getUserId(),
                'expense',
                $expenseId,
                $categoryId
            );

            return $fileId;
        } catch (FileUploadException $e) {
            LogManager::getLogger()?->error("File upload failed", [
                'error' => $e->getMessage(),
                'category_id' => $categoryId
            ]);
            throw $e;
        }
    }

    /**
     * Parse receipt if it exists
     */
    private function parseReceiptIfExists(?int $fileId, array $expenseData): array
    {
        if (!$fileId) {
            return $expenseData;
        }

        try {
            // Get file info
            $fileInfo = $this->fileService->getFileInfo($fileId);
            if (!$fileInfo) {
                return $expenseData;
            }

            // Check if file is an image or PDF
            $mimeType = $fileInfo['mime_type'] ?? '';
            $isImage = strpos($mimeType, 'image/') === 0;
            $isPdf = $mimeType === 'application/pdf';

            if (!$isImage && !$isPdf) {
                return $expenseData;
            }

            // Get file content
            $content = $this->fileService->getFileContent($fileId);
            if (empty($content)) {
                return $expenseData;
            }

            // Parse receipt
            $receiptData = $this->receiptParserService->parseReceipt($content, $mimeType);
            if (empty($receiptData)) {
                return $expenseData;
            }

            // Update expense data with receipt data
            if (!empty($receiptData['text'])) {
                $expenseData['receipt_text'] = $receiptData['text'];
            }

            if (empty($expenseData['amount']) && !empty($receiptData['total'])) {
                $expenseData['amount'] = $receiptData['total'];
            }

            if (empty($expenseData['date']) && !empty($receiptData['date'])) {
                $expenseData['date'] = $receiptData['date'];
            }

            if (empty($expenseData['merchant_id']) && !empty($receiptData['merchant'])) {
                // Try to find or create merchant
                $merchantId = $this->merchantService->findOrCreateMerchant(
                    $receiptData['merchant'],
                    $this->getUserId()
                );
                if ($merchantId) {
                    $expenseData['merchant_id'] = $merchantId;
                }
            }

            return $expenseData;
        } catch (\Exception $e) {
            LogManager::getLogger()?->error("Error parsing receipt", [
                'error' => $e->getMessage(),
                'file_id' => $fileId
            ]);
            return $expenseData;
        }
    }

    /**
     * Incorporate line items into notes
     */
    private function incorporateItemsIntoNotes(array $expenseData, array $items): array
    {
        if (empty($items)) {
            return $expenseData;
        }

        // Filter out empty items
        $validItems = array_filter($items, function ($item) {
            return !empty($item['name']) && isset($item['price']);
        });

        if (empty($validItems)) {
            return $expenseData;
        }

        // Format items as JSON and store in notes
        $itemsJson = json_encode($validItems);
        $expenseData['notes'] = $expenseData['notes'] ?? '';

        // Check if notes already contains items JSON
        if (strpos($expenseData['notes'], '{"items":') === false) {
            // Add items JSON to notes
            $expenseData['notes'] .= "\n\n" . '{"items":' . $itemsJson . '}';
        } else {
            // Replace existing items JSON in notes
            $pattern = '/\{"items":\[.*?\]\}/s';
            $replacement = '{"items":' . $itemsJson . '}';
            $expenseData['notes'] = preg_replace($pattern, $replacement, $expenseData['notes']);
        }

        return $expenseData;
    }

    /**
     * Update an expense resource
     */
    protected function updateResource(int $id, array $data, int $userId): bool
    {
        try {
            // Ensure the expense belongs to the user
            $expense = $this->expenseManager->getExpenseById($id, $userId);
            if (empty($expense)) {
                throw new NotFoundException('Expense not found');
            }

            // Process file uploads if any
            $data = $this->processFileUploads($data);

            // Update the expense
            $result = $this->expenseService->updateExpense($id, $data);

            return $result;
        } catch (\Exception $e) {
            LogManager::getLogger()?->error("Error updating expense", [
                'expense_id' => $id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Delete an expense resource
     */
    protected function deleteResource(int $id, int $userId): bool
    {
        try {
            // Ensure the expense belongs to the user
            $expense = $this->expenseManager->getExpenseById($id, $userId);
            if (empty($expense)) {
                throw new NotFoundException('Expense not found');
            }

            // Delete any associated files
            if (!empty($expense['file_id'])) {
                try {
                    $this->fileService->deleteFile($expense['file_id'], false);
                } catch (\Exception $e) {
                    LogManager::getLogger()?->error("Error deleting file for expense", [
                        'expense_id' => $id,
                        'file_id' => $expense['file_id'],
                        'error' => $e->getMessage()
                    ]);
                    // Continue with expense deletion even if file deletion fails
                }
            }

            // Delete the expense
            $result = $this->expenseService->deleteExpense($id, $userId);

            return $result;
        } catch (\Exception $e) {
            LogManager::getLogger()?->error("Error deleting expense", [
                'expense_id' => $id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Delete an expense via DELETE request (RESTful endpoint)
     */
    public function destroy(int $id): Response
    {
        try {
            $this->validateCsrf($this->getPostValue('csrf_token'));
            $userId = $this->getUserId();

            $success = $this->deleteResource($id, $userId);

            if ($success) {
                return $this->redirect('/expenses', 'Expense deleted successfully');
            } else {
                return $this->redirectWithErrors('/expenses', ['Failed to delete expense']);
            }
        } catch (NotFoundException $e) {
            return $this->redirectWithErrors('/expenses', ['Expense not found']);
        } catch (InvalidArgumentException $e) {
            return $this->redirectWithErrors('/expenses', [$e->getMessage()]);
        } catch (\Exception $e) {
            LogManager::getLogger()?->error("Error deleting expense via destroy method", [
                'expense_id' => $id,
                'error' => $e->getMessage()
            ]);
            return $this->redirectWithErrors('/expenses', ['An unexpected error occurred while deleting the expense']);
        }
    }

    /**
     * Bulk delete multiple expenses
     */
    public function bulkDelete(): Response
    {
        try {
            $this->validateCsrf($this->getPostValue('csrf_token'));
            $userId = $this->getUserId();

            // Get the comma-separated list of IDs
            $idsString = $this->getPostValue('expenseIds');

            // Split the comma-separated list into an array
            $ids = !empty($idsString) ? explode(',', $idsString) : [];

            if (empty($ids)) {
                return $this->redirectWithErrors('/expenses', ['No expenses selected for deletion']);
            }

            // Call the service to perform bulk deletion
            $result = $this->expenseService->bulkDeleteExpenses($ids, $userId);

            if ($result['success']) {
                $message = "Successfully deleted {$result['deleted']} expense(s)";
                if ($result['failed'] > 0) {
                    $message .= ". {$result['failed']} expense(s) could not be deleted";
                }
                return $this->redirect('/expenses', $message);
            } else {
                return $this->redirectWithErrors('/expenses', ['Failed to delete expenses']);
            }
        } catch (InvalidArgumentException $e) {
            return $this->redirectWithErrors('/expenses', [$e->getMessage()]);
        } catch (\Exception $e) {
            LogManager::getLogger()?->error("Error in bulk delete", [
                'error' => $e->getMessage()
            ]);
            return $this->redirectWithErrors('/expenses', ['An unexpected error occurred during bulk deletion']);
        }
    }
}
