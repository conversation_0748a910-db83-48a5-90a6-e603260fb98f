<?php

/**
 * Action button component
 * Displays a button with an icon and text
 *
 * @param string $url The URL for the button
 * @param string $text The button text
 * @param string $icon The icon ID (without the prefix)
 * @param string $size Button size (small, medium, large) - default: medium
 * @param string $type Button type (primary, secondary, danger) - default: primary
 * @param string $title Optional title attribute for the button
 */

// Helper function to safely escape values
if (!function_exists('e')) {
    function e($value)
    {
        if (is_null($value)) {
            return '';
        }
        if (is_array($value)) {
            return '';
        }
        if (is_object($value) && !method_exists($value, '__toString')) {
            return '';
        }
        return htmlspecialchars((string)$value, ENT_QUOTES, 'UTF-8');
    }
}

// Default parameters
$url = $url ?? '#';
$text = $text ?? 'Action';
$icon = $icon ?? '';
$size = $size ?? '';
$type = $type ?? '';
$title = $title ?? '';
$menuItems = $menuItems ?? [];

// Determine size class
$sizeClass = match ($size) {
    'small' => 'small',
    'large' => 'large',
    default => ''
};

// Determine type class
$typeClass = match ($type) {
    'secondary' => 'secondary',
    'danger' => 'danger',
    default => 'primary'
};

// Check if we're in the actions column of a table
$isTableAction = strpos(debug_backtrace()[0]['file'], '_table_row.php') !== false;

// Build classes
$classes = ['action-button'];
if ($sizeClass) {
    $classes[] = $sizeClass;
}
if ($typeClass) {
    $classes[] = $typeClass;
}
if (empty($text)) {
    $classes[] = 'icon-only';
}
if ($isTableAction) {
    $classes[] = 'table-action';
}
$classAttr = implode(' ', $classes);

// Title attribute
$titleAttr = $title ? ' title="' . e($title) . '"' : '';

// Aria-label for icon-only buttons
$ariaLabelAttr = (empty($text) && $title) ? ' aria-label="' . e($title) . '"' : '';

// Generate unique menu ID for dropdown targeting
$menuId = !empty($menuItems) ? 'menu-' . uniqid() : '';
?>

<?php if (!empty($menuItems)) : ?>
<div class="action-menu-wrapper">
<button class="<?= $classAttr ?>" data-menu-id="<?= $menuId ?>" aria-haspopup="true" aria-expanded="false"<?= $titleAttr ?><?= $ariaLabelAttr ?>>
    <?php if ($icon && $isTableAction && $icon === 'more-vert') : ?>
        <!-- Direct SVG for more-vert in table context -->
        <svg class="svg-icon <?= empty($text) ? 'icon-only-svg' : '' ?>" aria-hidden="true" viewBox="0 0 24 24">
            <path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z" fill="currentColor"/>
        </svg>
    <?php elseif ($icon) : ?>
        <svg class="svg-icon <?= empty($text) ? 'icon-only-svg' : '' ?>" aria-hidden="true" viewBox="0 0 24 24">
            <use xlink:href="/assets/icons.svg#icon-<?= e($icon) ?>"></use>
        </svg>
    <?php endif; ?>
    <?php if (!empty($text)) : ?>
        <span><?= e($text) ?></span>
    <?php endif; ?>
</button>
<ul class="action-menu" role="menu">
    <?php foreach ($menuItems as $item) : ?>
        <li role="none">
            <button role="menuitem" data-action="<?= e($item['key'] ?? '') ?>" data-url="<?= e($item['url'] ?? '') ?>">
                <?= e($item['label'] ?? '') ?>
            </button>
        </li>
    <?php endforeach; ?>
</ul>
</div>
<?php else : ?>
<a href="<?= e($url) ?>" class="<?= $classAttr ?>"<?= $titleAttr ?><?= $ariaLabelAttr ?>>
    <?php if ($icon && $isTableAction && $icon === 'more-vert') : ?>
        <!-- Direct SVG for more-vert in table context -->
        <svg class="svg-icon <?= empty($text) ? 'icon-only-svg' : '' ?>" aria-hidden="true" viewBox="0 0 24 24">
            <path d="M12 8c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm0 2c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0 6c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z" fill="currentColor"/>
        </svg>
    <?php elseif ($icon) : ?>
        <svg class="svg-icon <?= empty($text) ? 'icon-only-svg' : '' ?>" aria-hidden="true" viewBox="0 0 24 24">
            <use xlink:href="/assets/icons.svg#icon-<?= e($icon) ?>"></use>
        </svg>
    <?php endif; ?>
    <?php if (!empty($text)) : ?>
        <span><?= e($text) ?></span>
    <?php endif; ?>
</a>
<?php endif; ?>
