<?php

declare(strict_types=1);

namespace App\Core;

use App\Exceptions\AuthenticationException;
use App\Exceptions\AuthorizationException;
use App\Exceptions\BadRequestException;
use App\Exceptions\NotFoundException;
use App\Exceptions\ServiceUnavailableException;
use App\Core\LogManager;
use Psr\Log\LoggerInterface;

final class Router
{
    // HTTP and routing constants
    private const HTTP_METHODS = ['GET', 'POST', 'PUT', 'PATCH', 'DELETE', 'OPTIONS', 'HEAD'];
    private const PARAMETER_PATTERN = '/\{([^}]+)\}/';
    private const PARAMETER_REPLACEMENT = '(?P<$1>[^/]+)';
    private const PATH_DELIMITER = '/';
    private const DEFAULT_ERROR_MESSAGES = [
        400 => 'Bad Request - The server cannot process the request due to client error.',
        401 => 'Unauthorized - Authentication is required and has failed or not been provided.',
        403 => 'Forbidden - You do not have permission to access this resource.',
        404 => 'Not Found - The requested resource could not be found on this server or path.',
        405 => 'Method Not Allowed - The request method is not allowed for the requested resource.',
        500 => 'Internal Server Error - The server encountered an unexpected issue preventing response.',
        503 => 'Service Unavailable - The server is currently unavailable to handle this request.'
    ];

    // Core properties
    private array $routes = [];
    private array $errorHandlers = [];
    private ?LoggerInterface $logger;
    private ?Container $container;

    // Error handling properties
    private string $errorTemplatePath = '';
    private bool $showErrorDetails = false;
    private bool $isHandlingError = false;

    // Performance monitoring properties
    private array $performanceMetrics = [
        'route_matching' => [
            'time_ms' => 0.0,
            'routes_checked' => 0,
            'path' => '',
            'method' => '',
            'found' => false
        ],
        'route_processing' => [
            'time_ms' => 0,
            'handler_type' => '',
            'params_count' => 0,
            'response_type' => ''
        ],
        'response_preparation' => ['time_ms' => 0, 'final_prep_ms' => 0],
        'response_sending' => ['time_ms' => 0, 'content_size_bytes' => 0, 'estimated' => true],
        'overall' => ['total_time_ms' => 0, 'memory_usage_bytes' => 0, 'peak_memory_bytes' => 0],
        'response_prep_start_time' => 0
    ];
    private bool $collectPerformanceMetrics = false;
    private int $maxMetricsEntries = 100;

    public function __construct(?LoggerInterface $logger = null, ?Container $container = null)
    {
        $this->logger = $logger;
        $this->container = $container;
    }

    // Configuration methods
    public function setMaxMetricsEntries(int $maxEntries): self
    {
        $this->maxMetricsEntries = max(1, $maxEntries);
        return $this;
    }

    public function setCollectPerformanceMetrics(bool $collect): self
    {
        $this->collectPerformanceMetrics = $collect;
        return $this;
    }

    public function getPerformanceMetrics(): array
    {
        return $this->performanceMetrics;
    }

    public function setErrorTemplatePath(string $path): self
    {
        $this->errorTemplatePath = $path;
        return $this;
    }

    public function setShowErrorDetails(bool $show): self
    {
        $this->showErrorDetails = $show;
        return $this;
    }

    public function setContainer(Container $container): self
    {
        $this->container = $container;
        return $this;
    }

    // Route definition methods
    public function get(string $path, callable|array $handler): self
    {
        return $this->addRoute('GET', $path, $handler);
    }

    public function post(string $path, callable|array $handler): self
    {
        return $this->addRoute('POST', $path, $handler);
    }

    public function put(string $path, callable|array $handler): self
    {
        return $this->addRoute('PUT', $path, $handler);
    }

    public function patch(string $path, callable|array $handler): self
    {
        return $this->addRoute('PATCH', $path, $handler);
    }

    public function delete(string $path, callable|array $handler): self
    {
        return $this->addRoute('DELETE', $path, $handler);
    }

    public function options(string $path, callable|array $handler): self
    {
        return $this->addRoute('OPTIONS', $path, $handler);
    }

    public function any(string $path, callable|array $handler): self
    {
        foreach (self::HTTP_METHODS as $method) {
            $this->addRoute($method, $path, $handler);
        }
        return $this;
    }

    public function addRoute(string $method, string $path, callable|array $handler): self
    {
        $method = strtoupper($method);
        if (!in_array($method, self::HTTP_METHODS, true)) {
            throw new BadRequestException("Invalid HTTP method: {$method}");
        }

        $normalizedPath = $this->normalizePath($path);
        $pattern = $this->createPattern($normalizedPath);
        $hasParameters = (bool)preg_match(self::PARAMETER_PATTERN, $normalizedPath);

        $this->routes[$method][] = [
            'pattern' => $pattern,
            'handler' => $handler,
            'hasParameters' => $hasParameters,
        ];

        return $this;
    }

    public function defineRoutes(array $routes): void
    {
        foreach ($routes as $method => $paths) {
            $method = strtoupper($method);
            if (!in_array($method, self::HTTP_METHODS, true)) {
                throw new BadRequestException("Invalid HTTP method in route definition: {$method}");
            }

            foreach ($paths as $path => $handler) {
                $this->addRoute($method, $path, $handler);
            }
        }
    }

    // Core routing methods
    public function dispatch(string $method, string $uri): void
    {
        $dispatchStartTime = microtime(true);
        $memoryStart = memory_get_usage();
        $_SERVER['MEMORY_START'] = $memoryStart;

        $this->initializeDispatch();
        $requestContext = $this->prepareRequestContext($method, $uri);

        try {
            $this->processRequest($requestContext, $dispatchStartTime, $memoryStart);
        } catch (\Throwable $e) {
            $this->handleDispatchException($e, $requestContext, $dispatchStartTime, $memoryStart);
        }
    }

    private function initializeDispatch(): void
    {
        $this->ensureOutputBuffering();
        $this->resetPerformanceMetrics();
    }

    private function resetPerformanceMetrics(): void
    {
        if (!$this->collectPerformanceMetrics) {
            return;
        }

        $this->performanceMetrics = [
            'route_matching' => [
                'time_ms' => 0,
                'routes_checked' => 0,
                'path' => '',
                'method' => '',
                'found' => false
            ],
            'route_processing' => [
                'time_ms' => 0,
                'handler_type' => '',
                'params_count' => 0,
                'response_type' => ''
            ],
            'response_preparation' => ['time_ms' => 0, 'final_prep_ms' => 0],
            'response_sending' => ['time_ms' => 0, 'content_size_bytes' => 0, 'estimated' => true],
            'overall' => ['total_time_ms' => 0, 'memory_usage_bytes' => 0, 'peak_memory_bytes' => 0],
            'response_prep_start_time' => 0
        ];
    }

    private function prepareRequestContext(string $method, string $uri): array
    {
        $method = strtoupper($method);
        $normalizedPath = $this->normalizePath($uri);
        $errorId = bin2hex(random_bytes(4));

        error_log("Router: Dispatching {$method} request to {$normalizedPath}");

        return [
            'method' => $method,
            'path' => $normalizedPath,
            'log_context' => ['uri' => $normalizedPath, 'method' => $method, 'error_id' => $errorId]
        ];
    }

    private function processRequest(array $context, float $startTime, int $memoryStart): void
    {
        $matchedRoute = $this->findAndRecordMatchedRoute($context['method'], $context['path']);
        
        if (!$matchedRoute) {
            $this->handleNoRouteMatch($context, $startTime, $memoryStart);
            return;
        }

        $this->handleRouteMatch($matchedRoute, $context, $startTime, $memoryStart);
    }

    private function handleNoRouteMatch(array $context, float $startTime, int $memoryStart): void
    {
        error_log("Router: No route matched for {$context['method']} {$context['path']}");
        $this->handleUnmatchedRoute($context['method'], $context['path'], $context['log_context']);
        $this->recordErrorMetrics($startTime, $memoryStart, $context['path'], $context['method']);
    }

    private function handleRouteMatch(array $matchedRoute, array $context, float $startTime, int $memoryStart): void
    {
        error_log("Router: Route matched for {$context['method']} {$context['path']}");
        $response = $this->processAndRecordRoute($matchedRoute);
        
        $this->cleanOutputBuffer();
        
        if ($this->isRedirectResponse($response)) {
            $this->handleRedirectResponse($response);
            return;
        }

        $this->sendResponse($response);
        $this->recordSuccessMetrics($startTime, $memoryStart, $context['path'], $context['method']);
    }

    private function isRedirectResponse($response): bool
    {
        if (!$response instanceof Response) {
            return false;
        }

        $statusCode = $response->getStatusCode();
        $location = $response->getHeader('Location');

        return $statusCode >= 300 && $statusCode < 400 && $location;
    }

    private function handleRedirectResponse(Response $response): void
    {
        $statusCode = $response->getStatusCode();
        $location = $response->getHeader('Location');
        
        error_log("Router: Detected redirect response to {$location} with status {$statusCode}");
        
        $this->ensureSessionWritten();
        $response->send();
        exit;
    }

    private function ensureSessionWritten(): void
    {
        if (session_status() === PHP_SESSION_ACTIVE) {
            session_write_close();
        }
    }

    private function handleDispatchException(\Throwable $e, array $context, float $startTime, int $memoryStart): void
    {
        error_log("Router: Exception caught: " . $e->getMessage());
        $this->handleException($e, $context['log_context']);
        $this->recordErrorMetrics($startTime, $memoryStart, $context['path'], $context['method']);
    }

    private function ensureOutputBuffering(): void
    {
        if (ob_get_level() === 0) {
            ob_start();
        }
    }

    private function cleanOutputBuffer(): void
    {
        if (ob_get_level() > 0) {
            ob_clean();
        }
    }

    private function findAndRecordMatchedRoute(string $method, string $normalizedPath): ?array
    {
        $matchStartTime = microtime(true);
        $matchedRoute = $this->matchRoute($method, $normalizedPath);
        $matchEndTime = microtime(true);

        if (!$this->collectPerformanceMetrics) {
            return $matchedRoute;
        }

        $routesCount = isset($this->routes[$method]) ? count($this->routes[$method]) : 0;
        $this->performanceMetrics['route_matching'] = [
            'time_ms' => ($matchEndTime - $matchStartTime) * 1000,
            'routes_checked' => $routesCount,
            'path' => $normalizedPath,
            'method' => $method,
            'found' => $matchedRoute !== null
        ];

        return $matchedRoute;
    }

    private function processAndRecordRoute(array $matchedRoute): mixed
    {
        $processStartTime = microtime(true);
        $response = $this->processRoute($matchedRoute);
        $processEndTime = microtime(true);

        if ($this->collectPerformanceMetrics) {
            // Mark the start of response preparation (after route processing)
            $this->performanceMetrics['response_prep_start_time'] = $processEndTime;

            $handlerType = is_array($matchedRoute['handler']) ? 'controller' : 'closure';
            $responseType = $response instanceof Response ? 'Response' : gettype($response);

            $this->performanceMetrics['route_processing'] = [
                'time_ms' => ($processEndTime - $processStartTime) * 1000,
                'handler_type' => $handlerType,
                'params_count' => count($matchedRoute['params']),
                'response_type' => $responseType
            ];
        }

        return $response;
    }

    // Response handling methods
    private function sendResponse(mixed $response): void
    {
        $responseStartTime = microtime(true);
        $response = $this->prepareResponseObject($response);
        
        $this->recordResponsePreparationMetrics($responseStartTime);
        $this->estimateResponseSendingMetrics($response);
        
        $showMetrics = $this->shouldShowPerformanceMetrics();
        if ($this->collectPerformanceMetrics && $showMetrics) {
            $response = $this->addPerformanceMetricsToResponse($response);
        }

        $this->executeResponseSending($response);
    }

    private function prepareResponseObject(mixed $response): Response
    {
        if ($response instanceof Response) {
            return $response;
        }
        
        return $response !== null ? new Response((string)$response) : new Response('');
    }

    private function recordResponsePreparationMetrics(float $responseStartTime): void
    {
        if (!$this->collectPerformanceMetrics) {
            return;
        }

        $prepEndTime = microtime(true);
        $prepStartTime = $this->performanceMetrics['response_prep_start_time'] ?? $responseStartTime;
        $totalPrepTime = ($prepEndTime - $prepStartTime) * 1000;
        $finalPrepTime = ($prepEndTime - $responseStartTime) * 1000;

        $this->performanceMetrics['response_preparation'] = [
            'time_ms' => $totalPrepTime,
            'final_prep_ms' => $finalPrepTime
        ];

        if (isset($this->performanceMetrics['response_preparation']['final_prep_ms'])) {
            $this->performanceMetrics['response_preparation']['final_prep_ms'] = $finalPrepTime;
        }

        unset($this->performanceMetrics['response_prep_start_time']);
    }

    private function estimateResponseSendingMetrics(Response $response): void
    {
        if (!$this->collectPerformanceMetrics) {
            return;
        }

        $content = $response->getContent();
        $contentSize = is_string($content) ? strlen($content) : 0;
        $estimatedSendTime = max(1, $contentSize / 10000);

        $this->performanceMetrics['response_sending'] = [
            'time_ms' => $estimatedSendTime,
            'content_size_bytes' => $contentSize,
            'estimated' => true
        ];
    }

    private function shouldShowPerformanceMetrics(): bool
    {
        if (!isset($_SESSION['user_id'])) {
            error_log("Router: No user_id in session, performance metrics disabled for non-authenticated users");
            return false;
        }

        return $this->getUserMetricsPreference();
    }

    private function getUserMetricsPreference(): bool
    {
        if (!isset($_SESSION['show_performance_metrics'])) {
            error_log("Router: No user preference for performance metrics found in session, defaulting to disabled");
            return false;
        }

        $showMetrics = (bool)$_SESSION['show_performance_metrics'];
        $status = $showMetrics ? 'enabled' : 'disabled';
        error_log("Router: User preference for performance metrics: " . $status);
        
        return $showMetrics;
    }

    private function executeResponseSending(Response $response): void
    {
        $sendStartTime = microtime(true);
        
        if ($this->isRedirectResponse($response)) {
            $this->handleRedirectResponseSending($response);
            return;
        }

        $response->send();
        $this->logActualSendingMetrics($sendStartTime);
    }

    private function handleRedirectResponseSending(Response $response): void
    {
        $statusCode = $response->getStatusCode();
        $location = $response->getHeader('Location');
        
        error_log("Router: Sending redirect response to {$location} with status {$statusCode}");
        
        $response->send();
        $this->ensureSessionWritten();
        exit;
    }

    private function logActualSendingMetrics(float $sendStartTime): void
    {
        if (!$this->collectPerformanceMetrics) {
            return;
        }

        $sendEndTime = microtime(true);
        $actualSendTime = ($sendEndTime - $sendStartTime) * 1000;
        $contentSize = $this->performanceMetrics['response_sending']['content_size_bytes'] ?? 0;

        $this->logger?->debug('Response sending metrics', [
            'estimated_ms' => $this->performanceMetrics['response_sending']['time_ms'],
            'actual_ms' => $actualSendTime,
            'content_size_bytes' => $contentSize
        ]);
    }

    private function addPerformanceMetricsToResponse(Response $response): Response
    {
        $contentType = $response->getHeader('Content-Type');
        if ($contentType && strpos($contentType, 'text/html') === false) {
            return $response;
        }

        $content = $response->getContent();
        if (!is_string($content)) {
            return $response;
        }

        // Ensure we have valid metrics data
        if (empty($this->performanceMetrics['overall']['total_time_ms'])) {
            $endTime = microtime(true);
            $memoryEnd = memory_get_usage();

            // Use the actual memory at the start of the request or a reasonable estimate
            $memoryStart = $_SERVER['MEMORY_START'] ?? ($memoryEnd * 0.7); // Estimate 30% memory growth if not set

            $totalTime = ($endTime - $_SERVER['REQUEST_TIME_FLOAT']) * 1000;

            $this->performanceMetrics['overall'] = [
                'total_time_ms' => $totalTime,
                'memory_usage_bytes' => $memoryEnd - $memoryStart,
                'peak_memory_bytes' => memory_get_peak_usage()
            ];
        }

        $totalTime = $this->performanceMetrics['overall']['total_time_ms'];
        $this->estimateResponsePreparationTime($totalTime);

        $metricsHtml = $this->formatPerformanceMetricsAsHtml();
        $bodyPos = strripos($content, '</body>');

        if ($bodyPos !== false) {
            $content = substr_replace($content, "{$metricsHtml}</body>", $bodyPos, 7);
            return $response->withContent($content);
        }

        return $response;
    }

    private function formatPerformanceMetricsAsHtml(): string
    {
        $totalTime = $this->performanceMetrics['overall']['total_time_ms'] ?? 0;
        $memoryUsage = $this->performanceMetrics['overall']['memory_usage_bytes'] ?? 0;
        $peakMemory = $this->performanceMetrics['overall']['peak_memory_bytes'] ?? 0;
        $routeMatchTime = $this->performanceMetrics['route_matching']['time_ms'] ?? 0;
        $routeProcessTime = $this->performanceMetrics['route_processing']['time_ms'] ?? 0;
        $responsePrepTime = $this->performanceMetrics['response_preparation']['time_ms'] ?? 0;
        $responseSendTime = $this->performanceMetrics['response_sending']['time_ms'] ?? 0;
        $contentSize = $this->performanceMetrics['response_sending']['content_size_bytes'] ?? 0;

        $containerStyle = 'position:fixed; bottom:0; right:0; background:#f8f9fa; border:1px solid #ddd;';
        $containerStyle .= ' padding:10px; font-size:12px; font-family:monospace;';
        $containerStyle .= ' min-width:450px; max-width:550px; z-index:9999;';
        $containerStyle .= ' box-shadow: 0 0 10px rgba(0,0,0,0.1);';

        $styles = [
            'container' => $containerStyle,
            'heading' => 'margin:0 0 8px 0; font-size:14px; color:#333; border-bottom:1px solid #ddd; ' .
                         'padding-bottom:5px;',
            'paragraph' => 'margin:0 0 5px 0; display:flex; justify-content:space-between; align-items:center;',
            'label' => 'color:#555; min-width:200px;',
            'value' => 'font-weight:bold; color:#333; text-align:right; word-break:break-word;',
            'details' => 'margin-top:8px; border-top:1px solid #eee; padding-top:8px;',
            'summary' => 'cursor:pointer; color:#0066cc; font-weight:bold;',
            'pre' => 'margin:8px 0 0 0; max-height:200px; overflow:auto; background:#f1f1f1; ' .
                     'padding:8px; border-radius:4px; font-size:11px;'
        ];

        $metricsJson = htmlspecialchars(json_encode($this->performanceMetrics, JSON_PRETTY_PRINT));

        $html = "<div id=\"performance-metrics\" style=\"{$styles['container']}\">";
        $html .= "<div style=\"display:flex; justify-content:space-between; align-items:center;\">";
        $html .= "<h4 style=\"{$styles['heading']}; margin:0;\">
                    Performance Metrics
                    <a href=\"/docs/performance\" 
                       style=\"font-size:12px; margin-left:8px; color:#0066cc; text-decoration:none;\"
                       title=\"View Performance Guide\">
                       <span style=\"border:1px solid #0066cc; border-radius:50%; display:inline-block; 
                              width:16px; height:16px; text-align:center; line-height:14px; 
                              font-weight:bold;\">?</span>
                    </a>
                  </h4>";
        $html .= "<button onclick=\"document.getElementById('performance-metrics').style.display='none';\"
                  style=\"background:none; border:none; cursor:pointer; font-size:16px; color:#999;\">×</button>";
        $html .= "</div>";

        // Overall metrics
        $metricRow = "<p style=\"{$styles['paragraph']}\"><span style=\"{$styles['label']}\">%s</span> " .
                     "<span style=\"{$styles['value']}\">%s</span></p>";

        $html .= sprintf($metricRow, "Total Time:", number_format($totalTime, 2) . " ms");
        $html .= sprintf($metricRow, "Memory Usage:", $this->formatBytes($memoryUsage));
        $html .= sprintf($metricRow, "Peak Memory:", $this->formatBytes($peakMemory));

        // Detailed timing breakdown
        $html .= sprintf($metricRow, "Route Matching:", number_format($routeMatchTime, 2) . " ms");
        $html .= sprintf($metricRow, "Route Processing:", number_format($routeProcessTime, 2) . " ms");

        // Style for estimated values
        $estimatedStyle = 'color:#0066cc;';

        // Format response preparation time
        $finalPrepTime = $this->performanceMetrics['response_preparation']['final_prep_ms'] ?? 0;
        $isPrepEstimated = isset($this->performanceMetrics['response_preparation']['estimated']) &&
                          $this->performanceMetrics['response_preparation']['estimated'];

        $prepLabel = $finalPrepTime > 0 ? "Response Preparation: (total/final)" : "Response Preparation:";
        $prepValue = $this->formatMetricValue($responsePrepTime, $finalPrepTime, $isPrepEstimated, $estimatedStyle);
        $html .= sprintf($metricRow, $prepLabel, $prepValue);

        // Format response sending time
        $isSendEstimated = isset($this->performanceMetrics['response_sending']['estimated']) &&
                          $this->performanceMetrics['response_sending']['estimated'];

        $sendTimeLabel = $this->formatMetricValue($responseSendTime, 0, $isSendEstimated, $estimatedStyle);

        $html .= sprintf($metricRow, "Response Sending:", $sendTimeLabel);

        // Add content size information
        if ($contentSize > 0) {
            $html .= sprintf($metricRow, "Response Size:", $this->formatBytes($contentSize));
        }

        // Raw data in collapsible section
        $html .= "<details style=\"{$styles['details']}\">";
        $html .= "<summary style=\"{$styles['summary']}\">Raw Metrics Data</summary>";
        $html .= "<pre style=\"{$styles['pre']}\">{$metricsJson}</pre>";
        $html .= "</details>";
        $html .= "</div>";

        return $html;
    }

    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $i = 0;
        while ($bytes > 1024 && $i < count($units) - 1) {
            $bytes /= 1024;
            $i++;
        }
        return round($bytes, 2) . " {$units[$i]}";
    }

    private function estimateResponsePreparationTime(float $totalTime): void
    {
        $routeMatchTime = $this->performanceMetrics['route_matching']['time_ms'] ?? 0;
        $routeProcessTime = $this->performanceMetrics['route_processing']['time_ms'] ?? 0;
        $responseSendTime = $this->performanceMetrics['response_sending']['time_ms'] ?? 0;

        $accountedTime = $routeMatchTime + $routeProcessTime + $responseSendTime;
        $remainingTime = $totalTime - $accountedTime;
        $minPrepTime = $totalTime * 0.3;

        $estimatedPrepTime = max(
            $remainingTime * 0.9,
            $minPrepTime,
            10
        );

        $finalPrepTime = $this->performanceMetrics['response_preparation']['final_prep_ms'] ?? 0;
        $this->performanceMetrics['response_preparation'] = [
            'time_ms' => $estimatedPrepTime,
            'final_prep_ms' => $finalPrepTime,
            'estimated' => true
        ];
    }

    private function formatMetricValue(
        float $value,
        float $secondValue = 0,
        bool $isEstimated = false,
        string $estimatedStyle = ''
    ): string {
        $formattedValue = number_format($value, 2);

        if ($secondValue > 0) {
            $formattedSecondValue = number_format($secondValue, 2);
            return $isEstimated
                ? sprintf(
                    '<span style="%s">%s ms (est.)</span> / %s ms',
                    $estimatedStyle,
                    $formattedValue,
                    $formattedSecondValue
                )
                : sprintf('%s / %s ms', $formattedValue, $formattedSecondValue);
        }

        return $isEstimated
            ? sprintf('<span style="%s">%s ms (est.)</span>', $estimatedStyle, $formattedValue)
            : sprintf('%s ms', $formattedValue);
    }

    // Performance metrics methods
    private function recordSuccessMetrics(float $startTime, int $memoryStart, string $path, string $method): void
    {
        if (!$this->collectPerformanceMetrics) {
            return;
        }

        $endTime = microtime(true);
        $memoryEnd = memory_get_usage();

        // Calculate total time and memory usage
        $totalTime = ($endTime - $startTime) * 1000;
        $memoryUsage = $memoryEnd - $memoryStart;
        $peakMemory = memory_get_peak_usage() - $memoryStart;

        // Update overall metrics
        $this->performanceMetrics['overall'] = [
            'total_time_ms' => $totalTime,
            'memory_usage_bytes' => $memoryUsage,
            'peak_memory_bytes' => $peakMemory
        ];

        $this->estimateResponsePreparationTime($totalTime);

        $this->logger?->debug('Router performance metrics', [
            'metrics' => $this->performanceMetrics,
            'uri' => $path,
            'method' => $method
        ]);
    }

    private function recordErrorMetrics(float $startTime, int $memoryStart, string $path, string $method): void
    {
        if (!$this->collectPerformanceMetrics) {
            return;
        }

        $endTime = microtime(true);
        $memoryEnd = memory_get_usage();

        // Calculate total time and memory usage
        $totalTime = ($endTime - $startTime) * 1000;
        $memoryUsage = $memoryEnd - $memoryStart;
        $peakMemory = memory_get_peak_usage() - $memoryStart;

        // Update overall metrics
        $this->performanceMetrics['overall'] = [
            'total_time_ms' => $totalTime,
            'memory_usage_bytes' => $memoryUsage,
            'peak_memory_bytes' => $peakMemory,
            'error' => true
        ];

        $this->estimateResponsePreparationTime($totalTime);

        $this->logger?->debug('Router performance metrics (error case)', [
            'metrics' => $this->performanceMetrics,
            'uri' => $path,
            'method' => $method
        ]);
    }

    // Error handling configuration
    public function setErrorHandlers(array $handlers): void
    {
        foreach ($handlers as $code => $handler) {
            if (!is_callable($handler)) {
                throw new \InvalidArgumentException("Error handler for code {$code} must be callable.");
            }
        }

        $this->errorHandlers = $handlers;
    }

    // Path handling methods
    private function normalizePath(string $path): string
    {
        $parsedPath = parse_url($path, PHP_URL_PATH) ?: '';
        return self::PATH_DELIMITER . trim($parsedPath, self::PATH_DELIMITER);
    }

    private function createPattern(string $path): string
    {
        return '#^' . preg_replace(self::PARAMETER_PATTERN, self::PARAMETER_REPLACEMENT, $path) . '$#';
    }

    // Route matching methods
    private function matchRoute(string $method, string $path): ?array
    {
        if (!isset($this->routes[$method])) {
            return null;
        }

        $routeMatchMetrics = [];
        $matchResult = $this->findMatchingRoute($this->routes[$method], $path, $routeMatchMetrics);
        
        $this->storeRouteMatchMetrics($routeMatchMetrics);
        return $matchResult;
    }
    
    private function findMatchingRoute(array $routes, string $path, array &$metrics): ?array
    {
        $routeIndex = 0;
        
        foreach ($routes as $route) {
            $routeStartTime = $this->collectPerformanceMetrics ? microtime(true) : 0;
            $matchResult = $this->checkRouteMatch($route, $path, $routeIndex, $routeStartTime, $metrics);

            if ($matchResult) {
                return $matchResult;
            }

            $routeIndex++;
        }
        
        return null;
    }

    private function checkRouteMatch(
        array $route,
        string $path,
        int $routeIndex,
        float $startTime,
        array &$metrics
    ): ?array {
        if (!preg_match($route['pattern'], $path, $matches)) {
            $this->recordNonMatchedRouteMetrics($route, $routeIndex, $startTime, $metrics);
            return null;
        }

        $params = $route['hasParameters']
            ? array_filter($matches, fn($key) => !is_numeric($key), ARRAY_FILTER_USE_KEY)
            : [];

        $this->recordMatchedRouteMetrics($route, $routeIndex, $startTime, $params, $metrics);

        return [
            'handler' => $route['handler'],
            'params' => $params
        ];
    }

    private function recordMatchedRouteMetrics(
        array $route,
        int $routeIndex,
        float $startTime,
        array $params,
        array &$metrics
    ): void {
        if (!$this->collectPerformanceMetrics) {
            return;
        }

        $endTime = microtime(true);
        $metrics[] = [
            'route_index' => $routeIndex,
            'pattern' => $route['pattern'],
            'matched' => true,
            'time_ms' => ($endTime - $startTime) * 1000,
            'params_count' => count($params)
        ];
    }

    private function recordNonMatchedRouteMetrics(
        array $route,
        int $routeIndex,
        float $startTime,
        array &$metrics
    ): void {
        if (!$this->collectPerformanceMetrics) {
            return;
        }

        $endTime = microtime(true);
        $metrics[] = [
            'route_index' => $routeIndex,
            'pattern' => $route['pattern'],
            'matched' => false,
            'time_ms' => ($endTime - $startTime) * 1000
        ];
    }

    private function storeRouteMatchMetrics(array $metrics): void
    {
        if (!$this->collectPerformanceMetrics || empty($metrics)) {
            return;
        }

        if (count($metrics) > $this->maxMetricsEntries) {
            $metrics = array_slice($metrics, 0, $this->maxMetricsEntries);
        }

        $this->performanceMetrics['route_match_details'] = $metrics;
    }

    // Route handling methods
    private function handleUnmatchedRoute(string $method, string $path, array $logContext): void
    {
        $allowedMethods = $this->getAllowedMethods($path);

        if (!empty($allowedMethods)) {
            $this->handleMethodNotAllowed($method, $path, $allowedMethods, $logContext);
            return;
        }

        $this->handleNotFound($path, $logContext);
    }

    private function getAllowedMethods(string $path): array
    {
        $allowed = [];
        foreach (self::HTTP_METHODS as $method) {
            if (isset($this->routes[$method]) && $this->matchRoute($method, $path) !== null) {
                $allowed[] = $method;
            }
        }
        return $allowed;
    }

    private function processRoute(array $matchedRoute): mixed
    {
        $handler = $matchedRoute['handler'];
        $params = $matchedRoute['params'];
        return $this->executeHandler($handler, $params);
    }

    // Handler execution methods
    private function executeHandler(callable|array $handler, array $params): mixed
    {
        $handlerStartTime = $this->collectPerformanceMetrics ? microtime(true) : 0;
        $handlerMetrics = [];

        try {
            if (!is_array($handler)) {
                return $this->executeCallableHandler($handler, $params, $handlerStartTime, $handlerMetrics);
            }

            return $this->executeClassMethodHandler($handler, $params, $handlerStartTime, $handlerMetrics);
        } catch (\Throwable $e) {
            $this->recordHandlerErrorMetrics($handlerStartTime, $e);
            throw $e;
        }
    }

    private function executeCallableHandler(
        callable $handler,
        array $params,
        float $startTime,
        array $metrics
    ): mixed {
        $callableStartTime = $this->collectPerformanceMetrics ? microtime(true) : 0;
        $result = call_user_func_array($handler, $params);

        if (!$this->collectPerformanceMetrics) {
            return $result;
        }

        $callableEndTime = microtime(true);
        $metrics['callable_execution'] = [
            'time_ms' => ($callableEndTime - $callableStartTime) * 1000,
            'type' => 'closure'
        ];

        $handlerEndTime = microtime(true);
        $metrics['total_time_ms'] = ($handlerEndTime - $startTime) * 1000;
        $this->performanceMetrics['handler_execution'] = $metrics;

        return $result;
    }

    private function executeClassMethodHandler(
        array $handler,
        array $params,
        float $startTime,
        array $metrics
    ): mixed {
        [$className, $methodName] = $handler;

        $this->validateClassExists($className, $metrics);
        $this->validateMethodExists($className, $methodName, $metrics);

        $instance = $this->createClassInstance($className, $metrics);
        $result = $this->executeMethod($instance, $methodName, $params, $metrics);

        if ($this->collectPerformanceMetrics) {
            $handlerEndTime = microtime(true);
            $metrics['total_time_ms'] = ($handlerEndTime - $startTime) * 1000;
            $this->performanceMetrics['handler_execution'] = $metrics;
        }

        return $result;
    }

    private function validateClassExists(string $className, array &$metrics): void
    {
        $classCheckStartTime = $this->collectPerformanceMetrics ? microtime(true) : 0;
        $classExists = class_exists($className);

        if ($this->collectPerformanceMetrics) {
            $endTime = microtime(true);
            $metrics['class_check'] = [
                'time_ms' => ($endTime - $classCheckStartTime) * 1000,
                'class_exists' => $classExists
            ];
        }

        if (!$classExists) {
            throw new NotFoundException("Handler class [{$className}] does not exist.");
        }
    }

    private function validateMethodExists(string $className, string $methodName, array &$metrics): void
    {
        $methodCheckStartTime = $this->collectPerformanceMetrics ? microtime(true) : 0;
        $methodExists = method_exists($className, $methodName);

        if ($this->collectPerformanceMetrics) {
            $endTime = microtime(true);
            $metrics['method_check'] = [
                'time_ms' => ($endTime - $methodCheckStartTime) * 1000,
                'method_exists' => $methodExists
            ];
        }

        if (!$methodExists) {
            throw new NotFoundException("Handler method [{$className}::{$methodName}] does not exist.");
        }
    }

    private function createClassInstance(string $className, array &$metrics): object
    {
        $instanceStartTime = $this->collectPerformanceMetrics ? microtime(true) : 0;
        $instance = $this->container ? $this->container->get($className) : new $className();

        if ($this->collectPerformanceMetrics) {
            $endTime = microtime(true);
            $metrics['instance_creation'] = [
                'time_ms' => ($endTime - $instanceStartTime) * 1000,
                'used_container' => $this->container !== null
            ];
        }

        return $instance;
    }

    private function executeMethod(object $instance, string $methodName, array $params, array &$metrics): mixed
    {
        $methodStartTime = $this->collectPerformanceMetrics ? microtime(true) : 0;
        $result = call_user_func_array([$instance, $methodName], $params);

        if ($this->collectPerformanceMetrics) {
            $endTime = microtime(true);
            $metrics['method_execution'] = [
                'time_ms' => ($endTime - $methodStartTime) * 1000,
                'class' => get_class($instance),
                'method' => $methodName
            ];
        }

        return $result;
    }

    private function recordHandlerErrorMetrics(float $startTime, \Throwable $e): void
    {
        if (!$this->collectPerformanceMetrics) {
            return;
        }

        $handlerEndTime = microtime(true);
        $this->performanceMetrics['handler_execution'] = [
            'total_time_ms' => ($handlerEndTime - $startTime) * 1000,
            'error' => true,
            'error_type' => get_class($e)
        ];
    }

    // Error handling methods
    private function handleMethodNotAllowed(
        string $method,
        string $path,
        array $allowedMethods,
        array $logContext
    ): void {
        $message = "Method {$method} not allowed for {$path}";
        $statusCode = 405;

        $logContext['status'] = $statusCode;
        $logContext['message'] = $message;
        $logContext['allowed_methods'] = $allowedMethods;

        $this->logger?->warning("Error ID: {$logContext['error_id']}", $logContext);

        $response = Response::error($statusCode, $message)
            ->withHeader('Allow', implode(', ', $allowedMethods));

        $this->tryProcessErrorResponse($statusCode, $logContext, $response);
    }

    private function handleNotFound(string $path, array $logContext): void
    {
        $message = "Route {$path} not found";
        $statusCode = 404;

        $logContext['status'] = $statusCode;
        $logContext['message'] = $message;

        $this->logger?->warning("Error ID: {$logContext['error_id']}", $logContext);

        $response = Response::notFound($message);
        $this->tryProcessErrorResponse($statusCode, $logContext, $response);
    }

    private function outputSimpleErrorPage(int $statusCode, string $message, array $context): Response
    {
        $errorMessage = self::DEFAULT_ERROR_MESSAGES[$statusCode] ?? 'An error occurred';
        $errorId = $context['error_id'] ?? 'unknown';
        $htmlContent = $this->generateSimpleErrorHtml($statusCode, $errorMessage, $message, $errorId);

        return Response::html($htmlContent, $statusCode);
    }

    private function generateSimpleErrorHtml(
        int $statusCode,
        string $errorMessage,
        string $message,
        string $errorId
    ): string {
        $detailedInfo = '';

        if ($this->showErrorDetails) {
            $detailedInfo = $this->generateDebugInfoHtml($statusCode, $errorId);
        }

        return $this->generateErrorPageHtml($statusCode, $errorMessage, $message, $errorId, $detailedInfo);
    }

    private function generateDebugInfoHtml(int $statusCode, string $errorId): string
    {
        return '
        <div class="debug-info">
            <h2>Debug Information</h2>
            <div class="debug-section">
                <h3>Error Details</h3>
                <table>
                    <tr>
                        <th>Error ID:</th>
                        <td>' . htmlspecialchars($errorId, ENT_QUOTES) . '</td>
                    </tr>
                    <tr>
                        <th>Status Code:</th>
                        <td>' . htmlspecialchars((string)$statusCode, ENT_QUOTES) . '</td>
                    </tr>
                    <tr>
                        <th>Error Type:</th>
                        <td>' . htmlspecialchars($_SERVER['REDIRECT_STATUS'] ?? 'N/A', ENT_QUOTES) . '</td>
                    </tr>
                </table>
            </div>

            <div class="debug-section">
                <h3>Request Information</h3>
                <table>
                    <tr>
                        <th>Request URI:</th>
                        <td>' . htmlspecialchars($_SERVER['REQUEST_URI'] ?? 'N/A', ENT_QUOTES) . '</td>
                    </tr>
                    <tr>
                        <th>Request Method:</th>
                        <td>' . htmlspecialchars($_SERVER['REQUEST_METHOD'] ?? 'N/A', ENT_QUOTES) . '</td>
                    </tr>
                    <tr>
                        <th>Server Software:</th>
                        <td>' . htmlspecialchars($_SERVER['SERVER_SOFTWARE'] ?? 'N/A', ENT_QUOTES) . '</td>
                    </tr>
                    <tr>
                        <th>PHP Version:</th>
                        <td>' . htmlspecialchars(PHP_VERSION, ENT_QUOTES) . '</td>
                    </tr>
                    <tr>
                        <th>Time:</th>
                        <td>' . htmlspecialchars(date('Y-m-d H:i:s'), ENT_QUOTES) . '</td>
                    </tr>
                </table>
            </div>
        </div>';
    }

    private function generateErrorPageHtml(
        int $statusCode,
        string $errorMessage,
        string $message,
        string $errorId,
        string $detailedInfo
    ): string {
        return '<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <title>Error ' . $statusCode . '</title>
            <style>
                body { font-family: sans-serif; margin: 0; padding: 20px; text-align: center; }
                .container {
                    max-width: 800px;
                    margin: 40px auto;
                    padding: 20px;
                    border: 1px solid #eee;
                    border-radius: 5px;
                    text-align: left;
                }
                h1 { color: #e74c3c; text-align: center; }
                h2 { color: #3498db; margin-top: 30px; font-size: 18px; }
                h3 { color: #555; font-size: 16px; margin-top: 20px; }
                .error-code {
                    display: inline-block;
                    background-color: #f8d7da;
                    color: #721c24;
                    padding: 5px 10px;
                    border-radius: 4px;
                    margin-bottom: 10px;
                }
                .error-message {
                    text-align: center;
                    margin-bottom: 30px;
                }
                .home-link {
                    display: block;
                    text-align: center;
                    margin-top: 30px;
                }
                .debug-info {
                    margin-top: 40px;
                    padding: 20px;
                    background-color: #f8f9fa;
                    border-radius: 5px;
                    border-left: 4px solid #3498db;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 10px 0;
                }
                th, td {
                    padding: 8px;
                    text-align: left;
                    border-bottom: 1px solid #ddd;
                }
                th {
                    width: 30%;
                    color: #555;
                }
                .debug-section {
                    margin-bottom: 20px;
                }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="error-message">
                    <span class="error-code">Error ' . $statusCode . '</span>
                    <h1>' . htmlspecialchars($errorMessage, ENT_QUOTES) . '</h1>
                    <p>' . htmlspecialchars($message, ENT_QUOTES) . '</p>
                    <p>Error ID: ' . htmlspecialchars($errorId, ENT_QUOTES) . '</p>
                </div>

                ' . $detailedInfo . '

                <div class="home-link">
                    <a href="/"
                       style="display:inline-block;
                              padding:10px 15px;
                              background-color:#e74c3c;
                              color:white;
                              text-decoration:none;
                              border-radius:4px;">Back to Home</a>
                </div>
            </div>
        </body>
        </html>';
    }

    // Exception handling methods
    private function handleException(\Throwable $e, array $logContext): void
    {
        if ($this->isHandlingError) {
            $this->handleCriticalError($e, $logContext);
            return;
        }

        $this->processNormalException($e, $logContext);
    }

    private function processNormalException(\Throwable $e, array $logContext): void
    {
        $this->isHandlingError = true;

        $errorInfo = $this->getExceptionInfo($e);
        $this->logExceptionDetails($e, $errorInfo, $logContext);
        $this->sendExceptionResponse($errorInfo, $logContext);

        $this->isHandlingError = false;
    }

    private function logExceptionDetails(\Throwable $e, array $errorInfo, array $logContext): void
    {
        $statusCode = $errorInfo['statusCode'];
        $message = $errorInfo['message'];
        
        $diagnosticInfo = $this->collectDiagnosticInfo($e, $statusCode, $logContext);
        LogManager::logException($e, "Router error: {$message}", $diagnosticInfo);
    }

    private function sendExceptionResponse(array $errorInfo, array $logContext): void
    {
        $statusCode = $errorInfo['statusCode'];
        $message = $errorInfo['message'];
        
        $logContext['detailed_message'] = $this->showErrorDetails ? $message : $message;
        $logContext['exception_type'] = $errorInfo['exception_type'] ?? 'Exception';

        $response = $this->outputSimpleErrorPage($statusCode, $message, $logContext);
        $this->tryProcessErrorResponse($statusCode, $logContext, $response);
    }

    private function collectDiagnosticInfo(\Throwable $e, int $statusCode, array $logContext): array
    {
        return [
            'status_code' => $statusCode,
            'context' => $logContext,
            'exception_type' => get_class($e),
            'exception_code' => $e->getCode(),
            'exception_file' => $e->getFile(),
            'exception_line' => $e->getLine(),
            'router_state' => [
                'routes_count' => array_sum(array_map('count', $this->routes)),
                'routes_by_method' => array_map('count', $this->routes),
                'error_handlers' => array_keys($this->errorHandlers),
                'error_template_path' => $this->errorTemplatePath,
                'show_error_details' => $this->showErrorDetails
            ],
            'server_info' => [
                'php_version' => PHP_VERSION,
                'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'unknown',
                'request_time' => $_SERVER['REQUEST_TIME'] ?? time()
            ]
        ];
    }

    private function handleCriticalError(\Throwable $e, array $logContext): void
    {
        http_response_code(500);
        $criticalErrorId = bin2hex(random_bytes(6));

        echo "A critical error occurred during error handling. Error ID: {$criticalErrorId}";

        $criticalDiagnosticInfo = [
            'critical_error_id' => $criticalErrorId,
            'original_context' => $logContext,
            'severity' => 'CRITICAL',
            'exception_type' => get_class($e),
            'exception_code' => $e->getCode(),
            'exception_file' => $e->getFile(),
            'exception_line' => $e->getLine(),
            'memory_usage' => [
                'current' => memory_get_usage(true),
                'peak' => memory_get_peak_usage(true)
            ],
            'server_info' => [
                'php_version' => PHP_VERSION,
                'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'unknown',
                'request_time' => $_SERVER['REQUEST_TIME'] ?? time(),
                'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown',
                'request_method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown'
            ]
        ];

        LogManager::logException($e, "Critical router error during error handling", $criticalDiagnosticInfo);

        $this->logger?->critical("Critical error occurred during error handling", [
            'critical_error_id' => $criticalErrorId,
            'original_context' => $logContext,
            'exception' => $e->getMessage(),
            'exception_type' => get_class($e),
            'trace' => $e->getTraceAsString()
        ]);
    }

    private function getExceptionInfo(\Throwable $e): array
    {
        $exceptionType = get_class($e);
        
        return match (true) {
            $e instanceof BadRequestException => $this->handleBadRequestException($e, $exceptionType),
            $e instanceof AuthenticationException => $this->handleAuthenticationException($e, $exceptionType),
            $e instanceof AuthorizationException => $this->handleAuthorizationException($e, $exceptionType),
            $e instanceof NotFoundException => $this->handleNotFoundException($e, $exceptionType),
            $e instanceof ServiceUnavailableException => $this->handleServiceUnavailableException($e, $exceptionType),
            default => $this->createDefaultExceptionInfo($e, $exceptionType)
        };
    }

    private function handleBadRequestException(BadRequestException $e, string $exceptionType): array
    {
        return $this->createExceptionInfo(
            400,
            $e->getMessage(),
            $exceptionType,
            Response::error(400, $e->getMessage())
        );
    }

    private function handleAuthenticationException(AuthenticationException $e, string $exceptionType): array
    {
        return $this->createExceptionInfo(
            401,
            $e->getMessage(),
            $exceptionType,
            Response::error(401, $e->getMessage())->withHeader('WWW-Authenticate', 'Bearer')
        );
    }

    private function handleAuthorizationException(AuthorizationException $e, string $exceptionType): array
    {
        return $this->createExceptionInfo(
            403,
            $e->getMessage(),
            $exceptionType,
            Response::error(403, $e->getMessage())
        );
    }

    private function handleNotFoundException(NotFoundException $e, string $exceptionType): array
    {
        return $this->createExceptionInfo(
            404,
            $e->getMessage(),
            $exceptionType,
            Response::notFound($e->getMessage())
        );
    }

    private function handleServiceUnavailableException(ServiceUnavailableException $e, string $exceptionType): array
    {
        return $this->createExceptionInfo(
            503,
            $e->getMessage(),
            $exceptionType,
            Response::error(503, $e->getMessage())->withHeader('Retry-After', '3600')
        );
    }

    private function createExceptionInfo(int $statusCode, string $message, string $exceptionType, Response $response): array
    {
        return [
            'statusCode' => $statusCode,
            'message' => $message,
            'exception_type' => $exceptionType,
            'response' => $response
        ];
    }

    private function createDefaultExceptionInfo(\Throwable $e, string $exceptionType): array
    {
        $message = 'Internal server error occurred.';
        $displayMessage = $this->showErrorDetails ? $e->getMessage() : $message;
        
        return [
            'statusCode' => 500,
            'message' => $message,
            'exception_type' => $exceptionType,
            'response' => Response::error(500, $displayMessage)
        ];
    }

    private function tryProcessErrorResponse(
        int $statusCode,
        array $context,
        ?Response $response
    ): void {
        try {
            $this->processErrorResponse($statusCode, $context, $response);
        } catch (\Throwable $e) {
            $this->logger?->error("Failed to process error response", [
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    private function processErrorResponse(int $statusCode, array $context, ?Response $response): void
    {
        if (headers_sent()) {
            return;
        }

        if ($this->tryCustomErrorHandler($statusCode, $context)) {
            return;
        }

        if ($response instanceof Response) {
            $response->send();
            return;
        }

        $this->tryErrorTemplate($statusCode, $context);
    }

    private function tryCustomErrorHandler(int $statusCode, array $context): bool
    {
        if (!isset($this->errorHandlers[$statusCode])) {
            return false;
        }

        $result = call_user_func($this->errorHandlers[$statusCode], $context);

        if ($result instanceof Response) {
            $result->send();
            return true;
        }

        return false;
    }

    private function tryErrorTemplate(int $statusCode, array $context): bool
    {
        $templateFile = "{$this->errorTemplatePath}{$statusCode}.php";

        if (!$this->errorTemplatePath || !file_exists($templateFile)) {
            return false;
        }

        try {
            http_response_code($statusCode);
            extract($context);
            include $templateFile;
            return true;
        } catch (\Throwable $e) {
            LogManager::logException($e, "Error rendering error template", [
                'status_code' => $statusCode,
                'template_file' => $templateFile,
                'context_keys' => array_keys($context)
            ]);
            return false;
        }
    }
}
