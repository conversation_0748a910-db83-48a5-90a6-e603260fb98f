function initializeHeader()
{
    setupScrollEffect();
    setupSearchFunctionality();
}

function setupScrollEffect()
{
    const header = document.querySelector('.main-header');
    if (!header) {
        return;
    }

    window.addEventListener('scroll', () => {
        updateHeaderShadow(header, window.scrollY > 0);
    });
}

function updateHeaderShadow(header, isScrolled)
{
    header.style.boxShadow = isScrolled
        ? '0 4px 20px rgba(0, 0, 0, 0.2)'
        : 'var(--box-shadow-lg)';
}

function setupSearchFunctionality()
{
    const searchForm = document.querySelector('.search-container form');
    const searchInput = document.querySelector('#search-input');

    if (!searchForm || !searchInput) {
        return;
    }

    setupSearchFormClickHandler(searchForm, searchInput);
    setupSearchHistoryTracking(searchForm, searchInput);
}

function setupSearchFormClickHandler(form, input)
{
    form.addEventListener('click', e => {
        if (e.target !== input) {
            input.focus();
        }
    });
}

function setupSearchHistoryTracking(form, input)
{
    form.addEventListener('submit', e => {
        const query = input.value.trim();

        if (!query) {
            e.preventDefault();
            return;
        }

        saveSearchToHistory(query);
    });
}

function saveSearchToHistory(query)
{
    const searchHistory = getSearchHistory();

    if (searchHistory.includes(query)) {
        return;
    }

    updateSearchHistory(searchHistory, query);
}

function getSearchHistory()
{
    return JSON.parse(localStorage.getItem('searchHistory') || '[]');
}

function updateSearchHistory(history, query)
{
    history.unshift(query);

    if (history.length > 5) {
        history.pop();
    }

    localStorage.setItem('searchHistory', JSON.stringify(history));
}

export { initializeHeader };
