function initializeSidebar()
{
    const sidebarElements = getSidebarElements();
    if (!sidebarElements) {
        return;
    }

    const { sidebar, sidebarToggle, navLinks } = sidebarElements;

    setupSidebarToggle(sidebar, sidebarToggle);
    setupSidebarNavigation(sidebar, sidebarToggle, navLinks);
    setupKeyboardNavigation(sidebar, sidebarToggle, navLinks);
}

function getSidebarElements()
{
    const sidebar = document.getElementById('sidebar');
    const sidebarToggle = document.getElementById('sidebar-toggle');

    if (!sidebar || !sidebarToggle) {
        return null;
    }

    const navLinks = sidebar.querySelectorAll('.nav-link');
    return { sidebar, sidebarToggle, navLinks };
}

function setupSidebarToggle(sidebar, sidebarToggle)
{
    sidebarToggle.addEventListener('click', () => {
        toggleSidebar(sidebar, sidebarToggle);
    });

    restoreSidebarState(sidebar, sidebarToggle);
}

function toggleSidebar(sidebar, sidebarToggle)
{
    const isExpanded = sidebarToggle.getAttribute('aria-expanded') === 'true';
    const newExpandedState = !isExpanded;

    sidebarToggle.setAttribute('aria-expanded', newExpandedState);
    sidebar.classList.toggle('open');
    sidebarToggle.classList.toggle('active');

    localStorage.setItem('sidebarOpen', sidebar.classList.contains('open'));
}

function restoreSidebarState(sidebar, sidebarToggle)
{
    document.addEventListener('DOMContentLoaded', () => {
        const sidebarWasOpen = localStorage.getItem('sidebarOpen') === 'true';
        const isMobile = window.innerWidth <= 992;

        if (sidebarWasOpen && isMobile) {
            openSidebar(sidebar, sidebarToggle);
        }
    });
}

function openSidebar(sidebar, sidebarToggle)
{
    sidebar.classList.add('open');
    sidebarToggle.classList.add('active');
    sidebarToggle.setAttribute('aria-expanded', 'true');
}

function closeSidebar(sidebar, sidebarToggle)
{
    sidebar.classList.remove('open');
    sidebarToggle.classList.remove('active');
    sidebarToggle.setAttribute('aria-expanded', 'false');
}

function setupSidebarNavigation(sidebar, sidebarToggle, navLinks)
{
    navLinks.forEach(link => {
        link.addEventListener('click', () => {
            if (window.innerWidth <= 992) {
                closeSidebar(sidebar, sidebarToggle);
            }
        });
    });
}

function setupKeyboardNavigation(sidebar, sidebarToggle, navLinks)
{
    setupEscapeKeyHandler(sidebar, sidebarToggle);
    setupArrowKeyNavigation(navLinks);
}

function setupEscapeKeyHandler(sidebar, sidebarToggle)
{
    document.addEventListener('keydown', e => {
        const isMobile = window.innerWidth <= 992;
        const isOpen = sidebar.classList.contains('open');

        if (e.key === 'Escape' && isOpen && isMobile) {
            closeSidebar(sidebar, sidebarToggle);
        }
    });
}

function setupArrowKeyNavigation(navLinks)
{
    navLinks.forEach((link, index) => {
        link.addEventListener('keydown', e => {
            handleNavigationKeypress(e, index, navLinks);
        });
    });
}

function handleNavigationKeypress(event, currentIndex, navLinks)
{
    switch (event.key) {
        case 'ArrowUp':
            if (currentIndex > 0) {
                event.preventDefault();
                navLinks[currentIndex - 1].focus();
            }
            break;

        case 'ArrowDown':
            if (currentIndex < navLinks.length - 1) {
                event.preventDefault();
                navLinks[currentIndex + 1].focus();
            }
            break;

        case 'Home':
            event.preventDefault();
            navLinks[0].focus();
            break;

        case 'End':
            event.preventDefault();
            navLinks[navLinks.length - 1].focus();
            break;
    }
}

export { initializeSidebar };
