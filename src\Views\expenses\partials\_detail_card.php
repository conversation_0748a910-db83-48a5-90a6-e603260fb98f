<?php
// Partial: Expense Detail Card
// Expects $viewData['expense'], $viewData['category'], $viewData['merchant'], $viewData['paymentMethod']

// Debug information
error_log("[Debug] _detail_card.php - View data keys: " . json_encode(array_keys($viewData ?? [])));
error_log("[Debug] _detail_card.php - Has expense in viewData: " . (isset($viewData['expense']) ? 'yes' : 'no'));

// If expense is already set directly, use it
if (isset($expense) && is_array($expense) && !empty($expense)) {
    error_log("[Debug] _detail_card.php - Using directly provided expense");
} else {
    // Otherwise get it from viewData
    $expense = $viewData['expense'] ?? [];
    error_log("[Debug] _detail_card.php - Using expense from viewData");
}

$category = $viewData['category'] ?? [];
$merchant = $viewData['merchant'] ?? [];
$paymentMethod = $viewData['paymentMethod'] ?? [];

// Debug expense data
if (!empty($expense)) {
    error_log("[Debug] _detail_card.php - Expense ID: " . ($expense['id'] ?? 'not set'));
    error_log("[Debug] _detail_card.php - Expense description: " . ($expense['description'] ?? 'not set'));
} else {
    error_log("[Warning] _detail_card.php - Empty expense data");
}

// Determine merchant name (fallback to precomputed merchant_name if set)
$merchant_name = $merchant['name']
    ?? $viewData['merchant_name']
    ?? '';

// Ensure amount is formatted
$amount = isset($expense['amount'])
    ? number_format((float)$expense['amount'], 2)
    : '0.00';
// Determine border color based on amount sign
$amount_value = (float)($expense['amount'] ?? 0);
$border_class = $amount_value >= 0 ? 'u-border-left-income' : 'u-border-left-expense';
?>
<div class="card expense-card primary-details u-spacing-stack-lg <?= $border_class ?>">
    <div class="card-header d-flex justify-content-between align-items-center u-spacing-stack-sm">
        <h2 class="u-type-scale-large mb-0"><?= htmlspecialchars($expense['description'] ?? '') ?></h2>
        <h3 class="u-type-scale-xlarge u-text-highlight u-spacing-inline-sm u-text-right u-font-mono">
            $<?= $amount ?>
        </h3>
    </div>
    <div class="card-body u-spacing-stack-sm">
        <details class="progressive-disclosure" open>
            <summary class="u-text-bold u-spacing-stack-xs">Details</summary>
            <div class="details-content u-spacing-stack-sm">
                <?php
                    // Pass through variables expected by _metadata.php
                    $expense = $expense;
                    $category = $category;
                    $merchant_name = $merchant_name;
                    $paymentMethod = $paymentMethod;
                    include __DIR__ . '/_metadata.php';
                ?>
            </div>
        </details>
    </div>
</div>
