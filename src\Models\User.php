<?php

declare(strict_types=1);

namespace App\Models;

use App\Exceptions\DatabaseException;
use App\Exceptions\ValidationException;

class User extends BaseModel
{
    public const STATUS_ACTIVE = 1;
    public const STATUS_INACTIVE = 2;
    public const STATUS_SUSPENDED = 3;

    private const PASSWORD_MIN_LENGTH = 8;
    private const PASSWORD_COST = 12;
    private const RESET_TOKEN_EXPIRY_HOURS = 24;
    private const REMEMBER_TOKEN_LENGTH = 64;
    private const REMEMBER_TOKEN_EXPIRY_DAYS = 30;

    protected static function getTableName(): string
    {
        return 'users';
    }

    public static function authenticate(string $email, string $password): bool
    {
        $user = self::findByEmail($email);

        if (
            !$user
            || !password_verify($password, $user['password'])
            || $user['user_status_id'] !== self::STATUS_ACTIVE
        ) {
            return false;
        }

        self::createUserSession($user);
        return true;
    }

    public static function isAuthenticated(): bool
    {
        return isset($_SESSION['authenticated']) && $_SESSION['authenticated'] === true;
    }

    public static function logout(): void
    {
        session_unset();
        session_destroy();

        if (isset($_COOKIE[session_name()])) {
            setcookie(session_name(), '', time() - 3600, '/');
        }
    }

    public static function find(int $id): ?array
    {
        $query = "SELECT
                    id,
                    name,
                    email,
                    user_status_id,
                    remember_me_default,
                    show_performance_metrics,
                    created_at
                 FROM `users`
                 WHERE id = ? AND deleted_at IS NULL";

        return self::getPreparedStatement()->fetchOne($query, [$id]);
    }

    public static function findById(int $id): ?array
    {
        error_log("User::findById - Looking up user ID: {$id}");

        $query = "SELECT
                    id,
                    name,
                    email,
                    user_status_id,
                    remember_me_default,
                    show_performance_metrics,
                    created_at
                 FROM `users`
                 WHERE id = ? AND deleted_at IS NULL";

        $user = self::getPreparedStatement()->fetchOne($query, [$id]);

        if ($user) {
            error_log("User::findById - Found user: " . json_encode($user));
            error_log("User::findById - User remember_me_default: " . ($user['remember_me_default'] ?? 'not set'));
        } else {
            error_log("User::findById - User not found");
        }

        return $user;
    }

    public static function getStatistics(int $userId): ?array
    {
        $user = self::findById($userId);
        if (!$user) {
            return null;
        }

        // Use a single query to get both count and sum
        $stats = self::getPreparedStatement()->fetchOne(
            "SELECT
                COUNT(*) as expense_count,
                COALESCE(SUM(amount), 0) as total_spent
             FROM `expenses`
             WHERE user_id = ? AND deleted_at IS NULL",
            [$userId]
        );

        if ($stats) {
            $user['expense_count'] = (int)$stats['expense_count'];
            $user['total_spent'] = (float)$stats['total_spent'];
        } else {
            $user['expense_count'] = 0;
            $user['total_spent'] = 0.0;
        }

        return $user;
    }

    public static function findByEmail(string $email): ?array
    {
        $query = "SELECT
                    id,
                    name,
                    email,
                    password,
                    user_status_id,
                    remember_me_default,
                    show_performance_metrics,
                    created_at
                 FROM `users`
                 WHERE email = ? AND deleted_at IS NULL";

        return self::getPreparedStatement()->fetchOne($query, [$email]);
    }

    public static function registerUser(string $name, string $email, string $password): bool
    {
        self::validateUserData($name, $email, $password);

        return self::transaction(function () use ($name, $email, $password) {
            try {
                $hashedPassword = password_hash($password, PASSWORD_DEFAULT, ['cost' => self::PASSWORD_COST]);
                
                $userId = self::getPreparedStatement()->insert('users', [
                    'name' => $name,
                    'email' => $email,
                    'password' => $hashedPassword,
                    'user_status_id' => self::STATUS_ACTIVE,
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ]);
                
                return $userId > 0;
            } catch (DatabaseException $exception) {
                if (strpos($exception->getMessage(), 'Duplicate entry') !== false) {
                    return false;
                }
                throw $exception;
            }
        });
    }

    public static function update(int $userId, array $data): bool
    {
        error_log("User::update - Starting update for user ID: {$userId}");
        error_log("User::update - Input data: " . json_encode($data));

        self::validateUpdateData($data, $userId);

        $updateData = self::prepareUpdateData($data);
        if (empty($updateData['fields'])) {
            error_log("User::update - No fields to update, returning true");
            return true;
        }

        error_log("User::update - Fields to update: " . implode(', ', $updateData['fields']));

        $result = self::transaction(function () use ($updateData, $userId) {
            // Convert field assignments to data array for PreparedStatement
            $updateFields = [];
            foreach ($updateData['fields'] as $index => $field) {
                $fieldName = explode(' = ', $field)[0];
                $updateFields[$fieldName] = $updateData['params'][$index];
            }
            
            // Add updated_at timestamp
            $updateFields['updated_at'] = date('Y-m-d H:i:s');

            error_log("User::update - Update fields: " . json_encode($updateFields));

            $rowsAffected = self::getPreparedStatement()->update(
                'users',
                $updateFields,
                ['id' => $userId, 'deleted_at' => null]
            );
            
            $success = $rowsAffected > 0;
            error_log("User::update - SQL execution result: " . ($success ? 'success' : 'failure'));

            return $success;
        });

        error_log("User::update - Final result: " . ($result ? 'success' : 'failure'));

        // Verify the update by fetching the user again
        $updatedUser = self::findById($userId);
        if ($updatedUser && isset($data['remember_me_default'])) {
            $rememberDefault = $updatedUser['remember_me_default'] ?? 'not set';
            error_log("User::update - User remember_me_default after update: " . $rememberDefault);
        }

        return $result;
    }

    private static function validateUpdateData(array $data, int $userId): void
    {
        if (!isset($data['email'])) {
            return;
        }

        if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            throw new ValidationException(['Invalid email format']);
        }

        $existingUser = self::findByEmail($data['email']);
        if ($existingUser && $existingUser['id'] !== $userId) {
            throw new ValidationException(['Email is already in use']);
        }
    }

    private static function prepareUpdateData(array $data): array
    {
        $fields = [];
        $params = [];
        $allowedFields = [
            'name',
            'email',
            'remember_me_default',
            'currency',
            'date_format',
            'monthly_budget',
            'notify_weekly_summary',
            'notify_budget_alerts',
            'notify_recurring_expenses',
            'profile_image',
            'show_performance_metrics'
        ];

        // Debug output for update data
        error_log("User::prepareUpdateData - Input data: " . json_encode($data));

        foreach ($allowedFields as $field) {
            if (isset($data[$field])) {
                $fields[] = "$field = ?";
                $params[] = $data[$field];

                // Debug output for each field being updated
                error_log("User::prepareUpdateData - Setting field {$field} to: " . var_export($data[$field], true));
            }
        }

        if (!empty($fields)) {
            $fields[] = "updated_at = NOW()";
        }

        error_log("User::prepareUpdateData - Final fields: " . json_encode($fields));
        error_log("User::prepareUpdateData - Final params: " . json_encode($params));

        return ['fields' => $fields, 'params' => $params];
    }

    public static function updatePassword(int $userId, string $password): bool
    {
        self::validatePassword($password);

        return self::transaction(function () use ($userId, $password) {
            $query = "UPDATE users SET password = ?, reset_token = NULL,
                     reset_token_expires = NULL, updated_at = NOW()
                     WHERE id = ? AND deleted_at IS NULL";

            $hashedPassword = password_hash($password, PASSWORD_DEFAULT, ['cost' => self::PASSWORD_COST]);
            return self::executeSql($query, [$hashedPassword, $userId]);
        });
    }

    public static function updateStatus(int $userId, int $statusId): bool
    {
        self::validateStatusId($statusId);

        return self::transaction(function () use ($userId, $statusId) {
            $rowsAffected = self::getPreparedStatement()->update(
                'users',
                [
                    'user_status_id' => $statusId,
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                ['id' => $userId, 'deleted_at' => null]
            );

            return $rowsAffected > 0;
        });
    }

    public static function softDelete(int $id): bool
    {
        return self::transaction(function () use ($id) {
            $rowsAffected = self::getPreparedStatement()->update(
                'users',
                [
                    'deleted_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                ['id' => $id, 'deleted_at' => null]
            );

            return $rowsAffected > 0;
        });
    }

    public static function storeResetToken(string $email, string $token): bool
    {
        return self::transaction(function () use ($email, $token) {
            $expiryTime = date('Y-m-d H:i:s', strtotime('+' . self::RESET_TOKEN_EXPIRY_HOURS . ' hours'));
            
            $rowsAffected = self::getPreparedStatement()->update(
                'users',
                [
                    'reset_token' => $token,
                    'reset_token_expires' => $expiryTime,
                    'updated_at' => date('Y-m-d H:i:s')
                ],
                ['email' => $email, 'deleted_at' => null]
            );

            return $rowsAffected > 0;
        });
    }

    public static function findByResetToken(string $token): ?array
    {
        $query = "SELECT
                    id,
                    name,
                    email,
                    user_status_id,
                    reset_token_expires
                 FROM `users`
                 WHERE reset_token = ?
                 AND reset_token_expires > NOW()
                 AND deleted_at IS NULL";

        return self::getPreparedStatement()->fetchOne($query, [$token]);
    }



    private static function createUserSession(array $user): void
    {
        session_regenerate_id(true);
        $_SESSION['user_id'] = $user['id'];
        $_SESSION['user_name'] = $user['name'];
        $_SESSION['user_email'] = $user['email'];
        $_SESSION['authenticated'] = true;
        $_SESSION['auth_time'] = time();

        // Store user preferences in session
        if (isset($user['show_performance_metrics'])) {
            $_SESSION['show_performance_metrics'] = (bool)$user['show_performance_metrics'];
        }
    }

    public static function isValidEmail(string $email): bool
    {
        return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
    }

    public static function isEmailUnique(string $email, ?int $excludeUserId = null): bool
    {
        $query = "SELECT id FROM `users` WHERE email = ? AND deleted_at IS NULL";
        $params = [$email];

        if ($excludeUserId) {
            $query .= " AND id != ?";
            $params[] = $excludeUserId;
        }

        $result = self::getPreparedStatement()->fetchOne($query, $params);
        return $result === null;
    }

    public static function validateUserCredentials(string $email, string $password): array
    {
        $errors = [];

        if (!self::isValidEmail($email)) {
            $errors[] = 'Invalid email format';
        }

        if (strlen($password) < self::PASSWORD_MIN_LENGTH) {
            $errors[] = 'Password must be at least ' . self::PASSWORD_MIN_LENGTH . ' characters';
        }

        return $errors;
    }

    public static function findActiveUsers(): array
    {
        $query = "SELECT
                    id,
                    name,
                    email,
                    user_status_id,
                    created_at
                 FROM `users`
                 WHERE user_status_id = ? AND deleted_at IS NULL
                 ORDER BY name ASC";

        return self::getPreparedStatement()->fetchMany($query, [self::STATUS_ACTIVE]);
    }

    public static function findByStatus(int $statusId): array
    {
        $query = "SELECT
                    id,
                    name,
                    email,
                    user_status_id,
                    created_at
                 FROM `users`
                 WHERE user_status_id = ? AND deleted_at IS NULL
                 ORDER BY name ASC";

        return self::getPreparedStatement()->fetchMany($query, [$statusId]);
    }

    public static function getFullUserProfile(int $userId): ?array
    {
        $user = self::getStatistics($userId);

        if (!$user) {
            return null;
        }

        // Get user's categories with explicit column selection
        $query = "SELECT
                    id,
                    name,
                    description
                 FROM `user_categories`
                 WHERE user_id = ? AND deleted_at IS NULL
                 ORDER BY name ASC";

        $user['categories'] = self::getPreparedStatement()->fetchMany($query, [$userId]);

        // Get user's merchants with explicit column selection
        $query = "SELECT
                    id,
                    name
                 FROM `merchants`
                 WHERE user_id = ?
                 ORDER BY name ASC";

        $user['merchants'] = self::getPreparedStatement()->fetchMany($query, [$userId]);

        // Get recent expenses with explicit column selection
        $query = "SELECT
                    id,
                    category_id,
                    amount,
                    description,
                    date
                 FROM `expenses`
                 WHERE user_id = ? AND deleted_at IS NULL
                 ORDER BY date DESC
                 LIMIT 5";

        $user['recent_expenses'] = self::getPreparedStatement()->fetchMany($query, [$userId]);

        return $user;
    }

    private static function validateUserData(string $name, string $email, string $password): void
    {
        $errors = [];

        if (empty($name) || strlen($name) > 255) {
            $errors[] = 'Name must be between 1 and 255 characters';
        }

        if (!self::isValidEmail($email)) {
            $errors[] = 'Invalid email format';
        }

        try {
            self::validatePassword($password);
        } catch (ValidationException $exception) {
            $errors = array_merge($errors, $exception->getValidationErrors());
        }

        if (!empty($errors)) {
            throw new ValidationException($errors);
        }
    }

    private static function validatePassword(string $password): void
    {
        if (strlen($password) < self::PASSWORD_MIN_LENGTH) {
            throw new ValidationException(['Password must be at least ' . self::PASSWORD_MIN_LENGTH . ' characters']);
        }
    }

    private static function validateStatusId(int $statusId): void
    {
        $validStatuses = [self::STATUS_ACTIVE, self::STATUS_INACTIVE, self::STATUS_SUSPENDED];
        if (!in_array($statusId, $validStatuses, true)) {
            throw new ValidationException(['Invalid status ID']);
        }
    }

    /**
     * Generate a password reset token for a user
     *
     * @param string $email The user's email
     * @return string|null The reset token or null if the user was not found
     */
    public static function generateResetToken(string $email): ?string
    {
        try {
            $user = self::findByEmail($email);

            if (!$user) {
                return null;
            }

            $token = bin2hex(random_bytes(16));
            $expires = date('Y-m-d H:i:s', strtotime('+1 hour'));

            $query = "
                UPDATE users
                SET
                    reset_token = ?,
                    reset_token_expires = ?,
                    updated_at = NOW()
                WHERE id = ?
            ";

            self::executeSql($query, [$token, $expires, $user['id']]);

            return $token;
        } catch (\PDOException $e) {
            throw new DatabaseException("Error generating reset token: " . $e->getMessage(), (int)$e->getCode());
        }
    }

    /**
     * Reset a user's password using a token
     *
     * @param string $email The user's email
     * @param string $token The reset token
     * @param string $newPassword The new password
     * @return bool True if the password was reset successfully
     */
    public static function resetPassword(string $email, string $token, string $newPassword): bool
    {
        try {
            // First, find the user with the given email and token
            // Use explicit column selection instead of *
            $query = "
                SELECT id, email, name
                FROM users
                WHERE email = ?
                AND reset_token = ?
                AND reset_token_expires > NOW()
                AND deleted_at IS NULL
                LIMIT 1
            ";

            $user = self::fetchOne($query, [$email, $token]);

            if (!$user) {
                return false;
            }

            // Hash the new password
            $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);

            // Update the user's password and clear the reset token
            $updateQuery = "
                UPDATE users
                SET
                    password = ?,
                    reset_token = NULL,
                    reset_token_expires = NULL,
                    updated_at = NOW()
                WHERE id = ?
            ";

            return self::executeSql($updateQuery, [$hashedPassword, $user['id']]);
        } catch (\PDOException $e) {
            throw new DatabaseException("Error resetting password: " . $e->getMessage(), (int)$e->getCode());
        }
    }

    /**
     * Manage remember token for a user
     * This is the centralized method for all remember token operations
     *
     * @param int $userId The user ID
     * @param string $operation The operation to perform (create, rotate, delete)
     * @param string|null $oldToken The old token to rotate or delete (for rotate and delete operations)
     * @param int $expiryDays Number of days until token expires
     * @return array|bool The token data for create/rotate operations, or success status for delete
     * @throws \PDOException If a database connection error occurs that can't be handled
     */
    public static function manageRememberToken(
        int $userId,
        string $operation = 'create',
        ?string $oldToken = null,
        int $expiryDays = self::REMEMBER_TOKEN_EXPIRY_DAYS
    ) {
        if ($userId <= 0) {
            throw new ValidationException(['Invalid user ID']);
        }

        // Verify database connection before proceeding
        try {
            self::verifyDatabaseConnection();
        } catch (\PDOException $e) {
            // Log the connection error
            error_log("Database connection error in manageRememberToken: " . $e->getMessage());

            // For create and rotate operations, return a token anyway so the application doesn't crash
            if ($operation === 'create' || $operation === 'rotate') {
                $token = bin2hex(random_bytes(self::REMEMBER_TOKEN_LENGTH / 2));
                $expiresAt = date('Y-m-d H:i:s', strtotime("+{$expiryDays} days"));

                return [
                    'id' => 0,
                    'user_id' => $userId,
                    'token' => $token,
                    'expires_at' => $expiresAt,
                    'error' => true,
                    'message' => 'Database connection error: ' . $e->getMessage(),
                    'connection_error' => true
                ];
            }

            // For delete operations, return false to indicate failure
            return false;
        }

        try {
            // First check if the remember_tokens table exists
            $tableExists = self::checkTableExists('remember_tokens');
            if (!$tableExists) {
                // Create the table if it doesn't exist
                $tableCreated = self::createRememberTokensTable();
                if (!$tableCreated) {
                    throw new \RuntimeException("Failed to create remember_tokens table");
                }
                error_log("Created remember_tokens table");
            }

            // Ensure the table has all required columns
            $columnsEnsured = self::ensureTableColumns();
            if (!$columnsEnsured) {
                throw new \RuntimeException("Failed to ensure remember_tokens table columns");
            }

            // Handle different operations
            switch ($operation) {
                case 'create':
                    return self::createNewRememberToken($userId, $expiryDays);

                case 'rotate':
                    if ($oldToken === null) {
                        throw new \InvalidArgumentException('Old token is required for rotation');
                    }
                    return self::rotateRememberToken($userId, $oldToken, $expiryDays);

                case 'delete':
                    if ($oldToken !== null) {
                        return self::deleteRememberToken($oldToken);
                    } else {
                        return self::deleteRememberTokensByUserId($userId);
                    }

                default:
                    throw new \InvalidArgumentException("Invalid operation: {$operation}");
            }
        } catch (\Exception $e) {
            error_log("Error managing remember token: " . $e->getMessage());

            // For create and rotate operations, return a token anyway so the application doesn't crash
            if ($operation === 'create' || $operation === 'rotate') {
                $token = bin2hex(random_bytes(self::REMEMBER_TOKEN_LENGTH / 2));
                $expiresAt = date('Y-m-d H:i:s', strtotime("+{$expiryDays} days"));

                return [
                    'id' => 0,
                    'user_id' => $userId,
                    'token' => $token,
                    'expires_at' => $expiresAt,
                    'error' => true,
                    'message' => $e->getMessage()
                ];
            }

            // For delete operations, return false to indicate failure
            return false;
        }
    }

    /**
     * Verify database connection is active and working with retry mechanism
     *
     * @return bool True if connection is active
     * @throws \PDOException If connection fails after retry
     */
    private static function verifyDatabaseConnection(): bool
    {
        try {
            // Try with existing connection
            $db = parent::getDb();
            $db->query("SELECT 1");
            return true;
        } catch (\PDOException $connectionException) {
            // Log the initial connection error
            error_log("Initial database connection error: " . $connectionException->getMessage());

            // Try to reconnect once
            try {
                // Wait a moment before reconnecting
                usleep(100000); // 100ms

                // Try to reconnect by getting a fresh connection from Database class
                $db = \App\Core\Database::getInstance(true);
                $db->query("SELECT 1");
                error_log("Database connection recovered after initial failure");
                return true;
            } catch (\PDOException $reconnectException) {
                // If reconnection fails, log and throw the exception
                error_log("Failed to reconnect to database: " . $reconnectException->getMessage());
                throw $reconnectException;
            }
        }
    }

    /**
     * Create a remember token for a user
     * This is a wrapper around manageRememberToken for backward compatibility
     *
     * @param int $userId The user ID
     * @param int $expiryDays Number of days until token expires
     * @return array The created token data
     */
    public static function createRememberToken(int $userId, int $expiryDays = self::REMEMBER_TOKEN_EXPIRY_DAYS): array
    {
        return self::manageRememberToken($userId, 'create', null, $expiryDays);
    }

    /**
     * Create a new remember token (internal method)
     *
     * @param int $userId The user ID
     * @param int $expiryDays Number of days until token expires
     * @return array The created token data
     */
    private static function createNewRememberToken(int $userId, int $expiryDays): array
    {
        // Generate a secure random token
        $token = bin2hex(random_bytes(self::REMEMBER_TOKEN_LENGTH / 2));
        $expiresAt = date('Y-m-d H:i:s', strtotime("+{$expiryDays} days"));

        return self::transaction(function () use ($userId, $token, $expiresAt) {
            // Delete any existing tokens for this user
            self::deleteRememberTokensByUserId($userId);

            // Get user agent and IP address
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '';

            // Log the values for debugging
            error_log("Creating remember token with user agent: " . substr($userAgent, 0, 50) . "...");
            error_log("Creating remember token with IP address: " . $ipAddress);

            // Create a new token
            $sql = "INSERT INTO remember_tokens (user_id, token, created_at, expires_at, user_agent, ip_address)
                    VALUES (?, ?, NOW(), ?, ?, ?)";

            self::executeSql($sql, [$userId, $token, $expiresAt, $userAgent, $ipAddress]);
            $lastId = (int)self::getDb()->lastInsertId();

            error_log("Created remember token with ID {$lastId} for user {$userId}");

            return [
                'id' => $lastId,
                'user_id' => $userId,
                'token' => $token,
                'expires_at' => $expiresAt
            ];
        });
    }

    /**
     * Rotate a remember token (internal method)
     * This creates a new token and deletes the old one
     * Uses a more robust approach to handle race conditions
     *
     * @param int $userId The user ID
     * @param string $oldToken The old token to rotate
     * @param int $expiryDays Number of days until token expires
     * @return array The new token data
     */
    private static function rotateRememberToken(int $userId, string $oldToken, int $expiryDays): array
    {
        return self::transaction(function () use ($userId, $oldToken, $expiryDays) {
            // First verify the old token exists and belongs to this user
            $oldTokenData = self::findRememberToken($oldToken);

            if (!$oldTokenData || (int)$oldTokenData['user_id'] !== $userId) {
                // If the token doesn't exist or doesn't belong to this user,
                // log the issue and create a new token anyway
                error_log("Token rotation issue: Token {$oldToken} not found or doesn't belong to user {$userId}");
                return self::createNewRememberToken($userId, $expiryDays);
            }

            // Lock the row to prevent race conditions
            $sql = "SELECT id FROM remember_tokens WHERE token = ? FOR UPDATE";
            self::fetchOne($sql, [$oldToken]);

            // Generate a new token
            $token = bin2hex(random_bytes(self::REMEMBER_TOKEN_LENGTH / 2));
            $expiresAt = date('Y-m-d H:i:s', strtotime("+{$expiryDays} days"));

            // Get user agent and IP address
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
            $ipAddress = $_SERVER['REMOTE_ADDR'] ?? '';

            // Log the values for debugging
            error_log("Rotating remember token with user agent: " . substr($userAgent, 0, 50) . "...");
            error_log("Rotating remember token with IP address: " . $ipAddress);

            // Update the existing token instead of creating a new one
            $sql = "UPDATE remember_tokens
                    SET token = ?, expires_at = ?, user_agent = ?, ip_address = ?
                    WHERE token = ?";

            self::executeSql($sql, [$token, $expiresAt, $userAgent, $ipAddress, $oldToken]);

            error_log("Rotated remember token for user {$userId}");

            return [
                'id' => $oldTokenData['id'],
                'user_id' => $userId,
                'token' => $token,
                'expires_at' => $expiresAt
            ];
        });
    }

    /**
     * Check if a table exists in the database
     *
     * @param string $tableName The table name to check
     * @return bool True if the table exists
     */
    public static function checkTableExists(string $tableName): bool
    {
        try {
            $sql = "SHOW TABLES LIKE ?";
            $result = self::fetchOne($sql, [$tableName]);
            return $result !== null;
        } catch (\Exception $e) {
            error_log("Error checking if table exists: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Create the remember_tokens table
     *
     * @return bool True if the table was created successfully
     */
    private static function createRememberTokensTable(): bool
    {
        try {
            $sql = "CREATE TABLE IF NOT EXISTS remember_tokens (
                id INT AUTO_INCREMENT PRIMARY KEY,
                user_id INT NOT NULL,
                token VARCHAR(255) NOT NULL,
                created_at DATETIME NOT NULL,
                expires_at DATETIME,
                user_agent TEXT NULL,
                ip_address VARCHAR(45) NULL,
                FOREIGN KEY (user_id) REFERENCES users(id)
            )";

            self::executeSql($sql);

            // Create indexes for better performance
            try {
                self::executeSql("CREATE INDEX idx_remember_token ON remember_tokens (token)");
            } catch (\Exception $e) {
                // Index might already exist, ignore
                error_log("Note: Index creation for token: " . $e->getMessage());
            }

            try {
                self::executeSql("CREATE INDEX idx_user_remember_token ON remember_tokens (user_id)");
            } catch (\Exception $e) {
                // Index might already exist, ignore
                error_log("Note: Index creation for user_id: " . $e->getMessage());
            }

            try {
                self::executeSql("CREATE INDEX idx_expires_at ON remember_tokens (expires_at)");
            } catch (\Exception $e) {
                // Index might already exist, ignore
                error_log("Note: Index creation for expires_at: " . $e->getMessage());
            }

            // Check and update table structure if needed
            self::ensureTableColumns();

            return true;
        } catch (\Exception $e) {
            error_log("Error creating remember_tokens table: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Ensure the remember_tokens table has all required columns
     *
     * @return bool True if the table structure is correct
     */
    public static function ensureTableColumns(): bool
    {
        try {
            // Check if user_agent column exists
            $result = self::fetchOne("SHOW COLUMNS FROM remember_tokens LIKE 'user_agent'");
            if (!$result) {
                // Add user_agent column
                self::executeSql("ALTER TABLE remember_tokens ADD COLUMN user_agent TEXT NULL");
                error_log("Added user_agent column to remember_tokens table");
            }

            // Check if ip_address column exists
            $result = self::fetchOne("SHOW COLUMNS FROM remember_tokens LIKE 'ip_address'");
            if (!$result) {
                // Add ip_address column
                self::executeSql("ALTER TABLE remember_tokens ADD COLUMN ip_address VARCHAR(45) NULL");
                error_log("Added ip_address column to remember_tokens table");
            }

            return true;
        } catch (\Exception $e) {
            error_log("Error ensuring table columns: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Find a remember token by its value
     *
     * @param string $token The token to find
     * @return array|null The token data or null if not found
     */
    public static function findRememberToken(string $token): ?array
    {
        $sql = "SELECT id, user_id, token, created_at, expires_at, user_agent, ip_address
                FROM remember_tokens
                WHERE token = ? AND (expires_at IS NULL OR expires_at > NOW())
                LIMIT 1";

        return self::fetchOne($sql, [$token]);
    }

    /**
     * Find a remember token by user ID
     *
     * @param int $userId The user ID
     * @return array|null The token data or null if not found
     */
    public static function findRememberTokenByUserId(int $userId): ?array
    {
        $sql = "SELECT id, user_id, token, created_at, expires_at, user_agent, ip_address
                FROM remember_tokens
                WHERE user_id = ? AND (expires_at IS NULL OR expires_at > NOW())
                LIMIT 1";

        return self::fetchOne($sql, [$userId]);
    }

    /**
     * Delete a remember token by its value
     *
     * @param string $token The token to delete
     * @return bool True if successful
     */
    public static function deleteRememberToken(string $token): bool
    {
        $sql = "DELETE FROM remember_tokens WHERE token = ?";
        return self::executeSql($sql, [$token]);
    }

    /**
     * Delete all remember tokens for a user
     *
     * @param int $userId The user ID
     * @return bool True if successful
     */
    public static function deleteRememberTokensByUserId(int $userId): bool
    {
        $sql = "DELETE FROM remember_tokens WHERE user_id = ?";
        return self::executeSql($sql, [$userId]);
    }

    /**
     * Delete expired remember tokens
     *
     * @return bool True if successful
     */
    public static function deleteExpiredRememberTokens(): bool
    {
        $sql = "DELETE FROM remember_tokens WHERE expires_at IS NOT NULL AND expires_at <= NOW()";
        return self::executeSql($sql);
    }
}
