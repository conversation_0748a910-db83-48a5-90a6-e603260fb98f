<h2>Category Expense Report</h2>

<?php include __DIR__ . '/components/messages.php'; ?>

<div class="report-filters">
    <form method="GET" action="/reports/category">
        <div class="filter-group">
            <label for="period">Time Period</label>
            <select name="period" id="period" onchange="this.form.submit()">
                <option value="month" <?= $selectedPeriod == 'month' ? 'selected' : '' ?>>Current Month</option>
                <option value="quarter" <?= $selectedPeriod == 'quarter' ? 'selected' : '' ?>>Current Quarter</option>
                <option value="year" <?= $selectedPeriod == 'year' ? 'selected' : '' ?>>Current Year</option>
                <option value="all" <?= $selectedPeriod == 'all' ? 'selected' : '' ?>>All Time</option>
            </select>
        </div>

        <?php if ($selectedPeriod == 'month' || $selectedPeriod == 'quarter' || $selectedPeriod == 'year') : ?>
        <div class="filter-group">
            <label for="date">Select <?= ucfirst($selectedPeriod) ?></label>
            <?php if ($selectedPeriod == 'month') : ?>
                <input type="month" name="date" id="date" value="<?= $selectedDate ?>" onchange="this.form.submit()">
            <?php elseif ($selectedPeriod == 'quarter') : ?>
                <select name="date" id="date" onchange="this.form.submit()">
                    <?php foreach ($availableQuarters as $quarter) : ?>
                        <option value="<?= $quarter['value'] ?>" <?= $selectedDate == $quarter['value'] ? 'selected' : '' ?>>
                            <?= $quarter['label'] ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            <?php elseif ($selectedPeriod == 'year') : ?>
                <select name="date" id="date" onchange="this.form.submit()">
                    <?php foreach ($availableYears as $year) : ?>
                        <option value="<?= $year ?>" <?= $selectedDate == $year ? 'selected' : '' ?>>
                            <?= $year ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            <?php endif; ?>
        </div>
        <?php endif; ?>
    </form>
</div>

<?php if (empty($categoryData)) : ?>
    <div class="notice">
        No expense data available for the selected time period.
    </div>
<?php else : ?>
    <div class="report-summary">
        <div class="summary-stats">
            <div class="stat-item">
                <span class="stat-label">Total Expenses</span>
                <span class="stat-value">$<?= number_format($totalAmount, 2) ?></span>
            </div>

            <div class="stat-item">
                <span class="stat-label">Categories</span>
                <span class="stat-value"><?= count($categoryData) ?></span>
            </div>

            <div class="stat-item">
                <span class="stat-label">Top Category</span>
                <span class="stat-value"><?= htmlspecialchars($topCategory['name']) ?></span>
                <span class="stat-subvalue">$<?= number_format($topCategory['amount'], 2) ?></span>
                <span class="stat-subvalue"><?= number_format($topCategory['percentage'], 1) ?>%</span>
            </div>

            <div class="stat-item">
                <span class="stat-label">Period</span>
                <span class="stat-value"><?= $periodLabel ?></span>
            </div>
        </div>
    </div>

    <div class="chart-container">
        <div class="pie-chart-container">
            <canvas id="categoryPieChart" width="300" height="300"></canvas>
        </div>
        <div class="bar-chart-container">
            <canvas id="categoryBarChart" width="500" height="300"></canvas>
        </div>
    </div>

    <div class="table-responsive">
        <table aria-label="Category Expenses">
            <thead>
                <tr>
                    <th scope="col">Category</th>
                    <th scope="col">Total Amount</th>
                    <th scope="col">% of Total</th>
                    <th scope="col">Number of Expenses</th>
                    <th scope="col">Average per Expense</th>
                    <th scope="col">Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($categoryData as $category) : ?>
                    <tr>
                        <td><?= htmlspecialchars($category['name']) ?></td>
                        <td>$<?= number_format($category['amount'], 2) ?></td>
                        <td><?= number_format($category['percentage'], 1) ?>%</td>
                        <td><?= $category['count'] ?></td>
                        <td>$<?= number_format($category['average'], 2) ?></td>
                        <td>
                            <a href="/expenses?category_id=<?= $category['id'] ?>&period=<?= $selectedPeriod ?>&date=<?= urlencode($selectedDate) ?>" class="button small">
                                View Expenses
                            </a>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
            <tfoot>
                <tr>
                    <th>Total</th>
                    <th>$<?= number_format($totalAmount, 2) ?></th>
                    <th>100%</th>
                    <th><?= array_sum(array_column($categoryData, 'count')) ?></th>
                    <th>$<?= number_format($totalAmount / array_sum(array_column($categoryData, 'count')), 2) ?></th>
                    <th></th>
                </tr>
            </tfoot>
        </table>
    </div>

    <div class="download-section">
        <h3>Download Report</h3>
        <div class="download-options">
            <a href="/reports/export?type=category&period=<?= $selectedPeriod ?>&date=<?= urlencode($selectedDate) ?>&format=csv" class="button">
                Export as CSV
            </a>
            <a href="/reports/export?type=category&period=<?= $selectedPeriod ?>&date=<?= urlencode($selectedDate) ?>&format=pdf" class="button">
                Export as PDF
            </a>
        </div>
    </div>
<?php endif; ?>

<div class="navigation-links">
    <a href="/reports" class="button secondary">Back to Reports</a>
</div>

<?php if (!empty($categoryData)) : ?>
<!-- Hidden data elements for JavaScript -->
<script id="chart-categories-data" type="application/json">
    <?= json_encode(array_column($categoryData, 'name')) ?>
</script>
<script id="chart-amounts-data" type="application/json">
    <?= json_encode(array_column($categoryData, 'amount')) ?>
</script>
<script id="chart-percentages-data" type="application/json">
    <?= json_encode(array_column($categoryData, 'percentage')) ?>
</script>
<?php endif; ?>

<?php
// Add reports-specific scripts
$scripts ??= [];
$scripts[] = 'https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js';
$scripts[] = '/assets/js/pages/reports.js';
?>
