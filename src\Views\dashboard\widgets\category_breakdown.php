<?php

/**
 * Category breakdown widget
 * Displays a chart of expenses by category
 */

// Set widget parameters
$title = 'Category Breakdown';
$icon = '<svg class="icon"><use xlink:href="/assets/icons.svg#icon-categories"></use></svg>';
$widgetId = 'category-breakdown';
$widgetClass = 'category-breakdown-widget';
$hasData = !empty($categoryData) && count($categoryData) > 0;
$emptyMessage = 'No category data available. Add expenses with categories to see a breakdown.';

// Prepare chart data
$chartLabels = [];
$chartValues = [];

if (!empty($categoryData)) {
    // Sort categories by total amount (descending)
    usort($categoryData, function ($a, $b) {
        return ($b['total'] ?? 0) <=> ($a['total'] ?? 0);
    });

    // Take top 5 categories
    $topCategories = array_slice($categoryData, 0, 5);

    foreach ($topCategories as $category) {
        $chartLabels[] = $category['name'] ?? 'Unknown';
        $chartValues[] = $category['total'] ?? 0;
    }
}

$chartData = [
    'labels' => $chartLabels,
    'values' => $chartValues
];

// Prepare widget content
ob_start();
?>

<?php if ($hasData) : ?>
    <div class="chart-container">
        <canvas id="categoryChart" data-chart='<?= json_encode($chartData) ?>' width="400" height="200"></canvas>
    </div>
    <div class="widget-footer">
        <a href="/reports/category" class="button small">View Category Report</a>
    </div>
<?php endif; ?>

<?php
$content = ob_get_clean();

// Include the widget template
include __DIR__ . '/widget_template.php';
?>
