<?php

declare(strict_types=1);

namespace App\Core;

use Psr\Log\LoggerInterface;
use Psr\Log\LogLevel;

class Logger implements LoggerInterface
{
    private string $logFile;
    private int $minimumLevel;
    private int $maxLogSize;

    private const LOG_LEVELS = [
        LogLevel::EMERGENCY => 0,
        LogLevel::ALERT     => 1,
        LogLevel::CRITICAL  => 2,
        LogLevel::ERROR     => 3,
        LogLevel::WARNING   => 4,
        LogLevel::NOTICE    => 5,
        LogLevel::INFO      => 6,
        LogLevel::DEBUG     => 7,
    ];

    public function __construct(
        ?string $logFile = null,
        string $minimumLevel = LogLevel::DEBUG,
        int $maxLogSize = 10485760
    ) {
        $this->logFile = $logFile ?? __DIR__ . '/../../logs/app.log';
        $this->minimumLevel = self::LOG_LEVELS[$minimumLevel] ?? 7;
        $this->maxLogSize = $maxLogSize;
        $this->ensureLogDirectoryExists();
    }

    public function emergency($message, array $context = []): void
    {
        $this->log(LogLevel::EMERGENCY, $message, $context);
    }

    public function alert($message, array $context = []): void
    {
        $this->log(LogLevel::ALERT, $message, $context);
    }

    public function critical($message, array $context = []): void
    {
        $this->log(LogLevel::CRITICAL, $message, $context);
    }

    public function error($message, array $context = []): void
    {
        $this->log(LogLevel::ERROR, $message, $context);
    }

    public function warning($message, array $context = []): void
    {
        $this->log(LogLevel::WARNING, $message, $context);
    }

    public function notice($message, array $context = []): void
    {
        $this->log(LogLevel::NOTICE, $message, $context);
    }

    public function info($message, array $context = []): void
    {
        $this->log(LogLevel::INFO, $message, $context);
    }

    public function debug($message, array $context = []): void
    {
        $this->log(LogLevel::DEBUG, $message, $context);
    }

    public function log($level, $message, array $context = []): void
    {
        if (!isset(self::LOG_LEVELS[$level]) || self::LOG_LEVELS[$level] > $this->minimumLevel) {
            return;
        }

        $this->rotateLogFileIfNeeded();
        $logEntry = $this->formatLogEntry($level, $message, $context);
        file_put_contents($this->logFile, $logEntry, FILE_APPEND | LOCK_EX);
    }

    private function ensureLogDirectoryExists(): void
    {
        $logDir = dirname($this->logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
    }

    private function formatLogEntry(string $level, string $message, array $context): string
    {
        $timestamp = date('Y-m-d H:i:s');
        $processId = getmypid();
        $interpolatedMessage = $this->interpolate($message, $context);
        $contextStr = empty($context) ? '' : ' ' . json_encode(
            $context,
            JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE
        );

        return sprintf(
            "[%s] [%s] [PID:%d] %s%s%s",
            $timestamp,
            strtoupper($level),
            $processId,
            $interpolatedMessage,
            $contextStr,
            PHP_EOL
        );
    }

    private function interpolate(string $message, array $context): string
    {
        $replace = [];
        foreach ($context as $key => $val) {
            if (is_string($val) || is_numeric($val) || is_bool($val)) {
                $replace["{{$key}}"] = $val;
            }
        }
        return strtr($message, $replace);
    }

    private function rotateLogFileIfNeeded(): void
    {
        if (!file_exists($this->logFile) || filesize($this->logFile) < $this->maxLogSize) {
            return;
        }

        $archiveFile = sprintf('%s.%s', $this->logFile, date('Y-m-d-His'));
        rename($this->logFile, $archiveFile);
        $this->cleanupOldLogFiles();
    }

    private function cleanupOldLogFiles(): void
    {
        $files = glob("{$this->logFile}.*");
        if (count($files) <= 5) {
            return;
        }

        usort($files, fn($a, $b) => filemtime($b) - filemtime($a));
        foreach (array_slice($files, 5) as $file) {
            unlink($file);
        }
    }

    public function logException(\Throwable $e, string $context, array $additionalData = []): void
    {
        $timestamp = date('Y-m-d H:i:s');
        $errorId = bin2hex(random_bytes(4));
        $isDebug = filter_var($_ENV['APP_DEBUG'] ?? false, FILTER_VALIDATE_BOOLEAN);

        $logData = [
            'timestamp' => $timestamp,
            'error_id' => $errorId,
            'context' => $context,
            'message' => $e->getMessage(),
            'code' => $e->getCode(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'type' => get_class($e),
            'data' => $additionalData
        ];

        if ($isDebug) {
            $logData['trace'] = $e->getTraceAsString();
        }

        $this->error($context, $logData);
    }
}
