<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Merchant;
use App\Exceptions\InvalidArgumentException;

class MerchantService extends BaseService
{
    protected function getModelClass(): string
    {
        return Merchant::class;
    }

    public function getMerchantById(int $id): ?array
    {
        try {
            $merchant = $this->find($id);

            if ($merchant === null) {
                $this->logger->info("Merchant not found", ['merchant_id' => $id]);
            }

            return $merchant;
        } catch (\Exception $e) {
            $this->logger->error("Failed to retrieve merchant by ID", [
                'merchant_id' => $id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return null;
        }
    }

    public function findByName(string $name): ?array
    {
        return Merchant::findByName($name);
    }

    public function findOrCreateMerchant(string $name, int $userId): int
    {
        if (empty(trim($name))) {
            return 0;
        }

        $merchant = $this->findByName($name);
        return $merchant ? (int)$merchant['id'] : $this->createMerchant($name, $userId);
    }

    public function createMerchant(string $name, int $userId): int
    {
        if (empty(trim($name))) {
            throw new InvalidArgumentException("Merchant name cannot be empty");
        }

        try {
            return $this->create([
                'name' => $name,
                'user_id' => $userId
            ]);
        } catch (\Exception $e) {
            $this->propagateOrWrapException($e, "create");
            return 0; // This line will never be reached due to the exception
        }
    }

    public function getAllMerchants(): array
    {
        return $this->all();
    }

    public function getUserMerchants(int $userId): array
    {
        try {
            return Merchant::findByUserId($userId);
        } catch (\Throwable $e) {
            $this->logger->error("Failed to retrieve user merchants", [
                'user_id' => $userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }
}
