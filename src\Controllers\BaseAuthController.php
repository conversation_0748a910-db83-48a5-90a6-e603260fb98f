<?php

declare(strict_types=1);

namespace App\Controllers;

use App\Core\Response;
use App\Models\User;

/**
 * Base controller with standardized authentication methods
 * All controllers that require authentication should extend this class
 *
 * This controller provides a robust authentication system with the following features:
 * - Session-based authentication
 * - Remember-me functionality with secure token rotation
 * - Session timeout detection and handling
 * - CSRF token validation
 * - Secure cookie handling
 * - Comprehensive error handling and logging
 *
 * Usage:
 * 1. Extend this controller in your authentication-related controllers
 * 2. Use requireAuthentication() to protect routes that require authentication
 * 3. Use getCurrentUserId() to get the ID of the authenticated user
 * 4. Use validateCsrfWithRedirect() to validate CSRF tokens
 * 5. Use logoutUser() to log out the user
 *
 * Configuration:
 * The authentication system can be configured using the config/auth.php file.
 * See the loadAuthConfig() method for default configuration values.
 */
abstract class BaseAuthController extends BaseController
{
    /**
     * Authentication configuration
     *
     * @var array
     */
    protected array $authConfig;

    /**
     * Constructor to load authentication configuration
     */
    public function __construct()
    {
        $this->loadAuthConfig();
    }

    /**
     * Load authentication configuration
     */
    private function loadAuthConfig(): void
    {
        $configPath = dirname(__DIR__, 2) . '/config/auth.php';

        // Default configuration
        $defaultConfig = [
            'session_timeout' => 1800,
            'remember_token_expiry_days' => 30,
            'preserved_session_keys' => ['redirect_after_login', 'flash_messages'],
            'session_cookie' => [
                'secure' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on',
                'httponly' => true,
                'samesite' => 'Lax'
            ],
            'remember_cookie' => [
                'name' => 'remember_token',
                'path' => '/',
                'domain' => '',
                'secure' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on',
                'httponly' => true,
                'samesite' => 'Lax'
            ]
        ];

        try {
            if (file_exists($configPath)) {
                $userConfig = require $configPath;

                // Validate the user configuration
                if (!is_array($userConfig)) {
                    error_log("Auth configuration file does not return an array. Using defaults.");
                    $this->authConfig = $defaultConfig;
                    return;
                }

                // Merge user configuration with defaults
                $this->authConfig = array_merge($defaultConfig, $userConfig);

                // Log successful configuration loading
                error_log("Auth configuration loaded successfully from {$configPath}");
            } else {
                error_log("Auth configuration file not found at {$configPath}. Using defaults.");
                $this->authConfig = $defaultConfig;
            }
        } catch (\Exception $e) {
            error_log("Error loading auth configuration: " . $e->getMessage());
            $this->authConfig = $defaultConfig;
        }
    }

    /**
     * Check if the user is authenticated
     * This method checks both session and remember token
     *
     * @return bool True if user is authenticated, false otherwise
     */
    protected function checkAuthentication(): bool
    {
        // Debug: Log session data
        error_log("SESSION DATA: " . json_encode($_SESSION));
        error_log("COOKIE DATA: " . json_encode($_COOKIE));

        // Check for session timeout
        if (isset($_SESSION['last_activity']) && isset($_SESSION['user_id'])) {
            $sessionTimeout = $this->authConfig['session_timeout'] ?? 1800;
            $timeSinceLastActivity = time() - $_SESSION['last_activity'];

            error_log(sprintf(
                "Session check: User ID: %d, Last activity: %ds ago (timeout: %ds)",
                $_SESSION['user_id'],
                $timeSinceLastActivity,
                $sessionTimeout
            ));

            // Log session activity for debugging
            if ($timeSinceLastActivity > ($sessionTimeout / 2)) {
                error_log(sprintf(
                    "Session approaching timeout: %ds since last activity (timeout: %ds)",
                    $timeSinceLastActivity,
                    $sessionTimeout
                ));
            }

            if ($timeSinceLastActivity > $sessionTimeout) {
                // Session has timed out
                error_log(sprintf(
                    "Session timeout for user %d: %ds since last activity",
                    $_SESSION['user_id'],
                    $timeSinceLastActivity
                ));
                $this->clearSession();
                return false;
            }
        } else {
            error_log("No session data found for user");
        }

        // First check if user is already authenticated via session
        if (isset($_SESSION['user_id'])) {
            // Update last activity time for session timeout tracking
            $_SESSION['last_activity'] = time();
            // Ensure authenticated flag is set for consistency
            $_SESSION['authenticated'] = true;
            error_log("User authenticated via session: " . $_SESSION['user_id']);
            return true;
        }

        // If not authenticated via session, try remember token
        $cookieName = $this->authConfig['remember_cookie']['name'] ?? 'remember_token';
        if (isset($_COOKIE[$cookieName])) {
            try {
                // Attempt to authenticate using the remember token
                $token = $_COOKIE[$cookieName];

                // Validate token format to prevent SQL injection
                if (!$this->isValidTokenFormat($token)) {
                    error_log("Invalid token format: " . substr($token, 0, 8) . "...");
                    $this->clearRememberTokenCookie();
                    return false;
                }

                $tokenData = User::findRememberToken($token);

                if (!$tokenData) {
                    error_log("Token not found in database: " . substr($token, 0, 8) . "...");
                    $this->clearRememberTokenCookie();
                    return false;
                }

                if (time() >= strtotime($tokenData['expires_at'])) {
                    error_log("Token expired: " . $tokenData['expires_at']);
                    $this->clearRememberTokenCookie();
                    return false;
                }

                $user = User::findById($tokenData['user_id']);

                if (!$user) {
                    error_log("User not found for token: " . substr($token, 0, 8) . "...");
                    $this->clearRememberTokenCookie();
                    return false;
                }

                if ($user['user_status_id'] !== User::STATUS_ACTIVE) {
                    error_log("User not active: " . $user['id']);
                    $this->clearRememberTokenCookie();
                    return false;
                }

                // Start session for the user
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['user_email'] = $user['email'];
                $_SESSION['user_name'] = $user['name'];
                $_SESSION['remember_me'] = true;
                $_SESSION['authenticated'] = true;
                $_SESSION['last_activity'] = time();

                // Regenerate session ID for security
                session_regenerate_id(true);

                // Rotate the remember token for security
                $this->rotateRememberToken($user['id'], $token);

                error_log("User authenticated via remember token: " . $user['id']);
                return true;
            } catch (\Exception $e) {
                error_log("Error authenticating with remember token: " . $e->getMessage());
                $this->clearRememberTokenCookie();
            }
        }

        return false;
    }

    /**
     * Validate token format to prevent SQL injection
     *
     * @param string $token The token to validate
     * @return bool True if the token format is valid
     */
    private function isValidTokenFormat(string $token): bool
    {
        // Token should be a hexadecimal string of appropriate length
        return preg_match('/^[a-f0-9]{64}$/i', $token) === 1;
    }

    /**
     * Clear the remember token cookie
     *
     * @return void
     */
    protected function clearRememberTokenCookie(): void
    {
        $cookieName = $this->authConfig['remember_cookie']['name'] ?? 'remember_token';
        $cookieConfig = $this->authConfig['remember_cookie'] ?? [
            'path' => '/',
            'domain' => '',
            'secure' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on',
            'httponly' => true,
            'samesite' => 'Lax'
        ];

        setcookie(
            $cookieName,
            '',
            [
                'expires' => time() - 3600,
                'path' => $cookieConfig['path'],
                'domain' => $cookieConfig['domain'],
                'secure' => $cookieConfig['secure'],
                'httponly' => $cookieConfig['httponly'],
                'samesite' => $cookieConfig['samesite']
            ]
        );
    }

    /**
     * Logout the current user
     * This method handles both session and remember token cleanup
     *
     * @return Response|null Response object if redirect is needed, null otherwise
     */
    protected function logoutUser(): ?Response
    {
        try {
            // Get the user ID before clearing the session
            $userId = $this->getCurrentUserId();

            // Get the remember token from cookie
            $cookieName = $this->authConfig['remember_cookie']['name'] ?? 'remember_token';
            $token = $_COOKIE[$cookieName] ?? null;

            // Call parent method to handle basic session cleanup
            parent::logoutUser();

            // Additional cleanup specific to BaseAuthController

            // Delete the remember token from the database
            if ($token) {
                User::manageRememberToken($userId, 'delete', $token);
                error_log("Deleted remember token for user {$userId} during logout");
            } else {
                // If no specific token, delete all tokens for this user
                User::manageRememberToken($userId, 'delete');
                error_log("Deleted all remember tokens for user {$userId} during logout");
            }

            return null;
        } catch (\Exception $e) {
            error_log("Error during logout: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Rotate the remember token for security
     * This helps prevent token theft by changing the token on each use
     *
     * @param int $userId The user ID
     * @param string $oldToken The old token to invalidate
     * @return void
     */
    private function rotateRememberToken(int $userId, string $oldToken): void
    {
        try {
            // Use the centralized token management method
            $expiryDays = $this->authConfig['remember_token_expiry_days'] ?? 30;
            $tokenData = User::manageRememberToken($userId, 'rotate', $oldToken, $expiryDays);

            // Check if there was an error
            if (isset($tokenData['error']) && $tokenData['error']) {
                error_log("Error rotating token: " . ($tokenData['message'] ?? 'Unknown error'));
                return;
            }

            // Log token creation for debugging
            error_log("Rotated remember token for user $userId: " . substr($tokenData['token'], 0, 8) . "...");

            // Set the new token in a cookie
            $cookieName = $this->authConfig['remember_cookie']['name'] ?? 'remember_token';
            $cookieConfig = $this->authConfig['remember_cookie'] ?? [
                'path' => '/',
                'domain' => '',
                'secure' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on',
                'httponly' => true,
                'samesite' => 'Lax'
            ];

            $cookieSet = setcookie(
                $cookieName,
                $tokenData['token'],
                [
                    'expires' => strtotime($tokenData['expires_at']),
                    'path' => $cookieConfig['path'],
                    'domain' => $cookieConfig['domain'],
                    'secure' => $cookieConfig['secure'],
                    'httponly' => $cookieConfig['httponly'],
                    'samesite' => $cookieConfig['samesite']
                ]
            );

            if (!$cookieSet) {
                error_log("Failed to set remember token cookie for user $userId");
            }
        } catch (\Exception $e) {
            error_log("Error rotating remember token: " . $e->getMessage());
        }
    }

    /**
     * Clear the session data
     *
     * @return void
     */
    private function clearSession(): void
    {
        // Save data we want to keep based on configuration
        $preservedData = [];
        $preservedKeys = $this->authConfig['preserved_session_keys'] ?? ['redirect_after_login'];

        foreach ($preservedKeys as $key) {
            if (isset($_SESSION[$key])) {
                $preservedData[$key] = $_SESSION[$key];
            }
        }

        // Unset all session variables
        $_SESSION = [];

        // Restore preserved data
        foreach ($preservedData as $key => $value) {
            $_SESSION[$key] = $value;
        }

        // If a session cookie exists, expire it
        if (isset($_COOKIE[session_name()])) {
            $params = session_get_cookie_params();
            $cookieConfig = $this->authConfig['session_cookie'] ?? [
                'secure' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on',
                'httponly' => true,
                'samesite' => 'Lax'
            ];

            setcookie(
                session_name(),
                '',
                [
                    'expires' => time() - 3600,
                    'path' => $params['path'],
                    'domain' => $params['domain'],
                    'secure' => $cookieConfig['secure'] ?? $params['secure'],
                    'httponly' => $cookieConfig['httponly'] ?? $params['httponly'],
                    'samesite' => $cookieConfig['samesite'] ?? $params['samesite'] ?? 'Lax'
                ]
            );
        }
    }

    /**
     * Require authentication for an action
     * If user is not authenticated, redirects to login page
     *
     * @param string $message Message to display on redirect
     * @return Response|null Response object if redirect is needed, null if authenticated
     */
    protected function requireAuthentication(string $message = 'Please login to continue'): ?Response
    {
        error_log("requireAuthentication: Checking authentication");

        // Force a full authentication check
        $isAuthenticated = $this->checkAuthentication();

        error_log("requireAuthentication: Authentication result: " . ($isAuthenticated ? 'true' : 'false'));

        if (!$isAuthenticated) {
            error_log("requireAuthentication: User not authenticated, redirecting to login");

            // Store the current URL for redirect after login
            if (isset($_SERVER['REQUEST_URI'])) {
                $redirectUrl = $_SERVER['REQUEST_URI'];
                error_log("requireAuthentication: Storing redirect URL: " . $redirectUrl);

                // Validate that the URL is internal to prevent open redirect vulnerabilities
                if ($this->isInternalUrl($redirectUrl)) {
                    $_SESSION['redirect_after_login'] = $redirectUrl;
                }
            }

            return $this->redirect('/login', $message);
        }

        error_log("requireAuthentication: User is authenticated, proceeding");
        return null;
    }

    /**
     * Check if a URL is internal to the application
     *
     * @param string $url The URL to check
     * @return bool True if the URL is internal
     */
    protected function isInternalUrl(string $url): bool
    {
        // Check if the URL is relative (starts with / or ./)
        if (str_starts_with($url, '/') || str_starts_with($url, './')) {
            return true;
        }

        // Check if the URL is for the current host
        $host = $_SERVER['HTTP_HOST'] ?? '';
        if (!empty($host) && str_contains($url, $host)) {
            return true;
        }

        return false;
    }

    /**
     * Validate CSRF token with improved error handling
     *
     * @param string|null $token The CSRF token to validate
     * @param string $redirectUrl URL to redirect to on failure
     * @param string $errorMessage Error message to display on failure
     * @return Response|null Response object if redirect is needed, null if token is valid
     */
    protected function validateCsrfWithRedirect(
        ?string $token,
        string $redirectUrl,
        string $errorMessage = 'Invalid security token'
    ): ?Response {
        try {
            $this->validateCsrf($token);
            return null;
        } catch (\Exception $e) {
            error_log("CSRF validation error: " . $e->getMessage());
            return $this->redirect($redirectUrl, $errorMessage, 'error');
        }
    }

    /**
     * Get the current user ID
     *
     * @return int|null User ID if authenticated, null otherwise
     */
    protected function getCurrentUserId(): ?int
    {
        // First check if we need to authenticate the user
        if (!isset($_SESSION['user_id'])) {
            // Try to authenticate with remember token
            $this->checkAuthentication();
        }

        // Now check if we have a user ID
        if (isset($_SESSION['user_id'])) {
            error_log("getCurrentUserId: Returning user ID: " . $_SESSION['user_id']);
            return (int)$_SESSION['user_id'];
        }

        error_log("getCurrentUserId: No user ID found in session");
        return null;
    }

    /**
     * Get the current user
     *
     * @return array|null User data if authenticated, null otherwise
     */
    protected function getCurrentUser(): ?array
    {
        $userId = $this->getCurrentUserId();
        return $userId ? User::findById($userId) : null;
    }

    /**
     * Check if the current user has permission to access a resource
     *
     * @param int $resourceUserId User ID of the resource owner
     * @return bool True if user has permission, false otherwise
     */
    protected function hasPermission(int $resourceUserId): bool
    {
        $currentUserId = $this->getCurrentUserId();
        return $currentUserId !== null && $currentUserId === $resourceUserId;
    }

    /**
     * Logout the current user
     *
     * @return Response Redirect response
     */
    protected function logout(): Response
    {
        // Clear remember token if exists
        if (isset($_COOKIE['remember_token'])) {
            $token = $_COOKIE['remember_token'];
            User::deleteRememberToken($token);

            setcookie(
                'remember_token',
                '',
                [
                    'expires' => time() - 3600,
                    'path' => '/',
                    'domain' => '',
                    'secure' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on',
                    'httponly' => true,
                    'samesite' => 'Lax'
                ]
            );
        }

        // Clear session
        session_unset();
        session_destroy();

        // Clear session cookie
        if (isset($_COOKIE[session_name()])) {
            $params = session_get_cookie_params();
            setcookie(
                session_name(),
                '',
                [
                    'expires' => time() - 3600,
                    'path' => $params['path'],
                    'domain' => $params['domain'],
                    'secure' => $params['secure'],
                    'httponly' => $params['httponly'],
                    'samesite' => $params['samesite'] ?? 'Lax'
                ]
            );
        }

        return $this->redirect('/', 'You have been logged out');
    }
}
