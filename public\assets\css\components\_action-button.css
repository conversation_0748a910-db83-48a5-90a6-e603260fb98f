/* ===== ACTION BUTTON COMPONENT ===== */

.action-button {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  text-decoration: none;
  color: var(--text-color);
  font-weight: var(--font-weight-medium);
  transition: var(--transition);
  padding: var(--spacing-sm) var(--spacing);
  border-radius: var(--border-radius);
  border: none;
  cursor: pointer;
}

.action-button:hover {
  color: var(--primary-color);
}

.action-button .icon,
.action-button .svg-icon {
  color: var(--primary-color);
}

/* Action Button Sizes */
.action-button.small {
  font-size: var(--font-size-sm);
}

.action-button.large {
  font-size: var(--font-size-lg);
}

/* Icon-only Action Button */
.action-button.icon-only {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  gap: 0;
  width: var(--action-button-fluid-size);
  height: var(--action-button-fluid-size);
}

.action-button.icon-only .icon,
.action-button.icon-only .svg-icon {
    width: var(--action-icon-fluid-size);
    height: var(--action-icon-fluid-size);
    margin: 0;
}

/* Action Button Types */
.action-button.primary {
  color: var(--primary-color);
}

.action-button.primary:hover {
  color: var(--primary-dark);
}

.action-button.secondary {
  color: var(--text-muted);
}

.action-button.secondary:hover {
  color: var(--text-color);
}

.action-button.danger {
  color: var(--danger-color);
}

.action-button.danger:hover {
  color: var(--danger-dark);
}
