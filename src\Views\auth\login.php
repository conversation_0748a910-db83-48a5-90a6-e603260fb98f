<?php
// Get old input values
$oldEmail = $old['email'] ?? '';

// Prepare content for the layout
$title = 'Login';
ob_start();
?>

<form method="POST" action="/authenticate">
    <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($csrf_token) ?>">

    <!-- Email Field -->
    <div class="form-group">
        <label for="email">Email Address</label>
        <input type="email" name="email" id="email"
               value="<?= htmlspecialchars($oldEmail) ?>"
               required autofocus aria-describedby="email-help">
        <small id="email-help" class="form-hint">Enter the email address you registered with</small>
    </div>

    <!-- Password Field -->
    <div class="form-group">
        <label for="password">Password</label>
        <input type="password" name="password" id="password" required>
    </div>

    <!-- Remember Me -->
    <div class="form-group checkbox-group">
        <?php
        // Debug output
        error_log("login.php - remember_me_default value: " .
            (isset($remember_me_default) ? ($remember_me_default ? 'true' : 'false') : 'not set'));

        // Check for the remember_me cookie directly
        $rememberMeCookie = isset($_COOKIE['remember_me']) ? $_COOKIE['remember_me'] : null;
        error_log("login.php - remember_me cookie value: " . ($rememberMeCookie ?? 'not set'));

        // Determine if the checkbox should be checked
        $isChecked = $rememberMeCookie === 'true';
        error_log("login.php - checkbox should be checked: " . ($isChecked ? 'yes' : 'no'));
        ?>
        <input type="checkbox" name="remember" id="remember" value="1" <?= $isChecked ? 'checked' : '' ?>>
        <label for="remember">Remember me</label>
    </div>

    <!-- Form Actions -->
    <div class="form-actions">
        <button type="submit" class="button">Login</button>
    </div>
</form>

<script>
    // Store the remember me setting in localStorage and cookie when the form is submitted
    document.querySelector('form').addEventListener('submit', function() {
        const rememberMe = document.getElementById('remember').checked ? 'true' : 'false';
        localStorage.setItem('remember_me', rememberMe);

        // Set a cookie that expires in 30 days
        const expiryDate = new Date();
        expiryDate.setDate(expiryDate.getDate() + 30);
        document.cookie = `remember_me=${rememberMe}; expires=${expiryDate.toUTCString()}; path=/; SameSite=Lax`;
    });
</script>

<div class="form-links">
    <a href="/register">Don't have an account? Register</a>
    <a href="/password/reset">Forgot your password?</a>
</div>

<?php
$content = ob_get_clean();
require __DIR__ . '/_layout.php';
?>
