<?php

declare(strict_types=1);

namespace App\Models;

use App\Exceptions\InvalidArgumentException;

class Category extends BaseModel
{
    private const MAX_NAME_LENGTH = 255;

    protected static function getTableName(): string
    {
        return 'categories';
    }

    private static function getColumnList(): string
    {
        return "id, name, description";
    }

    public static function find(int $id): ?array
    {
        // Use a simpler query that only selects columns we know exist
        $sql = sprintf(
            "SELECT %s FROM %s WHERE id = ?",
            self::getColumnList(),
            self::getTableName()
        );

        return self::fetchOne($sql, [$id]);
    }

    public static function findByName(string $name): ?array
    {
        $sql = sprintf(
            "SELECT
                %s
             FROM %s
             WHERE LOWER(name) = LOWER(?)",
            self::getColumnList(),
            self::getTableName()
        );

        return self::fetchOne($sql, [$name]);
    }

    public static function all(): array
    {
        // Query that selects only columns we know exist
        $sql = sprintf(
            "SELECT %s FROM %s ORDER BY name ASC",
            self::getColumnList(),
            self::getTableName()
        );

        return self::fetchMany($sql);
    }

    /**
     * Get all system categories
     *
     * @return array The system categories
     */
    public static function getSystemCategories(): array
    {
        // All categories are considered system categories in this implementation
        return self::all();
    }

    public static function createCategory(string $name, ?string $description = null): int
    {
        self::validateName($name);
        self::ensureUniqueName($name);

        $data = [
            'name' => $name,
            'description' => $description
        ];

        return self::create($data);
    }

    public static function create(array $data): int
    {
        if (isset($data['name'])) {
            self::validateName($data['name']);
            self::ensureUniqueName($data['name']);
        }

        return self::transaction(function () use ($data) {
            $sql = sprintf(
                "INSERT INTO %s (name, description) VALUES (:name, :description)",
                self::getTableName()
            );

            self::executeSql($sql, $data);
            return (int)self::getDb()->lastInsertId();
        });
    }

    public static function updateCategory(int $id, ?string $name = null, ?string $description = null): bool
    {
        $category = self::find($id);
        if (!$category) {
            return false;
        }

        $data = [];

        if ($name !== null) {
            self::validateName($name);
            self::preventNameConflict($id, $name);
            $data['name'] = $name;
        }

        if ($description !== null) {
            $data['description'] = $description;
        }

        if (empty($data)) {
            return false;
        }

        return self::update($id, $data);
    }

    public static function update(int $id, array $data): bool
    {
        $category = self::find($id);
        if (!$category) {
            return false;
        }

        if (isset($data['name'])) {
            self::validateName($data['name']);
            self::preventNameConflict($id, $data['name']);
        }

        if (empty($data)) {
            return false;
        }

        return self::transaction(function () use ($data, $id) {
            $setClause = implode(', ', array_map(fn($col) => "{$col} = :{$col}", array_keys($data)));

            $sql = sprintf(
                "UPDATE %s SET %s WHERE id = :id",
                self::getTableName(),
                $setClause
            );

            $data['id'] = $id;
            return self::executeSql($sql, $data);
        });
    }

    public static function delete(int $id): bool
    {
        $category = self::find($id);
        if (!$category || self::isInUse($id)) {
            return false;
        }

        return self::transaction(function () use ($id) {
            $sql = sprintf("DELETE FROM %s WHERE id = ?", self::getTableName());
            return self::executeSql($sql, [$id]);
        });
    }

    public static function isInUse(int $id): bool
    {
        // Simplified query without index hint for better compatibility
        return (bool)self::fetchScalar(
            "SELECT EXISTS(
                SELECT 1 FROM expenses
                WHERE category_id = ? AND deleted_at IS NULL
                LIMIT 1
            )",
            [$id]
        );
    }

    public static function expenseCount(int $id, ?int $userId = null): int
    {
        $sql = "SELECT COUNT(id) FROM expenses
                WHERE category_id = ? AND deleted_at IS NULL";
        $params = [$id];

        if ($userId !== null) {
            $sql .= " AND user_id = ?";
            $params[] = $userId;
        }

        return (int)self::fetchScalar($sql, $params);
    }

    public static function totalExpenses(int $id, ?int $userId = null): float
    {
        $sql = "SELECT COALESCE(SUM(amount), 0) FROM expenses
                WHERE category_id = ? AND deleted_at IS NULL";
        $params = [$id];

        if ($userId !== null) {
            $sql .= " AND user_id = ?";
            $params[] = $userId;
        }

        return (float)self::fetchScalar($sql, $params);
    }

    public static function getExpenseBreakdown(int $userId): array
    {
        $dateRange = self::getCurrentMonthDateRange();

        // Simplified query for maximum compatibility
        $sql = "SELECT
                    c.id, c.name, c.description,
                    COALESCE(SUM(e.amount), 0) as total,
                    COUNT(e.id) as count
                FROM categories c
                LEFT JOIN expenses e
                    ON c.id = e.category_id
                    AND e.user_id = ?
                    AND e.date >= ?
                    AND e.date < ?
                    AND e.deleted_at IS NULL
                GROUP BY c.id, c.name, c.description
                ORDER BY c.name ASC";

        return self::fetchMany($sql, [$userId, $dateRange['start'], $dateRange['end']]);
    }

    private static function getCurrentMonthDateRange(): array
    {
        return [
            'start' => date('Y-m-01'),
            'end' => date('Y-m-01', strtotime('+1 month'))
        ];
    }

    // Validation methods
    private static function validateName(string $name): void
    {
        $trimmed = trim($name);
        if (self::isNameInvalid($trimmed)) {
            throw new InvalidArgumentException(
                sprintf("Name must be 1-%d characters", self::MAX_NAME_LENGTH)
            );
        }
    }

    private static function isNameInvalid(string $name): bool
    {
        return empty($name) || strlen($name) > self::MAX_NAME_LENGTH;
    }

    private static function ensureUniqueName(string $name): void
    {
        $existing = self::findByName($name);
        if ($existing) {
            throw new InvalidArgumentException('Category already exists');
        }
    }

    private static function preventNameConflict(int $currentId, string $name): void
    {
        $existing = self::findByName($name);
        if ($existing && $existing['id'] !== $currentId) {
            throw new InvalidArgumentException('Category name already in use');
        }
    }

    // User category retrieval
    public static function getUserCategory(int $id): ?array
    {
        return self::fetchOne(
            "SELECT
                id,
                user_id,
                name,
                description,
                deleted_at,
                created_at,
                updated_at
             FROM user_categories
             WHERE id = ? AND deleted_at IS NULL",
            [$id]
        );
    }

    public static function getUserCategoryByName(int $userId, string $name): ?array
    {
        return self::fetchOne(
            "SELECT
                id,
                user_id,
                name,
                description,
                deleted_at,
                created_at,
                updated_at
             FROM user_categories
             WHERE user_id = ?
               AND LOWER(name) = LOWER(?)
               AND deleted_at IS NULL",
            [$userId, $name]
        );
    }

    public static function getUserCategories(int $userId): array
    {
        return self::fetchMany(
            "SELECT
                id,
                user_id,
                name,
                description,
                deleted_at,
                created_at,
                updated_at
             FROM user_categories
             WHERE user_id = ?
               AND deleted_at IS NULL
             ORDER BY name ASC",
            [$userId]
        );
    }

    // User category management
    public static function createUserCategory(
        int $userId,
        string $name,
        ?string $description = null
    ): int {
        self::validateName($name);
        self::validateUserCategoryUnique($userId, $name);

        return self::transaction(function () use ($userId, $name, $description) {
            $sql = "INSERT INTO user_categories
                    (user_id, name, description, created_at, updated_at)
                    VALUES (?, ?, ?, NOW(), NOW())";

            self::executeSql($sql, [$userId, $name, $description]);
            return (int)self::getDb()->lastInsertId();
        });
    }

    private static function validateUserCategoryUnique(int $userId, string $name): void
    {
        $existing = self::getUserCategoryByName($userId, $name);
        if ($existing) {
            throw new InvalidArgumentException('Category already exists for this user');
        }
    }

    public static function updateUserCategory(
        int $id,
        int $userId,
        ?string $name = null,
        ?string $description = null
    ): bool {
        $category = self::getUserCategory($id);
        if (!$category || $category['user_id'] != $userId) {
            return false;
        }

        $updateData = self::prepareUserCategoryUpdateData($id, $userId, $name, $description);
        if (empty($updateData['fields'])) {
            return false;
        }

        $updateFields = $updateData['fields'];
        $params = $updateData['params'];

        $updateFields[] = 'updated_at = NOW()';
        $sql = "UPDATE user_categories SET " . implode(', ', $updateFields) .
               " WHERE id = ? AND user_id = ? AND deleted_at IS NULL";

        return self::transaction(fn () => self::executeSql($sql, [...$params, $id, $userId]));
    }

    private static function prepareUserCategoryUpdateData(
        int $id,
        int $userId,
        ?string $name,
        ?string $description
    ): array {
        $updateFields = [];
        $params = [];

        if ($name !== null) {
            self::validateName($name);
            self::validateUserCategoryName($id, $userId, $name);
            $updateFields[] = 'name = ?';
            $params[] = $name;
        }

        if ($description !== null) {
            $updateFields[] = 'description = ?';
            $params[] = $description;
        }

        return [
            'fields' => $updateFields,
            'params' => $params
        ];
    }

    private static function validateUserCategoryName(int $id, int $userId, string $name): void
    {
        $existing = self::getUserCategoryByName($userId, $name);
        if ($existing && $existing['id'] !== $id) {
            throw new InvalidArgumentException('Category name already in use');
        }
    }

    public static function deleteUserCategory(int $id, int $userId, bool $hardDelete = false): bool
    {
        $category = self::getUserCategory($id);
        if (!self::canDeleteUserCategory($category, $userId, $id)) {
            return false;
        }

        return self::transaction(function () use ($id, $userId, $hardDelete) {
            $sql = $hardDelete
                ? "DELETE FROM user_categories WHERE id = ? AND user_id = ?"
                : "UPDATE user_categories SET deleted_at = NOW(), updated_at = NOW()
                   WHERE id = ? AND user_id = ? AND deleted_at IS NULL";

            return self::executeSql($sql, [$id, $userId]);
        });
    }

    private static function canDeleteUserCategory(?array $category, int $userId, int $id): bool
    {
        return $category && $category['user_id'] == $userId && !self::isUserCategoryInUse($id);
    }

    public static function isUserCategoryInUse(int $id): bool
    {
        // Simplified query without index hint for better compatibility
        return (bool)self::fetchScalar(
            "SELECT EXISTS(
                SELECT 1 FROM expenses
                WHERE (user_category_id = ? OR category_id = ?) AND deleted_at IS NULL
                LIMIT 1
            )",
            [$id, $id]
        );
    }

    public static function transferExpenses(int $fromCategoryId, int $toCategoryId, ?int $userId = null): int
    {
        return self::transaction(function () use ($fromCategoryId, $toCategoryId, $userId) {
            $sql = "UPDATE expenses SET
                    category_id = ?,
                    updated_at = NOW()
                    WHERE category_id = ? AND deleted_at IS NULL";
            $params = [$toCategoryId, $fromCategoryId];

            if ($userId !== null) {
                $sql .= " AND user_id = ?";
                $params[] = $userId;
            }

            $stmt = self::getDb()->prepare($sql);
            $stmt->execute($params);
            return $stmt->rowCount();
        });
    }

    public static function validateCategoryExists(int $categoryId): bool
    {
        $category = self::find($categoryId);
        return $category !== null;
    }

    public static function validateUserCategoryExists(int $categoryId, int $userId): bool
    {
        $category = self::getUserCategory($categoryId);
        return $category !== null && $category['user_id'] === $userId;
    }

    /**
     * Validate that a category exists and belongs to the user
     *
     * @param int $categoryId The category ID
     * @param int|null $userId The user ID
     * @return array|null The category data or null if not found
     */
    public static function validateCategory(int $categoryId, ?int $userId = null): ?array
    {
        // First check if it's a system category
        $systemCategory = self::find($categoryId);
        if ($systemCategory) {
            // Convert to array to ensure we can add properties
            $result = [];
            foreach ($systemCategory as $key => $value) {
                $result[$key] = $value;
            }
            return $result;
        }

        // If not a system category, check if it's a user category
        if ($userId !== null) {
            $userCategory = self::getUserCategory($categoryId);
            if ($userCategory && $userCategory['user_id'] == $userId) {
                return $userCategory;
            }
        }

        return null;
    }

    public static function getCategoryWithExpenses(int $categoryId, ?int $userId = null): ?array
    {
        $category = self::find($categoryId);

        if (!$category) {
            return null;
        }

        // Convert to array to ensure we can add properties
        $result = [];
        foreach ($category as $key => $value) {
            $result[$key] = $value;
        }

        // Get expense stats in a single query
        $sql = "SELECT
                  COUNT(id) as expense_count,
                  COALESCE(SUM(amount), 0) as total_amount
                FROM expenses
                WHERE category_id = ?
                  AND deleted_at IS NULL";
        $params = [$categoryId];

        if ($userId !== null) {
            $sql .= " AND user_id = ?";
            $params[] = $userId;
        }

        $stats = self::fetchOne($sql, $params);

        if ($stats) {
            $result['expense_count'] = (int)$stats['expense_count'];
            $result['total_amount'] = (float)$stats['total_amount'];
        } else {
            $result['expense_count'] = 0;
            $result['total_amount'] = 0.0;
        }

        // Get recent expenses with explicit column selection
        $sql = "SELECT
                  id,
                  amount,
                  description,
                  date,
                  merchant_id
                FROM expenses
                WHERE category_id = ?
                  AND deleted_at IS NULL";
        $params = [$categoryId];

        if ($userId !== null) {
            $sql .= " AND user_id = ?";
            $params[] = $userId;
        }

        $sql .= " ORDER BY date DESC LIMIT 10";

        $result['recent_expenses'] = self::fetchMany($sql, $params);

        return $result;
    }

    public static function getUserCategoryWithExpenses(int $categoryId, int $userId): ?array
    {
        $category = self::getUserCategory($categoryId);

        if (!$category || $category['user_id'] !== $userId) {
            return null;
        }

        $result = $category;

        // Get expense stats in a single query
        $sql = "SELECT
                  COUNT(id) as expense_count,
                  COALESCE(SUM(amount), 0) as total_amount
                FROM expenses
                WHERE (user_category_id = ? OR category_id = ?)
                  AND user_id = ?
                  AND deleted_at IS NULL";

        $stats = self::fetchOne($sql, [$categoryId, $categoryId, $userId]);

        if ($stats) {
            $result['expense_count'] = (int)$stats['expense_count'];
            $result['total_amount'] = (float)$stats['total_amount'];
        } else {
            $result['expense_count'] = 0;
            $result['total_amount'] = 0.0;
        }

        // Get recent expenses with explicit column selection
        $sql = "SELECT
                  id,
                  amount,
                  description,
                  date,
                  merchant_id
                FROM expenses
                WHERE (user_category_id = ? OR category_id = ?)
                  AND user_id = ?
                  AND deleted_at IS NULL
                ORDER BY date DESC
                LIMIT 10";

        $result['recent_expenses'] = self::fetchMany($sql, [$categoryId, $categoryId, $userId]);

        return $result;
    }

    public static function deleteWithValidation(int $categoryId): bool
    {
        return self::transaction(function () use ($categoryId) {
            $category = self::find($categoryId);

            if (!$category) {
                throw new InvalidArgumentException("Category not found");
            }

            if (self::isInUse($categoryId)) {
                throw new InvalidArgumentException("Cannot delete category with expenses");
            }

            return self::delete($categoryId);
        });
    }

    public static function deleteUserCategoryWithValidation(
        int $categoryId,
        int $userId,
        bool $hardDelete = false
    ): bool {
        return self::transaction(function () use ($categoryId, $userId, $hardDelete) {
            $category = self::getUserCategory($categoryId);

            if (!$category) {
                throw new InvalidArgumentException("User category not found");
            }

            if ($category['user_id'] !== $userId) {
                throw new InvalidArgumentException("Category does not belong to user");
            }

            if (self::isUserCategoryInUse($categoryId)) {
                throw new InvalidArgumentException("Cannot delete category with expenses");
            }

            return self::deleteUserCategory($categoryId, $userId, $hardDelete);
        });
    }

    /**
     * Get expense counts for all categories for a specific user
     *
     * @param int $userId The user ID
     * @return array Array keyed by category_id with category_name and expense_count
     * @throws \App\Exceptions\DatabaseException
     */
    public static function getUserExpenseCounts(int $userId): array
    {
        try {
            $sql = "SELECT c.id AS category_id,
                           c.name AS category_name,
                           COUNT(e.id) AS expense_count
                      FROM categories c
                      LEFT JOIN expenses e
                        ON e.category_id = c.id
                       AND e.user_id = ?
                       AND e.deleted_at IS NULL
                     GROUP BY c.id, c.name
                     ORDER BY c.name";

            $results = static::fetchMany($sql, [$userId]);

            // Transform results to be keyed by category_id
            $categoryCounts = [];
            foreach ($results as $row) {
                $categoryCounts[$row['category_id']] = [
                    'category_name' => $row['category_name'],
                    'expense_count' => (int)$row['expense_count']
                ];
            }

            return $categoryCounts;
        } catch (\PDOException $e) {
            error_log("Category::getUserExpenseCounts() - Database error: " . $e->getMessage());
            error_log("Category::getUserExpenseCounts() - User ID: " . $userId);

            // Make sure we're passing an integer code to avoid type errors
            $code = is_numeric($e->getCode()) ? (int)$e->getCode() : 0;
            throw new \App\Exceptions\DatabaseException("Failed to get user expense counts", $code, $e);
        }
    }
}
