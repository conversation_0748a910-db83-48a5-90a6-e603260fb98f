<?php

declare(strict_types=1);

namespace App\Core\Security;

final class SecurityHeaders
{
    private const DEFAULT_HEADERS = [
        'X-Content-Type-Options' => 'nosniff',
        'X-Frame-Options' => 'DENY',
        'X-XSS-Protection' => '1; mode=block',
        'Referrer-Policy' => 'strict-origin-when-cross-origin',
        'Permissions-Policy' => 'geolocation=(), microphone=(), camera=(), payment=(), usb=(), magnetometer=(), gyroscope=(), speaker=()',
        'Content-Security-Policy' => "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self'; connect-src 'self'; frame-ancestors 'none'; base-uri 'self'; form-action 'self'",
        'Strict-Transport-Security' => 'max-age=31536000; includeSubDomains; preload'
    ];

    private array $headers = [];
    private bool $isHttps = false;

    public function __construct()
    {
        $this->isHttps = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on';
        $this->headers = self::DEFAULT_HEADERS;
        
        // Only add HSTS header for HTTPS connections
        if (!$this->isHttps) {
            unset($this->headers['Strict-Transport-Security']);
        }
    }

    public function setHeader(string $name, string $value): self
    {
        $this->headers[$name] = $value;
        return $this;
    }

    public function removeHeader(string $name): self
    {
        unset($this->headers[$name]);
        return $this;
    }

    public function getHeaders(): array
    {
        return $this->headers;
    }

    public function apply(): void
    {
        if (headers_sent()) {
            return;
        }

        foreach ($this->headers as $name => $value) {
            header("{$name}: {$value}");
        }
    }

    public static function applyDefault(): void
    {
        $instance = new self();
        $instance->apply();
    }

    public function withCSRFToken(string $token): self
    {
        // Add CSRF token to CSP nonce if needed
        $csp = $this->headers['Content-Security-Policy'] ?? '';
        if (strpos($csp, "'nonce-") === false) {
            $nonce = base64_encode($token);
            $csp = str_replace(
                "script-src 'self' 'unsafe-inline'",
                "script-src 'self' 'nonce-{$nonce}'",
                $csp
            );
            $this->headers['Content-Security-Policy'] = $csp;
        }
        return $this;
    }
}