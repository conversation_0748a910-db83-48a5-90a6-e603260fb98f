/* Page‐specific styles for Expense Details */

@import url('/assets/css/foundation/_variables.css');
@import "../components/_cards.css";
@import "../components/_buttons.css";
@import "../utilities/_animations.css";

/* SVG icon overrides */
.svg-icon {
  width: var(--icon-size-sm);
  height: var(--icon-size-sm);
  vertical-align: middle;
  fill: currentColor;
  margin-right: var(--spacing-sm);
  flex-shrink: 0;
  stroke-width: 1px;
  transition: transform var(--timing-gentle);
}
button:hover .svg-icon,
a:hover .svg-icon {
  transform: translateY(-1px);
}

/* Standardized button focus styles */
.button:focus {
  outline: none;
  box-shadow: var(--focus-ring);
}

/* Page layout */
.expense-container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

/* Header styling */
.page-header {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: var(--light-color);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-lg);
  margin-bottom: var(--spacing-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: var(--box-shadow);
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: transform var(--timing-gentle), box-shadow var(--timing-gentle);
}
.page-header:hover {
  transform: translateY(-2px);
  box-shadow: var(--hover-lift);
}
.page-header::before {
  content: "";
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  background: radial-gradient(circle at top right,
    rgba(255,255,255,0.1) 0%,
    transparent 70%);
  pointer-events: none;
  opacity: 0.7;
  transition: opacity var(--timing-gentle);
}
.page-header:hover::before {
  opacity: 1;
}

.header-content h2 {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  text-shadow: 0 1px 1px rgba(0, 0, 0, 0.2);
  letter-spacing: 0.01em;
}

.header-actions .button {
  background: rgba(255,255,255,0.2);
  border: 1px solid rgba(255,255,255,0.25);
  color: var(--light-color);
  padding: var(--spacing-sm) var(--spacing);
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  text-decoration: none;
  transition: all var(--timing-gentle);
  box-shadow: var(--box-shadow-sm);
  font-weight: var(--font-weight-medium);
  backdrop-filter: blur(4px);
}
.header-actions .button:hover {
  background: rgba(255,255,255,0.3);
  transform: translateY(-2px);
  box-shadow: var(--hover-lift);
}
.header-actions .button:active {
  transform: translateY(0);
  background: rgba(255,255,255,0.15);
  box-shadow: var(--active-press);
}

/* Expense header */
.expense-header {
  background-color: var(--light-color);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--box-shadow);
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--spacing-md);
  border: 1px solid var(--border-color);
  position: relative;
  overflow: hidden;
  transition: transform var(--timing-gentle), box-shadow var(--timing-gentle);
}
.expense-header:hover {
  transform: translateY(-2px);
  box-shadow: var(--hover-lift);
}
.expense-header::before {
  content: "";
  position: absolute;
  top: 0; left: 0;
  width: 100%; height: 4px;
  background: linear-gradient(to right,
    var(--primary-color),
    var(--secondary-color));
  opacity: 0.7;
  transition: opacity var(--timing-gentle), height var(--timing-gentle);
}
.expense-header:hover::before {
  opacity: 0.9;
  height: var(--spacing-xs);
}
.expense-header-content {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.expense-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--text-color);
  margin-bottom: var(--spacing);
  line-height: var(--line-height-tight);
  word-wrap: break-word;
  letter-spacing: -0.01em;
  position: relative;
  display: inline-block;
  transition: color var(--timing-gentle);
}
.expense-header:hover .expense-title {
  color: var(--primary-dark);
}
.expense-title::after {
  content: "";
  position: absolute;
  bottom: calc(-1 * var(--spacing-xs)); left: 0;
  width: var(--spacing-xl); height: var(--spacing-xs);
  background: linear-gradient(to right,
    var(--primary-color),
    var(--primary-light),
    transparent);
  border-radius: var(--border-radius-full);
  opacity: 0.7;
  transition: width var(--timing-gentle), opacity var(--timing-gentle), background var(--timing-gentle);
}
.expense-header:hover .expense-title::after {
  width: 80px;
  opacity: 1;
  background: linear-gradient(to right,
    var(--primary-dark),
    var(--primary-color),
    var(--primary-light),
    transparent);
}

/* Expense meta */
.expense-meta {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing);
  margin-bottom: var(--spacing);
  align-items: center;
}

/* Meta group container */
.meta-group {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing);
  align-items: center;
  padding: var(--spacing-sm);
  border-radius: var(--border-radius);
  transition: all var(--timing-gentle);
  background: rgba(var(--grey-rgb), 0.02);
  border: 1px solid transparent;
}
.meta-group:hover {
  background: rgba(var(--grey-rgb), 0.05);
  border-color: rgba(var(--grey-rgb), 0.1);
  box-shadow: var(--hover-lift);
  transform: translateY(-2px);
}
.expense-date {
  display: flex;
  align-items: center;
  color: var(--text-color);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  background-color: rgba(var(--primary-rgb), 0.08);
  padding: var(--spacing-sm) var(--spacing);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-sm);
  min-width: 200px;
  justify-content: center;
  border: 1px solid rgba(var(--primary-rgb), 0.15);
  transition: all var(--timing-gentle);
}
.expense-date:hover {
  background-color: rgba(var(--primary-rgb), 0.12);
  transform: translateY(-1px);
  box-shadow: var(--hover-lift);
}
.expense-date .svg-icon {
  color: var(--primary-color);
  margin-right: var(--spacing-sm);
}
.expense-date:hover .svg-icon {
  transform: scale(1.1);
}
.badge,
.category-badge {
  display: inline-flex;
  align-items: center;
  background: rgba(var(--secondary-rgb), 0.08);
  color: var(--secondary-color);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  letter-spacing: 0.01em;
  border: 1px solid rgba(var(--secondary-rgb), 0.2);
  box-shadow: var(--box-shadow-sm);
  max-width: fit-content;
  transition: all var(--timing-gentle);
}
.badge:hover,
.category-badge:hover {
  background: rgba(var(--secondary-rgb), 0.12);
  transform: translateY(-1px);
  box-shadow: var(--hover-lift);
}

/* Actions */
.expense-actions {
  display: flex;
  gap: var(--spacing-sm);
  align-items: flex-start;
}

/* Action tabs */
.action-tabs {
  display: flex;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-xs);
  background: rgba(var(--grey-rgb), 0.03);
  border-radius: var(--border-radius-lg);
  border: 1px solid var(--border-color);
  box-shadow: var(--box-shadow-sm);
}

.action-tabs .button {
  background: transparent;
  border: 1px solid transparent;
  color: var(--text-color-muted);
  padding: var(--spacing-sm) var(--spacing);
  border-radius: var(--border-radius);
  font-weight: var(--font-weight-medium);
  transition: all var(--timing-gentle);
  text-decoration: none;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.action-tabs .button:hover {
  background: rgba(var(--primary-rgb), 0.08);
  color: var(--primary-color);
  border-color: rgba(var(--primary-rgb), 0.15);
  transform: translateY(-1px);
  box-shadow: var(--hover-lift);
}

.action-tabs .button.active {
  background: var(--primary-color);
  color: var(--light-color);
  border-color: var(--primary-dark);
  box-shadow: var(--box-shadow);
  font-weight: var(--font-weight-semibold);
}

.action-tabs .button.active:hover {
  background: var(--primary-dark);
  transform: translateY(-2px);
  box-shadow: var(--hover-lift);
}

.action-tabs .button.active::before {
  content: "";
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  background: linear-gradient(135deg,
    rgba(255,255,255,0.2) 0%,
    transparent 50%);
  pointer-events: none;
}

/* Card grid & connector */
.card-grid.expense-card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  padding: 0;
}
.related-sections {
  position: relative;
}
.related-sections::before {
  content: "";
  position: absolute;
  top: 0; left: 50%;
  height: 100%; width: 2px;
  background: linear-gradient(to bottom,
    transparent 0%,
    var(--primary-light) 10%,
    var(--secondary-light) 90%,
    transparent 100%);
  opacity: 0.25;
  transition: opacity var(--timing-gentle);
  z-index: 0;
}
.related-sections:hover::before {
  opacity: 0.4;
}

/* Document viewer */
.document-viewer {
  background: var(--light-color);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--box-shadow);
  border: 1px solid var(--border-color);
  position: relative;
  overflow: hidden;
  transition: transform var(--timing-gentle), box-shadow var(--timing-gentle);
}

.document-viewer:hover {
  transform: translateY(-2px);
  box-shadow: var(--hover-lift);
}

.document-viewer::before {
  content: "";
  position: absolute;
  top: 0; left: 0;
  width: 100%; height: 4px;
  background: linear-gradient(to right,
    var(--secondary-color),
    var(--primary-color));
  opacity: 0.7;
  transition: opacity var(--timing-gentle), height var(--timing-gentle);
}

.document-viewer:hover::before {
  opacity: 0.9;
  height: var(--spacing-xs);
}

.document-viewer iframe,
.document-viewer embed,
.document-viewer object {
  width: 100%;
  min-height: 600px;
  border: none;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-sm);
  background: var(--light-color);
}

.document-viewer .document-placeholder {
  text-align: center;
  padding: var(--spacing-xl);
  color: var(--text-color-muted);
  background: rgba(var(--grey-rgb), 0.05);
  border-radius: var(--border-radius);
  border: 2px dashed var(--border-color);
}

.document-viewer .document-placeholder .svg-icon {
  width: var(--icon-size-lg);
  height: var(--icon-size-lg);
  margin-bottom: var(--spacing);
  color: var(--text-color-muted);
}

/* Expense-card modifiers */
.expense-card {
  transition: all var(--timing-gentle), box-shadow var(--timing-gentle);
}

.expense-card:hover {
  box-shadow: var(--hover-lift);
  transform: translateY(-2px);
}

.expense-card.primary-details {
  border-left: var(--spacing-xs) solid var(--primary-color);
}
.expense-card.primary-details:hover {
  border-left-color: var(--primary-dark);
}
.expense-card.notes-card {
  border-left: var(--spacing-xs) solid var(--secondary-color);
}
.expense-card.notes-card:hover {
  border-left-color: var(--secondary-dark);
}
.expense-card.document-card {
  max-width: 1100px;
  width: 80%;
  margin: var(--spacing-xl) auto;
}

/* Animate cards on entry with staggered delays */
.expense-card.primary-details {
  animation: fade-in 0.4s ease-in-out forwards;
  animation-delay: 0ms;
}
.expense-card.notes-card {
  animation: fade-in 0.4s ease-in-out forwards;
  animation-delay: 100ms;
}
.expense-card.document-card {
  animation: fade-in 0.4s ease-in-out forwards;
  animation-delay: 200ms;
}

/* Additional staggered animation for multiple cards */
.expense-card:nth-child(1) { animation-delay: 0ms; }
.expense-card:nth-child(2) { animation-delay: 100ms; }
.expense-card:nth-child(3) { animation-delay: 200ms; }
.expense-card:nth-child(4) { animation-delay: 300ms; }
.expense-card:nth-child(5) { animation-delay: 400ms; }

/* Tablet responsive adjustments */
@media (max-width: 992px) {
  .card-grid.expense-card-grid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-md);
  }
  .expense-container {
    padding: 0 var(--spacing-md);
  }
  .expense-header {
    padding: var(--spacing-md);
  }
  .page-header {
    padding: var(--spacing-md);
  }
}

/* Mobile responsive tweaks */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: var(--spacing);
    align-items: flex-start;
  }
  .header-actions .button {
    width: 100%;
    justify-content: center;
  }
  .expense-header {
    flex-direction: column;
  }
  .expense-header-content {
    width: 100%;
    margin-bottom: var(--spacing-sm);
  }
  .expense-actions {
    flex-wrap: wrap;
    gap: var(--spacing-sm);
  }
  .expense-actions .button {
    flex: 1 1 auto;
    min-width: 100px;
  }
  .expense-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
    width: 100%;
  }
  .meta-group {
    width: 100%;
    padding: var(--spacing-xs) var(--spacing-sm);
  }
  .expense-date {
    min-width: 0;
    width: 100%;
    justify-content: flex-start;
    padding: var(--spacing-xs) var(--spacing-sm);
  }
  .card-grid.expense-card-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }
  .expense-card.document-card {
    width: 95%;
    margin: var(--spacing-md) auto;
  }

  .action-tabs {
    padding: var(--spacing-xs);
    margin-bottom: var(--spacing-md);
  }

  .action-tabs .button {
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
    transition: all var(--timing-gentle);
  }

  .document-viewer {
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
  }

  .document-viewer iframe,
  .document-viewer embed,
  .document-viewer object {
    min-height: 300px;
  }

  .document-viewer .document-placeholder {
    padding: var(--spacing-lg);
  }

  .document-viewer .document-placeholder .svg-icon {
    width: var(--icon-size-md);
    height: var(--icon-size-md);
  }

  /* Enhanced mobile spacing with utility classes guidance */
  .expense-container {
    padding: 0 var(--spacing-md);
  }
  .page-header,
  .expense-header {
    margin-bottom: var(--spacing-lg);
    padding: var(--spacing-md);
  }
  .card-grid.expense-card-grid {
    gap: var(--spacing-md);
  }
}
