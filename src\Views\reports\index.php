<?php
// Include shared helper functions
include_once __DIR__ . '/../shared/helpers.php';
?>

<h2>Expense Reports</h2>

<?php include __DIR__ . '/../shared/messages.php'; ?>

<div class="report-dashboard">
    <div class="report-cards">
        <div class="report-card">
            <h3>Monthly Reports</h3>
            <p>View your expenses broken down by month. Analyze spending trends over time.</p>
            <a href="/reports/monthly" class="button">View Monthly Reports</a>
        </div>

        <div class="report-card">
            <h3>Category Reports</h3>
            <p>Analyze your expenses by category. Identify your biggest spending areas.</p>
            <a href="/reports/category" class="button">View Category Reports</a>
        </div>

        <div class="report-card">
            <h3>Export Data</h3>
            <p>Export your expense data for use in other applications or for backup purposes.</p>
            <a href="/reports/export" class="button">Export Data</a>
        </div>
    </div>

    <div class="report-summary">
        <h3>Quick Summary</h3>

        <?php if (empty($summary)) : ?>
            <div class="notice">
                No expense data available for summary.
            </div>
        <?php else : ?>
            <div class="summary-stats">
                <div class="stat-item">
                    <span class="stat-label">Total Expenses</span>
                    <span class="stat-value">$<?= number_format($summary['total_amount'], 2) ?></span>
                </div>

                <div class="stat-item">
                    <span class="stat-label">This Month</span>
                    <span class="stat-value">$<?= number_format($summary['current_month'], 2) ?></span>
                </div>

                <div class="stat-item">
                    <span class="stat-label">Last Month</span>
                    <span class="stat-value">$<?= number_format($summary['last_month'], 2) ?></span>
                    <?php if ($summary['month_change'] > 0) : ?>
                        <span class="change up">↑ <?= number_format(abs($summary['month_change']), 1) ?>%</span>
                    <?php elseif ($summary['month_change'] < 0) : ?>
                        <span class="change down">↓ <?= number_format(abs($summary['month_change']), 1) ?>%</span>
                    <?php else : ?>
                        <span class="change neutral">0%</span>
                    <?php endif; ?>
                </div>

                <div class="stat-item">
                    <span class="stat-label">Average/Month</span>
                    <span class="stat-value">$<?= number_format($summary['monthly_average'], 2) ?></span>
                </div>
            </div>

            <div class="top-categories">
                <h4>Top Spending Categories</h4>
                <?php if (empty($topCategories)) : ?>
                    <p>No category data available.</p>
                <?php else : ?>
                    <ul class="category-list">
                        <?php foreach ($topCategories as $category) : ?>
                            <li>
                                <span class="category-name"><?= htmlspecialchars($category['name']) ?></span>
                                <span class="category-amount"><?= formatCurrency((float)$category['amount']) ?></span>
                                <span class="category-percentage"><?= number_format($category['percentage'], 1) ?>%</span>
                                <div class="progress-bar">
                                    <div class="progress" style="width: <?= $category['percentage'] ?>%"></div>
                                </div>
                            </li>
                        <?php endforeach; ?>
                    </ul>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<div class="recent-transactions">
    <h3>Recent Transactions</h3>

    <?php if (empty($recentExpenses)) : ?>
        <div class="notice">
            No recent expenses found.
        </div>
    <?php else : ?>
        <div class="table-responsive">
            <table aria-label="Recent Expenses">
                <thead>
                    <tr>
                        <th scope="col">Date</th>
                        <th scope="col">Description</th>
                        <th scope="col">Category</th>
                        <th scope="col">Amount</th>
                        <th scope="col">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($recentExpenses as $expense) : ?>
                        <tr>
                            <td><?= formatDate($expense['date']) ?></td>
                            <td><?= htmlspecialchars($expense['description']) ?></td>
                            <td><?= htmlspecialchars($expense['category_name'] ?? 'Uncategorized') ?></td>
                            <td><?= formatCurrency((float)$expense['amount']) ?></td>
                            <td>
                                <a href="/expenses/<?= htmlspecialchars($expense['id']) ?>" class="button small">View</a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <div class="view-all">
            <a href="/expenses" class="button secondary">View All Expenses</a>
        </div>
    <?php endif; ?>
</div>

<?php
// Add reports-specific scripts
$scripts ??= [];
$scripts[] = 'https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js';
$scripts[] = '/assets/js/pages/reports.js';
?>
