/* ===== BORDER UTILITIES ===== */
.rounded-sm {
    border-radius: var(--border-radius-sm);
  }

  .rounded {
    border-radius: var(--border-radius);
  }

  .rounded-lg {
    border-radius: var(--border-radius-lg);
  }

  .rounded-full {
    border-radius: 9999px;
  }

  .border {
    border: 1px solid rgba(0, 0, 0, 0.1);
  }

  .border-t-3 {
    border-top: 3px solid var(--primary-light);
  }

  .border-primary {
    border-color: var(--primary-color);
  }

  .border-success {
    border-color: var(--success-color);
  }

  .border-danger {
    border-color: var(--danger-color);
  }