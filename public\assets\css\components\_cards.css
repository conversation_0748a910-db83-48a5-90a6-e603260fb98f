/* ===== CARD COMPONENTS ===== */
/* Based on Visual Hierarchy & Cognitive Psychology Principles */

/* Base Card - Foundation for All Card Types */
.card {
  background-color: var(--light-color);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  margin-bottom: var(--spacing-md);
}

/* Card Variations - Psychological Differentiation */
.card.interactive {
  cursor: pointer;
}

.card.interactive:hover {
  transform: translateY(-3px);
  box-shadow: var(--box-shadow-lg);
}

.card.flat {
  box-shadow: none;
  border: 1px solid var(--grey-300);
}

.card.flat:hover {
  border-color: var(--primary-color);
}

.card.highlighted {
  border-left: 4px solid var(--primary-color);
}

.card.success {
  border-left: 4px solid var(--success-color);
}

.card.warning {
  border-left: 4px solid var(--warning-color);
}

.card.danger {
  border-left: 4px solid var(--danger-color);
}

/* Card Sections - Consistent Structure */
.card-header {
  padding: var(--spacing) var(--spacing-lg);
  border-bottom: 1px solid var(--grey-200);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: rgba(var(--light-rgb), 0.7);
}

.card-title {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-semibold);
  color: var(--dark-color);
  margin: 0;
}

.card-subtitle {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  margin-top: var(--spacing-xs);
}

.card-body {
  padding: var(--spacing-lg);
  flex: 1;
}

.not-found .card-body {
    display: block;
}

.card-footer {
  padding: var(--spacing) var(--spacing-lg);
  border-top: 1px solid var(--grey-200);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-sm);
  background-color: var(--grey-100);
}

/* Card Content Layouts - Flexible Structure */
.card-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing);
}

.card-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--spacing);
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid var(--grey-200);
  transition: var(--transition-fast);
}

.card-row:hover {
  background-color: rgba(var(--light-rgb), 0.5);
}

.card-row:last-child {
  border-bottom: none;
}

.card-label {
  font-weight: var(--font-weight-medium);
  color: var(--text-muted);
  flex: 0 0 30%;
}

.card-value {
  flex: 1;
  text-align: right;
  word-break: break-word;
}

/* Card Grids - Responsive Layout */
.card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-md);
}

/* Card with Icon */
.card-icon {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(var(--primary-rgb), 0.1);
  color: var(--primary-color);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing);
}

.card-icon svg {
  width: 24px;
  height: 24px;
}

/* Card Badge */
.card .card-badge {
  position: absolute;
  top: 0;
  right: 0;
  background-color: var(--primary-color);
  color: white;
  padding: 0.25rem 0.5rem;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  border-radius: 0 var(--border-radius) 0 var(--border-radius-sm);
  z-index: 1;
}

.card .card-badge.success {
  background-color: var(--success-color);
}

.card .card-badge.warning {
  background-color: var(--warning-color);
}

.card .card-badge.danger {
  background-color: var(--danger-color);
}

/* Card Media - Visual Content */
.card-media {
  width: 100%;
  height: 180px;
  object-fit: cover;
  border-bottom: 1px solid var(--grey-200);
}

/* Card Actions - Interactive Elements */
.card-actions {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: var(--spacing);
}

/* Card Stats - Data Visualization */
.card-stats {
  display: flex;
  justify-content: space-around;
  padding: var(--spacing) 0;
  border-top: 1px solid var(--grey-200);
  background-color: var(--grey-100);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.stat-value {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--dark-color);
}

.stat-label {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
  margin-top: var(--spacing-xs);
}

/* Card Metrics - For Dashboard */
.metric-card {
  text-align: center;
  padding: var(--spacing-lg);
}

.metric-value {
  font-size: 2rem;
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  margin: var(--spacing) 0;
}

.metric-label {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metric-change {
  font-size: var(--font-size-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-sm);
}

.metric-change.positive {
  color: var(--success-color);
}

.metric-change.negative {
  color: var(--danger-color);
}

/* Category Cards */
.category-card {
  border-left: 4px solid var(--primary-color);
  transition: var(--transition);
}

.category-card:hover {
  border-left-width: 8px;
}

/* Expense Cards */
.expense-card {
  border-left: 4px solid var(--primary-color);
}

.expense-card.income {
  border-left-color: var(--success-color);
}

.expense-card.expense {
  border-left-color: var(--danger-color);
}

.expense-amount {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--dark-color);
  margin: var(--spacing-xs) 0;
}

.expense-amount.income {
  color: var(--success-color);
}

.expense-amount.expense {
  color: var(--danger-color);
}

.expense-date {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

.expense-category {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: var(--grey-200);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  margin-top: var(--spacing-sm);
}

/* Document and Notes */
.expense-document, .expense-notes {
  position: relative;
  padding: var(--spacing);
  border: 1px solid var(--grey-200);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing);
}

.expense-document::before, .expense-notes::before {
  content: "";
  position: absolute;
  top: 1rem;
  right: 1rem;
  width: 18px;
  height: 18px;
  opacity: 0.7;
  background-size: contain;
  background-repeat: no-repeat;
}

/* Scrollable Content */
pre#notesContent {
  background-color: var(--light-color);
  padding: var(--spacing);
  border-radius: var(--border-radius);
  white-space: pre-wrap;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid var(--grey-200);
  font-family: monospace;
  line-height: 1.5;
}

/* Card Animations - Subtle Feedback */
.card.animate-in {
  animation: fadeInUp 0.4s ease-out forwards;
}

.card.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.card.scale-in {
  animation: scaleIn 0.3s ease-out;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .card-grid {
    grid-template-columns: 1fr;
  }

  .card-header, .card-body, .card-footer {
    padding: var(--spacing);
  }

  .card-row {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .card-label, .card-value {
    flex: 1;
    text-align: left;
  }

  .card-stats {
    flex-wrap: wrap;
    gap: var(--spacing);
  }
}

@media (max-width: 576px) {
  .expense-content, .expense-header, .expense-notes, .expense-document {
    padding: var(--spacing);
  }

  .document-actions {
    flex-direction: column;
  }
}