<?php

declare(strict_types=1);

namespace App\Exceptions;

use Throwable;
use InvalidArgumentException as BaseInvalidArgumentException;

class InvalidArgumentException extends BaseInvalidArgumentException
{
    public function __construct(
        string $message = 'Invalid argument provided',
        int $code = 0,
        ?Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);
    }

    public function __toString(): string
    {
        return sprintf(
            '%s: [%d]: %s',
            static::class,
            $this->code,
            $this->message
        );
    }
}
