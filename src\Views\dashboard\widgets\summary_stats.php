<?php

/**
 * Dashboard summary statistics widget
 * Displays key metrics for the user's expenses
 */

// Set widget parameters
$title = 'Summary';
$icon = '<svg class="icon"><use xlink:href="/assets/icons.svg#icon-chart"></use></svg>';
$widgetId = 'summary-stats';
$widgetClass = 'summary-stats-widget';
$hasData = isset($monthlyTotal) && isset($weeklyTotal);
$emptyMessage = 'No expense data available yet. Add your first expense to see statistics.';

// Prepare widget content
ob_start();
?>

<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-title">This Month</div>
        <div class="stat-value">$<?= number_format($monthlyTotal ?? 0, 2) ?></div>
    </div>

    <div class="stat-card">
        <div class="stat-title">This Week</div>
        <div class="stat-value">$<?= number_format($weeklyTotal ?? 0, 2) ?></div>
    </div>

    <div class="stat-card">
        <div class="stat-title">Year to Date</div>
        <div class="stat-value">$<?= number_format($yearlyTotal ?? 0, 2) ?></div>
        <div class="stat-projected">Projected: $<?= number_format($yearProjection ?? 0, 2) ?></div>
    </div>

    <div class="stat-card">
        <div class="stat-title">Last 30 Days</div>
        <div class="stat-value"><?= $expenseCount ?? 0 ?></div>
        <div class="stat-label">Expenses</div>
    </div>
</div>

<?php
$content = ob_get_clean();

// Include the widget template
include __DIR__ . '/widget_template.php';
?>