/* ===== BUTTON COMPONENTS ===== */
/* Based on Interaction Psychology & Visual Feedback Principles */

/* Base Button - Foundation for All Button Types */
button, .button, .btn {
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.button, .btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  padding: var(--spacing) var(--spacing-lg);
  background-color: var(--primary-color);
  color: var(--light-color);
  border: none;
  border-radius: var(--border-radius);
  font-family: var(--font-main);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size);
  line-height: 1.5;
  text-align: center;
  text-decoration: none;
  cursor: pointer;
  transition: var(--transition);
  box-shadow: var(--box-shadow-sm);
  user-select: none;
  -webkit-tap-highlight-color: transparent;
  min-width: 100px;
}

/* Hover Effect - Subtle Elevation */
.button:hover, .btn:hover {
  background-color: var(--primary-dark);
  color: var(--light-color);
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

/* Active Effect - Clear Feedback */
.button:active, .btn:active {
  transform: translateY(0);
  box-shadow: var(--box-shadow-sm);
}

/* Focus State - Accessibility */
.button:focus-visible, .btn:focus-visible {
  outline: 2px solid var(--primary-light);
  outline-offset: 2px;
}

/* Button Variants - Psychological Color Impact */
.button.secondary, .btn-secondary {
  background-color: var(--light-color);
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
}

.button.secondary:hover, .btn-secondary:hover {
  background-color: var(--grey-100);
  color: var(--primary-dark);
  border-color: var(--primary-dark);
}

.button.success, .btn-success {
  background-color: var(--success-color);
  color: white;
}

.button.success:hover, .btn-success:hover {
  background-color: var(--success-dark);
}

.button.danger, .btn-danger {
  background-color: var(--danger-color);
  color: white;
}

.button.danger:hover, .btn-danger:hover {
  background-color: var(--danger-dark);
}

.button.warning, .btn-warning {
  background-color: var(--warning-color);
  color: var(--dark-color);
}

.button.warning:hover, .btn-warning:hover {
  background-color: var(--warning-dark);
}

.button.info, .btn-info {
  background-color: var(--info-color);
  color: white;
}

.button.info:hover, .btn-info:hover {
  background-color: var(--info-dark);
}

/* Button Styles - Visual Differentiation */
.button.outline, .btn-outline-primary {
  background-color: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.button.outline:hover, .btn-outline-primary:hover {
  background-color: var(--primary-color);
  color: white;
}

.button.text, .btn-text {
  background-color: transparent;
  color: var(--primary-color);
  box-shadow: none;
  padding: 0.5rem 0.8rem;
  min-width: auto;
}

.button.text:hover, .btn-text:hover {
  background-color: rgba(var(--primary-rgb), 0.08);
  transform: none;
  box-shadow: none;
}

/* Button Sizes - Visual Hierarchy */
.button.small, .btn-sm {
  padding: var(--spacing-xs) var(--spacing);
  font-size: var(--font-size-sm);
  min-width: 80px;
}

.button.large, .btn-lg {
  padding: var(--spacing) var(--spacing-xl);
  font-size: var(--font-size-md);
  min-width: 140px;
}

/* Button with Icon - Improved Recognition */
.button .svg-icon, .btn .svg-icon {
  width: 18px;
  height: 18px;
  margin-right: var(--spacing-xs);
  fill: currentColor;
}

.button.icon-right .svg-icon, .btn.icon-right .svg-icon {
  margin-right: 0;
  margin-left: var(--spacing-xs);
}

.button.icon-only, .btn-icon-only {
  padding: var(--spacing-sm);
  min-width: unset;
  width: 36px;
  height: 36px;
}

.button.icon-only .svg-icon, .btn-icon-only .svg-icon {
  margin: 0;
}

/* Primary CTA - Attention-Grabbing */
.button.primary-cta {
  background-color: var(--accent-energy);
  color: var(--dark-color);
  font-weight: var(--font-weight-bold);
  padding: var(--spacing) var(--spacing-xl);
  box-shadow: var(--box-shadow);
}

.button.primary-cta:hover {
  transform: translateY(-3px) scale(1.02);
  box-shadow: var(--box-shadow-lg);
}

/* Disabled State - Clear Visual Feedback */
.button:disabled,
.button.disabled,
.btn:disabled,
.btn.disabled {
  background-color: var(--grey-300);
  color: var(--grey-600);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  opacity: 0.65;
  pointer-events: none;
}

/* Loading State - User Feedback */
.button.loading,
.btn.loading {
  color: transparent;
  pointer-events: none;
}

.button.loading::after,
.btn.loading::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  top: calc(50% - 10px);
  left: calc(50% - 10px);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: loading 0.8s linear infinite;
}

.button.secondary.loading::after,
.btn-secondary.loading::after {
  border: 2px solid rgba(var(--primary-rgb), 0.3);
  border-top-color: var(--primary-color);
}

@keyframes loading {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Button Group - Related Actions */
.button-group,
.btn-group {
  display: flex;
  gap: var(--spacing-sm);
}

.button-group.tight,
.btn-group.tight {
  gap: 0;
}

.button-group.tight .button,
.btn-group.tight .btn {
  border-radius: 0;
}

.button-group.tight .button:first-child,
.btn-group.tight .btn:first-child {
  border-top-left-radius: var(--border-radius);
  border-bottom-left-radius: var(--border-radius);
}

.button-group.tight .button:last-child,
.btn-group.tight .btn:last-child {
  border-top-right-radius: var(--border-radius);
  border-bottom-right-radius: var(--border-radius);
}

/* Ripple Effect - Interactive Feedback */
.button.with-ripple,
.btn.ripple {
  position: relative;
  overflow: hidden;
}

.button.with-ripple::after,
.btn.ripple::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5px;
  height: 5px;
  background: rgba(255, 255, 255, 0.5);
  opacity: 0;
  border-radius: 100%;
  transform: scale(1, 1) translate(-50%, -50%);
  transform-origin: 50% 50%;
}

.button.with-ripple:focus:not(:active)::after,
.btn.ripple:focus:not(:active)::after {
  animation: ripple 0.6s ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(0, 0);
    opacity: 0.5;
  }
  20% {
    transform: scale(25, 25);
    opacity: 0.3;
  }
  100% {
    opacity: 0;
    transform: scale(40, 40);
  }
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .button,
  .btn {
    padding: var(--spacing-sm) var(--spacing);
  }

  .button.large,
  .btn-lg {
    padding: var(--spacing) var(--spacing-lg);
  }

  .button-group,
  .btn-group {
    flex-wrap: wrap;
  }

  .document-actions .button {
    width: 100%;
  }
}