<?php

/**
 * Shared helper functions for views
 */

/**
 * Format file size in human-readable format
 *
 * @param int $bytes File size in bytes
 * @return string Formatted file size
 */
function formatFileSize(int $bytes): string
{
    if ($bytes >= 1073741824) {
        return round($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return round($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return round($bytes / 1024, 2) . ' KB';
    } else {
        return "{$bytes} bytes";
    }
}

/**
 * Format date in a consistent format
 *
 * @param string $date Date string
 * @param string $format Format string (default: 'M d, Y')
 * @return string Formatted date
 */
function formatDate(string $date, string $format = 'M d, Y'): string
{
    return date($format, strtotime($date));
}

/**
 * Format currency amount
 *
 * @param float $amount Amount to format
 * @param string $currency Currency symbol (default: '$')
 * @return string Formatted amount
 */
function formatCurrency(float $amount, string $currency = '$'): string
{
    return "{$currency}" . number_format($amount, 2);
}

/**
 * Get file icon based on extension
 *
 * @param string $filename Filename to determine icon for
 * @return string Icon ID
 */
function getFileIconId(string $filename): string
{
    $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));

    return match ($extension) {
        'pdf' => 'file-pdf',
        'txt' => 'file-text',
        'csv' => 'file-csv',
        'log' => 'file-text',
        default => 'file-generic'
    };
}

/**
 * Create a CSRF token input field for forms
 *
 * @param string $token CSRF token from controller
 * @return string HTML for CSRF token input field
 * @throws \InvalidArgumentException If token is not provided
 */
function csrfTokenField(string $token): string
{
    if (empty($token)) {
        throw new \InvalidArgumentException('CSRF token must be provided by the controller');
    }
    return "<input type=\"hidden\" name=\"csrf_token\" value=\"" . htmlspecialchars($token) . "\">";
}

/**
 * Get a consistent color for a category
 *
 * @param string $categoryName Category name
 * @return string CSS color value
 */
function getCategoryColor(string $categoryName): string
{
    // Generate a consistent color based on the category name
    // This ensures the same category always gets the same color
    // even if it's not in our predefined list
    $hash = md5($categoryName);

    // Use a predefined color palette for better visual consistency
    // These are visually distinct colors that work well together
    $colorPalette = [
        '#4CAF50', // Green
        '#2196F3', // Blue
        '#9C27B0', // Purple
        '#FF9800', // Orange
        '#E91E63', // Pink
        '#607D8B', // Blue Grey
        '#F44336', // Red
        '#00BCD4', // Cyan
        '#3F51B5', // Indigo
        '#FF5722', // Deep Orange
        '#8BC34A', // Light Green
        '#795548', // Brown
        '#009688', // Teal
        '#673AB7', // Deep Purple
        '#FFC107', // Amber
    ];

    // Special case for uncategorized
    if ($categoryName === 'Uncategorized') {
        return '#9E9E9E'; // Grey
    }

    // Use the hash to select a color from our palette
    // This ensures the same category always gets the same color
    $colorIndex = hexdec(substr($hash, 0, 4)) % count($colorPalette);

    return $colorPalette[$colorIndex];
}

/**
 * Generate a unique pastel HSL color for a category name
 *
 * @param string $categoryName Category name
 * @return string HSL color string (e.g., "hsl(120,60%,70%)")
 */
function getCategoryColorFreeform(string $categoryName): string
{
    // Generate a consistent hue based on the category name using CRC32
    $hue = abs(crc32($categoryName)) % 360;

    // Return HSL with fixed saturation and lightness for pastel appearance
    return "hsl({$hue},60%,70%)";
}

/**
 * Determine the best text color (black or white) for contrast against an HSL background
 *
 * @param string $backgroundHsl HSL color string (e.g., "hsl(120,60%,70%)")
 * @return string Hex color code (#000000 or #FFFFFF) that meets WCAG AA contrast ratio
 */
function getContrastTextColor(string $backgroundHsl): string
{
    // Parse HSL values from the string
    if (!preg_match('/hsl\(\s*(\d+)\s*,\s*(\d+)%\s*,\s*(\d+)%\s*\)/', $backgroundHsl, $matches)) {
        // Fallback to black if parsing fails
        return '#000000';
    }

    $h = (int)$matches[1];
    $s = (int)$matches[2] / 100;
    $l = (int)$matches[3] / 100;

    // Convert HSL to RGB
    $c = (1 - abs(2 * $l - 1)) * $s;
    $x = $c * (1 - abs(fmod($h / 60, 2) - 1));
    $m = $l - $c / 2;

    if ($h < 60) {
        $r = $c;
        $g = $x;
        $b = 0;
    } elseif ($h < 120) {
        $r = $x;
        $g = $c;
        $b = 0;
    } elseif ($h < 180) {
        $r = 0;
        $g = $c;
        $b = $x;
    } elseif ($h < 240) {
        $r = 0;
        $g = $x;
        $b = $c;
    } elseif ($h < 300) {
        $r = $x;
        $g = 0;
        $b = $c;
    } else {
        $r = $c;
        $g = 0;
        $b = $x;
    }

    $r = ($r + $m);
    $g = ($g + $m);
    $b = ($b + $m);

    // Calculate relative luminance using WCAG formula
    $rLum = $r <= 0.03928 ? $r / 12.92 : pow(($r + 0.055) / 1.055, 2.4);
    $gLum = $g <= 0.03928 ? $g / 12.92 : pow(($g + 0.055) / 1.055, 2.4);
    $bLum = $b <= 0.03928 ? $b / 12.92 : pow(($b + 0.055) / 1.055, 2.4);

    $luminance = 0.2126 * $rLum + 0.7152 * $gLum + 0.0722 * $bLum;

    // Calculate contrast ratios with black and white
    // White luminance = 1, Black luminance = 0
    $contrastWithWhite = ($luminance + 0.05) / (1 + 0.05);
    $contrastWithBlack = (1 + 0.05) / ($luminance + 0.05);

    // Return the color that provides better contrast (minimum 4.5:1 for WCAG AA)
    return $contrastWithBlack >= $contrastWithWhite ? '#000000' : '#FFFFFF';
}
