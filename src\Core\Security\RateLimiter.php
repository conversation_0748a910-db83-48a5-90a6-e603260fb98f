<?php

declare(strict_types=1);

namespace App\Core\Security;

final class RateLimiter
{
    private const DEFAULT_MAX_ATTEMPTS = 5;
    private const DEFAULT_WINDOW_MINUTES = 15;
    private const CLEANUP_PROBABILITY = 0.01; // 1% chance to cleanup old entries

    private string $storageFile;
    private int $maxAttempts;
    private int $windowMinutes;

    public function __construct(
        int $maxAttempts = self::DEFAULT_MAX_ATTEMPTS,
        int $windowMinutes = self::DEFAULT_WINDOW_MINUTES,
        ?string $storageFile = null
    ) {
        $this->maxAttempts = $maxAttempts;
        $this->windowMinutes = $windowMinutes;
        $this->storageFile = $storageFile ?? sys_get_temp_dir() . '/rate_limiter.json';
        
        // Randomly cleanup old entries
        if (mt_rand(1, 100) <= (self::CLEANUP_PROBABILITY * 100)) {
            $this->cleanup();
        }
    }

    public function isAllowed(string $identifier): bool
    {
        $attempts = $this->getAttempts($identifier);
        $windowStart = time() - ($this->windowMinutes * 60);
        
        // Filter attempts within the current window
        $recentAttempts = array_filter($attempts, fn($timestamp) => $timestamp > $windowStart);
        
        return count($recentAttempts) < $this->maxAttempts;
    }

    public function recordAttempt(string $identifier): void
    {
        $data = $this->loadData();
        
        if (!isset($data[$identifier])) {
            $data[$identifier] = [];
        }
        
        $data[$identifier][] = time();
        
        // Keep only recent attempts to prevent unlimited growth
        $windowStart = time() - ($this->windowMinutes * 60);
        $data[$identifier] = array_filter(
            $data[$identifier],
            fn($timestamp) => $timestamp > $windowStart
        );
        
        $this->saveData($data);
    }

    public function getRemainingAttempts(string $identifier): int
    {
        $attempts = $this->getAttempts($identifier);
        $windowStart = time() - ($this->windowMinutes * 60);
        
        $recentAttempts = array_filter($attempts, fn($timestamp) => $timestamp > $windowStart);
        
        return max(0, $this->maxAttempts - count($recentAttempts));
    }

    public function getTimeUntilReset(string $identifier): int
    {
        $attempts = $this->getAttempts($identifier);
        
        if (empty($attempts)) {
            return 0;
        }
        
        $oldestRecentAttempt = min($attempts);
        $windowStart = time() - ($this->windowMinutes * 60);
        
        if ($oldestRecentAttempt <= $windowStart) {
            return 0;
        }
        
        return ($oldestRecentAttempt + ($this->windowMinutes * 60)) - time();
    }

    public function reset(string $identifier): void
    {
        $data = $this->loadData();
        unset($data[$identifier]);
        $this->saveData($data);
    }

    public static function getClientIdentifier(): string
    {
        // Use IP address as primary identifier
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'] ?? 
              $_SERVER['HTTP_X_REAL_IP'] ?? 
              $_SERVER['REMOTE_ADDR'] ?? 
              'unknown';
        
        // If behind proxy, get the first IP
        if (strpos($ip, ',') !== false) {
            $ip = trim(explode(',', $ip)[0]);
        }
        
        // Add user agent for additional uniqueness
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        
        return hash('sha256', $ip . '|' . $userAgent);
    }

    private function getAttempts(string $identifier): array
    {
        $data = $this->loadData();
        return $data[$identifier] ?? [];
    }

    private function loadData(): array
    {
        if (!file_exists($this->storageFile)) {
            return [];
        }
        
        $content = file_get_contents($this->storageFile);
        if ($content === false) {
            return [];
        }
        
        $data = json_decode($content, true);
        return is_array($data) ? $data : [];
    }

    private function saveData(array $data): void
    {
        $directory = dirname($this->storageFile);
        if (!is_dir($directory)) {
            mkdir($directory, 0755, true);
        }
        
        file_put_contents(
            $this->storageFile,
            json_encode($data, JSON_THROW_ON_ERROR),
            LOCK_EX
        );
    }

    private function cleanup(): void
    {
        $data = $this->loadData();
        $windowStart = time() - ($this->windowMinutes * 60);
        $cleaned = false;
        
        foreach ($data as $identifier => $attempts) {
            $recentAttempts = array_filter($attempts, fn($timestamp) => $timestamp > $windowStart);
            
            if (empty($recentAttempts)) {
                unset($data[$identifier]);
                $cleaned = true;
            } elseif (count($recentAttempts) !== count($attempts)) {
                $data[$identifier] = array_values($recentAttempts);
                $cleaned = true;
            }
        }
        
        if ($cleaned) {
            $this->saveData($data);
        }
    }
}