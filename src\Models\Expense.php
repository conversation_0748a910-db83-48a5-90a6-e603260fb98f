<?php

declare(strict_types=1);

namespace App\Models;

use App\Core\Database;
use App\Exceptions\DatabaseException;
use App\Exceptions\InvalidArgumentException;

class Expense extends BaseModel
{
    private const REQUIRED_FIELDS = ['user_id', 'amount', 'description', 'date'];
    private const OPTIONAL_FIELDS = ['merchant_id', 'file_id', 'payment_method_id', 'receipt_text', 'notes'];
    private static array $cache = [];
    private const CACHE_TTL = 300;

    protected static function getTableName(): string
    {
        return 'expenses';
    }

    private static function getBaseColumns(): string
    {
        return "id, user_id, category_id, user_category_id, amount, description, date,
                merchant_id, file_id, payment_method_id, receipt_text, notes,
                created_at, updated_at";
    }

    private static function getBaseWhereClause(
        ?int $userId = null,
        bool $includeDeleted = false,
        array $additionalConditions = []
    ): array {
        $conditions = [];
        $params = [];

        if (!$includeDeleted) {
            $conditions[] = 'deleted_at IS NULL';
        }

        if ($userId !== null) {
            $conditions[] = 'user_id = ?';
            $params[] = $userId;
        }

        foreach ($additionalConditions as $condition => $value) {
            $conditions[] = $condition;
            if ($value !== null) {
                $params[] = $value;
            }
        }

        return [$conditions, $params];
    }

    public static function find(int $id, ?int $userId = null): ?array
    {
        error_log("[Debug] Expense::find - Looking for expense ID: {$id}, User ID: " . ($userId ?? 'null'));

        $cacheKey = "expense_{$id}_" . ($userId ?? 'null');
        if (isset(self::$cache[$cacheKey]) && self::$cache[$cacheKey]['expires'] > time()) {
            error_log("[Debug] Expense::find - Found in cache");
            return self::$cache[$cacheKey]['data'];
        }

        [$conditions, $params] = self::getBaseWhereClause($userId, false, ['id = ?' => $id]);

        $sql = sprintf(
            "SELECT %s FROM %s WHERE %s",
            self::getBaseColumns(),
            self::getTableName(),
            implode(' AND ', $conditions)
        );

        error_log("[Debug] Expense::find - SQL: {$sql}");
        error_log("[Debug] Expense::find - Params: " . json_encode($params));

        $result = self::fetchOne($sql, $params);

        if ($result) {
            error_log("[Debug] Expense::find - Found expense: " . json_encode($result));
            self::$cache[$cacheKey] = [
                'data' => $result,
                'expires' => time() + self::CACHE_TTL
            ];
        } else {
            error_log("[Debug] Expense::find - No expense found with ID: {$id}");
        }

        return $result;
    }

    public static function all(?int $userId = null, array $filters = []): array
    {
        error_log("[Debug] Expense::all - User ID: " . ($userId ?? 'null'));
        error_log("[Debug] Expense::all - Filters: " . json_encode($filters));

        // Extract sort parameters before processing filters
        $sortField = 'date';
        $sortOrder = 'DESC';

        if (isset($filters['_sort'])) {
            $sortField = $filters['_sort'];
            unset($filters['_sort']);
        }

        if (isset($filters['_order'])) {
            $sortOrder = $filters['_order'];
            unset($filters['_order']);
        }

        // Validate sort field to prevent SQL injection
        $allowedSortFields = ['date', 'description', 'amount', 'category_id', 'category_name'];
        if (!in_array($sortField, $allowedSortFields)) {
            $sortField = 'date';
        }

        // Validate sort order to prevent SQL injection
        $sortOrder = strtoupper($sortOrder) === 'ASC' ? 'ASC' : 'DESC';

        [$conditions, $params] = self::getBaseWhereClause($userId);

        foreach ($filters as $field => $value) {
            if ($value !== null && $value !== '') {
                $conditions[] = "$field = ?";
                $params[] = $value;
            }
        }

        // We need to join with the user_categories table to get the user_category_name
        // Make sure to use the correct column names in the ORDER BY clause
        $orderByField = $sortField;
        if ($sortField === 'category_name') {
            // Special handling for category_name since it could be in either table
            $orderByField = "COALESCE(uc.name, c.name)";
        } elseif (!in_array($sortField, ['id', 'amount', 'description', 'date'])) {
            // For other fields, make sure to prefix with the table alias
            $orderByField = "e." . $sortField;
        } else {
            // For common fields, prefix with the table alias
            $orderByField = "e." . $sortField;
        }

        // Modify the conditions to explicitly specify the table for deleted_at and user_id
        $modifiedConditions = [];
        foreach ($conditions as $condition) {
            if (strpos($condition, 'deleted_at') !== false) {
                $modifiedConditions[] = str_replace('deleted_at', 'e.deleted_at', $condition);
            } elseif (strpos($condition, 'user_id') !== false) {
                $modifiedConditions[] = str_replace('user_id', 'e.user_id', $condition);
            } else {
                $modifiedConditions[] = $condition;
            }
        }

        $sql = sprintf(
            "SELECT e.id, e.user_id, e.category_id, e.user_category_id, e.amount, e.description, e.date,
                    e.merchant_id, e.file_id, e.payment_method_id, e.receipt_text, e.notes,
                    e.created_at, e.updated_at, 
                    uc.name as user_category_name, c.name as category_name 
             FROM %s e 
             LEFT JOIN categories c ON e.category_id = c.id
             LEFT JOIN user_categories uc ON e.user_category_id = uc.id
             WHERE %s 
             ORDER BY %s %s",
            self::getTableName(),
            implode(' AND ', $modifiedConditions),
            $orderByField,
            $sortOrder
        );

        error_log("[Debug] Expense::all - SQL: " . $sql);
        error_log("[Debug] Expense::all - Params: " . json_encode($params));

        $results = self::fetchMany($sql, $params);

        error_log("[Debug] Expense::all - Results count: " . count($results));
        if (empty($results)) {
            error_log("[Warning] Expense::all - No DB records found for user " . $userId);
        } else {
            // Log the first few results to see what data is being returned
            for ($i = 0; $i < min(3, count($results)); $i++) {
                error_log("[Debug] Expense::all - Result {$i}: " . json_encode([
                    'id' => $results[$i]['id'] ?? 'not set',
                    'category_id' => $results[$i]['category_id'] ?? 'not set',
                    'user_category_id' => $results[$i]['user_category_id'] ?? 'not set',
                    'description' => $results[$i]['description'] ?? 'not set'
                ]));
            }
        }

        return $results;
    }

    public static function create(array $expenseData): int
    {
        error_log("[Debug] Expense::create - Creating expense with data: " . json_encode($expenseData));
        error_log("[Debug] Expense::create - Category data: " . json_encode([
            'category_id' => $expenseData['category_id'] ?? 'not set',
            'user_category_id' => $expenseData['user_category_id'] ?? 'not set'
        ]));

        self::validateCategoryFields($expenseData);
        self::validateRequiredFields($expenseData);
        
        $expenseData = self::addTimestamps($expenseData);
        $fields = array_keys($expenseData);
        $placeholders = rtrim(str_repeat('?,', count($fields)), ',');

        return self::transaction(function () use ($expenseData, $fields, $placeholders) {
            $expenseData = self::normalizeCategoryConstraints($expenseData);
            
            // Rebuild fields and placeholders after potential changes
            $fields = array_keys($expenseData);
            $placeholders = rtrim(str_repeat('?,', count($fields)), ',');

            return self::executeInsertAndVerify($expenseData, $fields, $placeholders);
        });
    }

    private static function validateCategoryFields(array $expenseData): void
    {
        $hasCategoryId = isset($expenseData['category_id']) && 
                        $expenseData['category_id'] !== null && 
                        (int)$expenseData['category_id'] !== 0;
        
        $hasUserCategoryId = isset($expenseData['user_category_id']) && 
                            $expenseData['user_category_id'] !== null && 
                            (int)$expenseData['user_category_id'] !== 0;
        
        if (!$hasCategoryId && !$hasUserCategoryId) {
            error_log("[Error] Expense::create - Missing required category");
            throw new InvalidArgumentException(
                "Missing required category: either category_id or user_category_id must be set"
            );
        }
        
        error_log("[Debug] Expense::create - Category validation passed");
    }

    private static function validateRequiredFields(array $expenseData): void
    {
        foreach (self::REQUIRED_FIELDS as $field) {
            if (self::isNumericField($field)) {
                self::validateNumericField($expenseData, $field);
            } else {
                self::validateTextField($expenseData, $field);
            }
        }
    }

    private static function isNumericField(string $field): bool
    {
        return in_array($field, ['amount', 'user_id']);
    }

    private static function validateNumericField(array $expenseData, string $field): void
    {
        $value = $expenseData[$field] ?? null;
        $isZero = $value === 0 || $value === '0';

        if (!isset($expenseData[$field]) || (!$isZero && empty($value))) {
            throw new InvalidArgumentException("Missing required field: $field");
        }
    }

    private static function validateTextField(array $expenseData, string $field): void
    {
        if (!isset($expenseData[$field]) || $expenseData[$field] === '') {
            throw new InvalidArgumentException("Missing required field: $field");
        }
    }

    private static function addTimestamps(array $expenseData): array
    {
        $timestamp = date('Y-m-d H:i:s');
        $expenseData['created_at'] ??= $timestamp;
        $expenseData['updated_at'] ??= $timestamp;
        
        return $expenseData;
    }

    private static function normalizeCategoryConstraints(array $expenseData): array
    {
        // Handle the check_one_category constraint
        // The constraint requires that exactly one of category_id or user_category_id must be NULL
        // and the other must be NOT NULL

        if (self::hasPositiveUserCategoryId($expenseData)) {
            $expenseData['category_id'] = null;
        } elseif (self::hasPositiveCategoryId($expenseData)) {
            $expenseData['user_category_id'] = null;
        } else {
            $expenseData = self::setDefaultCategory($expenseData);
        }

        return self::ensureCategoryConstraint($expenseData);
    }

    private static function hasPositiveUserCategoryId(array $expenseData): bool
    {
        return isset($expenseData['user_category_id']) && (int)$expenseData['user_category_id'] > 0;
    }

    private static function hasPositiveCategoryId(array $expenseData): bool
    {
        return isset($expenseData['category_id']) && (int)$expenseData['category_id'] > 0;
    }

    private static function setDefaultCategory(array $expenseData): array
    {
        $expenseData['category_id'] = 1; // Default to first system category
        $expenseData['user_category_id'] = null;
        
        return $expenseData;
    }

    private static function ensureCategoryConstraint(array $expenseData): array
    {
        // Final check to ensure the constraint is satisfied
        if (self::hasBothPositiveCategories($expenseData)) {
            // Both can't be positive - prioritize category_id
            $expenseData['user_category_id'] = null;
        }
        
        return $expenseData;
    }

    private static function hasBothPositiveCategories(array $expenseData): bool
    {
        return isset($expenseData['category_id']) && isset($expenseData['user_category_id']) &&
               (int)$expenseData['category_id'] > 0 && (int)$expenseData['user_category_id'] > 0;
    }

    private static function executeInsertAndVerify(array $expenseData, array $fields, string $placeholders): int
    {
        $sql = sprintf(
            "INSERT INTO %s (%s) VALUES (%s)",
            self::getTableName(),
            implode(', ', $fields),
            $placeholders
        );
        
        error_log("[Debug] Expense::create - Final SQL: " . $sql);
        error_log("[Debug] Expense::create - Final params: " . json_encode(array_values($expenseData)));

        try {
            self::executeSql($sql, array_values($expenseData));
            $lastId = (int)self::getDb()->lastInsertId();
            
            error_log("[Debug] Expense::create - Last insert ID: " . $lastId);
            self::verifyExpenseCreation($lastId);
            
            return $lastId;
        } catch (\Exception $e) {
            error_log("[Error] Expense::create - SQL execution error: " . $e->getMessage());
            throw $e;
        }
    }

    private static function verifyExpenseCreation(int $lastId): void
    {
        $createdExpense = self::find($lastId);
        
        if ($createdExpense) {
            error_log("[Debug] Expense::create - Successfully verified expense creation: " . json_encode($createdExpense));
        } else {
            error_log("[Warning] Expense::create - Could not verify expense creation, expense not found after insert");
        }
    }

    public static function update(int $id, array $expenseData, ?int $userId = null): bool
    {
        if (empty($expenseData)) {
            return false;
        }

        // Handle the check_one_category constraint
        // The constraint requires that exactly one of category_id or user_category_id must be NULL
        // and the other must be NOT NULL

        // First, check if we're updating either category field
        if (isset($expenseData['category_id']) || isset($expenseData['user_category_id'])) {
            // Get the current expense data to see existing values
            $currentExpense = self::find($id, $userId);

            // If user_category_id is provided and positive, use it and set category_id to NULL
            if (isset($expenseData['user_category_id']) && (int)$expenseData['user_category_id'] > 0) {
                $expenseData['category_id'] = null; // Set system category to NULL when using user category
            } elseif (isset($expenseData['category_id']) && (int)$expenseData['category_id'] > 0) {
                $expenseData['user_category_id'] = null; // Set user category to NULL when using system category
            } elseif (isset($expenseData['category_id']) && isset($expenseData['user_category_id'])) {
                $expenseData['category_id'] = 1; // Default to first system category
                $expenseData['user_category_id'] = null;
            } else {
                // If updating only category_id and it's not positive
                if (isset($expenseData['category_id']) && (int)$expenseData['category_id'] <= 0) {
                    // Check if current user_category_id is positive
                    if (isset($currentExpense['user_category_id']) && (int)$currentExpense['user_category_id'] > 0) {
                        // Keep using the user category
                        $expenseData['category_id'] = null;
                    } else {
                        // Default to system category 1
                        $expenseData['category_id'] = 1;
                        $expenseData['user_category_id'] = null;
                    }
                } elseif (isset($expenseData['user_category_id']) && (int)$expenseData['user_category_id'] <= 0) {
                    // Check if current category_id is positive
                    if (isset($currentExpense['category_id']) && (int)$currentExpense['category_id'] > 0) {
                        // Keep using the system category
                        $expenseData['user_category_id'] = null;
                    } else {
                        // Default to system category 1
                        $expenseData['category_id'] = 1;
                        $expenseData['user_category_id'] = null;
                    }
                }
            }

            // Final check to ensure the constraint is satisfied
            if (
                isset($expenseData['category_id']) && isset($expenseData['user_category_id']) &&
                (int)$expenseData['category_id'] > 0 && (int)$expenseData['user_category_id'] > 0
            ) {
                // Both can't be positive - prioritize category_id
                $expenseData['user_category_id'] = null;
            }
        }

        $updateParts = [];
        $values = [];

        foreach (array_merge(self::REQUIRED_FIELDS, self::OPTIONAL_FIELDS) as $field) {
            if (isset($expenseData[$field])) {
                $updateParts[] = "$field = ?";
                $values[] = $expenseData[$field];
            }
        }

        if (empty($updateParts)) {
            return false;
        }

        $updateParts[] = "updated_at = ?";
        $values[] = date('Y-m-d H:i:s');

        [$conditions, $params] = self::getBaseWhereClause($userId, false, ['id = ?' => $id]);
        $values = array_merge($values, $params);

        return self::transaction(function () use ($id, $updateParts, $values, $conditions, $userId) {
            $sql = sprintf(
                "UPDATE %s SET %s WHERE %s",
                self::getTableName(),
                implode(', ', $updateParts),
                implode(' AND ', $conditions)
            );

            $result = self::executeSql($sql, $values);

            if ($result) {
                $cacheKey = "expense_{$id}_" . ($userId ?? 'null');
                unset(self::$cache[$cacheKey]);
            }

            return $result;
        });
    }

    public static function delete(int $id, ?int $userId = null): bool
    {
        [$conditions, $params] = self::getBaseWhereClause($userId, false, ['id = ?' => $id]);

        return self::transaction(function () use ($id, $conditions, $params, $userId) {
            $sql = sprintf(
                "UPDATE %s SET deleted_at = NOW(), updated_at = NOW() WHERE %s",
                self::getTableName(),
                implode(' AND ', $conditions)
            );

            $result = self::executeSql($sql, $params);

            if ($result) {
                $cacheKey = "expense_{$id}_" . ($userId ?? 'null');
                unset(self::$cache[$cacheKey]);
            }

            return $result;
        });
    }

    public static function getRecent(int $userId, int $limit = 5): array
    {
        $sql = sprintf(
            "SELECT
                e.id, e.amount, e.description, e.date,
                c.name as category_name
            FROM %s e
            JOIN categories c ON e.category_id = c.id
            WHERE e.user_id = ? AND e.deleted_at IS NULL
            ORDER BY e.date DESC
            LIMIT ?",
            self::getTableName()
        );

        return self::fetchMany($sql, [$userId, $limit]);
    }

    public static function getMonthlyTotal(int $userId): float
    {
        return self::getPeriodTotal($userId, 'month');
    }

    public static function getYearlyTotal(int $userId): float
    {
        return self::getPeriodTotal($userId, 'year');
    }

    public static function getWeeklyTotal(int $userId): float
    {
        return self::getPeriodTotal($userId, 'week');
    }

    private static function getDateRange(string $period, string $year, ?string $month = null): array
    {
        $year = is_numeric($year) ? $year : date('Y');
        $month = ($month !== null && is_numeric($month)) ? $month : date('m');

        $dates = [
            'month' => self::getMonthDateRange($year, $month),
            'year' => self::getYearDateRange($year),
            'week' => self::getWeekDateRange()
        ];

        if (!isset($dates[$period])) {
            throw new InvalidArgumentException("Invalid period: {$period}");
        }

        return $dates[$period];
    }

    private static function getMonthDateRange(string $year, string $month): array
    {
        $monthStr = sprintf('%s-%s-01', $year, $month);
        $timestamp = strtotime($monthStr);

        if ($timestamp === false) {
            return [date('Y-m-01'), date('Y-m-t')];
        }

        return [$monthStr, date('Y-m-t', $timestamp)];
    }

    private static function getYearDateRange(string $year): array
    {
        return [
            sprintf('%s-01-01', $year),
            sprintf('%s-12-31', $year)
        ];
    }

    private static function getWeekDateRange(): array
    {
        $mondayTimestamp = strtotime('monday this week');
        $sundayTimestamp = strtotime('sunday this week');

        $mondayDate = $mondayTimestamp !== false ? date('Y-m-d', $mondayTimestamp) : date('Y-m-d');
        $sundayDate = $sundayTimestamp !== false ? date('Y-m-d', $sundayTimestamp) : date('Y-m-d');

        return [$mondayDate, $sundayDate];
    }

    public static function getPeriodTotal(
        int $userId,
        string $period,
        ?string $year = null,
        ?string $month = null
    ): float {
        $year ??= date('Y');
        $month ??= date('m');

        [$startDate, $endDate] = self::getDateRange($period, $year, $month);

        [$conditions, $params] = self::getBaseWhereClause($userId, false, [
            'date >= ?' => $startDate,
            'date <= ?' => $endDate
        ]);

        $sql = sprintf(
            "SELECT COALESCE(SUM(amount), 0) FROM %s WHERE %s",
            self::getTableName(),
            implode(' AND ', $conditions)
        );

        return (float)self::fetchScalar($sql, $params);
    }

    public static function getMonthlyExpenses(int $userId, string $year, string $month): array
    {
        [$startDate, $endDate] = self::getDateRange('month', $year, $month);

        [$conditions, $params] = self::getBaseWhereClause($userId, false, [
            'date >= ?' => $startDate,
            'date <= ?' => $endDate
        ]);

        // Optimize JOIN sequence and use clear table aliases
        // Use index hint for better performance
        $sql = "SELECT
                e.id, e.amount, e.description, e.date,
                c.name as category_name,
                m.name as merchant_name,
                uc.name as user_category_name
            FROM expenses e USE INDEX (idx_user_date)
            INNER JOIN categories c ON e.category_id = c.id
            LEFT JOIN merchants m ON e.merchant_id = m.id
            LEFT JOIN user_categories uc ON e.user_category_id = uc.id
            WHERE " . implode(' AND ', $conditions) . "
            ORDER BY e.date DESC";

        return self::fetchMany($sql, $params);
    }

    public static function getCategoryTotals(int $userId, string $period, string $year, ?string $month = null): array
    {
        [$startDate, $endDate] = self::getDateRange($period, $year, $month);

        // Optimize query by adding an index hint and using INNER JOIN
        $sql = "SELECT
                c.name as category_name,
                SUM(e.amount) as total
            FROM expenses e USE INDEX (idx_user_date)
            INNER JOIN categories c ON e.category_id = c.id
            WHERE e.user_id = ?
            AND e.date >= ?
            AND e.date <= ?
            AND e.deleted_at IS NULL
            GROUP BY e.category_id, c.name
            ORDER BY total DESC";

        return self::fetchMany($sql, [$userId, $startDate, $endDate]);
    }

    public static function getCategoryTotalsForMonth(int $userId, string $year, string $month): array
    {
        return self::getCategoryTotals($userId, 'month', $year, $month);
    }

    public static function getCategoryTotalsForYear(int $userId, string $year): array
    {
        return self::getCategoryTotals($userId, 'year', $year);
    }

    public static function getUserCategoryTotals(
        int $userId,
        string $period,
        string $year,
        ?string $month = null
    ): array {
        [$startDate, $endDate] = self::getDateRange($period, $year, $month);

        // Optimize with index hint and explicit JOIN type
        $sql = "SELECT
                uc.name as user_category_name,
                SUM(e.amount) as total
            FROM expenses e USE INDEX (idx_user_date)
            INNER JOIN user_categories uc ON e.user_category_id = uc.id
            WHERE e.user_id = ?
            AND e.date >= ?
            AND e.date <= ?
            AND e.deleted_at IS NULL
            GROUP BY e.user_category_id, uc.name
            ORDER BY total DESC";

        return self::fetchMany($sql, [$userId, $startDate, $endDate]);
    }

    public static function getMonthlyTotalsForYear(int $userId, string $year): array
    {
        // Optimize by using a date range instead of YEAR() function which can prevent index usage
        $startDate = "{$year}-01-01";
        $endDate = "{$year}-12-31";

        $sql = sprintf(
            "SELECT
                MONTH(date) as month,
                MONTHNAME(date) as month_name,
                SUM(amount) as total
            FROM %s USE INDEX (idx_user_date)
            WHERE user_id = ?
            AND date >= ?
            AND date <= ?
            AND deleted_at IS NULL
            GROUP BY MONTH(date), MONTHNAME(date)
            ORDER BY month",
            self::getTableName()
        );

        return self::fetchMany($sql, [$userId, $startDate, $endDate]);
    }

    public static function getMerchantTotalsForYear(int $userId, string $year): array
    {
        [$startDate, $endDate] = self::getDateRange('year', $year);

        // Optimize with index hint and explicit JOIN type
        // Place most restrictive filters first (user_id)
        $sql = "SELECT
                m.name as merchant_name,
                SUM(e.amount) as total
            FROM expenses e USE INDEX (idx_user_date)
            INNER JOIN merchants m ON e.merchant_id = m.id
            WHERE e.user_id = ?
            AND e.deleted_at IS NULL
            AND e.date >= ?
            AND e.date <= ?
            GROUP BY e.merchant_id, m.name
            ORDER BY total DESC
            LIMIT 10";

        return self::fetchMany($sql, [$userId, $startDate, $endDate]);
    }

    public static function getExpenseCount(int $userId, int $days = 30): int
    {
        $startDate = date('Y-m-d', strtotime("-$days days"));

        [$conditions, $params] = self::getBaseWhereClause($userId, false, [
            'date >= ?' => $startDate
        ]);

        $sql = sprintf(
            "SELECT COUNT(*) FROM %s WHERE %s",
            self::getTableName(),
            implode(' AND ', $conditions)
        );

        return (int)self::fetchScalar($sql, $params);
    }

    public static function getDocumentCountsByCategory(int $userId): array
    {
        $sql = "SELECT
                category_id,
                COUNT(*) as document_count
            FROM expenses
            WHERE user_id = ?
            AND deleted_at IS NULL
            GROUP BY category_id";

        $result = self::fetchMany($sql, [$userId]);

        $counts = [];
        foreach ($result as $row) {
            $counts[$row['category_id']] = (int)$row['document_count'];
        }

        return $counts;
    }

    public static function removeFileAssociation(int $fileId): bool
    {
        return self::transaction(function () use ($fileId) {
            $sql = sprintf(
                "UPDATE %s SET file_id = NULL, updated_at = NOW() WHERE file_id = ?",
                self::getTableName()
            );

            return self::executeSql($sql, [$fileId]);
        });
    }

    public static function findByMerchantName(string $merchantName, ?int $userId = null, int $limit = 10): array
    {
        // Optimize JOIN sequence - start with merchants since we're filtering by merchant name
        $sql = sprintf(
            "SELECT
                e.id, e.amount, e.description, e.date, e.merchant_id,
                c.name AS category_name,
                uc.name AS user_category_name,
                m.name AS merchant_name
            FROM merchants m
            JOIN %s e ON m.id = e.merchant_id AND e.deleted_at IS NULL
            JOIN categories c ON e.category_id = c.id
            LEFT JOIN user_categories uc ON e.user_category_id = uc.id
            WHERE LOWER(m.name) LIKE ?",
            self::getTableName()
        );

        $params = ["%" . strtolower($merchantName) . "%"];

        if ($userId !== null) {
            $sql .= " AND e.user_id = ?";
            $params[] = $userId;
        }

        $sql .= " ORDER BY e.date DESC LIMIT ?";
        $params[] = $limit;

        return self::fetchMany($sql, $params);
    }

    public static function search(
        int $userId,
        string $query = '',
        string $dateFrom = '',
        string $dateTo = '',
        array $categoryIds = [],
        float $minAmount = 0,
        float $maxAmount = 0,
        string $hasDocument = '',
        int $limit = 50,
        int $offset = 0
    ): array {
        $cacheKey = md5(serialize([
            'search', $userId, $query, $dateFrom, $dateTo, $categoryIds,
            $minAmount, $maxAmount, $hasDocument, $limit, $offset
        ]));

        if (isset(self::$cache[$cacheKey]) && self::$cache[$cacheKey]['expires'] > time()) {
            return self::$cache[$cacheKey]['data'];
        }

        [$conditions, $params] = self::buildSearchConditions(
            $userId,
            $query,
            $dateFrom,
            $dateTo,
            $categoryIds,
            $minAmount,
            $maxAmount,
            $hasDocument
        );

        // Optimize JOIN sequence based on conditions
        $sql = "SELECT
                e.id,
                e.amount,
                e.description,
                e.date,
                e.file_id,
                e.merchant_id,
                e.category_id,
                e.user_category_id,
                e.payment_method_id,
                e.notes,
                c.name AS category_name,
                uc.name AS user_category_name,
                m.name AS merchant_name
            FROM expenses e";

        // If we're filtering by category, use INNER JOIN instead of LEFT JOIN
        if (!empty($categoryIds)) {
            $sql .= " INNER JOIN categories c ON e.category_id = c.id";
        } else {
            $sql .= " LEFT JOIN categories c ON e.category_id = c.id";
        }

        // Only join user_categories if we need it
        if (strpos($query, 'user_category') !== false) {
            $sql .= " INNER JOIN user_categories uc ON e.user_category_id = uc.id";
        } else {
            $sql .= " LEFT JOIN user_categories uc ON e.user_category_id = uc.id";
        }

        // Only join merchants if we need it
        if (strpos($query, 'merchant') !== false) {
            $sql .= " INNER JOIN merchants m ON e.merchant_id = m.id";
        } else {
            $sql .= " LEFT JOIN merchants m ON e.merchant_id = m.id";
        }

        $sql .= " WHERE " . implode(' AND ', $conditions) . "
            ORDER BY e.date DESC";

        if ($limit > 0) {
            $sql .= " LIMIT ? OFFSET ?";
            $params[] = $limit;
            $params[] = $offset;
        }

        $results = self::fetchMany($sql, $params);
        self::$cache[$cacheKey] = ['data' => $results, 'expires' => time() + self::CACHE_TTL];

        return $results;
    }

    public static function countSearchResults(
        int $userId,
        string $query = '',
        string $dateFrom = '',
        string $dateTo = '',
        array $categoryIds = [],
        float $minAmount = 0,
        float $maxAmount = 0,
        string $hasDocument = ''
    ): int {
        $cacheKey = md5(serialize([
            'count', $userId, $query, $dateFrom, $dateTo, $categoryIds,
            $minAmount, $maxAmount, $hasDocument
        ]));

        if (isset(self::$cache[$cacheKey]) && self::$cache[$cacheKey]['expires'] > time()) {
            return self::$cache[$cacheKey]['data'];
        }

        [$conditions, $params] = self::buildSearchConditions(
            $userId,
            $query,
            $dateFrom,
            $dateTo,
            $categoryIds,
            $minAmount,
            $maxAmount,
            $hasDocument
        );

        // Optimize JOIN sequence based on conditions - same as in search method
        $sql = "SELECT COUNT(*) FROM expenses e";

        // If we're filtering by category, use INNER JOIN instead of LEFT JOIN
        if (!empty($categoryIds)) {
            $sql .= " INNER JOIN categories c ON e.category_id = c.id";
        } else {
            $sql .= " LEFT JOIN categories c ON e.category_id = c.id";
        }

        // Only join user_categories if we need it
        if (strpos($query, 'user_category') !== false) {
            $sql .= " INNER JOIN user_categories uc ON e.user_category_id = uc.id";
        } else {
            $sql .= " LEFT JOIN user_categories uc ON e.user_category_id = uc.id";
        }

        // Only join merchants if we need it
        if (strpos($query, 'merchant') !== false) {
            $sql .= " INNER JOIN merchants m ON e.merchant_id = m.id";
        } else {
            $sql .= " LEFT JOIN merchants m ON e.merchant_id = m.id";
        }

        $sql .= " WHERE " . implode(' AND ', $conditions);

        $count = (int)self::fetchScalar($sql, $params);
        self::$cache[$cacheKey] = ['data' => $count, 'expires' => time() + self::CACHE_TTL];

        return $count;
    }

    private static function buildSearchConditions(
        int $userId,
        string $query,
        string $dateFrom,
        string $dateTo,
        array $categoryIds,
        float $minAmount,
        float $maxAmount,
        string $hasDocument
    ): array {
        // Start with the most restrictive conditions first
        $conditions = ["e.user_id = ?", "e.deleted_at IS NULL"];
        $params = [$userId];

        // Exact matches first (most restrictive)
        if (!empty($categoryIds)) {
            // Filter out non-positive values for backward compatibility
            $validCategoryIds = array_filter($categoryIds, function ($id) {
                return is_numeric($id) && (int)$id > 0;
            });

            if (!empty($validCategoryIds)) {
                $placeholders = str_repeat('?,', count($validCategoryIds) - 1) . '?';
                $conditions[] = "e.category_id IN ($placeholders)";
                $params = array_merge($params, array_map('intval', $validCategoryIds));
            }
        }

        if ($hasDocument === '1') {
            $conditions[] = "e.file_id IS NOT NULL";
        } elseif ($hasDocument === '0') {
            $conditions[] = "e.file_id IS NULL";
        }

        // Range conditions next
        if ($minAmount) {
            $conditions[] = "e.amount >= ?";
            $params[] = $minAmount;
        }

        if ($maxAmount) {
            $conditions[] = "e.amount <= ?";
            $params[] = $maxAmount;
        }

        if ($dateFrom) {
            $conditions[] = "e.date >= ?";
            $params[] = $dateFrom;
        }

        if ($dateTo) {
            $conditions[] = "e.date <= ?";
            $params[] = $dateTo;
        }

        // LIKE conditions last (least restrictive)
        if ($query) {
            $conditions[] = "(e.description LIKE ? OR m.name LIKE ? OR e.notes LIKE ? OR e.receipt_text LIKE ?)";
            $paramQuery = "%{$query}%";
            $params = array_merge($params, [$paramQuery, $paramQuery, $paramQuery, $paramQuery]);
        }

        return [$conditions, $params];
    }

    public static function clearCache(): void
    {
        self::$cache = [];
    }

    // Call this method periodically to clean expired cache items
    public static function cleanExpiredCacheItems(): void
    {
        $now = time();
        foreach (self::$cache as $key => $item) {
            if ($item['expires'] < $now) {
                unset(self::$cache[$key]);
            }
        }
    }

    /**
     * Find expenses by user ID
     *
     * @param int $userId The user ID
     * @param int $limit The maximum number of expenses to return
     * @param int $offset The offset for pagination
     * @return array The expenses
     */
    public static function findByUserId(int $userId, int $limit = 20, int $offset = 0): array
    {
        try {
            $db = Database::getInstance();
            $stmt = $db->prepare("
                SELECT
                    e.id, e.amount, e.description, e.date, e.category_id,
                    e.user_category_id, e.merchant_id, e.payment_method_id, e.file_id,
                    c.name as category_name,
                    uc.name as user_category_name,
                    m.name as merchant_name
                FROM expenses e USE INDEX (idx_user_date)
                INNER JOIN categories c ON e.category_id = c.id
                LEFT JOIN user_categories uc ON e.user_category_id = uc.id
                LEFT JOIN merchants m ON e.merchant_id = m.id
                WHERE e.user_id = ? AND e.deleted_at IS NULL
                ORDER BY e.date DESC
                LIMIT ? OFFSET ?
            ");
            $stmt->execute([$userId, $limit, $offset]);

            return $stmt->fetchAll(\PDO::FETCH_ASSOC);
        } catch (\PDOException $e) {
            throw new DatabaseException("Error finding expenses by user ID: " . $e->getMessage(), (int)$e->getCode());
        }
    }

    /**
     * Find expenses by category ID
     *
     * @param int $categoryId The category ID
     * @param int|null $userId The user ID (optional)
     * @param int $limit The maximum number of expenses to return
     * @param int $offset The offset for pagination
     * @return array The expenses
     */
    public static function findByCategoryId(
        int $categoryId,
        ?int $userId = null,
        int $limit = 20,
        int $offset = 0
    ): array {
        try {
            // Use explicit column selection and index hint
            $sql = "
                SELECT
                    e.id, e.amount, e.description, e.date, e.category_id,
                    e.user_category_id, e.merchant_id, e.payment_method_id, e.file_id,
                    c.name as category_name,
                    m.name as merchant_name
                FROM expenses e USE INDEX (idx_category)
                INNER JOIN categories c ON e.category_id = c.id
                LEFT JOIN merchants m ON e.merchant_id = m.id
                WHERE e.category_id = ? AND e.deleted_at IS NULL
            ";
            $params = [$categoryId];

            // Most restrictive filter first (user_id)
            if ($userId !== null) {
                $sql .= " AND e.user_id = ?";
                $params[] = $userId;
            }

            $sql .= " ORDER BY e.date DESC LIMIT ? OFFSET ?";
            $params[] = $limit;
            $params[] = $offset;

            $db = Database::getInstance();
            $stmt = $db->prepare($sql);
            $stmt->execute($params);

            return $stmt->fetchAll(\PDO::FETCH_ASSOC);
        } catch (\PDOException $e) {
            $message = "Error finding expenses by category ID: " . $e->getMessage();
            throw new DatabaseException($message, (int)$e->getCode());
        }
    }

    /**
     * Find expenses by user category ID
     *
     * @param int $userCategoryId The user category ID
     * @param int|null $userId The user ID (optional)
     * @param int $limit The maximum number of expenses to return
     * @param int $offset The offset for pagination
     * @return array The expenses
     */
    public static function findByUserCategoryId(
        int $userCategoryId,
        ?int $userId = null,
        int $limit = 20,
        int $offset = 0
    ): array {
        try {
            $sql = "
                SELECT e.*, uc.name as user_category_name, m.name as merchant_name
                FROM expenses e
                LEFT JOIN user_categories uc ON e.user_category_id = uc.id
                LEFT JOIN merchants m ON e.merchant_id = m.id
                WHERE e.user_category_id = ? AND e.deleted_at IS NULL
            ";
            $params = [$userCategoryId];

            if ($userId !== null) {
                $sql .= " AND e.user_id = ?";
                $params[] = $userId;
            }

            $sql .= " ORDER BY e.date DESC LIMIT ? OFFSET ?";
            $params[] = $limit;
            $params[] = $offset;

            $db = Database::getInstance();
            $stmt = $db->prepare($sql);
            $stmt->execute($params);

            return $stmt->fetchAll();
        } catch (\PDOException $e) {
            $message = "Error finding expenses by user category ID: " . $e->getMessage();
            throw new DatabaseException($message, (int)$e->getCode());
        }
    }

    /**
     * Find expenses by date range
     *
     * @param string $startDate The start date (YYYY-MM-DD)
     * @param string $endDate The end date (YYYY-MM-DD)
     * @param int|null $userId The user ID (optional)
     * @param int $limit The maximum number of expenses to return
     * @param int $offset The offset for pagination
     * @return array The expenses
     */
    public static function findByDateRange(
        string $startDate,
        string $endDate,
        ?int $userId = null,
        int $limit = 20,
        int $offset = 0
    ): array {
        try {
            $sql = "
                SELECT e.*, c.name as category_name, uc.name as user_category_name,
                       m.name as merchant_name
                FROM expenses e
                LEFT JOIN categories c ON e.category_id = c.id
                LEFT JOIN user_categories uc ON e.user_category_id = uc.id
                LEFT JOIN merchants m ON e.merchant_id = m.id
                WHERE e.date >= ? AND e.date <= ? AND e.deleted_at IS NULL
            ";
            $params = [$startDate, $endDate];

            if ($userId !== null) {
                $sql .= " AND e.user_id = ?";
                $params[] = $userId;
            }

            $sql .= " ORDER BY e.date DESC LIMIT ? OFFSET ?";
            $params[] = $limit;
            $params[] = $offset;

            $db = Database::getInstance();
            $stmt = $db->prepare($sql);
            $stmt->execute($params);

            return $stmt->fetchAll();
        } catch (\PDOException $e) {
            $message = "Error finding expenses by date range: " . $e->getMessage();
            throw new DatabaseException($message, (int)$e->getCode());
        }
    }

    /**
     * Find expenses by merchant ID
     *
     * @param int $merchantId The merchant ID
     * @param int|null $userId The user ID (optional)
     * @param int $limit The maximum number of expenses to return
     * @param int $offset The offset for pagination
     * @return array The expenses
     */
    public static function findByMerchantId(
        int $merchantId,
        ?int $userId = null,
        int $limit = 20,
        int $offset = 0
    ): array {
        try {
            $sql = "
                SELECT e.*, c.name as category_name, uc.name as user_category_name,
                       m.name as merchant_name
                FROM expenses e
                LEFT JOIN categories c ON e.category_id = c.id
                LEFT JOIN user_categories uc ON e.user_category_id = uc.id
                LEFT JOIN merchants m ON e.merchant_id = m.id
                WHERE e.merchant_id = ? AND e.deleted_at IS NULL
            ";
            $params = [$merchantId];

            if ($userId !== null) {
                $sql .= " AND e.user_id = ?";
                $params[] = $userId;
            }

            $sql .= " ORDER BY e.date DESC LIMIT ? OFFSET ?";
            $params[] = $limit;
            $params[] = $offset;

            $db = Database::getInstance();
            $stmt = $db->prepare($sql);
            $stmt->execute($params);

            return $stmt->fetchAll();
        } catch (\PDOException $e) {
            $message = "Error finding expenses by merchant ID: " . $e->getMessage();
            throw new DatabaseException($message, (int)$e->getCode());
        }
    }

    /**
     * Get amount bounds (min and max) for a user's expenses
     *
     * @param int $userId The user ID
     * @return array Array with 'min' and 'max' keys containing float values
     * @throws DatabaseException If database query fails
     */
    public static function getAmountBoundsForUser(int $userId): array
    {
        try {
            $sql = "SELECT COALESCE(MIN(amount),0) AS min_amount, COALESCE(MAX(amount),0) AS max_amount FROM expenses WHERE user_id = ? AND deleted_at IS NULL";
            $row = self::fetchOne($sql, [$userId]);
            
            return [
                'min' => (float)$row['min_amount'],
                'max' => (float)$row['max_amount']
            ];
        } catch (\PDOException $e) {
            error_log("Error getting amount bounds for user {$userId}: " . $e->getMessage());
            $code = is_numeric($e->getCode()) ? (int)$e->getCode() : 0;
            throw new DatabaseException("Failed to get amount bounds for user", $code, $e);
        }
    }
}
