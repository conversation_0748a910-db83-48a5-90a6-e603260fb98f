<?php

declare(strict_types=1);

namespace App\Controllers;

use App\Core\Response;
use App\Core\LogManager;
use App\Services\ReportingService;
use App\Exceptions\InvalidArgumentException;

class ReportController extends BaseAuthController
{
    public function __construct(
        private readonly ReportingService $reportingService
    ) {
        // Constructor should not contain authentication checks that redirect
        // Each action method will check authentication
    }



    public function index(): Response
    {
        if (!$this->checkAuthentication()) {
            return $this->redirect('/login', 'Please login to view reports');
        }

        try {
            $userId = $this->getUserId();
            $dashboardData = $this->reportingService->getDashboardData($userId);

            return $this->view('reports/index', [
                'monthlyTotal' => $dashboardData['monthlyTotal'],
                'yearlyTotal' => $dashboardData['yearlyTotal'],
                'recentExpenses' => $dashboardData['recentExpenses']
            ]);
        } catch (\Exception $e) {
            error_log("Error in ReportController::index: " . $e->getMessage());
            return $this->redirect('/dashboard', 'An error occurred while loading reports', 'error');
        }
    }

    public function monthlyReport(): Response
    {
        if (!$this->checkAuthentication()) {
            return $this->redirect('/login', 'Please login to view monthly reports');
        }

        try {
            $userId = $this->getUserId();
            $year = $this->getQueryValue('year', date('Y'));
            $month = $this->getQueryValue('month', date('m'));

            $reportData = $this->reportingService->generateMonthlyReport($userId, $year, $month);
            return $this->view('reports/monthly', $reportData);
        } catch (\Exception $e) {
            LogManager::logException($e, 'Error generating monthly report');
            return $this->redirectWithErrors('/reports', 'Error generating monthly report');
        }
    }

    public function summaryReport(): Response
    {
        if (!$this->checkAuthentication()) {
            return $this->redirect('/login', 'Please login to view summary reports');
        }

        try {
            $userId = $this->getUserId();
            $year = $this->getQueryValue('year', date('Y'));

            $reportData = $this->reportingService->generateSummaryReport($userId, $year);
            return $this->view('reports/summary', $reportData);
        } catch (\Exception $e) {
            LogManager::logException($e, 'Error generating summary report');
            return $this->redirectWithErrors('/reports', 'Error generating summary report');
        }
    }

    public function categoryReport(): Response
    {
        if (!$this->checkAuthentication()) {
            return $this->redirect('/login', 'Please login to view category reports');
        }

        try {
            $userId = $this->getUserId();
            $period = $this->getQueryValue('period', 'month');
            $date = $this->getQueryValue('date', date('Y-m'));

            $viewData = [
                'categoryData' => $this->reportingService->getCategoryWiseExpenses($userId),
                'selectedPeriod' => $period,
                'selectedDate' => $date,
                'availableQuarters' => $this->getAvailableQuarters(),
                'availableYears' => $this->getAvailableYears()
            ];

            return $this->view('reports/category', $viewData);
        } catch (\Exception $e) {
            LogManager::logException($e, 'Error generating category report');
            return $this->redirectWithErrors('/reports', 'Error generating category report');
        }
    }

    public function export(): Response
    {
        if (!$this->checkAuthentication()) {
            return $this->redirect('/login', 'Please login to export reports');
        }

        try {
            $userId = $this->getUserId();
            $type = $this->getQueryValue('type', 'summary');
            $format = $this->getQueryValue('format', 'csv');

            // If no parameters are provided, show the export options page
            if (empty($_GET['type']) && empty($_GET['format'])) {
                // Get categories for the filter dropdown
                $categoryData = $this->reportingService->getCategoryWiseExpenses($userId);

                // We don't have a method to get previous exports, so we'll just pass an empty array
                $previousExports = [];

                return $this->view('reports/export', [
                    'categories' => $categoryData,
                    'previous_exports' => $previousExports
                ]);
            }

            // Handle different export types
            switch ($type) {
                case 'monthly':
                    $year = $this->getQueryValue('year', date('Y'));

                    // Use existing methods to get the data
                    $monthlyData = $this->reportingService->generateMonthlyReport($userId, $year, date('m'));
                    $data = $monthlyData['monthlyData'];
                    break;

                case 'category':
                    // Use existing methods to get the data
                    $data = $this->reportingService->getCategoryWiseExpenses($userId);
                    break;

                case 'summary':
                default:
                    $data = $this->getExportData($userId, $type);
                    break;
            }

            $filename = $this->generateExportFilename($type, $format);
            $headers = $this->getExportHeaders($filename, $format);

            $content = $this->reportingService->formatExportData($data, $format);
            return new Response($content, 200, $headers);
        } catch (\Exception $e) {
            LogManager::logException($e, 'Error exporting report');
            return $this->redirectWithErrors('/reports', 'Error exporting report');
        }
    }

    private function getExportData(int $userId, string $type): array
    {
        return match ($type) {
            'detailed' => $this->reportingService->getDetailedExportData($userId),
            'summary' => $this->reportingService->getSummaryExportData($userId),
            default => throw new InvalidArgumentException('Invalid export type')
        };
    }

    private function generateExportFilename(string $type, string $format): string
    {
        return "expense_report_{$type}_" . date('Y-m-d') . ".{$format}";
    }

    private function getExportHeaders(string $filename, string $format): array
    {
        return [
            'Content-Type' => "application/{$format}",
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
            'Cache-Control' => 'no-cache, must-revalidate',
            'Pragma' => 'no-cache',
            'Expires' => '0'
        ];
    }

    private function getAvailableQuarters(): array
    {
        $currentYear = (int)date('Y');
        $currentQuarter = ceil(date('n') / 3);
        $quarters = [];

        for ($year = $currentYear; $year >= $currentYear - 2; $year--) {
            $maxQuarters = ($year === $currentYear) ? $currentQuarter : 4;

            for ($q = $maxQuarters; $q >= 1; $q--) {
                $quarters[] = [
                    'value' => "{$year}-Q{$q}",
                    'label' => "Q{$q} {$year}"
                ];
            }
        }

        return $quarters;
    }

    private function getAvailableYears(): array
    {
        $currentYear = (int)date('Y');
        return range($currentYear, $currentYear - 2);
    }

    public function generateExport(): Response
    {
        if (!$this->checkAuthentication()) {
            return $this->redirect('/login', 'Please login to generate reports');
        }

        try {
            $userId = $this->getUserId();
            $format = $this->getQueryValue('format', 'csv');
            $reportType = $this->getQueryValue('type', 'monthly');

            $data = $this->getReportData($userId, $reportType);

            if ($data === null) {
                return $this->redirect('/reports', 'Invalid report type', 'error');
            }

            return $this->generateExportByFormat($data, $reportType, $format);
        } catch (\Exception $e) {
            return $this->handleExportError($e);
        }
    }

    private function getReportData(int $userId, string $reportType): ?array
    {
        return match ($reportType) {
            'monthly' => $this->getMonthlyReportData($userId),
            'category' => $this->getCategoryReportData($userId),
            'summary' => $this->reportingService->getSummaryReport($userId),
            default => null
        };
    }

    private function getMonthlyReportData(int $userId): array
    {
        $period = $this->getQueryValue('period', date('Y-m'));
        return $this->reportingService->getMonthlyReport($userId, $period);
    }

    private function getCategoryReportData(int $userId): array
    {
        $categoryId = (int)$this->getQueryValue('category_id', '0');
        return $this->reportingService->getCategoryReport($userId, $categoryId);
    }

    private function generateExportByFormat(array $data, string $reportType, string $format): Response
    {
        return match ($format) {
            'csv' => $this->generateCsvExport($data, $reportType),
            'json' => $this->generateJsonExport($data, $reportType),
            'pdf' => $this->redirect('/reports', 'PDF export is not yet implemented', 'info'),
            default => $this->redirect('/reports', 'Invalid export format', 'error'),
        };
    }

    private function handleExportError(\Exception $e): Response
    {
        LogManager::logException($e, "Error generating export");
        return $this->redirect('/reports', 'An error occurred while generating the export', 'error');
    }

    private function generateCsvExport(array $data, string $reportType): Response
    {
        $filename = "expense_report_{$reportType}_" . date('Y-m-d') . ".csv";
        $output = fopen('php://temp', 'r+');

        $this->writeCsvData($output, $data, $reportType);

        rewind($output);
        $csvContent = stream_get_contents($output);
        fclose($output);

        return $this->createCsvResponse($csvContent, $filename);
    }

    private function writeCsvData($output, array $data, string $reportType): void
    {
        switch ($reportType) {
            case 'monthly':
                $this->writeMonthlyReportCsv($output, $data);
                break;
            case 'category':
                $this->writeCategoryReportCsv($output, $data);
                break;
            case 'summary':
                $this->writeSummaryReportCsv($output, $data);
                break;
        }
    }

    private function writeMonthlyReportCsv($output, array $data): void
    {
        fputcsv($output, ['Date', 'Category', 'Description', 'Amount']);
        foreach ($data['expenses'] as $expense) {
            fputcsv($output, [
                $expense['date'],
                $expense['category_name'] ?? $expense['user_category_name'] ?? 'Uncategorized',
                $expense['description'],
                $expense['amount']
            ]);
        }
    }

    private function writeCategoryReportCsv($output, array $data): void
    {
        fputcsv($output, ['Category', 'Total Amount', 'Count', 'Average']);
        foreach ($data['categories'] as $category) {
            fputcsv($output, [
                $category['name'],
                $category['total'],
                $category['count'],
                $category['average']
            ]);
        }
    }

    private function writeSummaryReportCsv($output, array $data): void
    {
        fputcsv($output, ['Period', 'Total Expenses', 'Categories', 'Average Per Day']);
        foreach ($data['summary'] as $summary) {
            fputcsv($output, [
                $summary['period'],
                $summary['total'],
                $summary['categories'],
                $summary['average_per_day']
            ]);
        }
    }

    private function createCsvResponse(string $csvContent, string $filename): Response
    {
        return Response::text($csvContent, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
            'Content-Length' => strlen($csvContent)
        ]);
    }

    private function generateJsonExport(array $data, string $reportType): Response
    {
        $filename = "expense_report_{$reportType}_" . date('Y-m-d') . ".json";

        return Response::json($data, 200, [
            'Content-Disposition' => "attachment; filename=\"{$filename}\""
        ]);
    }
}
