<?php

/**
 * Authentication configuration settings
 */

return [
    // Session timeout in seconds (30 minutes)
    'session_timeout' => 1800,

    // Remember token expiry in days
    'remember_token_expiry_days' => 30,

    // Password reset token expiry in hours
    'reset_token_expiry_hours' => 24,

    // Minimum password length
    'password_min_length' => 8,

    // Password hashing cost
    'password_hash_cost' => 12,

    // Session cookie settings
    'session_cookie' => [
        'secure' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on',
        'httponly' => true,
        'samesite' => 'Lax'
    ],

    // Remember token cookie settings
    'remember_cookie' => [
        'name' => 'remember_token',
        'path' => '/',
        'domain' => '',
        'secure' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on',
        'httponly' => true,
        'samesite' => 'Lax'
    ],

    // Important session keys that should be preserved during session clearing
    'preserved_session_keys' => [
        'redirect_after_login',
        'flash_messages'
    ]
];
