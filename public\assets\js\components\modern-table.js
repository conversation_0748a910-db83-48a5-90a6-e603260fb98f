/**
 * Modern Table functionality
 * Handles sorting, filtering, and responsive behavior for the modern table component
 */
export function initializeModernTable()
{
    console.log('Initializing modern tables');
    const tables = document.querySelectorAll('.modern-table table');
    console.log(`Found ${tables.length} tables to initialize`);

    tables.forEach(table => {
        const sortButtons = table.querySelectorAll('.sort-button');

        // Set initial sort on date column (newest first)
        if (sortButtons.length > 0) {
            // First, check if any button is already active (from previous sort)
            const activeButton = table.querySelector('.sort-button.active');

            if (activeButton) {
                // Use the existing active button's settings
                const column = activeButton.dataset.sort;
                const direction = activeButton.dataset.direction || 'desc';

                // Make sure aria-sort is set correctly
                const parentTh = activeButton.closest('th');
                if (parentTh) {
                    parentTh.setAttribute('aria-sort', direction === 'asc' ? 'ascending' : 'descending');
                }

                // Update the sort indicator with direction arrow
                const sortIcon = activeButton.querySelector('.sort-icon');
                const directionIndicator = activeButton.querySelector('.direction-indicator');

                // Set the direction on the button itself - this will trigger the CSS rotation
                activeButton.setAttribute('data-direction', direction);

                // Update the direction indicator text
                if (directionIndicator) {
                    directionIndicator.textContent = direction === 'asc' ? '↑' : '↓';
                    directionIndicator.classList.add('active');
                }

                if (sortIcon) {
                    sortIcon.classList.add('active');
                }

                // Update the aria-label to indicate what will happen on next click
                const columnName = activeButton.closest('.th-content').querySelector('span').textContent.trim();
                const currentDirection = direction === 'asc' ? 'ascending' : 'descending';
                const nextDirection = direction === 'asc' ? 'descending' : 'ascending';
                activeButton.setAttribute('aria-label', `Currently sorted by ${columnName} ${currentDirection}. Click to sort ${nextDirection}`);

                // Sort the table using existing settings
                sortTable(table, column, direction);
            } else {
                // No active button, default to date sorting
                const dateButton = table.querySelector('.sort-button[data-sort="date"]');
                if (dateButton) {
                    // Set initial sort state
                    dateButton.classList.add('active');
                    dateButton.dataset.direction = 'desc';

                    // Set aria-sort on parent th
                    const parentTh = dateButton.closest('th');
                    if (parentTh) {
                        parentTh.setAttribute('aria-sort', 'descending');
                    }

                    // Update the sort indicator with direction arrow
                    const sortIcon = dateButton.querySelector('.sort-icon');
                    const directionIndicator = dateButton.querySelector('.direction-indicator');

                    // Set the direction on the button itself - this will trigger the CSS rotation
                    dateButton.setAttribute('data-direction', 'desc');

                    // Update the direction indicator text
                    if (directionIndicator) {
                        directionIndicator.textContent = '↓';
                        directionIndicator.classList.add('active');
                    }

                    if (sortIcon) {
                        sortIcon.classList.add('active');
                    }

                    // Update the aria-label to indicate what will happen on next click
                    const columnName = dateButton.closest('.th-content').querySelector('span').textContent.trim();
                    dateButton.setAttribute('aria-label', `Currently sorted by ${columnName} descending. Click to sort ascending`);

                    // Sort the table
                    sortTable(table, 'date', 'desc');
                }
            }
        }

        // Add click handlers to sort buttons
        console.log(`Adding click handlers to ${sortButtons.length} sort buttons`);
        sortButtons.forEach(button => {
            button.addEventListener('click', function (e) {
                e.preventDefault();
                e.stopPropagation();
                console.log('Sort button clicked');

                const column = this.dataset.sort;
                const currentDirection = this.dataset.direction || 'desc';
                const newDirection = currentDirection === 'asc' ? 'desc' : 'asc';

                // Reset all buttons and their parent th elements
                sortButtons.forEach(btn => {
                    btn.classList.remove('active');
                    btn.removeAttribute('data-direction');

                    // Reset sort icon
                    const sortIcon = btn.querySelector('.sort-icon');
                    if (sortIcon) {
                        sortIcon.classList.remove('active');
                    }

                    // Reset direction indicator
                    const directionIndicator = btn.querySelector('.direction-indicator');
                    if (directionIndicator) {
                        directionIndicator.textContent = '';
                        directionIndicator.classList.remove('active');
                    }

                    // Reset aria-sort on parent th
                    const parentTh = btn.closest('th');
                    if (parentTh) {
                        parentTh.setAttribute('aria-sort', 'none');
                    }
                });

                // Set active state and direction for clicked button
                this.classList.add('active');
                this.dataset.direction = newDirection;

                // Set aria-sort on parent th
                const parentTh = this.closest('th');
                if (parentTh) {
                    parentTh.setAttribute('aria-sort', newDirection === 'asc' ? 'ascending' : 'descending');
                }

                // Update the sort indicator with direction arrow
                const sortIcon = this.querySelector('.sort-icon');
                const directionIndicator = this.querySelector('.direction-indicator');

                // Set the direction on the button itself - this will trigger the CSS rotation
                this.setAttribute('data-direction', newDirection);

                // Update the direction indicator text
                if (directionIndicator) {
                    directionIndicator.textContent = newDirection === 'asc' ? '↑' : '↓';
                    directionIndicator.classList.add('active');
                }

                if (sortIcon) {
                    // Add a class to indicate active state
                    sortIcon.classList.add('active');
                    console.log('Updated sort button with direction:', newDirection);
                } else {
                    console.error('Sort icon not found in button:', this.outerHTML);
                }

                // Update the aria-label to indicate what will happen on next click
                const columnName = this.closest('.th-content').querySelector('span').textContent.trim();
                const directionText = newDirection === 'asc' ? 'ascending' : 'descending';
                const nextDirectionText = newDirection === 'asc' ? 'descending' : 'ascending';
                this.setAttribute('aria-label', `Currently sorted by ${columnName} ${directionText}. Click to sort ${nextDirectionText}`);

                // Sort the table
                sortTable(table, column, newDirection);
            });
        });

        // Initialize action menus
        const actionMenus = table.querySelectorAll('.action-menu');

        actionMenus.forEach(menu => {
            const toggle = menu.querySelector('.action-menu-toggle');
            const dropdown = menu.querySelector('.action-menu-dropdown');

            if (toggle && dropdown) {
                // Show dropdown on toggle click
                toggle.addEventListener('click', function (e) {
                    e.stopPropagation();

                    // Close all other dropdowns
                    document.querySelectorAll('.action-menu-dropdown.show').forEach(el => {
                        if (el !== dropdown) {
                            el.classList.remove('show');

                            // Find the toggle button for this dropdown and update its state
                            const otherToggle = el.parentNode.querySelector('.action-menu-toggle');
                            if (otherToggle) {
                                otherToggle.setAttribute('aria-expanded', 'false');
                            }
                        }
                    });

                    // Toggle current dropdown
                    const isExpanded = dropdown.classList.toggle('show');

                    // Update aria-expanded attribute
                    toggle.setAttribute('aria-expanded', isExpanded);

                    // Position the dropdown to ensure it's fully visible
                    if (isExpanded) {
                        // Get the position of the toggle button
                        const toggleRect = toggle.getBoundingClientRect();
                        const viewportHeight = window.innerHeight;
                        const viewportWidth = window.innerWidth;

                        // Position dropdown to the right of the toggle button
                        let top = toggleRect.top;
                        let left = toggleRect.right + 5; // 5px to the right of the toggle

                        // Check if there's enough space to the right
                        if (left + 180 > viewportWidth) {
                            // Not enough space to the right, try to the left
                            left = toggleRect.left - 180 - 5; // 5px to the left of the toggle

                            // If not enough space to the left either, position below
                            if (left < 0) {
                                left = toggleRect.left;
                                top = toggleRect.bottom + 5; // 5px below the toggle

                                // If not enough space below, position above
                                if (top + 200 > viewportHeight) {
                                    top = toggleRect.top - 200 - 5; // 5px above the toggle
                                }
                            }
                        }

                        // Apply the position
                        dropdown.style.top = `${top}px`;
                        dropdown.style.left = `${left}px`;
                    }
                });

                // Close dropdown when clicking outside
                document.addEventListener('click', function (e) {
                    if (!menu.contains(e.target)) {
                        dropdown.classList.remove('show');
                        toggle.setAttribute('aria-expanded', 'false');
                    }
                });

                // Add keyboard navigation
                toggle.addEventListener('keydown', function (e) {
                    // Open menu on Enter or Space
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        toggle.click();

                        // Focus first menu item
                        if (dropdown.classList.contains('show')) {
                            const firstItem = dropdown.querySelector('a, button');
                            if (firstItem) {
                                firstItem.focus();
                            }
                        }
                    }
                });

                // Handle keyboard navigation within dropdown
                dropdown.addEventListener('keydown', function (e) {
                    const items = dropdown.querySelectorAll('a, button');
                    const currentIndex = Array.from(items).indexOf(document.activeElement);

                    // Close on Escape
                    if (e.key === 'Escape') {
                        dropdown.classList.remove('show');
                        toggle.setAttribute('aria-expanded', 'false');
                        toggle.focus();
                    }
                    // Navigate with arrow keys
                    else if (e.key === 'ArrowDown' && currentIndex < items.length - 1) {
                        e.preventDefault();
                        items[currentIndex + 1].focus();
                    } else if (e.key === 'ArrowUp' && currentIndex > 0) {
                        e.preventDefault();
                        items[currentIndex - 1].focus();
                    }
                    // Loop to first/last item
                    else if (e.key === 'ArrowDown' && currentIndex === items.length - 1) {
                        e.preventDefault();
                        items[0].focus();
                    } else if (e.key === 'ArrowUp' && currentIndex === 0) {
                        e.preventDefault();
                        items[items.length - 1].focus();
                    }
                });
            }
        });

        // Make table responsive
        makeTableResponsive(table);
    });
}

/**
 * Sort table by column
 * @param {HTMLElement} table - The table element
 * @param {string} column - The column to sort by
 * @param {string} direction - The sort direction ('asc' or 'desc')
 */
function sortTable(table, column, direction)
{
    console.log(`Sorting by ${column} in ${direction} direction`);

    const tbody = table.querySelector('tbody');
    if (!tbody) {
        console.error('No tbody found in table:', table);
        return;
    }

    const rows = Array.from(tbody.querySelectorAll('tr'));

    if (rows.length === 0) {
        console.log('No rows found to sort');
        return;
    }

    // Debug: Log the first row structure to check selectors
    if (rows.length > 0) {
        console.log('First row structure:', rows[0].outerHTML);
    }

    // Sort rows based on column content
    const sortedRows = rows.sort((a, b) => {
        let aValue, bValue;

        try {
            switch (column) {
                case 'date':
                    console.log('Sorting by date column');
                    const aDateEl = a.querySelector('.date-column time');
                    const bDateEl = b.querySelector('.date-column time');

                    if (!aDateEl || !bDateEl) {
                        console.error(
                            'Date elements not found:',
                            aDateEl ? 'A date found' : 'A date missing',
                            bDateEl ? 'B date found' : 'B date missing'
                        );
                        return 0;
                    }

                    aValue = aDateEl.getAttribute('datetime');
                    bValue = bDateEl.getAttribute('datetime');
                    console.log('Date values:', aValue, bValue);
                    break;

                case 'description':
                    console.log('Sorting by description column');
                    const aDescEl = a.querySelector('.description-column');
                    const bDescEl = b.querySelector('.description-column');

                    if (!aDescEl || !bDescEl) {
                        console.error(
                            'Description elements not found:',
                            aDescEl ? 'A desc found' : 'A desc missing',
                            bDescEl ? 'B desc found' : 'B desc missing'
                        );
                        return 0;
                    }

                    aValue = aDescEl.textContent.trim().toLowerCase();
                    bValue = bDescEl.textContent.trim().toLowerCase();
                    console.log('Description values:', aValue, bValue);
                    break;

                case 'category_name':
                    console.log('Sorting by category column');
                    const aCatEl = a.querySelector('.category-column .badge');
                    const bCatEl = b.querySelector('.category-column .badge');

                    if (!aCatEl || !bCatEl) {
                        console.error(
                            'Category elements not found:',
                            aCatEl ? 'A category found' : 'A category missing',
                            bCatEl ? 'B category found' : 'B category missing',
                            'Row A:',
                            a.outerHTML,
                            'Row B:',
                            b.outerHTML
                        );
                        return 0;
                    }

                    aValue = aCatEl.textContent.trim().toLowerCase();
                    bValue = bCatEl.textContent.trim().toLowerCase();
                    console.log('Category values:', aValue, bValue);
                    break;

                case 'amount':
                    console.log('Sorting by amount column');
                    const aAmountEl = a.querySelector('.amount-column .amount');
                    const bAmountEl = b.querySelector('.amount-column .amount');

                    if (!aAmountEl || !bAmountEl) {
                        console.error(
                            'Amount elements not found:',
                            aAmountEl ? 'A amount found' : 'A amount missing',
                            bAmountEl ? 'B amount found' : 'B amount missing'
                        );
                        return 0;
                    }

                    // Extract numeric value from currency string
                    const aText = aAmountEl.textContent.trim();
                    const bText = bAmountEl.textContent.trim();

                    // Remove currency symbols and commas, then parse as float
                    aValue = parseFloat(aText.replace(/[^0-9.-]+/g, ''));
                    bValue = parseFloat(bText.replace(/[^0-9.-]+/g, ''));

                    // Handle NaN values
                    if (isNaN(aValue)) {
                        aValue = 0;
                    }
                    if (isNaN(bValue)) {
                        bValue = 0;
                    }

                    console.log('Amount values:', aValue, bValue);
                    break;

                default:
                    console.log(`Unknown column: ${column}`);
                    return 0;
            }
        } catch (error) {
            console.error(`Error sorting column ${column}:`, error);
            return 0;
        }

        // Compare values based on direction
        if (direction === 'asc') {
            return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
        } else {
            return aValue < bValue ? 1 : aValue > bValue ? -1 : 0;
        }
    });

    // Clear and append sorted rows
    while (tbody.firstChild) {
        tbody.removeChild(tbody.firstChild);
    }

    sortedRows.forEach(row => {
        tbody.appendChild(row);
    });

    console.log(`Sorted ${sortedRows.length} rows`);
}

/**
 * Make table responsive
 * @param {HTMLElement} table - The table element
 */
function makeTableResponsive(table)
{
    // Only apply to small screens
    if (window.innerWidth <= 576) {
        const rows = table.querySelectorAll('tbody tr');

        rows.forEach(row => {
            const cells = row.querySelectorAll('td');

            cells.forEach(cell => {
                // Add data attributes for responsive display
                if (cell.classList.contains('date-column')) {
                    cell.setAttribute('data-label', 'Date');
                } else if (cell.classList.contains('description-column')) {
                    cell.setAttribute('data-label', 'Description');
                } else if (cell.classList.contains('category-column')) {
                    cell.setAttribute('data-label', 'Category');
                } else if (cell.classList.contains('amount-column')) {
                    cell.setAttribute('data-label', 'Amount');
                }
            });
        });
    }
}

// Initialize on resize
window.addEventListener('resize', function () {
    const tables = document.querySelectorAll('.modern-table table');
    tables.forEach(makeTableResponsive);
});

// CSS for sort indicators is now in /public/assets/css/components/_sort-indicators.css
