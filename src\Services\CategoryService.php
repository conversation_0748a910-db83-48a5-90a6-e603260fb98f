<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Category;
use App\Models\File;
use App\Exceptions\InvalidArgumentException;
use App\Exceptions\NotFoundException;

class CategoryService extends BaseService
{
    protected function getModelClass(): string
    {
        return Category::class;
    }

    public function getCategoryById(int $id): array
    {
        return $this->findOrFail($id);
    }

    public function findByName(string $name): ?array
    {
        return Category::findByName($name);
    }

    public function all(): array
    {
        return Category::all();
    }

    public function getAllCategories(): array
    {
        try {
            return $this->all();
        } catch (\Throwable $e) {
            // log error
            return [];
        }
    }

    public function createCategory(string $name, ?string $description = null): int
    {
        try {
            return Category::createCategory($name, $description);
        } catch (\Exception $e) {
            $this->handleCategoryException($e, "Failed to create category");
            return 0; // This line will never be reached due to the exception
        }
    }

    public function create(array $data): int
    {
        try {
            return Category::create($data);
        } catch (\Exception $e) {
            $this->handleCategoryException($e, "Failed to create category");
            return 0; // This line will never be reached due to the exception
        }
    }

    public function updateCategory(int $id, ?string $name = null, ?string $description = null): bool
    {
        $this->findOrFail($id);

        try {
            $result = Category::updateCategory($id, $name, $description);
            if (!$result) {
                throw new InvalidArgumentException("No changes were made to the category");
            }
            return $result;
        } catch (\Exception $e) {
            $this->handleCategoryException($e, "Failed to update category");
            return false; // This line will never be reached due to the exception
        }
    }

    public function update(int $id, array $data): bool
    {
        $this->findOrFail($id);

        try {
            $result = Category::update($id, $data);
            if (!$result) {
                throw new InvalidArgumentException("No changes were made to the category");
            }
            return $result;
        } catch (\Exception $e) {
            $this->handleCategoryException($e, "Failed to update category");
            return false; // This line will never be reached due to the exception
        }
    }

    public function delete(int $id): bool
    {
        try {
            return Category::deleteWithValidation($id);
        } catch (\Exception $e) {
            $this->handleCategoryException($e, "Failed to delete category");
            return false; // This line will never be reached due to the exception
        }
    }

    private function handleCategoryException(\Exception $e, string $message): void
    {
        throw new InvalidArgumentException("{$message}: " . $e->getMessage(), 0, $e);
    }

    public function isInUse(int $id): bool
    {
        return Category::isInUse($id);
    }

    public function expenseCount(int $id, ?int $userId = null): int
    {
        return Category::expenseCount($id, $userId);
    }

    public function totalExpenses(int $id, ?int $userId = null): float
    {
        return Category::totalExpenses($id, $userId);
    }

    public function getExpenseBreakdown(int $userId): array
    {
        return Category::getExpenseBreakdown($userId);
    }

    public function getUserCategory(int $id): ?array
    {
        return Category::getUserCategory($id);
    }

    public function getUserCategoryById(int $id, int $userId): ?array
    {
        $category = $this->getUserCategory($id);
        if (!$category || $category['user_id'] !== $userId) {
            return null;
        }
        return $category;
    }

    public function getUserCategoryOrFail(int $id): array
    {
        $category = $this->getUserCategory($id);
        if (!$category) {
            throw new NotFoundException("User category not found");
        }
        return $category;
    }

    public function getUserCategoryByName(int $userId, string $name): ?array
    {
        return Category::getUserCategoryByName($userId, $name);
    }

    public function getUserCategories(int $userId): array
    {
        return Category::getUserCategories($userId);
    }

    public function createUserCategory(int $userId, string $name, ?string $description = null): int
    {
        try {
            return Category::createUserCategory($userId, $name, $description);
        } catch (\Exception $e) {
            $this->handleCategoryException($e, "Failed to create user category");
            return 0; // This line will never be reached due to the exception
        }
    }

    public function updateUserCategory(int $id, int $userId, ?string $name = null, ?string $description = null): bool
    {
        $this->getUserCategoryOrFail($id);

        try {
            $result = Category::updateUserCategory($id, $userId, $name, $description);
            if (!$result) {
                throw new InvalidArgumentException("No changes were made to the user category");
            }
            return $result;
        } catch (\Exception $e) {
            $this->handleCategoryException($e, "Failed to update user category");
            return false; // This line will never be reached due to the exception
        }
    }

    public function deleteUserCategory(int $id, int $userId, bool $hardDelete = false): bool
    {
        try {
            return Category::deleteUserCategoryWithValidation($id, $userId, $hardDelete);
        } catch (\Exception $e) {
            $this->handleCategoryException($e, "Failed to delete user category");
            return false; // This line will never be reached due to the exception
        }
    }

    public function isUserCategoryInUse(int $id): bool
    {
        return Category::isUserCategoryInUse($id);
    }

    public function transferExpenses(int $fromCategoryId, int $toCategoryId, ?int $userId = null): int
    {
        $this->validateTransferCategories($fromCategoryId, $toCategoryId);

        $count = $this->expenseCount($fromCategoryId, $userId);
        if ($count === 0) {
            return 0;
        }

        try {
            return Category::transferExpenses($fromCategoryId, $toCategoryId, $userId);
        } catch (\Exception $e) {
            $this->handleCategoryException($e, "Failed to transfer expenses");
            return 0; // This line will never be reached due to the exception
        }
    }

    private function validateTransferCategories(int $fromCategoryId, int $toCategoryId): void
    {
        $this->findOrFail($fromCategoryId);
        $this->findOrFail($toCategoryId);

        if ($fromCategoryId === $toCategoryId) {
            throw new InvalidArgumentException("Source and destination categories cannot be the same");
        }
    }

    public function getCategoryWithExpenses(int $categoryId, ?int $userId = null): array
    {
        try {
            $category = Category::getCategoryWithExpenses($categoryId, $userId);
            if (!$category) {
                throw new NotFoundException("Category not found");
            }
            return $category;
        } catch (NotFoundException $e) {
            throw $e;
        } catch (\Exception $e) {
            $this->handleCategoryException($e, "Failed to get category with expenses");
            return []; // This line will never be reached due to the exception
        }
    }

    public function getUserCategoryWithExpenses(int $categoryId, int $userId): array
    {
        try {
            $category = Category::getUserCategoryWithExpenses($categoryId, $userId);
            if (!$category) {
                throw new NotFoundException("User category not found");
            }
            return $category;
        } catch (NotFoundException $e) {
            throw $e;
        } catch (\Exception $e) {
            $this->handleCategoryException($e, "Failed to get user category with expenses");
            return []; // This line will never be reached due to the exception
        }
    }

    public function validateCategoryExists(int $categoryId): bool
    {
        return Category::validateCategoryExists($categoryId);
    }

    public function validateUserCategoryExists(int $categoryId, int $userId): bool
    {
        return Category::validateUserCategoryExists($categoryId, $userId);
    }

    public function getUserCategoryExpenseCount(int $categoryId, int $userId): int
    {
        $category = $this->getUserCategoryById($categoryId, $userId);
        if (!$category) {
            return 0;
        }

        return $category['expense_count'] ?? 0;
    }

    public function getUserCategoryTotalExpenses(int $categoryId, int $userId): float
    {
        $category = $this->getUserCategoryById($categoryId, $userId);
        if (!$category) {
            return 0.0;
        }

        return $category['total_amount'] ?? 0.0;
    }

    public function getUserCategoryFileCount(int $categoryId, int $userId): int
    {
        $files = $this->getFilesForCategory($categoryId, $userId);
        return count($files);
    }

    public function getUserCategoryTotalFileSize(int $categoryId, int $userId): int
    {
        $files = $this->getFilesForCategory($categoryId, $userId);
        return array_reduce($files, fn($total, $file) => $total + (int)$file['filesize'], 0);
    }

    public function getDocumentCountsByCategory(int $userId): array
    {
        $categories = $this->getUserCategories($userId);
        $result = [];

        foreach ($categories as $category) {
            $fileCount = $this->getUserCategoryFileCount($category['id'], $userId);
            if ($fileCount > 0) {
                $result[] = [
                    'category_id' => $category['id'],
                    'category_name' => $category['name'],
                    'file_count' => $fileCount
                ];
            }
        }

        return $result;
    }

    private function getFilesForCategory(int $categoryId, int $userId): array
    {
        return File::findByFilter(['category_id' => $categoryId], false, $userId);
    }

    public function getCategoryFiles(int $categoryId, int $userId): array
    {
        $this->findOrFail($categoryId);
        return $this->getFilesForCategory($categoryId, $userId);
    }

    public function getUserCategoryFiles(int $categoryId, int $userId): array
    {
        $this->getUserCategoryOrFail($categoryId);
        return $this->getFilesForCategory($categoryId, $userId);
    }

    public function getCategoryExpenseBreakdown(int $userId): array
    {
        return Category::getExpenseBreakdown($userId);
    }

    public function getCategoryStatistics(int $categoryId, int $userId): array
    {
        try {
            $categoryWithExpenses = Category::getCategoryWithExpenses($categoryId, $userId);
            $recentExpenses = $categoryWithExpenses['recent_expenses'] ?? [];
        } catch (\Exception) {
            $recentExpenses = [];
        }

        return $this->buildCategoryStatistics($categoryId, $userId, $recentExpenses);
    }

    private function buildCategoryStatistics(int $categoryId, int $userId, array $recentExpenses): array
    {
        return [
            'expense_count' => $this->expenseCount($categoryId, $userId),
            'total_expenses' => $this->totalExpenses($categoryId, $userId),
            'file_count' => $this->getCategoryFileCount($categoryId, $userId),
            'file_size' => $this->getCategoryTotalFileSize($categoryId, $userId),
            'recent_expenses' => $recentExpenses
        ];
    }

    public function getUserCategoryStatistics(int $categoryId, int $userId): array
    {
        try {
            $categoryWithExpenses = Category::getUserCategoryWithExpenses($categoryId, $userId);
            $recentExpenses = $categoryWithExpenses['recent_expenses'] ?? [];
        } catch (\Exception) {
            $recentExpenses = [];
        }

        return $this->buildUserCategoryStatistics($categoryId, $userId, $recentExpenses);
    }

    private function buildUserCategoryStatistics(int $categoryId, int $userId, array $recentExpenses): array
    {
        return [
            'expense_count' => $this->getUserCategoryExpenseCount($categoryId, $userId),
            'total_expenses' => $this->getUserCategoryTotalExpenses($categoryId, $userId),
            'file_count' => $this->getUserCategoryFileCount($categoryId, $userId),
            'file_size' => $this->getUserCategoryTotalFileSize($categoryId, $userId),
            'recent_expenses' => $recentExpenses
        ];
    }

    public function getCategoryFileCount(int $categoryId, int $userId): int
    {
        $files = $this->getFilesForCategory($categoryId, $userId);
        return count($files);
    }

    public function getCategoryTotalFileSize(int $categoryId, int $userId): int
    {
        $files = $this->getFilesForCategory($categoryId, $userId);
        return array_reduce($files, fn($total, $file) => $total + (int)$file['filesize'], 0);
    }
}
