/**
 * User dropdown menu functionality
 * Handles toggling the user dropdown menu and closing it when clicking outside
 */
export function initializeUserDropdown()
{
    // Wait for a short delay to ensure all DOM elements are fully loaded and processed
    setTimeout(() => {
        initializeDropdown();
    }, 100);
}

function initializeDropdown()
{
    // Get the dropdown elements
    const dropdownToggle = document.getElementById('userDropdownToggle');
    const dropdown = document.getElementById('user-dropdown');

    if (!dropdownToggle || !dropdown) {
        console.log('User dropdown elements not found');
        return;
    }

    console.log('Initializing user dropdown');

    // Ensure dropdown is initially hidden and has no conflicting inline styles
    dropdown.style.display = '';
    dropdown.classList.remove('show');

    // Remove any existing click listeners to prevent duplicates
    dropdownToggle.removeEventListener('click', handleToggleClick);

    // Add the click listener with a named function so we can remove it if needed
    dropdownToggle.addEventListener('click', handleToggleClick);

    // Named function for the click handler
    function handleToggleClick(e)
    {
        e.preventDefault();
        e.stopPropagation();

        console.log('Toggle button clicked');

        // Force the dropdown to be visible/hidden based on current state
        if (dropdown.classList.contains('show')) {
            dropdown.classList.remove('show');
            console.log('Dropdown is now hidden');
            document.removeEventListener('click', closeOnClickOutside);
        } else {
            dropdown.classList.add('show');
            console.log('Dropdown is now visible');

            // Add a slight delay before adding the document click listener
            setTimeout(() => {
                document.addEventListener('click', closeOnClickOutside);
            }, 10);
        }
    }

    // Close dropdown when clicking outside
    function closeOnClickOutside(e)
    {
        if (!dropdown.contains(e.target) && !dropdownToggle.contains(e.target)) {
            console.log('Clicked outside dropdown');
            dropdown.classList.remove('show');
            document.removeEventListener('click', closeOnClickOutside);
        }
    }

    // Close dropdown with Escape key
    document.addEventListener('keydown', function (e) {
        if (e.key === 'Escape' && dropdown.classList.contains('show')) {
            console.log('Escape key pressed');
            dropdown.classList.remove('show');
            document.removeEventListener('click', closeOnClickOutside);
        }
    });
}
