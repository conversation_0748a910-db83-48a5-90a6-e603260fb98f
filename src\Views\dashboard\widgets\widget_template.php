<?php

/**
 * Base widget template for dashboard widgets
 * Provides a standardized structure for all dashboard widgets
 *
 * @param string $title Widget title
 * @param string $icon Widget icon (optional)
 * @param bool $collapsible Whether the widget can be collapsed (default: true)
 * @param string $widgetId Unique ID for the widget (optional)
 * @param string $widgetClass Additional CSS classes for the widget (optional)
 * @param bool $loading Whether to show a loading state (default: false)
 * @param bool $hasData Whether the widget has data to display (default: true)
 * @param string $emptyMessage Message to display when no data is available (optional)
 */

// Default values
$title ??= 'Widget';
$icon ??= null;
$collapsible ??= true;
$widgetId ??= 'widget-' . strtolower(str_replace(' ', '-', $title));
$widgetClass ??= '';
$loading ??= false;
$hasData ??= true;
$emptyMessage ??= 'No data available.';
?>

<div id="<?= htmlspecialchars($widgetId) ?>" class="widget <?= htmlspecialchars($widgetClass) ?>">
    <div class="widget-header">
        <h3>
            <?php if ($icon) : ?>
                <span class="widget-icon"><?= $icon ?></span>
            <?php endif; ?>
            <?= htmlspecialchars($title) ?>
        </h3>

        <div class="widget-actions">
            <?php if ($collapsible) : ?>
                <button type="button" class="toggle-widget" aria-label="Toggle widget">↑</button>
            <?php endif; ?>
        </div>
    </div>

    <div class="widget-content <?= $loading ? 'loading' : '' ?>">
        <?php if ($loading) : ?>
            <div class="widget-loading">
                <div class="spinner"></div>
                <p>Loading data...</p>
            </div>
        <?php elseif (!$hasData) : ?>
            <div class="widget-empty">
                <p><?= htmlspecialchars($emptyMessage) ?></p>
            </div>
        <?php else : ?>
            <?= $content ?? '' ?>
        <?php endif; ?>
    </div>
</div>
