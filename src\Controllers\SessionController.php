<?php

declare(strict_types=1);

namespace App\Controllers;

use App\Core\Response;
use App\Services\AuthService;

class SessionController extends BaseAuthController
{
    public function __construct(
        private readonly AuthService $authService
    ) {
    }

    public function checkSession(): Response
    {
        try {
            error_log("Session check requested");

            // Check if user is authenticated
            if (!isset($_SESSION['user_id'])) {
                error_log("No user_id in session, checking for remember token");

                // Try to authenticate with remember token
                $isAuthenticated = $this->checkAuthentication();

                if (!$isAuthenticated) {
                    error_log("Authentication failed, no valid session or remember token");
                    return Response::json([
                        'valid' => false,
                        'reason' => 'not_authenticated',
                        'redirect' => '/' // Redirect to home page
                    ]);
                }

                error_log("Authenticated via remember token");
            }

            // At this point, we should have a valid session
            // Check if this is a new browser session
            $isNewSession = \App\Core\Security\Session::isNewBrowserSession();

            // Get the remember_me flag from the session
            $rememberMe = isset($_SESSION['remember_me']) && $_SESSION['remember_me'] === true;

            // Log the session state
            error_log("Session state: user_id=" . ($_SESSION['user_id'] ?? 'not set') .
                      ", remember_me=" . ($rememberMe ? 'true' : 'false') .
                      ", new_session=" . ($isNewSession ? 'true' : 'false'));

            // Check session cookie parameters
            $params = session_get_cookie_params();
            error_log("Session cookie params: " . json_encode($params));

            // Check if session cookie exists
            $sessionName = session_name();
            error_log("Session cookie name: $sessionName");
            error_log("Session cookie exists: " . (isset($_COOKIE[$sessionName]) ? 'Yes' : 'No'));

            // Check remember token cookie
            $cookieName = 'remember_token';
            error_log("Remember token cookie: " . (isset($_COOKIE[$cookieName]) ? 'Present' : 'Not present'));

            // If this is a new browser session and remember_me is false, invalidate the session
            if ($isNewSession && !$rememberMe) {
                error_log("New browser session detected with remember_me=false, invalidating session");

                // Use our enhanced logout method
                $this->logoutUser();

                // Return a response indicating the session is invalid and redirect to home page
                return Response::json([
                    'valid' => false,
                    'reason' => 'new_session_no_remember_me',
                    'redirect' => '/', // Redirect to home page
                    'remember_me' => false,
                    'new_session' => true
                ]);
            }

            // If this is a new browser session but remember_me is true, update the session
            if ($isNewSession && $rememberMe) {
                error_log("New browser session detected with remember_me=true, keeping session active");

                // Update last activity time
                $_SESSION['last_activity'] = time();

                // Ensure the remember_me flag is set
                $_SESSION['remember_me'] = true;
            }

            // Log the result
            error_log("Session check: authenticated=true, new_session=" .
                      ($isNewSession ? 'true' : 'false') .
                      ", remember_me=" . ($rememberMe ? 'true' : 'false'));

            // For non-persistent sessions, check for session timeout
            if (!$rememberMe) {
                $lastActivity = $_SESSION['last_activity'] ?? 0;
                $timeSinceLastActivity = time() - $lastActivity;
                $sessionTimeout = 1800; // 30 minutes

                error_log("Session timeout check: {$timeSinceLastActivity}s idle (max: {$sessionTimeout}s)");

                if ($timeSinceLastActivity > $sessionTimeout) {
                    error_log("Session timeout detected, logging out user");
                    $this->logoutUser();

                    return Response::json([
                        'valid' => false,
                        'reason' => 'session_timeout',
                        'redirect' => '/', // Redirect to home page
                        'remember_me' => false
                    ]);
                }

                // Update last activity time
                $_SESSION['last_activity'] = time();
                error_log("Updated last activity time");
            }

            // Always return the current session state
            return Response::json([
                'valid' => true,
                'new_session' => $isNewSession,
                'remember_me' => $rememberMe,
                'redirect' => null
            ]);
        } catch (\Exception $e) {
            error_log("Error checking session: " . $e->getMessage());
            return Response::json([
                'valid' => false,
                'error' => $e->getMessage(),
                'redirect' => '/'
            ]);
        }
    }

    public function invalidateSession(): Response
    {
        try {
            // Get the request data
            $data = json_decode(file_get_contents('php://input'), true) ?? [];
            $action = $data['action'] ?? 'check';

            error_log("Session invalidation request with action: {$action}");

            // Check if user is authenticated
            if (!isset($_SESSION['user_id'])) {
                error_log("No active session to invalidate");

                // If this is a check for a new browser session, we should redirect to home
                if ($action === 'check_new_session') {
                    error_log("New browser session check with no active session, redirecting to home");
                    return Response::json([
                        'success' => true,
                        'action' => 'logout',
                        'redirect' => '/' // Redirect to home page
                    ]);
                }

                return Response::json([
                    'success' => true,
                    'action' => 'no_action',
                    'message' => 'No active session'
                ]);
            }

            // Get the current user ID for logging
            $userId = $_SESSION['user_id'];
            error_log("Processing session request for user {$userId}");

            // Get remember_me setting
            $rememberMe = $_SESSION['remember_me'] ?? false;
            error_log("Remember me setting: " . ($rememberMe ? 'true' : 'false'));

            // Handle different action types
            if ($action === 'invalidate') {
                // Explicit invalidation request
                error_log("Explicit invalidation request for user {$userId}");

                if (!$rememberMe) {
                    error_log("Non-persistent session detected, logging out user {$userId}");
                    $this->logoutUser();

                    return Response::json([
                        'success' => true,
                        'action' => 'logout',
                        'redirect' => '/' // Redirect to home page
                    ]);
                } else {
                    error_log("Persistent session detected, keeping session active");
                    return Response::json(['success' => true, 'action' => 'keep_session']);
                }
            } elseif ($action === 'check_new_session') {
                // Check for new browser session
                error_log("Checking for new browser session for user {$userId}");

                // If this is a new browser session and remember_me is false, invalidate
                if (\App\Core\Security\Session::isNewBrowserSession()) {
                    error_log("New browser session confirmed for user {$userId}");

                    if (!$rememberMe) {
                        error_log("Non-persistent session in new browser session, logging out user {$userId}");
                        $this->logoutUser();

                        return Response::json([
                            'success' => true,
                            'action' => 'logout',
                            'redirect' => '/' // Redirect to home page
                        ]);
                    } else {
                        error_log("Persistent session in new browser session, keeping session active");
                        return Response::json(['success' => true, 'action' => 'keep_session']);
                    }
                } else {
                    error_log("Not a new browser session for user {$userId}");
                    return Response::json(['success' => true, 'action' => 'no_action']);
                }
            } elseif ($action === 'close') {
                // Tab or window close event
                error_log("Tab/window close event for user {$userId}");

                // Get the remember_me flag from the request data or session
                $rememberMe = $data['remember_me'] ?? ($_SESSION['remember_me'] ?? false);
                error_log("Remember me setting for tab close: " . ($rememberMe ? 'true' : 'false'));

                // We don't need to do anything else here, just log it
                // When all tabs are closed, the browser will clear sessionStorage
                // and we'll detect a new browser session when the user returns
                return Response::json(['success' => true, 'action' => 'no_action']);
            } else {
                // Default check
                error_log("Default session check for user {$userId}");

                // Check if this is a new browser session
                if (\App\Core\Security\Session::isNewBrowserSession()) {
                    error_log("New browser session detected for user {$userId}");

                    if (!$rememberMe) {
                        error_log("Non-persistent session in new browser session, logging out user {$userId}");
                        $this->logoutUser();

                        return Response::json([
                            'success' => true,
                            'action' => 'logout',
                            'redirect' => '/' // Redirect to home page
                        ]);
                    } else {
                        error_log("Persistent session in new browser session, keeping session active");
                        return Response::json(['success' => true, 'action' => 'keep_session']);
                    }
                }

                error_log("No action needed for user {$userId}");
                return Response::json(['success' => true, 'action' => 'no_action']);
            }
        } catch (\Exception $e) {
            error_log("Error in session handling: " . $e->getMessage());
            return Response::json([
                'success' => false,
                'message' => 'Error processing session request',
                'redirect' => '/' // Redirect to home page on error
            ]);
        }
    }
}
