/* ===== HEADER STYLES ===== */
.main-header {
  position: sticky;
  top: 0;
  left: 0;
  width: 100vw;
  height: var(--header-height);
  background-color: var(--light-color);
  box-shadow: var(--box-shadow-lg);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 var(--spacing-lg);
  z-index: 100;
  transition: var(--transition);
}

.header-left {
  display: flex;
  align-items: center;
}

.sidebar-toggle {
  display: none;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: var(--spacing-xs);
  margin-right: var(--spacing);
  border-radius: var(--border-radius-sm);
  transition: var(--transition-fast);
}

.bar {
  display: block;
  width: 22px;
  height: 2px;
  margin: 4px 0;
  background-color: var(--dark-color);
  transition: var(--transition);
}

.sidebar-toggle.active .bar:nth-child(1) {
  transform: rotate(-45deg) translate(-4px, 5px);
}

.sidebar-toggle.active .bar:nth-child(2) {
  opacity: 0;
}

.sidebar-toggle.active .bar:nth-child(3) {
  transform: rotate(45deg) translate(-4px, -5px);
}

.sidebar-toggle:hover {
  background-color: var(--grey-200);
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  text-decoration: none;
}

.logo:hover {
  color: var(--primary-dark);
}

.svg-icon {
  width: 24px;
  height: 24px;
  fill: currentColor;
  margin-right: var(--spacing-sm);
}

.search-container .svg-icon {
  width: 20px;
  height: 20px;
  margin-right: 0;
}

.svg-icon--lg {
  width: 32px;
  height: 32px;
}

.app-name {
  margin-left: var(--spacing-xs);
  font-weight: var(--font-weight-semibold);
}

@media (max-width: 768px) {
  .app-name {
    display: none;
  }
}

.header-actions {
  display: flex;
  align-items: center;
}

.search-container {
  position: relative;
  margin-right: var(--spacing-md);
}

.search-container form {
  display: flex;
  align-items: center;
  position: relative;
}

.search-container input {
  width: 250px;
  padding: var(--spacing-sm) var(--spacing-md);
  padding-right: 48px;
  border: 1px solid var(--grey-300);
  border-radius: 20px;
  background-color: var(--grey-100);
  font-size: var(--font-size-sm);
  transition: var(--transition-fast);
}

.search-container input:focus {
  outline: none;
  border-color: var(--primary-light);
  box-shadow: 0 0 0 3px rgba(var(--primary-hue), 59%, 52%, 0.1);
  background-color: var(--light-color);
}

.search-container button {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  color: var(--grey-600);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  padding: var(--spacing-xs);
  cursor: pointer;
  transition: var(--transition-fast);
}

.search-container button:hover {
  color: var(--primary-color);
}

@media (max-width: 768px) {
  .search-container input {
    width: 180px;
  }
}

@media (max-width: 576px) {
  .search-container {
    display: none;
  }
}

.user-menu {
  position: relative;
}

.user-dropdown-toggle {
  display: flex;
  align-items: center;
  background: transparent;
  border: 1px solid transparent;
  padding: var(--spacing-sm) var(--spacing);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-color);
  cursor: pointer;
  border-radius: var(--border-radius);
  transition: var(--transition-fast);
}

.user-dropdown-toggle:hover {
  border-color: var(--primary-light);
  background-color: rgba(var(--primary-hue), 59%, 52%, 0.05);
}

.user-name {
  margin-right: var(--spacing-xs);
}

.dropdown-menu {
  position: absolute;
  top: calc(100% + 5px);
  right: 0;
  width: 200px;
  background-color: var(--light-color);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-lg);
  z-index: 1000;
  display: none;
  overflow: hidden;
}

/* User dropdown styling */
#user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  width: 200px;
  background-color: var(--light-color);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-lg);
  z-index: 1000;
  margin-top: 5px;
  display: none;
}

/* Show dropdown when .show class is applied */
.dropdown-menu.show,
#user-dropdown.show {
  display: block;
}

.dropdown-menu a,
.dropdown-menu button {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing);
  color: var(--text-color);
  font-size: var(--font-size-sm);
  transition: var(--transition-fast);
  width: 100%;
  text-align: left;
  background: none;
  border: none;
  cursor: pointer;
}

.dropdown-menu a:hover,
.dropdown-menu button:hover {
  background-color: var(--grey-200);
}

.dropdown-divider {
  height: 1px;
  background-color: var(--grey-300);
  margin: var(--spacing-xs) 0;
}

.text-button {
  background: none;
  border: none;
  color: inherit;
  font: inherit;
  cursor: pointer;
  padding: 0;
}