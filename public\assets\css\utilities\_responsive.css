/* ===== RESPONSIVE UTILITIES ===== */
@media (max-width: 992px) {
    .category-list {
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }

    .documents-grid {
      grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }

    .expense-header {
      flex-direction: column;
      align-items: flex-start;
    }

    .expense-actions {
      margin-top: var(--spacing);
    }

    .button {
      padding: 0.7rem 1.2rem;
    }
  }

  @media (max-width: 768px) {
    .dashboard-grid,
    .stats-grid {
      grid-template-columns: 1fr;
    }

    .dashboard-actions {
      flex-direction: column;
    }

    .category-list {
      grid-template-columns: 1fr;
    }

    .stat-card {
      padding: var(--spacing-md);
    }

    .container {
      padding: var(--spacing) var(--spacing-sm);
    }

    .search-container input {
      width: 140px;
    }

    table {
      display: block;
    }

    .document-card {
      flex-direction: column;
    }

    .document-icon {
      width: 100%;
      padding: var(--spacing) 0;
    }

    .documents-grid {
      grid-template-columns: 1fr;
    }

    .currency-symbol {
      width: 35px;
    }

    .currency-input input {
      padding-left: 45px;
    }
  }

  @media (max-width: 576px) {
    .expense-content,
    .expense-header,
    .expense-notes,
    .expense-document {
      padding: var(--spacing);
    }

    .document-actions {
      flex-direction: column;
    }

    .document-actions .button {
      width: 100%;
    }
  }
