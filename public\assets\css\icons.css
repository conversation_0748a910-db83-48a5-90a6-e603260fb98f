.icon, .svg-icon {
  display: inline-block;
  width: 24px;
  height: 24px;
  stroke-width: 0;
  stroke: currentColor;
  fill: currentColor;
  vertical-align: middle;
}

/* Special handling for expense icon which has a different aspect ratio */
.icon-expense {
  width: 20px;
  height: 26px;
}

.icon-sm {
  width: 18px;
  height: 18px;
}

.icon-lg {
  width: 32px;
  height: 32px;
}

.icon-xl {
  width: 48px;
  height: 48px;
}

/* Icon colors */
.icon-primary {
  color: #3498db;
}

.icon-success {
  color: #2ecc71;
}

.icon-warning {
  color: #f39c12;
}

.icon-danger {
  color: #e74c3c;
}

.icon-info {
  color: #3498db;
}

.icon-muted {
  color: #95a5a6;
}

/* Stat icons */
.stat-icon .icon {
  width: 36px;
  height: 36px;
}

/* Action icons */
.action-icon .icon {
  width: 20px;
  height: 20px;
  margin-right: 5px;
}

.action-icon .icon-expense {
  width: 24px;
  height: 32px;
}

/* Navigation icons */
.nav-icon .icon {
  margin-right: 8px;
}

/* Button icons */
.btn .icon {
  margin-right: 5px;
}

/* Animation for interactive icons */
.icon-interactive {
  transition: transform 0.2s ease-in-out;
}

.icon-interactive:hover {
  transform: scale(1.1);
}

/* Document viewer specific icons */
.document-preview .svg-icon {
  width: 48px;
  height: 48px;
}

.status-indicator .svg-icon {
  width: 18px;
  height: 18px;
}

.receipt-header .svg-icon {
  width: 20px;
  height: 20px;
}

.action-button .svg-icon {
  width: 20px;
  height: 20px;
  margin-right: 8px;
}

.prompt-icon .svg-icon {
  width: 32px;
  height: 32px;
}

.help-button .svg-icon {
  width: 20px;
  height: 20px;
  margin-right: 0;
}

.not-found-icon .svg-icon {
  width: 48px;
  height: 48px;
}
