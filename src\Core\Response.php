<?php

declare(strict_types=1);

namespace App\Core;

final class Response
{
    private int $statusCode = 200;
    private array $headers = [];
    private string|array|object|null $content = null;

    public function __construct(
        string|array|object|null $content = null,
        int $statusCode = 200,
        array $headers = []
    ) {
        $this->content = $content;
        $this->statusCode = $statusCode;
        $this->headers = $headers;
    }

    // Immutable modifier methods
    public function withHeader(string $name, string $value): self
    {
        $clone = clone $this;
        $clone->headers[$name] = $value;
        return $clone;
    }

    public function withHeaders(array $headers): self
    {
        $clone = clone $this;
        $clone->headers = array_merge($clone->headers, $headers);
        return $clone;
    }

    public function withStatus(int $statusCode): self
    {
        $clone = clone $this;
        $clone->statusCode = $statusCode;
        return $clone;
    }

    public function withContent(string|array|object|null $content): self
    {
        $clone = clone $this;
        $clone->content = $content;
        return $clone;
    }

    // Getter methods
    public function getContent(): string|array|object|null
    {
        return $this->content;
    }

    public function getHeader(string $name): ?string
    {
        return $this->headers[$name] ?? null;
    }

    public function getHeaders(): array
    {
        return $this->headers;
    }

    public function getStatusCode(): int
    {
        return $this->statusCode;
    }

    // Response sending methods
    public function send(): void
    {
        // Check if headers have already been sent
        if (headers_sent($file, $line)) {
            error_log("Headers already sent in {$file} on line {$line}");
            // If headers are already sent, just output content without setting headers
            $this->sendContentOnly();
            return;
        }

        // Check if this is a redirect response
        $isRedirect = $this->statusCode >= 300 && $this->statusCode < 400 && isset($this->headers['Location']);

        // For redirects, ensure session data is written before sending the response
        if ($isRedirect && session_status() === PHP_SESSION_ACTIVE) {
            session_write_close();
        }

        // Clear any buffered output before sending headers
        while (ob_get_level()) {
            ob_end_clean();
        }

        // Start output buffering to capture any unexpected output
        ob_start();

        try {
            http_response_code($this->statusCode);
            $this->sendHeaders();
            $this->sendContent();
        } catch (\Throwable $e) {
            error_log("Error sending response: " . $e->getMessage());
            // Clean the buffer and send a basic error response
            ob_clean();
            if (!headers_sent()) {
                http_response_code(500);
                header('Content-Type: text/plain');
            }
            echo "Internal Server Error";
        } finally {
            if (ob_get_level()) {
                ob_end_flush();
            }
        }
    }

    private function sendHeaders(): void
    {
        foreach ($this->headers as $name => $value) {
            if (!headers_sent()) {
                header("$name: $value");
            }
        }
    }

    private function sendContent(): void
    {
        if ($this->content === null) {
            return;
        }

        if (is_array($this->content) || is_object($this->content)) {
            if (!isset($this->headers['Content-Type'])) {
                header('Content-Type: application/json');
            }
            echo json_encode($this->content, JSON_PRETTY_PRINT);
            return;
        }

        echo $this->content;
    }

    private function sendContentOnly(): void
    {
        if ($this->content === null) {
            return;
        }

        if (is_array($this->content) || is_object($this->content)) {
            echo json_encode($this->content, JSON_PRETTY_PRINT);
            return;
        }

        echo $this->content;
    }

    // Static factory methods
    public static function json(array $data, int $statusCode = 200, array $headers = []): self
    {
        return new self(
            $data,
            $statusCode,
            array_merge(['Content-Type' => 'application/json'], $headers)
        );
    }

    public static function text(string $content, int $statusCode = 200, array $headers = []): self
    {
        return new self(
            $content,
            $statusCode,
            array_merge(['Content-Type' => 'text/plain'], $headers)
        );
    }

    public static function html(string $content, int $statusCode = 200, array $headers = []): self
    {
        return new self(
            $content,
            $statusCode,
            array_merge(['Content-Type' => 'text/html'], $headers)
        );
    }

    public static function redirect(string $url, int $statusCode = 302): self
    {
        // Ensure session data is written before creating a redirect response
        if (session_status() === PHP_SESSION_ACTIVE) {
            // Update last activity time to prevent session timeout
            if (isset($_SESSION['user_id'])) {
                $_SESSION['last_activity'] = time();
                $_SESSION['authenticated'] = true;
            }
        }

        return new self(null, $statusCode, ['Location' => $url]);
    }

    public static function notFound(?string $message = null): self
    {
        return new self($message, 404);
    }

    public static function error(int $statusCode = 500, ?string $message = null): self
    {
        return new self($message, $statusCode);
    }
}
