/* ===== SPACING UTILITIES ===== */
/* Based on Visual Rhythm & Cognitive Spacing Principles */

/* Margin Utilities */
.m-0 { margin: 0; }
.m-xs { margin: var(--spacing-xs); }
.m-sm { margin: var(--spacing-sm); }
.m { margin: var(--spacing); }
.m-md { margin: var(--spacing-md); }
.m-lg { margin: var(--spacing-lg); }
.m-xl { margin: var(--spacing-xl); }
.m-auto { margin: auto; }

/* Margin Top */
.mt-0 { margin-top: 0; }
.mt-xs { margin-top: var(--spacing-xs); }
.mt-sm { margin-top: var(--spacing-sm); }
.mt { margin-top: var(--spacing); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }
.mt-auto { margin-top: auto; }

/* Margin Right */
.mr-0 { margin-right: 0; }
.mr-xs { margin-right: var(--spacing-xs); }
.mr-sm { margin-right: var(--spacing-sm); }
.mr { margin-right: var(--spacing); }
.mr-md { margin-right: var(--spacing-md); }
.mr-lg { margin-right: var(--spacing-lg); }
.mr-xl { margin-right: var(--spacing-xl); }
.mr-auto { margin-right: auto; }

/* Margin Bottom */
.mb-0 { margin-bottom: 0; }
.mb-xs { margin-bottom: var(--spacing-xs); }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb { margin-bottom: var(--spacing); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }
.mb-auto { margin-bottom: auto; }

/* Margin Left */
.ml-0 { margin-left: 0; }
.ml-xs { margin-left: var(--spacing-xs); }
.ml-sm { margin-left: var(--spacing-sm); }
.ml { margin-left: var(--spacing); }
.ml-md { margin-left: var(--spacing-md); }
.ml-lg { margin-left: var(--spacing-lg); }
.ml-xl { margin-left: var(--spacing-xl); }
.ml-auto { margin-left: auto; }

/* Margin X (Horizontal) */
.mx-0 { margin-left: 0; margin-right: 0; }
.mx-xs { margin-left: var(--spacing-xs); margin-right: var(--spacing-xs); }
.mx-sm { margin-left: var(--spacing-sm); margin-right: var(--spacing-sm); }
.mx { margin-left: var(--spacing); margin-right: var(--spacing); }
.mx-md { margin-left: var(--spacing-md); margin-right: var(--spacing-md); }
.mx-lg { margin-left: var(--spacing-lg); margin-right: var(--spacing-lg); }
.mx-xl { margin-left: var(--spacing-xl); margin-right: var(--spacing-xl); }
.mx-auto { margin-left: auto; margin-right: auto; }

/* Margin Y (Vertical) */
.my-0 { margin-top: 0; margin-bottom: 0; }
.my-xs { margin-top: var(--spacing-xs); margin-bottom: var(--spacing-xs); }
.my-sm { margin-top: var(--spacing-sm); margin-bottom: var(--spacing-sm); }
.my { margin-top: var(--spacing); margin-bottom: var(--spacing); }
.my-md { margin-top: var(--spacing-md); margin-bottom: var(--spacing-md); }
.my-lg { margin-top: var(--spacing-lg); margin-bottom: var(--spacing-lg); }
.my-xl { margin-top: var(--spacing-xl); margin-bottom: var(--spacing-xl); }

/* Padding Utilities */
.p-0 { padding: 0; }
.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p { padding: var(--spacing); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

/* Padding Top */
.pt-0 { padding-top: 0; }
.pt-xs { padding-top: var(--spacing-xs); }
.pt-sm { padding-top: var(--spacing-sm); }
.pt { padding-top: var(--spacing); }
.pt-md { padding-top: var(--spacing-md); }
.pt-lg { padding-top: var(--spacing-lg); }
.pt-xl { padding-top: var(--spacing-xl); }

/* Padding Right */
.pr-0 { padding-right: 0; }
.pr-xs { padding-right: var(--spacing-xs); }
.pr-sm { padding-right: var(--spacing-sm); }
.pr { padding-right: var(--spacing); }
.pr-md { padding-right: var(--spacing-md); }
.pr-lg { padding-right: var(--spacing-lg); }
.pr-xl { padding-right: var(--spacing-xl); }

/* Padding Bottom */
.pb-0 { padding-bottom: 0; }
.pb-xs { padding-bottom: var(--spacing-xs); }
.pb-sm { padding-bottom: var(--spacing-sm); }
.pb { padding-bottom: var(--spacing); }
.pb-md { padding-bottom: var(--spacing-md); }
.pb-lg { padding-bottom: var(--spacing-lg); }
.pb-xl { padding-bottom: var(--spacing-xl); }

/* Padding Left */
.pl-0 { padding-left: 0; }
.pl-xs { padding-left: var(--spacing-xs); }
.pl-sm { padding-left: var(--spacing-sm); }
.pl { padding-left: var(--spacing); }
.pl-md { padding-left: var(--spacing-md); }
.pl-lg { padding-left: var(--spacing-lg); }
.pl-xl { padding-left: var(--spacing-xl); }

/* Padding X (Horizontal) */
.px-0 { padding-left: 0; padding-right: 0; }
.px-xs { padding-left: var(--spacing-xs); padding-right: var(--spacing-xs); }
.px-sm { padding-left: var(--spacing-sm); padding-right: var(--spacing-sm); }
.px { padding-left: var(--spacing); padding-right: var(--spacing); }
.px-md { padding-left: var(--spacing-md); padding-right: var(--spacing-md); }
.px-lg { padding-left: var(--spacing-lg); padding-right: var(--spacing-lg); }
.px-xl { padding-left: var(--spacing-xl); padding-right: var(--spacing-xl); }

/* Padding Y (Vertical) */
.py-0 { padding-top: 0; padding-bottom: 0; }
.py-xs { padding-top: var(--spacing-xs); padding-bottom: var(--spacing-xs); }
.py-sm { padding-top: var(--spacing-sm); padding-bottom: var(--spacing-sm); }
.py { padding-top: var(--spacing); padding-bottom: var(--spacing); }
.py-md { padding-top: var(--spacing-md); padding-bottom: var(--spacing-md); }
.py-lg { padding-top: var(--spacing-lg); padding-bottom: var(--spacing-lg); }
.py-xl { padding-top: var(--spacing-xl); padding-bottom: var(--spacing-xl); }

/* Gap Utilities for Flex and Grid */
.gap-0 { gap: 0; }
.gap-xs { gap: var(--spacing-xs); }
.gap-sm { gap: var(--spacing-sm); }
.gap { gap: var(--spacing); }
.gap-md { gap: var(--spacing-md); }
.gap-lg { gap: var(--spacing-lg); }
.gap-xl { gap: var(--spacing-xl); }

/* Responsive Spacing Adjustments */
@media (max-width: 768px) {
  /* Reduce spacing on mobile */
  .m-md, .mt-md, .mr-md, .mb-md, .ml-md, .mx-md, .my-md { margin: var(--spacing); }
  .m-lg, .mt-lg, .mr-lg, .mb-lg, .ml-lg, .mx-lg, .my-lg { margin: var(--spacing-md); }
  .m-xl, .mt-xl, .mr-xl, .mb-xl, .ml-xl, .mx-xl, .my-xl { margin: var(--spacing-lg); }

  .p-md, .pt-md, .pr-md, .pb-md, .pl-md, .px-md, .py-md { padding: var(--spacing); }
  .p-lg, .pt-lg, .pr-lg, .pb-lg, .pl-lg, .px-lg, .py-lg { padding: var(--spacing-md); }
  .p-xl, .pt-xl, .pr-xl, .pb-xl, .pl-xl, .px-xl, .py-xl { padding: var(--spacing-lg); }

  .gap-md { gap: var(--spacing); }
  .gap-lg { gap: var(--spacing-md); }
  .gap-xl { gap: var(--spacing-lg); }
}