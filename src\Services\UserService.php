<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\User;
use App\Exceptions\InvalidArgumentException;

class UserService extends BaseService
{
    protected function getModelClass(): string
    {
        return User::class;
    }

    public function getUserById(int $userId): ?array
    {
        if ($userId <= 0) {
            throw new InvalidArgumentException('Invalid user ID');
        }
        return $this->find($userId);
    }

    public function getUserByEmail(string $email): ?array
    {
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new InvalidArgumentException('Invalid email format');
        }
        return User::findByEmail($email);
    }

    public function getUserStatistics(int $userId): ?array
    {
        if ($userId <= 0) {
            throw new InvalidArgumentException('Invalid user ID');
        }
        return User::getStatistics($userId);
    }

    public function getFullUserProfile(int $userId): ?array
    {
        if ($userId <= 0) {
            throw new InvalidArgumentException('Invalid user ID');
        }
        return User::getFullUserProfile($userId);
    }

    public function getActiveUsers(): array
    {
        return User::findActiveUsers();
    }

    public function getUsersByStatus(int $statusId): array
    {
        if ($statusId <= 0) {
            throw new InvalidArgumentException('Invalid status ID');
        }
        return User::findByStatus($statusId);
    }

    public function getCurrentUserId(): ?int
    {
        return isset($_SESSION['user_id']) ? (int)$_SESSION['user_id'] : null;
    }

    public function registerUser(string $name, string $email, string $password): bool
    {
        if (empty($name) || !filter_var($email, FILTER_VALIDATE_EMAIL) || empty($password)) {
            throw new InvalidArgumentException('Invalid registration data');
        }
        $errors = $this->validateUserCredentials($email, $password);
        if (!empty($errors)) {
            throw new InvalidArgumentException(implode(', ', $errors));
        }
        if (!User::isEmailUnique($email)) {
            throw new InvalidArgumentException('Email already in use');
        }
        return User::registerUser($name, $email, $password);
    }

    public function authenticateUser(string $email, string $password): bool
    {
        if (!filter_var($email, FILTER_VALIDATE_EMAIL) || empty($password)) {
            throw new InvalidArgumentException('Invalid login credentials');
        }
        return User::authenticate($email, $password);
    }

    public function isUserAuthenticated(): bool
    {
        return User::isAuthenticated();
    }

    public function logoutUser(): void
    {
        $userId = $this->getCurrentUserId();
        if ($userId) {
            User::deleteRememberTokensByUserId($userId);
        }
        User::logout();
    }

    public function validateUserCredentials(string $email, string $password): array
    {
        if (!filter_var($email, FILTER_VALIDATE_EMAIL) || empty($password)) {
            return ['Invalid email or password format'];
        }
        return User::validateUserCredentials($email, $password);
    }

    public function validateCurrentPassword(int $userId, string $password): bool
    {
        if ($userId <= 0 || empty($password)) {
            throw new InvalidArgumentException('Invalid user ID or password');
        }
        $user = $this->getUserById($userId);
        if (!$user) {
            throw new InvalidArgumentException('User not found');
        }
        return password_verify($password, $user['password']);
    }

    public function updateUserEmail(int $userId, string $newEmail): bool
    {
        if ($userId <= 0 || !filter_var($newEmail, FILTER_VALIDATE_EMAIL)) {
            throw new InvalidArgumentException('Invalid user ID or email format');
        }
        if (!User::isEmailUnique($newEmail, $userId)) {
            throw new InvalidArgumentException('Email already in use');
        }
        return User::update($userId, ['email' => $newEmail]);
    }

    public function updateUser(int $userId, array $userData): bool
    {
        if ($userId <= 0 || empty($userData)) {
            throw new InvalidArgumentException('Invalid user ID or update data');
        }
        error_log("UserService::updateUser - Starting update for user ID: {$userId}");
        error_log("UserService::updateUser - Input data: " . json_encode($userData));
        $user = $this->getUserById($userId);
        if (!$user) {
            error_log("UserService::updateUser - User not found");
            throw new InvalidArgumentException('User not found');
        }
        error_log("UserService::updateUser - Current user data: " . json_encode($user));
        if (isset($userData['email']) && $userData['email'] !== $user['email']) {
            error_log("UserService::updateUser - Email is being changed from {$user['email']} to {$userData['email']}");
            if (!filter_var($userData['email'], FILTER_VALIDATE_EMAIL)) {
                error_log("UserService::updateUser - Invalid email format: {$userData['email']}");
                throw new InvalidArgumentException('Invalid email format');
            }
            if (!User::isEmailUnique($userData['email'], $userId)) {
                error_log("UserService::updateUser - Email already in use: {$userData['email']}");
                throw new InvalidArgumentException('Email already in use');
            }
        }
        if (isset($userData['remember_me_default'])) {
            error_log("UserService::updateUser - Updating remember_me_default to: {$userData['remember_me_default']}");
        }
        $result = User::update($userId, $userData);
        error_log("UserService::updateUser - Update result: " . ($result ? 'success' : 'failure'));
        return $result;
    }

    public function updateUserProfile(int $userId, array $userData): bool
    {
        if ($userId <= 0 || empty($userData)) {
            throw new InvalidArgumentException('Invalid user ID or update data');
        }
        $user = $this->getUserById($userId);
        if (!$user) {
            throw new InvalidArgumentException('User not found');
        }
        if (isset($userData['email']) && $userData['email'] !== $user['email']) {
            if (!filter_var($userData['email'], FILTER_VALIDATE_EMAIL)) {
                throw new InvalidArgumentException('Invalid email format');
            }
            if (!User::isEmailUnique($userData['email'], $userId)) {
                throw new InvalidArgumentException('Email already in use');
            }
        }
        return User::update($userId, $userData);
    }

    public function updateUserPassword(int $userId, string $newPassword): bool
    {
        if ($userId <= 0 || empty($newPassword)) {
            throw new InvalidArgumentException('Invalid user ID or password');
        }
        $user = $this->getUserById($userId);
        if (!$user) {
            throw new InvalidArgumentException('User not found');
        }
        return User::updatePassword($userId, $newPassword);
    }

    public function updateUserStatus(int $userId, int $statusId): bool
    {
        if ($userId <= 0 || $statusId <= 0) {
            throw new InvalidArgumentException('Invalid user ID or status ID');
        }
        $user = $this->getUserById($userId);
        if (!$user) {
            throw new InvalidArgumentException('User not found');
        }
        return User::updateStatus($userId, $statusId);
    }

    public function deleteUser(int $userId): bool
    {
        if ($userId <= 0) {
            throw new InvalidArgumentException('Invalid user ID');
        }
        $user = $this->getUserById($userId);
        if (!$user) {
            throw new InvalidArgumentException('User not found');
        }
        return User::softDelete($userId);
    }

    public function createRememberToken(int $userId, int $expiryDays = 30): array
    {
        if ($userId <= 0 || $expiryDays <= 0) {
            throw new InvalidArgumentException('Invalid user ID or expiry days');
        }
        return User::createRememberToken($userId, $expiryDays);
    }

    public function deleteRememberToken(string $token): bool
    {
        if (empty($token)) {
            throw new InvalidArgumentException('Invalid token');
        }
        return User::deleteRememberToken($token);
    }

    public function deleteRememberTokensByUserId(int $userId): bool
    {
        if ($userId <= 0) {
            throw new InvalidArgumentException('Invalid user ID');
        }
        return User::deleteRememberTokensByUserId($userId);
    }

    public function findUserByRememberToken(string $token): ?array
    {
        if (empty($token)) {
            throw new InvalidArgumentException('Invalid token');
        }
        $tokenData = User::findRememberToken($token);
        if (!$tokenData) {
            return null;
        }
        return $this->getUserById($tokenData['user_id']);
    }

    public function findRememberTokenByUserId(int $userId): ?array
    {
        if ($userId <= 0) {
            throw new InvalidArgumentException('Invalid user ID');
        }
        return User::findRememberTokenByUserId($userId);
    }

    public function generatePasswordResetToken(string $email): bool
    {
        if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
            throw new InvalidArgumentException('Invalid email format');
        }
        $user = $this->getUserByEmail($email);
        if (!$user) {
            return false;
        }
        $token = bin2hex(random_bytes(32));
        return User::storeResetToken($email, $token);
    }

    public function validateResetToken(string $token): ?array
    {
        if (empty($token)) {
            throw new InvalidArgumentException('Invalid token');
        }
        return User::findByResetToken($token);
    }

    public function resetPassword(string $token, string $newPassword): bool
    {
        if (empty($token) || empty($newPassword)) {
            throw new InvalidArgumentException('Invalid token or password');
        }
        $user = $this->validateResetToken($token);
        if (!$user) {
            throw new InvalidArgumentException('Invalid or expired reset token');
        }
        return User::updatePassword($user['id'], $newPassword);
    }
}
