<?php

/**
 * Standardized form field component for consistent form field rendering
 * Used across all profile forms
 *
 * @param string $type          Input type (text, email, password, select, checkbox, file)
 * @param string $name          Field name attribute
 * @param string $id            Field id attribute (defaults to $name if not provided)
 * @param string $label         Field label text
 * @param string $value         Current field value
 * @param string $placeholder   Placeholder text
 * @param bool   $required      Whether field is required
 * @param string $class         Additional CSS classes
 * @param string $help          Help text to display below field
 * @param array  $options       Options for select fields (key-value pairs)
 * @param array  $attributes    Additional HTML attributes as key-value pairs
 */

// Set defaults
$id = $id ?? $name;
$class = $class ?? 'form-control';
$required = $required ?? false;
$placeholder = $placeholder ?? '';
$help = $help ?? '';
$options = $options ?? [];
$attributes = $attributes ?? [];

// For checkboxes, remove attributes that don't apply
if ($type === 'checkbox') {
    // Remove numeric attributes that don't apply to checkboxes
    if (isset($attributes['step'])) {
        unset($attributes['step']);
    }
    if (isset($attributes['min'])) {
        unset($attributes['min']);
    }

    // Checkboxes are rarely required
    $required = false;
}

// Build additional attributes string
$attr_string = '';
foreach ($attributes as $attr_name => $attr_value) {
    $attr_string .= ' ' . $attr_name . '="' . htmlspecialchars($attr_value) . '"';
}

// Add required attribute if needed
if ($required) {
    $attr_string .= ' required';
}

// Generate unique ID for help text if provided
$help_id = !empty($help) ? $id . '-help' : '';

// Add aria-describedby if help text exists
if (!empty($help_id)) {
    $attr_string .= ' aria-describedby="' . $help_id . '"';
}
?>

<div class="form-group<?= $type === 'checkbox' ? ' checkbox-form-group' : '' ?>">
    <?php if ($type !== 'checkbox') : ?>
        <label for="<?= htmlspecialchars($id) ?>"><?= htmlspecialchars($label) ?><?= $required ? ' <span class="required">*</span>' : '' ?></label>
    <?php endif; ?>

    <?php if ($type === 'select') : ?>
        <select
            name="<?= htmlspecialchars($name) ?>"
            id="<?= htmlspecialchars($id) ?>"
            class="<?= htmlspecialchars($class) ?>"
            <?= $attr_string ?>
        >
            <?php if (!empty($placeholder)) : ?>
                <option value=""><?= htmlspecialchars($placeholder) ?></option>
            <?php endif; ?>

            <?php foreach ($options as $option_value => $option_label) :
                $selected = (string)$value === (string)$option_value ? 'selected' : '';
                ?>
                <option value="<?= htmlspecialchars($option_value) ?>" <?= $selected ?>>
                    <?= htmlspecialchars($option_label) ?>
                </option>
            <?php endforeach; ?>
        </select>

    <?php elseif ($type === 'checkbox') : ?>
        <?php
        // Debug output for checkbox
        error_log("form_field.php - Checkbox {$name} value: " . var_export($value, true));
        $isChecked = !empty($value);
        error_log("form_field.php - Checkbox {$name} isChecked: " . ($isChecked ? 'true' : 'false'));
        ?>
        <input
            type="checkbox"
            name="<?= htmlspecialchars($name) ?>"
            id="<?= htmlspecialchars($id) ?>"
            value="1"
            <?= $isChecked ? 'checked' : '' ?>
            <?= $attr_string ?>
        >
        <label for="<?= htmlspecialchars($id) ?>">
            <?= htmlspecialchars($label) ?>
        </label>

    <?php elseif ($type === 'file') : ?>
        <input
            type="file"
            name="<?= htmlspecialchars($name) ?>"
            id="<?= htmlspecialchars($id) ?>"
            class="file-input <?= htmlspecialchars($class) ?>"
            <?= $attr_string ?>
        >

    <?php elseif ($type === 'textarea') : ?>
        <textarea
            name="<?= htmlspecialchars($name) ?>"
            id="<?= htmlspecialchars($id) ?>"
            class="<?= htmlspecialchars($class) ?>"
            placeholder="<?= htmlspecialchars($placeholder) ?>"
            <?= $attr_string ?>
        ><?= htmlspecialchars($value ?? '') ?></textarea>

    <?php else : ?>
        <?php if ($type === 'number' && isset($prepend) && $prepend === '$') : ?>
            <div class="currency-input">
                <div class="currency-symbol" aria-hidden="true">
                    <span>$</span>
                </div>
        <?php endif; ?>

        <input
            type="<?= htmlspecialchars($type) ?>"
            name="<?= htmlspecialchars($name) ?>"
            id="<?= htmlspecialchars($id) ?>"
            class="<?= htmlspecialchars($class) ?>"
            value="<?= htmlspecialchars($value ?? '') ?>"
            placeholder="<?= htmlspecialchars($placeholder) ?>"
            <?= $attr_string ?>
        >

        <?php if ($type === 'number' && isset($prepend) && $prepend === '$') : ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>

    <?php if (!empty($help)) : ?>
        <small id="<?= $help_id ?>" class="form-hint"><?= $help ?></small>
    <?php endif; ?>
</div>
