/* ===== BASE RESET ===== */
*, *::before, *::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  /* Prevent animation conflicts */
  .no-animation {
    animation: none !important;
    transition: none !important;
  }

  /* Fix SVG icon alignment */
  .svg-icon {
    display: block;
    margin: 0;
    vertical-align: middle;
  }

  /* ===== CORE DOCUMENT STYLES ===== */
  html {
    padding: 0 !important;
    font-size: 16px;
    scroll-behavior: smooth;
  }

  body {
    font-family: var(--font-main);
    color: var(--text-color);
    background-color: var(--grey-100);
    line-height: var(--line-height);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    overflow-x: hidden;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* Performance optimizations for animations */
    will-change: auto;
    backface-visibility: hidden;
  }

  ul, ol {
    list-style: none;
  }
