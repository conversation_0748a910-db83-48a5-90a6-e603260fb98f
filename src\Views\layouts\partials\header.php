<header class="main-header" role="banner">
    <div class="header-left">
        <button id="sidebar-toggle"
                class="sidebar-toggle"
                aria-label="Toggle navigation menu"
                aria-expanded="false"
                aria-controls="sidebar">
            <span class="bar"></span>
            <span class="bar"></span>
            <span class="bar"></span>
        </button>

        <div class="logo-container">
            <a href="/" class="logo" aria-label="PET Home">
                <svg class="svg-icon" aria-hidden="true" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M21 18v1c0 1.1-.9 2-2 2H5c-1.1 0-2-.9-2-2V5c0-1.1.9-2 2-2h14c1.1 0 2 .9 2 2v1h-9c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2h9zm-9-2h10V8H12v8zm4-2.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5z"/>
                </svg>
                <span class="app-name">PET</span>
            </a>
        </div>
o        <div class="header-dates" style="display:flex;gap:1.5em;align-items:center;margin-left:2em;">
            <span id="gregorian-date" style="font-weight:bold;"></span>
            <span id="hijri-date" style="font-weight:bold;"></span>
        </div>
    </div>

    <?php if (isset($_SESSION['user_id'])) : ?>
    <div class="header-actions">
        <div class="search-container">
            <form action="/search" method="GET" role="search">
                <label for="search-input" class="sr-only">Search expenses</label>
                <input type="text"
                       id="search-input"
                       name="q"
                       placeholder="Search expenses..."
                       aria-label="Search expenses">
                <button type="submit" aria-label="Submit search">
                    <svg class="svg-icon" aria-hidden="true" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M15.5 14h-.79l-.28-.27A6.471 6.471 0 0016 9.5 6.5 6.5 0 109.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                    </svg>
                </button>
            </form>
        </div>

        <div class="user-menu">
            <button class="user-dropdown-toggle" onclick="toggleUserDropdown(event)">
                <span class="user-name"><?= htmlspecialchars($_SESSION['user_name'] ?? 'User') ?></span>
                <svg class="svg-icon" aria-hidden="true" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path d="M7 10l5 5 5-5z"/>
                </svg>
            </button>

            <div id="user-dropdown" class="dropdown-menu" style="display: none;">
                <a href="/profile" role="menuitem">
                    <svg class="svg-icon" aria-hidden="true" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z"/>
                    </svg>
                    My Profile
                </a>
                <a href="/password/reset" role="menuitem">
                    <svg class="svg-icon" aria-hidden="true" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path d="M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm3.1-9H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z"/>
                    </svg>
                    Change Password
                </a>
                <div class="dropdown-divider" role="separator"></div>
                <form action="/logout" method="POST" class="dropdown-form">
                    <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($csrf_token ?? '') ?>">
                    <button type="submit" class="text-button" role="menuitem">
                        <svg class="svg-icon" aria-hidden="true" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path d="M17 7l-1.41 1.41L18.17 11H8v2h10.17l-2.58 2.58L17 17l5-5-5-5zM4 5h8V3H4c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h8v-2H4V5z"/>
                        </svg>
                        Logout
                    </button>
                </form>
            </div>
        </div>

        <script>
            // Simple inline script to handle the dropdown
            function toggleUserDropdown(event) {
                event.preventDefault();
                event.stopPropagation();

                var dropdown = document.getElementById('user-dropdown');
                if (dropdown.style.display === 'block') {
                    dropdown.style.display = 'none';
                } else {
                    dropdown.style.display = 'block';

                    // Close when clicking outside
                    setTimeout(function() {
                        document.addEventListener('click', closeDropdown);
                    }, 0);
                }
            }

            function closeDropdown(event) {
                var dropdown = document.getElementById('user-dropdown');
                var button = document.querySelector('.user-dropdown-toggle');

                if (!dropdown.contains(event.target) && !button.contains(event.target)) {
                    dropdown.style.display = 'none';
                    document.removeEventListener('click', closeDropdown);
                }
            }

            // Close dropdown with Escape key
            document.addEventListener('keydown', function(event) {
                if (event.key === 'Escape') {
                    var dropdown = document.getElementById('user-dropdown');
                    if (dropdown.style.display === 'block') {
                        dropdown.style.display = 'none';
                        document.removeEventListener('click', closeDropdown);
                    }
                }
            });
        </script>
    </div>
    <?php endif; ?>
<script>
// --- Date update logic ---
(async function() {
    // Helper: get user's timezone offset in minutes
    function getLocalDateString() {
        const now = new Date();
        const yyyy = now.getFullYear();
        const mm = String(now.getMonth() + 1).padStart(2, '0');
        const dd = String(now.getDate()).padStart(2, '0');
        return `${dd}-${mm}-${yyyy}`;
    }

    // Helper: get user's lat/lon (fallback to Mecca)
    async function getLocation() {
        return new Promise(resolve => {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                    pos => resolve({lat: pos.coords.latitude, lon: pos.coords.longitude}),
                    () => resolve({lat: 21.3891, lon: 39.8579}) // Mecca fallback
                );
            } else {
                resolve({lat: 21.3891, lon: 39.8579});
            }
        });
    }

    // Fetch Gregorian/Hijri date for today
    async function fetchDates(dateStr) {
        const url = `https://api.aladhan.com/v1/gToH/${dateStr}`;
        const resp = await fetch(url);
        const data = await resp.json();
        return data.data;
    }

    // Fetch Maghrib time for today
    async function fetchMaghribTime(lat, lon) {
        const today = new Date();
        const yyyy = today.getFullYear();
        const mm = String(today.getMonth() + 1).padStart(2, '0');
        const dd = String(today.getDate()).padStart(2, '0');
        const url = `https://api.aladhan.com/v1/timings/${dd}-${mm}-${yyyy}?latitude=${lat}&longitude=${lon}&method=2`;
        const resp = await fetch(url);
        const data = await resp.json();
        return data.data.timings.Maghrib;
    }

    // Set date displays
    function setDates(gregorian, hijri) {
        document.getElementById('gregorian-date').textContent = `Gregorian: ${gregorian.date}`;
        document.getElementById('hijri-date').textContent = `Hijri: ${hijri.date}`;
    }

    // Main logic
    const loc = await getLocation();
    let dateStr = getLocalDateString();
    let {gregorian, hijri} = await fetchDates(dateStr);
    setDates(gregorian, hijri);

    // Schedule Gregorian update at next midnight
    function msToNextMidnight() {
        const now = new Date();
        const next = new Date(now);
        next.setHours(24,0,0,0);
        return next - now;
    }
    setTimeout(() => location.reload(), msToNextMidnight() + 1000); // reload at midnight

    // Schedule Hijri update at Maghrib
    const maghribStr = await fetchMaghribTime(loc.lat, loc.lon); // e.g. "18:45"
    const [maghribHour, maghribMin] = maghribStr.split(':').map(Number);
    const now = new Date();
    const maghrib = new Date(now);
    maghrib.setHours(maghribHour, maghribMin, 0, 0);
    if (maghrib < now) maghrib.setDate(maghrib.getDate() + 1); // if Maghrib already passed, use tomorrow
    setTimeout(() => location.reload(), maghrib - now + 1000); // reload at Maghrib
})();
</script>
</header>

