<?php
/**
 * Empty state partial for expenses list
 *
 * @var array $expenses Array of expense records
 * @var array $filters Current active filters
 */

$hasFilters = !empty($filters) && (
    !empty($filters['category_ids']) || 
    !empty($filters['date_from']) || 
    !empty($filters['date_to']) || 
    !empty($filters['date_preset']) || 
    !empty($filters['minAmount']) || 
    !empty($filters['maxAmount']) || 
    !empty($filters['search'])
);

$message = $hasFilters 
    ? 'No expenses match your current filters. Try adjusting your filter criteria or clear filters to see all expenses.'
    : 'No expenses found. Add your first expense to get started.';

$actionText = $hasFilters ? 'Clear Filters' : 'Add Expense';
$actionUrl = $hasFilters ? '#' : '/expenses/new';
$actionClass = $hasFilters ? 'empty-state__action empty-state__action--clear' : 'empty-state__action';
?>

<?php if (empty($expenses)) : ?>
    <div class="empty-state empty-state--enhanced">
        <div class="empty-state__icon">📊</div>
        <h3 class="empty-state__title">No Expenses Found</h3>
        <p class="empty-state__message"><?= htmlspecialchars($message) ?></p>
        <div class="empty-state__actions">
            <a href="<?= htmlspecialchars($actionUrl) ?>" class="<?= htmlspecialchars($actionClass) ?>">
                <?= htmlspecialchars($actionText) ?>
            </a>
            <?php if (!$hasFilters): ?>
                <a href="/expenses/upload" class="empty-state__action empty-state__action--secondary">
                    Upload Receipt
                </a>
            <?php endif; ?>
        </div>
    </div>
<?php endif; ?>
