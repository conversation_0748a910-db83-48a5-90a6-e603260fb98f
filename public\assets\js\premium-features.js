/**
 * Premium Features - Enhanced JavaScript functionality
 * For Personal Expense Tracker
 */

document.addEventListener('DOMContentLoaded', function () {
    // Initialize all premium features
    initializeAnimations();
    initializeTabSystem();
    initializeTooltips();
    initializeAmountAnimation();
    initializePrintFunctionality();
    initializeResponsiveNavigation();
    initializeScrollEffects();

    // Add keyboard navigation support
    document.addEventListener('keydown', handleKeyboardNavigation);
});

/**
 * Initialize entrance animations for key elements
 */
function initializeAnimations()
{
    // Animate header elements
    const headerElements = document.querySelectorAll('.app-header .page-title, .app-header .back-link');
    headerElements.forEach((el, index) => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(-10px)';
        el.style.transition = 'opacity 0.5s ease, transform 0.5s ease';

        setTimeout(() => {
            el.style.opacity = '1';
            el.style.transform = 'translateY(0)';
        }, 100 + (index * 100));
    });

    // Animate expense card
    const expenseCard = document.querySelector('.expense-card');
    if (expenseCard) {
        expenseCard.style.opacity = '0';
        expenseCard.style.transform = 'translateY(20px)';
        expenseCard.style.transition = 'opacity 0.6s ease, transform 0.6s ease';

        setTimeout(() => {
            expenseCard.style.opacity = '1';
            expenseCard.style.transform = 'translateY(0)';
        }, 300);
    }

    // Animate tags with staggered delay
    const tags = document.querySelectorAll('.tag');
    tags.forEach((tag, index) => {
        tag.style.opacity = '0';
        tag.style.transform = 'translateY(10px)';
        tag.style.transition = 'opacity 0.3s ease, transform 0.3s ease';

        setTimeout(() => {
            tag.style.opacity = '1';
            tag.style.transform = 'translateY(0)';
        }, 500 + (index * 100));
    });

    // Animate action buttons
    const actionButtons = document.querySelectorAll('.action-buttons .btn');
    actionButtons.forEach((btn, index) => {
        btn.style.opacity = '0';
        btn.style.transform = 'translateY(10px)';
        btn.style.transition = 'opacity 0.3s ease, transform 0.3s ease';

        setTimeout(() => {
            btn.style.opacity = '1';
            btn.style.transform = 'translateY(0)';
        }, 700 + (index * 100));
    });
}

/**
 * Initialize tab system with smooth transitions
 */
function initializeTabSystem()
{
    const tabs = document.querySelectorAll('.tab');
    const panels = document.querySelectorAll('.tab-panel');

    tabs.forEach(tab => {
        tab.addEventListener('click', () => {
            // Remove active class from all tabs and panels
            tabs.forEach(t => t.setAttribute('aria-selected', 'false'));
            tabs.forEach(t => t.classList.remove('active'));
            panels.forEach(p => p.classList.remove('active'));

            // Add active class to clicked tab and its panel
            tab.setAttribute('aria-selected', 'true');
            tab.classList.add('active');

            const panelId = tab.getAttribute('aria-controls');
            const panel = document.getElementById(panelId);

            if (panel) {
                // Apply fade-in animation to the panel
                panel.style.opacity = '0';
                panel.classList.add('active');

                setTimeout(() => {
                    panel.style.opacity = '1';
                    panel.style.transition = 'opacity 0.3s ease';
                }, 50);
            }
        });
    });
}

/**
 * Initialize tooltips
 */
function initializeTooltips()
{
    const tooltips = document.querySelectorAll('[data-tooltip]');

    tooltips.forEach(tooltip => {
        // Create tooltip element
        const tooltipText = tooltip.getAttribute('data-tooltip');
        const tooltipEl = document.createElement('div');
        tooltipEl.className = 'tooltip-text';
        tooltipEl.textContent = tooltipText;
        tooltipEl.style.position = 'absolute';
        tooltipEl.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
        tooltipEl.style.color = 'white';
        tooltipEl.style.padding = '0.5rem 0.75rem';
        tooltipEl.style.borderRadius = '0.375rem';
        tooltipEl.style.fontSize = '0.75rem';
        tooltipEl.style.zIndex = '1000';
        tooltipEl.style.opacity = '0';
        tooltipEl.style.transition = 'opacity 0.2s ease';
        tooltipEl.style.pointerEvents = 'none';
        tooltipEl.style.whiteSpace = 'nowrap';

        // Position the tooltip
        tooltip.style.position = 'relative';
        tooltip.appendChild(tooltipEl);

        // Show/hide tooltip on hover
        tooltip.addEventListener('mouseenter', () => {
            const rect = tooltip.getBoundingClientRect();
            tooltipEl.style.bottom = 'calc(100% + 10px)';
            tooltipEl.style.left = '50%';
            tooltipEl.style.transform = 'translateX(-50%)';
            tooltipEl.style.opacity = '1';
        });

        tooltip.addEventListener('mouseleave', () => {
            tooltipEl.style.opacity = '0';
        });
    });
}

/**
 * Animate amount value with counting effect
 */
function initializeAmountAnimation()
{
    const amountValue = document.getElementById('amount-value');
    if (amountValue) {
        const originalValue = amountValue.textContent;
        amountValue.textContent = '';

        // Animate counting up
        const duration = 1000; // 1 second
        const frameDuration = 1000 / 60; // 60fps
        const totalFrames = Math.round(duration / frameDuration);

        const cleanValue = parseFloat(originalValue.replace(/[^0-9.-]+/g, ''));
        let frame = 0;

        const counter = setInterval(() => {
            frame++;
            const progress = frame / totalFrames;
            const currentValue = Math.floor(cleanValue * progress * 100) / 100;

            amountValue.textContent = '$' + currentValue.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",");

            if (frame === totalFrames) {
                clearInterval(counter);
                amountValue.textContent = originalValue;

                // Add subtle pulse animation after counting is done
                setTimeout(() => {
                    amountValue.classList.add('animate-pulse');
                    setTimeout(() => {
                        amountValue.classList.remove('animate-pulse');
                    }, 1000);
                }, 500);
            }
        }, frameDuration);
    }
}

/**
 * Initialize print functionality
 */
function initializePrintFunctionality()
{
    const printButton = document.querySelector('.fab');
    if (printButton) {
        printButton.addEventListener('click', () => {
            // Prepare page for printing
            document.body.classList.add('printing');

            // Print the page
            window.print();

            // Reset after printing
            setTimeout(() => {
                document.body.classList.remove('printing');
            }, 1000);
        });
    }
}

/**
 * Initialize responsive navigation
 */
function initializeResponsiveNavigation()
{
    const menuToggle = document.querySelector('.menu-toggle');
    const navMenu = document.querySelector('.navbar-nav');

    if (menuToggle && navMenu) {
        menuToggle.addEventListener('click', () => {
            navMenu.classList.toggle('active');
            menuToggle.classList.toggle('active');
        });
    }
}

/**
 * Initialize scroll effects
 */
function initializeScrollEffects()
{
    // Add scroll-triggered animations
    const animateOnScroll = document.querySelectorAll('.animate-on-scroll');

    const checkScroll = () => {
        animateOnScroll.forEach(element => {
            const elementTop = element.getBoundingClientRect().top;
            const elementVisible = 150;

            if (elementTop < window.innerHeight - elementVisible) {
                element.classList.add('active');
            }
        });
    };

    // Check elements on load
    checkScroll();

    // Check elements on scroll
    window.addEventListener('scroll', checkScroll);
}

/**
 * Handle keyboard navigation
 */
function handleKeyboardNavigation(e)
{
    // ESC key to go back
    if (e.key === 'Escape') {
        const backLink = document.querySelector('.back-link');
        if (backLink) {
            backLink.click();
        }
    }

    // Tab navigation with arrow keys
    if (e.key === 'ArrowRight' || e.key === 'ArrowLeft') {
        const tabs = Array.from(document.querySelectorAll('.tab'));
        const activeTab = document.querySelector('.tab.active');

        if (tabs.length > 0 && activeTab) {
            const currentIndex = tabs.indexOf(activeTab);
            let newIndex;

            if (e.key === 'ArrowRight') {
                newIndex = (currentIndex + 1) % tabs.length;
            } else {
                newIndex = (currentIndex - 1 + tabs.length) % tabs.length;
            }

            tabs[newIndex].click();
            tabs[newIndex].focus();
            e.preventDefault();
        }
    }

    // Print with Ctrl+P
    if (e.key === 'p' && (e.ctrlKey || e.metaKey)) {
        const printButton = document.querySelector('.fab');
        if (printButton) {
            e.preventDefault();
            printButton.click();
        }
    }
}

/**
 * Format currency values
 * @param {number} amount - The amount to format
 * @param {string} currency - The currency code (default: USD)
 * @returns {string} Formatted currency string
 */
function formatCurrency(amount, currency = 'USD')
{
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: currency
    }).format(amount);
}

/**
 * Format dates in a user-friendly way
 * @param {string} dateString - The date string to format
 * @param {string} format - The format to use (default: 'medium')
 * @returns {string} Formatted date string
 */
function formatDate(dateString, format = 'medium')
{
    const date = new Date(dateString);

    switch (format) {
        case 'short':
            return date.toLocaleDateString();
        case 'medium':
            return date.toLocaleDateString(undefined, {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        case 'long':
            return date.toLocaleDateString(undefined, {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        case 'relative':
            const now = new Date();
            const diffTime = Math.abs(now - date);
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            if (diffDays === 0) {
                return 'Today';
            }
            if (diffDays === 1) {
                return 'Yesterday';
            }
            if (diffDays < 7) {
                return `$diffDays} days ago`;
            if (diffDays < 30) {
                return `$Math.floor(diffDays / 7)} weeks ago`;
            if (diffDays < 365) {
                return `$Math.floor(diffDays / 30)} months ago`;
            return `${Math.floor(diffDays / 365)} years ago`;
        default:
            return date.toLocaleDateString();
    }
}

/**
 * Add dark mode toggle functionality
 */
function toggleDarkMode()
{
    document.body.classList.toggle('dark-mode');

    // Save preference to localStorage
    const isDarkMode = document.body.classList.contains('dark-mode');
    localStorage.setItem('darkMode', isDarkMode);

    // Update toggle button text/icon if it exists
    const darkModeToggle = document.getElementById('dark-mode-toggle');
    if (darkModeToggle) {
        const icon = darkModeToggle.querySelector('i');
        if (icon) {
            if (isDarkMode) {
                icon.classList.remove('fa-moon');
                icon.classList.add('fa-sun');
            } else {
                icon.classList.remove('fa-sun');
                icon.classList.add('fa-moon');
            }
        }
    }
}

/**
 * Check for saved dark mode preference
 */
function checkDarkModePreference()
{
    const savedDarkMode = localStorage.getItem('darkMode');

    if (savedDarkMode === 'true') {
        document.body.classList.add('dark-mode');

        // Update toggle button if it exists
        const darkModeToggle = document.getElementById('dark-mode-toggle');
        if (darkModeToggle) {
            const icon = darkModeToggle.querySelector('i');
            if (icon) {
                icon.classList.remove('fa-moon');
                icon.classList.add('fa-sun');
            }
        }
    }
}

// Check dark mode preference on load
checkDarkModePreference();