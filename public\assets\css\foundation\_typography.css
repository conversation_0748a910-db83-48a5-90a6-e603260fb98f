/* ===== TYPOGRAPHY SYSTEM ===== */
h1, h2, h3, h4, h5, h6 {
    margin-bottom: var(--spacing);
    font-weight: var(--font-weight-bold);
    line-height: 1.3;
    color: var(--dark-color);
  }

  h1 {
    font-size: 2.5rem;
    letter-spacing: -0.025em;
  }

  h2 {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--dark-color);
    margin-bottom: var(--spacing-lg);
    border-bottom: 1px solid var(--grey-300);
    padding-top: calc(var(--header-height) + var(--spacing-sm));
    padding-bottom: var(--spacing);
    letter-spacing: -0.015em;
  }

  h3 {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--dark-color);
    margin-bottom: var(--spacing);
  }

  h4 {
    font-size: var(--font-size-md);
    font-weight: var(--font-weight-semibold);
    color: var(--dark-color);
    margin-bottom: var(--spacing-sm);
  }

  h5 {
    font-size: 1.125rem;
  }

  h6 {
    font-size: 1rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  p {
    margin-bottom: var(--spacing);
  }

  /* Links */
  a {
    color: var(--primary-color);
    text-decoration: none;
    transition: var(--transition-fast);
  }

  a:hover {
    color: var(--primary-dark);
    text-decoration: none;
  }

  /* a:not(.button):hover {
    text-decoration: underline;
    text-decoration-thickness: 2px;
    text-underline-offset: 2px;
  } */

  .text-muted {
    color: var(--text-muted);
    font-style: italic;
  }