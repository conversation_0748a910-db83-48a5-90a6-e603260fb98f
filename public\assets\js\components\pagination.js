function initializePagination(paginationContainer)
{
    if (!paginationContainer) {
        return;
    }

    const paginationLinks = paginationContainer.querySelectorAll('.pagination-button');
    paginationLinks.forEach(link => {
        link.addEventListener('click', handlePaginationClick);
    });
}

function handlePaginationClick(event)
{
    if (isOpenInNewTabClick(event)) {
        return;
    }

    event.preventDefault();
    navigateToPage(this.href);
}

function isOpenInNewTabClick(event)
{
    return event.button !== 0 || event.ctrlKey || event.metaKey;
}

function navigateToPage(linkHref)
{
    const url = new URL(linkHref);
    const page = url.searchParams.get('page');

    const currentUrl = new URL(window.location.href);
    currentUrl.searchParams.set('page', page);

    window.location.href = currentUrl.toString();
}

function createPagination(totalItems, itemsPerPage, currentPage, baseUrl)
{
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    const paginationContainer = createPaginationContainer();

    if (totalPages <= 1) {
        return paginationContainer;
    }

    if (currentPage > 1) {
        addPreviousPageLink(paginationContainer, baseUrl, currentPage);
    }

    addPageInfo(paginationContainer, currentPage, totalPages);

    if (currentPage < totalPages) {
        addNextPageLink(paginationContainer, baseUrl, currentPage);
    }

    return paginationContainer;
}

function createPaginationContainer()
{
    const container = document.createElement('nav');
    container.className = 'pagination';
    container.setAttribute('aria-label', 'Pagination');
    return container;
}

function addPreviousPageLink(container, baseUrl, currentPage)
{
    const prevLink = createPaginationLink(
        `${baseUrl} ? page = ${currentPage - 1}`,
        '« Previous'
    );
    container.appendChild(prevLink);
}

function addPageInfo(container, currentPage, totalPages)
{
    const pageInfo = document.createElement('span');
    pageInfo.className = 'pagination-info';
    pageInfo.textContent = `Page ${currentPage} of ${totalPages}`;
    container.appendChild(pageInfo);
}

function addNextPageLink(container, baseUrl, currentPage)
{
    const nextLink = createPaginationLink(
        `${baseUrl} ? page = ${currentPage + 1}`,
        'Next »'
    );
    container.appendChild(nextLink);
}

function createPaginationLink(href, text)
{
    const link = document.createElement('a');
    link.className = 'pagination-button';
    link.href = href;
    link.textContent = text;
    link.addEventListener('click', handlePaginationClick);
    return link;
}

export { initializePagination, createPagination };
