<?php

declare(strict_types=1);

namespace App\Models;

use App\Exceptions\InvalidArgumentException;

class PaymentMethod extends BaseModel
{
    protected static function getTableName(): string
    {
        return 'payment_methods';
    }

    private static function getColumnList(): string
    {
        return "id, method";
    }

    /**
     * =========================================================================
     * CRUD Operations
     * =========================================================================
     */

    public static function createMethod(string $method): int
    {
        self::validateMethod($method);

        $data = [
            'method' => $method
        ];

        return self::create($data);
    }

    public static function create(array $data): int
    {
        if (isset($data['method'])) {
            self::validateMethod($data['method']);
        }

        return self::transaction(function () use ($data) {
            $columns = implode(', ', array_keys($data));
            $placeholders = ':' . implode(', :', array_keys($data));

            $sql = sprintf(
                "INSERT INTO %s (%s) VALUES (%s)",
                self::getTableName(),
                $columns,
                $placeholders
            );

            self::executeSql($sql, $data);
            return (int)self::getDb()->lastInsertId();
        });
    }

    /**
     * =========================================================================
     * Retrieval Methods
     * =========================================================================
     */

    public static function find(int $id): ?array
    {
        $sql = sprintf(
            "SELECT %s FROM %s WHERE id = ?",
            self::getColumnList(),
            self::getTableName()
        );

        return self::fetchOne($sql, [$id]);
    }

    public static function findByMethod(string $method): ?array
    {
        $sql = sprintf(
            "SELECT %s FROM %s WHERE method = ?",
            self::getColumnList(),
            self::getTableName()
        );

        return self::fetchOne($sql, [$method]);
    }

    public static function all(): array
    {
        try {
            $sql = sprintf(
                "SELECT %s FROM %s ORDER BY method ASC",
                self::getColumnList(),
                self::getTableName()
            );

            error_log("PaymentMethod::all() - SQL: " . $sql);

            $result = self::fetchMany($sql);
            error_log("PaymentMethod::all() - Result: " . json_encode($result));

            return $result;
        } catch (\Exception $e) {
            error_log("PaymentMethod::all() - Error: " . $e->getMessage());

            // Return default payment methods as a fallback
            $defaultMethods = [
                ['id' => 1, 'method' => 'Cash'],
                ['id' => 2, 'method' => 'Credit Card'],
                ['id' => 3, 'method' => 'Debit Card'],
                ['id' => 4, 'method' => 'Bank Transfer'],
                ['id' => 5, 'method' => 'PayPal'],
                ['id' => 6, 'method' => 'Check']
            ];

            error_log("PaymentMethod::all() - Using default methods: " . json_encode($defaultMethods));
            return $defaultMethods;
        }
    }

    public static function updateMethod(int $id, string $method): bool
    {
        self::validateMethod($method);

        $data = [
            'method' => $method
        ];

        return self::update($id, $data);
    }

    public static function update(int $id, array $data): bool
    {
        if (isset($data['method'])) {
            self::validateMethod($data['method']);
        }

        if (empty($data)) {
            return false;
        }

        return self::transaction(function () use ($id, $data) {
            $setClause = implode(', ', array_map(fn($col) => "{$col} = :{$col}", array_keys($data)));

            $sql = sprintf(
                "UPDATE %s SET %s WHERE id = :id",
                self::getTableName(),
                $setClause
            );

            $data['id'] = $id;
            return self::executeSql($sql, $data);
        });
    }

    public static function delete(int $id): bool
    {
        return self::transaction(function () use ($id) {
            $sql = sprintf(
                "DELETE FROM %s WHERE id = ?",
                self::getTableName()
            );

            return self::executeSql($sql, [$id]);
        });
    }

    /**
     * =========================================================================
     * Validation Methods
     * =========================================================================
     */

    /**
     * =========================================================================
     * Utility Methods
     * =========================================================================
     */

    public static function findSimilar(string $method, float $threshold = 0.3): ?array
    {
        $methods = self::all();
        $bestMatch = null;
        $highestSimilarity = 0;

        foreach ($methods as $paymentMethod) {
            similar_text(
                strtolower($method),
                strtolower($paymentMethod['method']),
                $percent
            );

            if ($percent > $threshold * 100 && $percent > $highestSimilarity) {
                $highestSimilarity = $percent;
                $bestMatch = $paymentMethod;
            }
        }

        return $bestMatch;
    }

    public static function resolveOrCreate(string $method): ?int
    {
        if (!$method) {
            return null;
        }

        $methodTrimmed = trim($method);
        $exactMatch = self::findByMethod($methodTrimmed);

        if ($exactMatch) {
            return $exactMatch['id'];
        }

        $similar = self::findSimilar($methodTrimmed);
        if ($similar) {
            return $similar['id'];
        }

        return self::createMethod($methodTrimmed);
    }

    /**
     * =========================================================================
     * Validation Methods
     * =========================================================================
     */

    private static function validateMethod(string $method): void
    {
        if (empty(trim($method))) {
            throw new InvalidArgumentException('Payment method name is required');
        }
    }
}
