<?php
/**
 * PDF Receipt Template
 * 
 * @var string $receiptContent The formatted receipt content
 * @var string $title Optional title for the receipt
 */

$title = $title ?? 'Receipt';
$generatedDate = date('Y-m-d H:i:s');
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($title) ?> - PDF</title>
    <link rel="stylesheet" href="/css/receipt.css">
    <script>
        window.onload = function() {
            var printBtn = document.createElement("button");
            printBtn.innerHTML = "Print Receipt";
            printBtn.className = "print-button";
            printBtn.onclick = function() { window.print(); };
            document.body.appendChild(printBtn);
        };
    </script>
</head>
<body>
    <div class="pdf-header">
        <h1><?= htmlspecialchars($title) ?></h1>
        <p>Generated on <?= htmlspecialchars($generatedDate) ?></p>
    </div>
    <div class="pdf-content">
        <?= $receiptContent ?>
    </div>
</body>
</html>