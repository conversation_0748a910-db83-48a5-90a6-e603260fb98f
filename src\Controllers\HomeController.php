<?php

declare(strict_types=1);

namespace App\Controllers;

use App\Core\Response;

class HomeController extends BaseController
{
    public function index(): Response
    {
        // If user is already authenticated, redirect to dashboard
        if ($this->isAuthenticated()) {
            return $this->redirect('/dashboard');
        }

        // Otherwise, show the home page
        return $this->direct('home/index');
    }

    public function how(): Response
    {
        return $this->direct('home/how');
    }
}
