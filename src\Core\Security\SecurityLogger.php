<?php

declare(strict_types=1);

namespace App\Core\Security;

use App\Core\LogManager;
use Psr\Log\LoggerInterface;

final class SecurityLogger
{
    private LoggerInterface $logger;
    private bool $logSensitiveData;

    public function __construct(?LoggerInterface $logger = null, bool $logSensitiveData = false)
    {
        $this->logger = $logger ?? LogManager::getInstance()->getLogger('security');
        $this->logSensitiveData = $logSensitiveData;
    }

    public function logAuthenticationAttempt(string $email, bool $success, array $context = []): void
    {
        $sanitizedEmail = $this->logSensitiveData ? $email : $this->maskEmail($email);
        
        $logContext = array_merge([
            'event' => 'authentication_attempt',
            'email' => $sanitizedEmail,
            'success' => $success,
            'ip_address' => $this->getClientIp(),
            'user_agent' => $this->getUserAgent(),
            'timestamp' => date('Y-m-d H:i:s'),
            'session_id' => $this->getSessionId()
        ], $context);

        if ($success) {
            $this->logger->info('Successful authentication attempt', $logContext);
        } else {
            $this->logger->warning('Failed authentication attempt', $logContext);
        }
    }

    public function logPasswordReset(string $email, string $action, array $context = []): void
    {
        $sanitizedEmail = $this->logSensitiveData ? $email : $this->maskEmail($email);
        
        $logContext = array_merge([
            'event' => 'password_reset',
            'action' => $action, // 'requested', 'completed', 'failed'
            'email' => $sanitizedEmail,
            'ip_address' => $this->getClientIp(),
            'user_agent' => $this->getUserAgent(),
            'timestamp' => date('Y-m-d H:i:s')
        ], $context);

        $this->logger->info('Password reset event', $logContext);
    }

    public function logCSRFViolation(string $expectedToken, string $providedToken, array $context = []): void
    {
        $logContext = array_merge([
            'event' => 'csrf_violation',
            'expected_token_hash' => hash('sha256', $expectedToken),
            'provided_token_hash' => hash('sha256', $providedToken),
            'ip_address' => $this->getClientIp(),
            'user_agent' => $this->getUserAgent(),
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown',
            'request_method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown',
            'timestamp' => date('Y-m-d H:i:s')
        ], $context);

        $this->logger->error('CSRF token violation detected', $logContext);
    }

    public function logRateLimitExceeded(string $identifier, int $attempts, array $context = []): void
    {
        $logContext = array_merge([
            'event' => 'rate_limit_exceeded',
            'identifier' => $identifier,
            'attempts' => $attempts,
            'ip_address' => $this->getClientIp(),
            'user_agent' => $this->getUserAgent(),
            'timestamp' => date('Y-m-d H:i:s')
        ], $context);

        $this->logger->warning('Rate limit exceeded', $logContext);
    }

    public function logSuspiciousActivity(string $activity, array $context = []): void
    {
        $logContext = array_merge([
            'event' => 'suspicious_activity',
            'activity' => $activity,
            'ip_address' => $this->getClientIp(),
            'user_agent' => $this->getUserAgent(),
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown',
            'timestamp' => date('Y-m-d H:i:s')
        ], $context);

        $this->logger->warning('Suspicious activity detected', $logContext);
    }

    public function logFileUpload(string $filename, int $fileSize, string $mimeType, bool $success, array $context = []): void
    {
        $logContext = array_merge([
            'event' => 'file_upload',
            'filename' => basename($filename), // Only log basename for security
            'file_size' => $fileSize,
            'mime_type' => $mimeType,
            'success' => $success,
            'ip_address' => $this->getClientIp(),
            'user_agent' => $this->getUserAgent(),
            'timestamp' => date('Y-m-d H:i:s')
        ], $context);

        if ($success) {
            $this->logger->info('File upload completed', $logContext);
        } else {
            $this->logger->warning('File upload failed', $logContext);
        }
    }

    public function logAccessDenied(string $resource, string $reason, array $context = []): void
    {
        $logContext = array_merge([
            'event' => 'access_denied',
            'resource' => $resource,
            'reason' => $reason,
            'ip_address' => $this->getClientIp(),
            'user_agent' => $this->getUserAgent(),
            'timestamp' => date('Y-m-d H:i:s'),
            'user_id' => $_SESSION['user_id'] ?? null
        ], $context);

        $this->logger->warning('Access denied', $logContext);
    }

    public function logSessionEvent(string $event, array $context = []): void
    {
        $logContext = array_merge([
            'event' => 'session_event',
            'session_event' => $event, // 'created', 'regenerated', 'destroyed', 'hijack_detected'
            'ip_address' => $this->getClientIp(),
            'user_agent' => $this->getUserAgent(),
            'session_id' => $this->getSessionId(),
            'timestamp' => date('Y-m-d H:i:s'),
            'user_id' => $_SESSION['user_id'] ?? null
        ], $context);

        $this->logger->info('Session event', $logContext);
    }

    public function logDatabaseError(string $query, string $error, array $context = []): void
    {
        $logContext = array_merge([
            'event' => 'database_error',
            'query_hash' => hash('sha256', $query), // Don't log actual query for security
            'error' => $error,
            'ip_address' => $this->getClientIp(),
            'timestamp' => date('Y-m-d H:i:s'),
            'user_id' => $_SESSION['user_id'] ?? null
        ], $context);

        $this->logger->error('Database error occurred', $logContext);
    }

    private function maskEmail(string $email): string
    {
        if (strpos($email, '@') === false) {
            return '***';
        }

        [$local, $domain] = explode('@', $email, 2);
        
        if (strlen($local) <= 2) {
            $maskedLocal = str_repeat('*', strlen($local));
        } else {
            $maskedLocal = substr($local, 0, 1) . str_repeat('*', strlen($local) - 2) . substr($local, -1);
        }

        return $maskedLocal . '@' . $domain;
    }

    private function getClientIp(): string
    {
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'] ?? 
              $_SERVER['HTTP_X_REAL_IP'] ?? 
              $_SERVER['REMOTE_ADDR'] ?? 
              'unknown';
        
        // If behind proxy, get the first IP
        if (strpos($ip, ',') !== false) {
            $ip = trim(explode(',', $ip)[0]);
        }
        
        return $ip;
    }

    private function getUserAgent(): string
    {
        return $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
    }

    private function getSessionId(): string
    {
        return session_id() ?: 'no_session';
    }
}