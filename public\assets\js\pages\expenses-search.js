document.addEventListener('DOMContentLoaded', initializeExpensesSearchPage);

function initializeExpensesSearchPage()
{
    setupSearchForm();
    enhanceFilterDisplay();
    setupDateRangePickers();
    enhanceSearchResults();
}

function setupSearchForm()
{
    const searchForm = document.querySelector('.search-form');
    if (!searchForm) {
        return;
    }

    setupResetButton(searchForm);
    setupFormValidation(searchForm);
}

function setupResetButton(form)
{
    const resetButton = form.querySelector('a.button.secondary');
    if (!resetButton) {
        return;
    }

    resetButton.addEventListener('click', () => {
        form.querySelectorAll('input, select').forEach(field => field.value = '');
        form.submit();
    });
}

function setupFormValidation(form)
{
    form.addEventListener('submit', e => {
        if (!hasAtLeastOneFieldFilled(form)) {
            e.preventDefault();
            alert('Please enter at least one search criteria.');
            return;
        }

        if (!validateDateRange(form)) {
            e.preventDefault();
            return;
        }

        if (!validateAmountRange(form)) {
            e.preventDefault();
            return;
        }
    });
}

function hasAtLeastOneFieldFilled(form)
{
    const formFields = form.querySelectorAll('input, select');
    return Array.from(formFields).some(field => field.value.trim());
}

function validateDateRange(form)
{
    const dateFrom = form.querySelector('#date_from');
    const dateTo = form.querySelector('#date_to');

    if (dateFrom ? .value && dateTo ? .value) {
        if (new Date(dateFrom.value) > new Date(dateTo.value)) {
            alert('From date cannot be after To date.');
            dateFrom.focus();
            return false;
        }
    }
    return true;
}

function validateAmountRange(form)
{
    const minAmount = form.querySelector('#min_amount');
    const maxAmount = form.querySelector('#max_amount');

    if (minAmount ? .value && maxAmount ? .value) {
        if (parseFloat(minAmount.value) > parseFloat(maxAmount.value)) {
            alert('Minimum amount cannot be greater than maximum amount.');
            minAmount.focus();
            return false;
        }
    }
    return true;
}

function enhanceFilterDisplay()
{
    const activeFilters = document.querySelector('.active-filters');
    if (!activeFilters) {
        return;
    }

    const filterItems = activeFilters.querySelectorAll('.filter-item');
    if (filterItems.length === 0) {
        return;
    }

    const clearAllButton = createClearAllButton();
    activeFilters.appendChild(clearAllButton);
}

function createClearAllButton()
{
    const button = document.createElement('button');
    button.type = 'button';
    button.className = 'clear-all-filters';
    button.textContent = 'Clear All';
    button.addEventListener('click', () => window.location.href = '/expenses/search');
    return button;
}

function setupDateRangePickers()
{
    const dateFrom = document.getElementById('date_from');
    const dateTo = document.getElementById('date_to');
    if (!dateFrom || !dateTo) {
        return;
    }

    const today = new Date().toISOString().split('T')[0];
    setupDateLimits(dateFrom, dateTo, today);
    setupDateChangeHandlers(dateFrom, dateTo, today);
    addQuickDateSelectors(dateFrom, dateTo);
}

function setupDateLimits(dateFrom, dateTo, today)
{
    dateFrom.max = today;
    dateTo.max = today;
}

function setupDateChangeHandlers(dateFrom, dateTo, today)
{
    dateFrom.addEventListener('change', () => {
        dateTo.min = dateFrom.value || '';
        if (!dateTo.min) {
            dateTo.removeAttribute('min');
        }
    });

    dateTo.addEventListener('change', () => {
        dateFrom.max = dateTo.value || today;
    });
}

function addQuickDateSelectors(_, dateTo)
{
    const dateRangeButtons = [
        { label: 'Today', from: 0, to: 0 },
        { label: 'Yesterday', from: 1, to: 1 },
        { label: 'Last 7 Days', from: 6, to: 0 },
        { label: 'This Month', from: 'month', to: 0 },
        { label: 'Last Month', from: 'lastMonth', to: 'lastMonth' }
    ];

    const container = createDateRangeContainer(dateRangeButtons);
    insertDateRangeContainer(container, dateTo);
}

function createDateRangeContainer(dateRangeButtons)
{
    const container = document.createElement('div');
    container.className = 'date-range-shortcuts';
    container.innerHTML = '<span>Quick select: </span>';

    dateRangeButtons.forEach(range => {
        const button = document.createElement('button');
        button.type = 'button';
        button.className = 'date-range-button';
        button.textContent = range.label;
        button.addEventListener('click', () => setDateRange(range.from, range.to));
        container.appendChild(button);
    });

    return container;
}

function insertDateRangeContainer(container, dateTo)
{
    const dateFieldsContainer = dateTo.closest('.form-row');
    if (dateFieldsContainer) {
        dateFieldsContainer.parentNode.insertBefore(container, dateFieldsContainer.nextSibling);
    }
}

function setDateRange(fromValue, toValue)
{
    const dateFrom = document.getElementById('date_from');
    const dateTo = document.getElementById('date_to');
    if (!dateFrom || !dateTo) {
        return;
    }

    const today = new Date();
    const fromDate = calculateFromDate(fromValue, today);
    const toDate = calculateToDate(toValue, today);

    dateFrom.value = fromDate.toISOString().split('T')[0];
    dateTo.value = toDate.toISOString().split('T')[0];

    dateFrom.dispatchEvent(new Event('change'));
    dateTo.dispatchEvent(new Event('change'));
}

function calculateFromDate(fromValue, today)
{
    if (fromValue === 'month') {
        return new Date(today.getFullYear(), today.getMonth(), 1);
    }

    if (fromValue === 'lastMonth') {
        return new Date(today.getFullYear(), today.getMonth() - 1, 1);
    }

    const fromDate = new Date(today);
    fromDate.setDate(today.getDate() - fromValue);
    return fromDate;
}

function calculateToDate(toValue, today)
{
    if (toValue === 'lastMonth') {
        return new Date(today.getFullYear(), today.getMonth(), 0);
    }

    const toDate = new Date(today);
    toDate.setDate(today.getDate() - toValue);
    return toDate;
}

function enhanceSearchResults()
{
    const resultsTable = document.querySelector('.expenses-table');
    if (!resultsTable) {
        return;
    }

    setupTableSorting(resultsTable);
    setupRowHighlighting(resultsTable);
}

function setupTableSorting(table)
{
    table.querySelectorAll('thead th').forEach((th, index) => {
        if (th.textContent.trim().toLowerCase() === 'actions') {
            return;
        }

        makeSortable(th, index, table);
    });
}

function makeSortable(th, index, table)
{
    th.style.cursor = 'pointer';
    th.title = 'Click to sort';
    th.setAttribute('aria-sort', 'none');

    th.addEventListener('click', () => sortTable(table, index, th));
}

function setupRowHighlighting(table)
{
    table.querySelectorAll('tbody tr').forEach(row => {
        row.addEventListener('mouseenter', () => {
            row.style.backgroundColor = 'var(--grey-100)';
        });

        row.addEventListener('mouseleave', () => {
            row.style.backgroundColor = '';
        });
    });
}

function sortTable(table, columnIndex, headerCell)
{
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const currentSort = headerCell.getAttribute('aria-sort');
    const isAscending = currentSort !== 'ascending';

    updateSortIndicators(table, headerCell, isAscending);
    sortAndRearrangeRows(tbody, rows, columnIndex, isAscending);
}

function updateSortIndicators(table, headerCell, isAscending)
{
    const headerElements = table.querySelectorAll('th');
    headerElements.forEach(th => th.setAttribute('aria-sort', 'none'));

    const currentDirection = isAscending ? 'ascending' : 'descending';
    headerCell.setAttribute('aria-sort', currentDirection);

    // Update the sort button's aria-label
    const sortButton = headerCell.querySelector('.sort-button');
    if (sortButton) {
        const columnName = headerCell.querySelector('span').textContent.trim();
        const nextDirection = isAscending ? 'descending' : 'ascending';
        sortButton.setAttribute('aria-label', `Sort by ${columnName} ${nextDirection}`);
    }
}

function sortAndRearrangeRows(tbody, rows, columnIndex, isAscending)
{
    rows.sort((a, b) => {
        const cellA = a.querySelectorAll('td')[columnIndex].textContent.trim();
        const cellB = b.querySelectorAll('td')[columnIndex].textContent.trim();

        return compareValues(cellA, cellB, columnIndex, isAscending);
    });

    rows.forEach(row => tbody.appendChild(row));
}

function compareValues(cellA, cellB, columnIndex, isAscending)
{
    if (columnIndex === 0) {
        return compareDates(cellA, cellB, isAscending);
    }

    if (columnIndex === 3) {
        return compareAmounts(cellA, cellB, isAscending);
    }

    return compareStrings(cellA, cellB, isAscending);
}

function compareDates(a, b, isAscending)
{
    const dateA = new Date(a);
    const dateB = new Date(b);
    return isAscending ? dateA - dateB : dateB - dateA;
}

function compareAmounts(a, b, isAscending)
{
    const amountA = parseFloat(a.replace(/[^0-9.-]+/g, ''));
    const amountB = parseFloat(b.replace(/[^0-9.-]+/g, ''));
    return isAscending ? amountA - amountB : amountB - amountA;
}

function compareStrings(a, b, isAscending)
{
    return isAscending ? a.localeCompare(b) : b.localeCompare(a);
}

