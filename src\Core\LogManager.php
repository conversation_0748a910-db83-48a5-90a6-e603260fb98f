<?php

declare(strict_types=1);

namespace App\Core;

use Psr\Log\LoggerInterface;

final class LogManager
{
    private static ?LoggerInterface $instance = null;

    public static function setLogger(LoggerInterface $logger): void
    {
        self::$instance = $logger;
    }

    public static function getLogger(): ?LoggerInterface
    {
        return self::$instance;
    }

    public static function emergency(string|\Stringable $message, array $context = []): void
    {
        self::$instance?->emergency($message, $context);
    }

    public static function alert(string|\Stringable $message, array $context = []): void
    {
        self::$instance?->alert($message, $context);
    }

    public static function critical(string|\Stringable $message, array $context = []): void
    {
        self::$instance?->critical($message, $context);
    }

    public static function error(string|\Stringable $message, array $context = []): void
    {
        self::$instance?->error($message, $context);
    }

    public static function warning(string|\Stringable $message, array $context = []): void
    {
        self::$instance?->warning($message, $context);
    }

    public static function notice(string|\Stringable $message, array $context = []): void
    {
        self::$instance?->notice($message, $context);
    }

    public static function info(string|\Stringable $message, array $context = []): void
    {
        self::$instance?->info($message, $context);
    }

    public static function debug(string|\Stringable $message, array $context = []): void
    {
        self::$instance?->debug($message, $context);
    }

    public static function log($level, string|\Stringable $message, array $context = []): void
    {
        self::$instance?->log($level, $message, $context);
    }

    /**
     * Log an exception with context and additional data
     *
     * @param \Throwable $e The exception to log
     * @param string $context The context in which the exception occurred
     * @param array $additionalData Additional data to log
     */
    public static function logException(\Throwable $e, string $context, array $additionalData = []): void
    {
        // If we have a logger instance that supports logException, use it
        if (self::$instance instanceof Logger) {
            self::$instance->logException($e, $context, $additionalData);
            return;
        }

        // Generate a unique error ID for tracking
        $errorId = bin2hex(random_bytes(4));

        // Get debug mode from environment
        $isDebug = filter_var($_ENV['APP_DEBUG'] ?? false, FILTER_VALIDATE_BOOLEAN);

        // Format error location
        $errorLocation = basename($e->getFile()) . ':' . $e->getLine();

        // Create log data array
        $logData = [
            'timestamp' => date('Y-m-d H:i:s'),
            'error_id' => $errorId,
            'context' => $context,
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'type' => get_class($e),
            'data' => $additionalData
        ];

        // Add stack trace in debug mode
        if ($isDebug) {
            $logData['trace'] = $e->getTraceAsString();
        }

        // Format a message for error_log
        $logMessage = sprintf(
            "[ERROR][%s] %s: %s in %s (Error ID: %s)",
            $logData['timestamp'],
            $context,
            $e->getMessage(),
            $errorLocation,
            $errorId
        );

        // Log to PHP error log as a fallback
        error_log($logMessage);

        // Also log to the PSR logger if available
        self::$instance?->error($context, $logData);
    }
}
