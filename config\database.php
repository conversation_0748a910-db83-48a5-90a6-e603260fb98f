<?php

declare(strict_types=1);

namespace App\Config;

use App\Core\Database;
use PDO;

$databaseConfig = [
    'driver' => $_ENV['DB_DRIVER'] ?? 'mysql',
    'host' => DB_CONFIG['host'],
    'database' => DB_CONFIG['name'],
    'username' => DB_CONFIG['user'],
    'password' => DB_CONFIG['password'],
    'charset' => 'utf8mb4',
    'collation' => 'utf8mb4_0900_ai_ci',
    'options' => [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_0900_ai_ci",
    ],
];

Database::initialize($databaseConfig);
