/* ===== ENHANCED UI COMPONENTS ===== */

/* Accessibility */
.visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

/* Animation Effects */
.animate-entry {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.pulse-once {
  animation: pulse 1s ease-in-out 1;
  animation-fill-mode: forwards;
}

.pulse-subtle {
  animation: subtlePulse 2s infinite;
  animation-fill-mode: forwards;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(var(--primary-rgb), 0.4);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(var(--primary-rgb), 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(var(--primary-rgb), 0);
  }
}

@keyframes subtlePulse {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--primary-rgb), 0.2);
  }
  70% {
    box-shadow: 0 0 0 5px rgba(var(--primary-rgb), 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--primary-rgb), 0);
  }
}

/* Enhanced Tabs */
.enhanced-tabs {
  border-bottom: 1px solid var(--border-color);
  margin-bottom: var(--spacing-lg);
  display: flex;
  gap: var(--spacing-sm);
}

.enhanced-tabs .tab-button {
  position: relative;
  padding: var(--spacing) var(--spacing-lg);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  border-radius: var(--border-radius) var(--border-radius) 0 0;
  transition: all 0.3s ease;
  border-bottom: none; /* Override the default tab-button border */
}

.enhanced-tabs .tab-button:hover {
  background-color: var(--grey-100);
}

.enhanced-tabs .tab-button.active {
  background-color: var(--grey-100);
  color: var(--primary-color);
}

.tab-icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--grey-200);
  transition: all 0.3s ease;
}

/* Fix tab icon centering */
.tab-icon-container svg {
  display: block;
  width: 24px;
  height: 24px;
  margin: 0;
}

.tab-button.active .tab-icon-container {
  background-color: var(--primary-light);
  transform: scale(1.1);
}

.tab-button .tab-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.tab-indicator {
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: transparent;
  transition: all 0.3s ease;
  z-index: 1; /* Ensure it appears above the border */
}

.tab-button.active .tab-indicator {
  background-color: var(--primary-color);
  box-shadow: 0 1px 3px rgba(var(--primary-rgb), 0.3); /* Add subtle shadow for depth */
}

.document-indicator {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: var(--success-color);
  border: 2px solid var(--grey-100);
}

.tab-button.has-document .tab-icon-container {
  color: var(--primary-color);
}

/* Enhanced Document Placeholder - Aligned with show_document.php */
#tab-document .document-placeholder.emotional-design {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-lg);
  padding: var(--spacing-xl);
  text-align: center;
  background-color: var(--grey-100);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-sm);
  transition: all 0.3s ease;
  border: 1px solid var(--border-color);
}

#tab-document .document-placeholder .placeholder-icon.animated {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-light), var(--primary-color));
  border-radius: 50%;
  margin: 0 auto;
  color: white;
  transition: all 0.3s ease;
  animation: float 3s ease-in-out infinite;
  box-shadow: 0 10px 20px rgba(var(--primary-rgb), 0.2);
}

#tab-document .document-placeholder .placeholder-icon.animated svg {
  display: block;
  width: 40px;
  height: 40px;
  margin: 0; /* Remove any default margins */
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

#tab-document .document-placeholder .placeholder-title {
  font-size: var(--font-size-lg);
  margin-bottom: var(--spacing-sm);
  color: var(--text-color);
  font-weight: var(--font-weight-bold);
}

#tab-document .document-placeholder .info-message {
  margin-bottom: var(--spacing);
  color: var(--text-muted);
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.5;
}

#tab-document .benefits-container {
  background-color: rgba(var(--primary-rgb), 0.05);
  border-radius: var(--border-radius);
  padding: var(--spacing);
  margin: var(--spacing) auto;
  max-width: 400px;
}

#tab-document .benefits-title {
  font-size: var(--font-size);
  margin-bottom: var(--spacing-sm);
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

#tab-document .benefits-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin: var(--spacing) 0;
  text-align: left;
}

#tab-document .benefit-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--text-color);
  font-size: var(--font-size);
  padding: var(--spacing-xs) 0;
}

#tab-document .benefit-icon {
  color: var(--success-color);
  flex-shrink: 0;
  width: 18px;
  height: 18px;
  display: block;
  margin: 0;
}

#tab-document .placeholder-actions {
  margin-top: var(--spacing);
}

#tab-document .document-add-action {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  margin: 0 auto;
  max-width: 300px;
  border: none;
  padding: var(--spacing-lg);
  box-shadow: 0 4px 15px rgba(var(--primary-rgb), 0.3);
  position: relative;
  overflow: hidden;
}

#tab-document .document-add-action::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
  z-index: 1;
}

#tab-document .document-add-action .action-icon-container {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  box-shadow: 0 2px 10px rgba(255, 255, 255, 0.2);
  z-index: 2;
}

#tab-document .document-add-action .action-text {
  z-index: 2;
}

#tab-document .document-add-action .action-title,
#tab-document .document-add-action .action-description {
  color: white;
}

#tab-document .document-add-action:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(var(--primary-rgb), 0.4);
  background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
}

#tab-document .document-add-action:hover .action-icon-container {
  transform: scale(1.15);
  background-color: rgba(255, 255, 255, 0.3);
}

/* Document File Info Styles - Aligned with show_document.php */
.document-file-info {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing);
  padding: var(--spacing);
  margin-bottom: var(--spacing-lg);
  background-color: var(--grey-100);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.file-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: var(--primary-light);
  color: var(--primary-color);
}

/* Fix icon centering */
.file-icon-container svg {
  display: block;
  width: 28px;
  height: 28px;
  margin: 0; /* Remove any default margins */
}

.document-icon {
  width: 28px;
  height: 28px;
}

.file-info-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.file-info-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.file-name {
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size);
  color: var(--text-color);
}

.extraction-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
}

.extraction-status.success {
  background-color: var(--success-light);
  color: var(--success-color);
}

.status-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-icon svg {
  display: block;
  width: 16px;
  height: 16px;
  margin: 0; /* Remove any default margins */
}

.status-text {
  font-weight: var(--font-weight-medium);
}

.file-info-details {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing) var(--spacing-lg);
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

/* Document Actions Section - Specific to show.php */
#tab-document .document-actions-section {
  margin-top: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

#tab-document .section-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing);
  color: var(--text-color);
  padding-bottom: var(--spacing-xs);
  border-bottom: 1px solid var(--border-color);
}

#tab-document .action-icon {
  color: var(--primary-color);
  width: 24px;
  height: 24px;
  display: block;
  margin: 0;
}

#tab-document .action-buttons-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-sm);
  border: 1px solid var(--border-color);
}

#tab-document .action-button {
  display: flex;
  align-items: center;
  gap: var(--spacing);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius);
  background-color: var(--grey-100);
  border: 1px solid var(--border-color);
  cursor: pointer;
  text-decoration: none;
  color: var(--text-color);
  transition: all 0.3s ease;
  box-shadow: var(--box-shadow-sm);
}

#tab-document .action-button:hover {
  background-color: var(--grey-200);
  transform: translateY(-3px);
  box-shadow: var(--box-shadow);
}

#tab-document .action-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: var(--grey-200);
  color: var(--primary-color);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

/* Fix icon centering */
#tab-document .action-icon-container svg {
  display: block;
  width: 24px;
  height: 24px;
  margin: 0; /* Remove any default margins */
}

#tab-document .action-button:hover .action-icon-container {
  transform: scale(1.1);
}

/* Original action button styles for other pages */
.action-button:not(#tab-document .action-button) {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing);
  border-radius: var(--border-radius);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  transition: var(--transition-fast);
  border: none;
  cursor: pointer;
  font-size: var(--font-size);
}

.primary-action-icon {
  background-color: var(--primary-color);
  color: white;
}

.action-text {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.action-title {
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  color: var(--text-color);
}

.action-description {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
}

.document-viewer-link {
  background-color: var(--primary-light);
  color: var(--primary-dark);
}

.document-viewer-link:hover {
  background-color: var(--primary-color);
  color: white;
}

.document-viewer-link:hover .action-title,
.document-viewer-link:hover .action-description {
  color: white;
}

.download-action .action-icon-container {
  background-color: var(--info-light);
  color: var(--info-color);
}

.print-action .action-icon-container {
  background-color: var(--warning-light);
  color: var(--warning-color);
}

.text-action .action-icon-container {
  background-color: var(--success-light);
  color: var(--success-color);
}

/* Animation for reveal effect */
.reveal-effect {
  animation: revealIn 0.5s ease-out;
}

@keyframes revealIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Tab Content with Enhanced Transitions */
.tab-content {
  display: none;
  opacity: 0;
  transition: opacity 0.3s ease-out, transform 0.3s ease-out;
}

.tab-content.active {
  display: block;
  opacity: 1;
}

.tab-content.transitioning-out {
  opacity: 0;
  transform: translateY(10px);
}

.tab-content.transitioning-in {
  animation: tabContentIn 0.3s ease-out forwards;
}

@keyframes tabContentIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Prevent interaction during tab transitions */
body.tab-transitioning {
  pointer-events: none;
}

body.tab-transitioning .tab-button {
  pointer-events: none;
}

/* Enhanced Help Tooltip */
.help-tooltip {
  position: relative;
  margin-top: var(--spacing-lg);
  display: flex;
  justify-content: flex-end;
}

.help-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background-color: var(--grey-200);
  border: none;
  border-radius: 50%;
  color: var(--text-muted);
  cursor: pointer;
  transition: all 0.3s ease;
}

.help-button:hover {
  background-color: var(--primary-light);
  color: var(--primary-color);
  transform: scale(1.05);
}

.help-button:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.tooltip-content {
  position: absolute;
  top: calc(100% + 10px);
  right: 0;
  width: 320px;
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-lg);
  z-index: 1000; /* Higher z-index to ensure it appears above other elements */
  opacity: 0;
  visibility: hidden;
  border: 1px solid var(--border-color);
  overflow: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease, transform 0.3s ease;
  transform: translateY(-10px) scale(0.95);
}

.tooltip-content.visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0) scale(1);
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.tooltip-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing) var(--spacing);
  border-bottom: 1px solid var(--border-color);
  background-color: var(--grey-100);
}

.tooltip-header h4 {
  margin: 0;
  font-size: var(--font-size);
  color: var(--text-color);
}

.close-tooltip {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-tooltip:hover {
  background-color: var(--grey-200);
  color: var(--text-color);
}

.tooltip-body {
  padding: var(--spacing);
}

.tooltip-body p {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-muted);
  font-size: var(--font-size-sm);
}

.tooltip-body ul {
  margin: 0 0 var(--spacing-sm) 0;
  padding-left: var(--spacing);
}

.tooltip-body li {
  margin-bottom: var(--spacing-xs);
  color: var(--text-muted);
  font-size: var(--font-size-sm);
}

.tooltip-note {
  font-style: italic;
  font-size: var(--font-size-xs);
  color: var(--text-muted);
  margin-top: var(--spacing);
  padding-top: var(--spacing-sm);
  border-top: 1px dashed var(--border-color);
}

.tooltip-footer {
  padding: var(--spacing-sm) var(--spacing);
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
}

.tooltip-action-button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: var(--spacing-xs) var(--spacing);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-sm);
  cursor: pointer;
  transition: all 0.3s ease;
}

.tooltip-action-button:hover {
  background-color: var(--primary-dark);
}

/* Button with hover effect */
.with-hover-effect {
  transition: all 0.3s ease;
}

.with-hover-effect:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .enhanced-tabs {
    overflow-x: auto;
    padding-bottom: var(--spacing-xs);
  }

  .enhanced-tabs .tab-button {
    padding: var(--spacing-sm) var(--spacing);
  }

  .document-actions.enhanced {
    gap: var(--spacing);
  }

  .secondary-actions {
    flex-direction: column;
  }

  .tooltip-content {
    width: 280px;
  }
}