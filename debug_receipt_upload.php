<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Services\ExpenseManagerService;
use App\Services\ExpenseService;
use App\Services\FileService;
use App\Services\ReceiptParserService;
use App\Services\CategoryService;
use App\Services\MerchantService;
use App\Services\PaymentMethodService;
use App\Core\LogManager;

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Create a test receipt file
$receiptContent = <<<EOT
Cinema: MovieMax
Date: 2023-11-01
Items:
  - Movie Ticket: $12.00
  - Popcorn: $5.50
  - Soda: $3.00
Total: $20.50
Payment Method: Cash
EOT;

$tempFile = tempnam(sys_get_temp_dir(), 'receipt_');
file_put_contents($tempFile, $receiptContent);

// Create a mock $_FILES array
$_FILES['receipt'] = [
    'name' => 'test_receipt.txt',
    'type' => 'text/plain',
    'tmp_name' => $tempFile,
    'error' => UPLOAD_ERR_OK,
    'size' => strlen($receiptContent)
];

// Set up services
$expenseService = new ExpenseService();

// Create a PSR-3 compatible logger
$logger = new class implements \Psr\Log\LoggerInterface {
    public function emergency($message, array $context = []): void
    {
        echo "EMERGENCY: $message\n";
    }
    public function alert($message, array $context = []): void
    {
        echo "ALERT: $message\n";
    }
    public function critical($message, array $context = []): void
    {
        echo "CRITICAL: $message\n";
    }
    public function error($message, array $context = []): void
    {
        echo "ERROR: $message\n";
    }
    public function warning($message, array $context = []): void
    {
        echo "WARNING: $message\n";
    }
    public function notice($message, array $context = []): void
    {
        echo "NOTICE: $message\n";
    }
    public function info($message, array $context = []): void
    {
        echo "INFO: $message\n";
    }
    public function debug($message, array $context = []): void
    {
        echo "DEBUG: $message\n";
    }
    public function log($level, $message, array $context = []): void
    {
        echo "LOG ($level): $message\n";
    }
};

$fileService = new FileService(
    $logger,
    $expenseService,
    1
);
$categoryService = new CategoryService();
$merchantService = new MerchantService();
$paymentMethodService = new PaymentMethodService();
$receiptParserService = new ReceiptParserService(
    $fileService,
    $expenseService
);

// Create the ExpenseManagerService
$expenseManager = new ExpenseManagerService(
    $expenseService,
    $categoryService,
    $merchantService,
    $fileService,
    $receiptParserService,
    $paymentMethodService
);

// Debug information
echo "Starting receipt upload debug...\n\n";

try {
    // Test the createExpenseFromReceipt method
    echo "Testing createExpenseFromReceipt method...\n";

    // Set test parameters
    $categoryId = 1; // Assuming category ID 1 exists
    $userId = 1; // Assuming user ID 1 exists

    // Call the method
    $expenseId = $expenseManager->createExpenseFromReceipt(
        $_FILES['receipt'],
        $categoryId,
        $userId
    );

    echo "Success! Created expense with ID: {$expenseId}\n";

    // Get the created expense
    echo "\nRetrieving created expense...\n";
    $expense = $expenseService->getExpenseById($expenseId, $userId);

    echo "Expense details:\n";
    echo "----------------\n";
    echo "ID: {$expense['id']}\n";
    echo "Amount: {$expense['amount']}\n";
    echo "Description: {$expense['description']}\n";
    echo "Date: {$expense['date']}\n";
    echo "Category ID: {$expense['category_id']}\n";
    echo "Merchant ID: {$expense['merchant_id']}\n";
    echo "Payment Method ID: {$expense['payment_method_id']}\n";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

// Clean up
unlink($tempFile);
