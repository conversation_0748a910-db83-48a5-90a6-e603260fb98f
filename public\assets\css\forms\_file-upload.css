/* ===== FILE UPLOAD COMPONENTS ===== */
.file-input-container {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .file-input-label {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    background-color: var(--light-color);
    border: 1px solid #ced4da;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius);
    cursor: pointer;
    font-weight: var(--font-weight-medium);
    transition: var(--transition-fast);
    max-width: fit-content;
  }

  .file-input-label:hover {
    background-color: #e9ecef;
    border-color: #bdc3c7;
  }

  .file-input-label:active {
    background-color: #dde1e3;
  }

  .file-input-label svg {
    width: 20px;
    height: 20px;
  }

  .file-info {
    margin-top: var(--spacing-xs);
    font-size: var(--font-size-sm);
    color: var(--text-muted);
  }

  .file-preview {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-xs) var(--spacing-sm);
    background-color: var(--light-color);
    border-radius: var(--border-radius);
    margin-top: var(--spacing-xs);
  }

  .file-preview-name {
    font-weight: var(--font-weight-medium);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .file-preview-size {
    color: var(--text-muted);
    font-size: var(--font-size-sm);
  }

  .file-remove {
    margin-left: auto;
    color: var(--danger-color);
    cursor: pointer;
    opacity: 0.7;
    transition: var(--transition-fast);
  }

  .file-remove:hover {
    opacity: 1;
  }

  @media (max-width: 576px) {
  .document-actions .button {
      width: 100%;
    }
  }