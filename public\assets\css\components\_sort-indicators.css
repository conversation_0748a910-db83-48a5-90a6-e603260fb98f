/**
 * Sort indicators styling
 */

/* Sort icon styling */
.sort-icon {
    width: 16px;
    height: 16px;
    vertical-align: middle;
    transition: transform 0.3s ease, fill 0.2s ease;
    fill: #888; /* Default color */
}

.sort-icon.active {
    fill: var(--primary-color, #3a7bd5);
}

/* Rotate the icon based on sort direction */
.sort-button[data-direction="asc"] .sort-icon {
    transform: rotate(180deg);
}

.sort-button[data-direction="desc"] .sort-icon {
    transform: rotate(0deg);
}

/* Hover effect for sort buttons */
.sort-button:hover .sort-icon {
    fill: var(--primary-color, #3a7bd5);
    opacity: 0.8;
}

/* Direction indicator styling */
.direction-indicator {
    display: inline-block;
    margin-left: 4px;
    font-weight: bold;
    color: var(--primary-color, #3a7bd5);
    font-size: 14px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.direction-indicator.active {
    opacity: 1;
}

/* Active sort button styling */
.sort-button.active {
    border-radius: 4px;
    padding: 4px 8px;
}

/* Improve button appearance */
.sort-button {
    border: none;
    background: transparent;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
    transition: background-color 0.2s ease, box-shadow 0.2s ease;
}
