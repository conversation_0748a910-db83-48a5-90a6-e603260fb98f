<?php

declare(strict_types=1);

namespace App\Services;

use App\Exceptions\InvalidArgumentException;
use App\Exceptions\NotFoundException;

abstract class BaseService
{
    abstract protected function getModelClass(): string;

    public function find(int $id): ?array
    {
        $modelClass = $this->getModelClass();
        return $modelClass::find($id);
    }

    public function findOrFail(int $id): array
    {
        $result = $this->find($id);
        if (!$result) {
            throw new NotFoundException($this->buildNotFoundMessage($id));
        }
        return $result;
    }

    public function all(): array
    {
        $modelClass = $this->getModelClass();
        return $modelClass::all();
    }

    public function create(array $data): int
    {
        $modelClass = $this->getModelClass();
        return $modelClass::create($data);
    }

    public function update(int $id, array $data): bool
    {
        $modelClass = $this->getModelClass();
        return $modelClass::update($id, $data);
    }

    public function delete(int $id): bool
    {
        $modelClass = $this->getModelClass();
        return $modelClass::delete($id);
    }

    protected function getResourceName(): string
    {
        $className = (new \ReflectionClass($this))->getShortName();
        return str_replace('Service', '', $className);
    }

    protected function buildNotFoundMessage(int $id): string
    {
        $resourceName = $this->getResourceName();
        return "{$resourceName} with ID {$id} not found";
    }

    protected function propagateOrWrapException(\Exception $e, string $context): never
    {
        if ($e instanceof InvalidArgumentException || $e instanceof NotFoundException) {
            throw $e;
        }

        $resourceName = $this->getResourceName();
        throw new InvalidArgumentException("Failed to {$context} {$resourceName}: {$e->getMessage()}", 0, $e);
    }
}
