<?php

declare(strict_types=1);

namespace App\Controllers;

use App\Core\Response;
use App\Core\LogManager;
use App\Exceptions\InvalidArgumentException;
use App\Exceptions\NotFoundException;
use App\Exceptions\AuthorizationException;

abstract class ResourceController extends BaseController
{
    abstract protected function getService();
    abstract protected function getResourceName(): string;

    // Path and naming methods
    protected function getViewPath(): string
    {
        return $this->getResourceName();
    }

    protected function getIndexPath(): string
    {
        return '/' . $this->getResourceName();
    }

    protected function getSingularName(): string
    {
        $name = $this->getResourceName();
        return substr($name, -1) === 's' ? substr($name, 0, -1) : $name;
    }

    // CRUD operations
    public function index(): Response
    {
        try {
            $userId = $this->getUserId();
            $data = $this->getIndexData($userId);

            return $this->view($this->getViewPath() . '/index', $data);
        } catch (NotFoundException $e) {
            return $this->handleNotFoundException($e);
        } catch (AuthorizationException $e) {
            return $this->handleAuthorizationException($e);
        } catch (\Exception $e) {
            return $this->handleGenericException($e, "Error loading {$this->getResourceName()} index");
        }
    }

    protected function handleNotFoundException(NotFoundException $e): Response
    {
        LogManager::getLogger()?->notice("Resource not found", [
            'resource' => $this->getResourceName(),
            'error' => $e->getMessage()
        ]);
        return $this->redirect('/dashboard', $e->getMessage(), 'error');
    }

    protected function handleAuthorizationException(AuthorizationException $e): Response
    {
        LogManager::getLogger()?->warning("Authorization error", [
            'resource' => $this->getResourceName(),
            'error' => $e->getMessage()
        ]);
        return $this->redirect('/dashboard', $e->getMessage(), 'error');
    }

    protected function handleGenericException(\Exception $e, string $context): Response
    {
        LogManager::logException($e, $context);
        $errorMessage = filter_var($_ENV['APP_DEBUG'] ?? false, FILTER_VALIDATE_BOOLEAN)
            ? "Error: " . $e->getMessage()
            : "Failed to load {$this->getResourceName()}";

        return $this->redirect('/dashboard', $errorMessage, 'error');
    }

    protected function getIndexData(int $userId): array
    {
        return [
            'csrf_token' => $this->getCsrfToken()
        ];
    }

    public function create(): Response
    {
        try {
            $userId = $this->getUserId();
            $data = $this->getCreateData($userId);
            $data['csrf_token'] = $this->getCsrfToken();

            return $this->view($this->getViewPath() . '/form', $data);
        } catch (\Exception $e) {
            return $this->handleGenericException($e, "Error creating {$this->getResourceName()}");
        }
    }

    protected function getCreateData(int $userId): array
    {
        return [
            'csrf_token' => $this->getCsrfToken()
        ];
    }

    public function store(): Response
    {
        try {
            $this->validateCsrf($this->getPostValue('csrf_token'));
            $userId = $this->getUserId();
            $data = $this->validateAndPrepareData($_POST, $userId);

            $id = $this->storeResource($data, $userId);
            return $this->handleSuccessfulStore($id);
        } catch (InvalidArgumentException $e) {
            return $this->handleValidationException($e);
        } catch (\Exception $e) {
            return $this->handleStoreException($e);
        }
    }

    protected function handleSuccessfulStore(int $id): Response
    {
        $redirectPath = "{$this->getIndexPath()}/{$id}";
        $message = "{$this->getSingularName()} created successfully";

        return $this->redirect($redirectPath, $message);
    }

    protected function handleValidationException(InvalidArgumentException $e): Response
    {
        return $this->redirectWithErrors($this->getIndexPath() . '/new', $e->getMessage(), $_POST);
    }

    protected function handleStoreException(\Exception $e): Response
    {
        LogManager::logException($e, "Error storing {$this->getResourceName()}");
        return $this->redirectWithErrors($this->getIndexPath() . '/new', 'An unexpected error occurred', $_POST);
    }

    protected function validateAndPrepareData(array $data, int $userId): array
    {
        return $data;
    }

    protected function storeResource(array $data, int $userId): int
    {
        return 0;
    }

    public function show(int $id): Response
    {
        try {
            error_log("[Debug] ResourceController::show - Showing resource with ID: {$id}");
            $userId = $this->getUserId();
            error_log("[Debug] ResourceController::show - User ID: {$userId}");

            // Ensure ID is an integer
            $id = (int)$id;

            $resource = $this->findResource($id, $userId);
            error_log("[Debug] ResourceController::show - Found resource: " . json_encode($resource));

            $data = $this->getShowData($resource, $userId);
            error_log("[Debug] ResourceController::show - Prepared view data");

            // Data is ready for the view
            // Make sure we're passing the data correctly to the view
            if (!isset($data['expense']) && isset($resource['id'])) {
                error_log("[Warning] ResourceController::show - View data doesn't contain expense, adding resource directly");
                $data['expense'] = $resource;
            }

            // Use the premium show file with enhanced design
            return $this->view($this->getViewPath() . '/show', $data);
        } catch (NotFoundException | AuthorizationException $e) {
            // Handle resource exception
            return $this->handleResourceException($e);
        } catch (\Exception $e) {
            // Handle generic exception
            return $this->handleGenericException($e, "Error showing {$this->getResourceName()}");
        }
    }

    protected function handleResourceException(\Exception $e): Response
    {
        error_log("[Debug] ResourceController::handleResourceException - Exception: " . $e->getMessage());

        // Provide a more user-friendly message for NotFoundException
        if ($e instanceof NotFoundException) {
            $resourceName = ucfirst($this->getResourceName());
            $message = "{$resourceName} Not Found: The {$this->getResourceName()} you're looking for doesn't exist or you don't have permission to access it.";
        } else {
            $message = $e->getMessage();
        }

        return $this->redirect($this->getIndexPath(), $message, 'error');
    }

    protected function findResource(int $id, int $userId): array
    {
        return [];
    }

    protected function getShowData(array $resource, int $userId): array
    {
        return [
            'resource' => $resource,
            'csrf_token' => $this->getCsrfToken()
        ];
    }

    public function edit(int $id): Response
    {
        try {
            $userId = $this->getUserId();
            $resource = $this->findResource($id, $userId);
            $data = $this->getEditData($resource, $userId);
            $data['csrf_token'] = $this->getCsrfToken();

            return $this->view($this->getViewPath() . '/form', $data);
        } catch (NotFoundException | AuthorizationException $e) {
            return $this->handleResourceException($e);
        } catch (\Exception $e) {
            return $this->handleGenericException($e, "Error editing {$this->getResourceName()}");
        }
    }

    protected function getEditData(array $resource, int $userId): array
    {
        return [
            'resource' => $resource,
            'csrf_token' => $this->getCsrfToken()
        ];
    }

    public function update(int $id): Response
    {
        try {
            $this->validateCsrf($this->getPostValue('csrf_token'));
            $userId = $this->getUserId();
            $this->findResource($id, $userId);
            $data = $this->validateAndPrepareData($_POST, $userId);

            $this->updateResource($id, $data, $userId);
            return $this->handleSuccessfulUpdate();
        } catch (InvalidArgumentException | NotFoundException | AuthorizationException $e) {
            return $this->handleUpdateException($e, $id);
        } catch (\Exception $e) {
            return $this->handleGenericUpdateException($e, $id);
        }
    }

    protected function handleSuccessfulUpdate(): Response
    {
        $message = "{$this->getSingularName()} updated successfully";
        return $this->redirect($this->getIndexPath(), $message);
    }

    protected function handleUpdateException(\Exception $e, int $id): Response
    {
        $errorPath = "{$this->getIndexPath()}/{$id}/edit";
        return $this->redirectWithErrors($errorPath, $e->getMessage(), $_POST);
    }

    protected function handleGenericUpdateException(\Exception $e, int $id): Response
    {
        LogManager::logException($e, "Error updating {$this->getResourceName()}");
        $errorPath = "{$this->getIndexPath()}/{$id}/edit";
        return $this->redirectWithErrors($errorPath, 'An unexpected error occurred', $_POST);
    }

    protected function updateResource(int $id, array $data, int $userId): bool
    {
        return false;
    }

    public function delete(int $id): Response
    {
        try {
            $this->validateCsrf($this->getPostValue('csrf_token'));
            $userId = $this->getUserId();
            $this->findResource($id, $userId);

            $this->deleteResource($id, $userId);
            return $this->handleSuccessfulDelete();
        } catch (InvalidArgumentException | NotFoundException | AuthorizationException $e) {
            return $this->handleResourceException($e);
        } catch (\Exception $e) {
            return $this->handleGenericException($e, "Error deleting {$this->getResourceName()}");
        }
    }

    protected function handleSuccessfulDelete(): Response
    {
        $message = "{$this->getSingularName()} deleted successfully";
        return $this->redirect($this->getIndexPath(), $message);
    }

    protected function deleteResource(int $id, int $userId): bool
    {
        return false;
    }
}
