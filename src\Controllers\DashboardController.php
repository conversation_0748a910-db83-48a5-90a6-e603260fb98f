<?php

declare(strict_types=1);

namespace App\Controllers;

use App\Core\Response;
use App\Core\LogManager;
use App\Services\ReportingService;

class DashboardController extends BaseController
{
    public function __construct(
        private readonly ReportingService $reportingService
    ) {
        // Empty constructor - no authentication check here
    }

    public function index(): Response
    {
        error_log('DashboardController::index() called');

        // Force authentication check and redirect if not authenticated
        if (!isset($_SESSION['user_id'])) {
            error_log('No user_id in session, redirecting to login');
            // Use direct Response object to ensure immediate redirect
            $response = Response::redirect('/login');
            $response->send();
            exit;
        }

        error_log('User is authenticated (user_id in session), continuing to dashboard');

        try {
            $userId = (int)$_SESSION['user_id'];
            error_log('User ID: ' . $userId);
            $period = $this->getQueryValue('period', 'month');
            error_log('Period: ' . $period);

            return $this->renderDashboard($userId, $period);
        } catch (\Exception $e) {
            error_log('Exception in DashboardController::index(): ' . $e->getMessage());
            error_log('Exception trace: ' . $e->getTraceAsString());
            return $this->handleDashboardError($e);
        }
    }

    private function renderDashboard(int $userId, string $period): Response
    {
        $dashboardData = $this->reportingService->getDashboardData($userId);
        $dashboardData['period'] = $period;

        return $this->view('dashboard/index', $dashboardData);
    }

    private function handleDashboardError(\Exception $e): Response
    {
        LogManager::logException($e, "Error loading dashboard");
        return $this->redirect('/', "Failed to load dashboard", 'error');
    }
}
