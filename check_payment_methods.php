<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Core\Database;
use App\Models\PaymentMethod;

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "Checking payment_methods table structure...\n\n";

try {
    $db = Database::getInstance();

    // Check if the payment_methods table exists
    $stmt = $db->query("SHOW TABLES LIKE 'payment_methods'");
    $tableExists = $stmt->fetchColumn();

    if (!$tableExists) {
        echo "The payment_methods table does not exist!\n";
        exit;
    }

    echo "The payment_methods table exists.\n\n";

    // Get the table structure
    $stmt = $db->query("DESCRIBE payment_methods");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo "Table structure:\n";
    foreach ($columns as $column) {
        echo "- {$column['Field']} ({$column['Type']})\n";
    }

    echo "\n";

    // Check if the 'method' column exists
    $methodColumnExists = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'method') {
            $methodColumnExists = true;
            break;
        }
    }

    if (!$methodColumnExists) {
        echo "WARNING: The 'method' column does not exist in the payment_methods table!\n";
    } else {
        echo "The 'method' column exists in the payment_methods table.\n";
    }

    // Check if the 'name' column exists
    $nameColumnExists = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'name') {
            $nameColumnExists = true;
            break;
        }
    }

    if ($nameColumnExists) {
        echo "WARNING: The 'name' column exists in the payment_methods table!\n";
        echo "This might be causing confusion in SQL queries.\n";
    } else {
        echo "The 'name' column does not exist in the payment_methods table.\n";
    }

    // Get some sample data
    $stmt = $db->query("SELECT * FROM payment_methods LIMIT 5");
    $data = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($data)) {
        echo "\nNo data found in the payment_methods table.\n";
    } else {
        echo "\nSample data from payment_methods table:\n";
        foreach ($data as $row) {
            echo json_encode($row, JSON_PRETTY_PRINT) . "\n";
        }
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
