<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\Receipt;

// Test receipt content
$content = <<<EOT
Cinema: MovieMax
Date: 2023-11-01
Items:
  - Movie Ticket: $12.00
  - Popcorn: $5.50
  - Soda: $3.00
Total: $20.50
Payment Method: Cash
EOT;

// Parse the receipt content
$result = Receipt::parseContent($content);

// Output the result
echo "Parsed Receipt Data:\n";
echo "-------------------\n";
echo "Merchant: " . $result['merchant'] . "\n";
echo "Date: " . $result['date'] . "\n";
echo "Amount: $" . number_format($result['amount'], 2) . "\n";
echo "Payment Method: " . $result['payment_method'] . "\n";
echo "Category: " . $result['category'] . "\n";
echo "Items:\n";

if (!empty($result['items'])) {
    foreach ($result['items'] as $item) {
        echo "  - " . $item['name'] . ": $" . number_format($item['price'], 2) . "\n";
    }
} else {
    echo "  No items found\n";
}

echo "\nDescription: " . $result['description'] . "\n";
