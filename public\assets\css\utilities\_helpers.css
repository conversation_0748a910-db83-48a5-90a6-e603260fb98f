/* ===== HELPER CLASSES ===== */
/* Spacing Utilities */
.mt-xs { margin-top: var(--spacing-xs); }
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mb-xs { margin-bottom: var(--spacing-xs); }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }

/* Text Utilities */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-muted { color: var(--text-muted); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-danger { color: var(--danger-color); }
.text-primary { color: var(--primary-color); }

/* Display Utilities */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.justify-between { justify-content: space-between; }
.justify-center { justify-content: center; }
.gap-sm { gap: var(--spacing-sm); }
.gap-md { gap: var(--spacing-md); }

/* Visibility Utilities */
.hidden { display: none; }
.visible { display: block; }
@media (max-width: 768px) {
  .hidden-mobile { display: none; }
}
@media (min-width: 769px) {
  .hidden-desktop { display: none; }
}

/* Width Utilities */
.w-full { width: 100%; }
.w-auto { width: auto; }
.max-w-800 { max-width: 800px; }

/* Cursor Utilities */
.cursor-pointer { cursor: pointer; }