<?php
// Partial: Page Header for Expense Details

// Helper function to safely escape values
if (!function_exists('e')) {
    function e($value)
    {
        if (is_null($value)) {
            return '';
        }
        if (is_array($value)) {
            return '';
        }
        if (is_object($value) && !method_exists($value, '__toString')) {
            return '';
        }
        return htmlspecialchars((string)$value, ENT_QUOTES, 'UTF-8');
    }
}

// Make sure we have the expense data
if (!isset($expense) && isset($viewData['expense'])) {
    error_log("[Debug] _header.php - Getting expense from viewData");
    $expense = $viewData['expense'];
}

// Debug expense data
if (isset($expense)) {
    error_log("[Debug] _header.php - Expense ID: " . ($expense['id'] ?? 'not set'));
    error_log("[Debug] _header.php - Expense description: " . ($expense['description'] ?? 'not set'));
} else {
    error_log("[Warning] _header.php - No expense data available");
}
?>
<header class="page-header">
  <div class="header-content">
    <nav aria-label="Breadcrumb" class="breadcrumbs">
      <ol class="breadcrumbs__list">
        <li class="breadcrumbs__item">
          <a href="/" class="breadcrumbs__link">Home</a>
        </li>
        <li class="breadcrumbs__item">
          <a href="/expenses" class="breadcrumbs__link">Expenses</a>
        </li>
        <li class="breadcrumbs__item breadcrumbs__item--active" aria-current="page">
          <?= e($expense['description'] ?? 'Expense') ?>
        </li>
      </ol>
    </nav>
    <h1 class="page-header__title"><?= e($expense['description'] ?? 'Expense') ?></h1>
  </div>
  <div class="header-messages" aria-live="polite">
    <?php include __DIR__ . '/../../shared/messages.php'; ?>
  </div>
</header>
