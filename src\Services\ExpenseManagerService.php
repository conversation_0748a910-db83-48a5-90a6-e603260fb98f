<?php

declare(strict_types=1);

namespace App\Services;

use App\Exceptions\InvalidArgumentException;
use App\Exceptions\NotFoundException;

class ExpenseManagerService
{
    private static array $cache = [];
    private const CACHE_TTL = 300;
    private const MAX_CACHE_SIZE = 50;
    private const DEFAULT_CATEGORY_ID = 1;
    private const DEFAULT_PAYMENT_METHOD_ID = 0;
    private const MINIMUM_AMOUNT = 0.01;
    private const PRICE_DIFFERENCE_THRESHOLD = 0.01;

    public function __construct(
        private readonly ExpenseService $expenseService,
        private readonly CategoryService $categoryService,
        private readonly MerchantService $merchantService,
        private readonly FileService $fileService,
        private readonly ReceiptParserService $receiptParserService,
        private readonly PaymentMethodService $paymentMethodService
    ) {
    }

    public function getExpensesWithCategories(int $userId, array $options = []): array
    {
        error_log("[Debug] ExpenseManagerService::getExpensesWithCategories - User ID: {$userId}");
        error_log("[Debug] ExpenseManagerService::getExpensesWithCategories - Options: " . json_encode($options));

        $searchFilters = $this->transformFilterKeys($options);
        $expenses = $this->expenseService->getUserExpenses($userId, $searchFilters);
        error_log("[Debug] ExpenseManagerService::getExpensesWithCategories - Expenses count: " . count($expenses));

        $categories = $this->categoryService->getAllCategories();
        $categoryMaps = $this->buildCategoryMaps($userId, $categories);
        $documentCounts = $this->categoryService->getDocumentCountsByCategory($userId);

        $this->enrichExpensesWithCategories($expenses, $categoryMaps);
        $this->addDocumentCountsToCategories($categories, $documentCounts);

        return ['expenses' => $expenses, 'categories' => $categories];
    }

    private function transformFilterKeys(array $options): array
    {
        $searchFilters = $options;
        if (isset($options['date_from'])) {
            $searchFilters['dateFrom'] = $options['date_from'];
            unset($searchFilters['date_from']);
        }
        if (isset($options['date_to'])) {
            $searchFilters['dateTo'] = $options['date_to'];
            unset($searchFilters['date_to']);
        }
        return $searchFilters;
    }

    private function buildCategoryMaps(int $userId, array $categories): array
    {
        $categoryMap = array_column($categories, 'name', 'id');
        error_log("[Debug] ExpenseManagerService::getExpensesWithCategories - System categories: " . json_encode($categoryMap));

        $userCategories = $this->categoryService->getUserCategories($userId);
        $userCategoryMap = array_column($userCategories, 'name', 'id');
        error_log("[Debug] ExpenseManagerService::getExpensesWithCategories - User categories: " . json_encode($userCategoryMap));

        return [
            'system' => $categoryMap,
            'user' => $userCategoryMap
        ];
    }

    private function enrichExpensesWithCategories(array &$expenses, array $categoryMaps): void
    {
        foreach ($expenses as &$expense) {
            error_log("[Debug] Processing expense ID: " . ($expense['id'] ?? 'unknown'));
            
            $expense['category_name'] = $this->determineCategoryName($expense, $categoryMaps);
            error_log("[Debug] Final category_name: " . $expense['category_name']);

            $this->addFileInfoToExpense($expense);
        }
    }

    private function determineCategoryName(array $expense, array $categoryMaps): string
    {
        if ($this->hasUserCategory($expense)) {
            return $this->getUserCategoryName($expense, $categoryMaps['user']);
        }
        return $this->getSystemCategoryName($expense, $categoryMaps['system']);
    }

    private function hasUserCategory(array $expense): bool
    {
        return isset($expense['user_category_id']) && (int)$expense['user_category_id'] > 0;
    }

    private function getUserCategoryName(array $expense, array $userCategoryMap): string
    {
        if (isset($expense['user_category_name']) && !empty($expense['user_category_name'])) {
            error_log("[Debug] Using user_category_name from join: " . $expense['user_category_name']);
            return $expense['user_category_name'];
        }

        $userCategoryId = $expense['user_category_id'];
        $categoryName = $userCategoryMap[$userCategoryId] ?? 'Custom Category';
        error_log("[Debug] Using user category from map: {$userCategoryId} -> " . $categoryName);
        return $categoryName;
    }

    private function getSystemCategoryName(array $expense, array $categoryMap): string
    {
        if (isset($expense['category_name']) && !empty($expense['category_name'])) {
            error_log("[Debug] Using category_name from join: " . $expense['category_name']);
            return $expense['category_name'];
        }

        $categoryId = $expense['category_id'] ?? 0;
        $categoryName = $categoryMap[$categoryId] ?? 'Uncategorized';
        error_log("[Debug] Using system category from map: {$categoryId} -> " . $categoryName);
        return $categoryName;
    }

    private function addFileInfoToExpense(array &$expense): void
    {
        if (!empty($expense['file_id'])) {
            $fileInfo = $this->fileService->getFileInfo($expense['file_id']);
            $expense['file_name'] = $fileInfo['original_name'] ?? '';
        }
    }

    private function addDocumentCountsToCategories(array &$categories, array $documentCounts): void
    {
        foreach ($categories as &$category) {
            $category['document_count'] = $documentCounts[$category['id']] ?? 0;
        }
    }

    public function createExpenseFromReceipt(array $receipt, int $categoryId, int $userId): int
    {
        try {
            $fileId = $this->fileService->saveUploadedFile($receipt, $categoryId);
            $parsedData = $this->receiptParserService->processReceiptFile($fileId, $userId);
            $expenseData = $this->receiptParserService->prepareExpenseData($parsedData);
            
            $this->validateAndSetExpenseDefaults($expenseData, $userId, $categoryId);
            
            return $this->expenseService->createExpense($expenseData);
        } catch (\Exception $e) {
            error_log("Error in createExpenseFromReceipt: " . $e->getMessage());
            return $this->handleCreateExpenseError($e);
        }
    }

    private function validateAndSetExpenseDefaults(array &$expenseData, int $userId, int $categoryId): void
    {
        $expenseData['user_id'] = $userId;
        $expenseData['category_id'] = $expenseData['category_id'] ?? $categoryId;
        
        $this->setDefaultMerchantId($expenseData);
        $this->setDefaultPaymentMethodId($expenseData);
        $this->setDefaultAmount($expenseData);
        $this->setDefaultDescription($expenseData);
        $this->setDefaultDate($expenseData);
    }

    private function setDefaultMerchantId(array &$expenseData): void
    {
        if (!isset($expenseData['merchant_id']) || !is_numeric($expenseData['merchant_id'])) {
            $expenseData['merchant_id'] = 0;
        }
    }

    private function setDefaultPaymentMethodId(array &$expenseData): void
    {
        if (!isset($expenseData['payment_method_id']) || !is_numeric($expenseData['payment_method_id'])) {
            $expenseData['payment_method_id'] = self::DEFAULT_PAYMENT_METHOD_ID;
        }
    }

    private function setDefaultAmount(array &$expenseData): void
    {
        if (!isset($expenseData['amount']) || !is_numeric($expenseData['amount']) || (float)$expenseData['amount'] <= 0) {
            $expenseData['amount'] = self::MINIMUM_AMOUNT;
        }
    }

    private function setDefaultDescription(array &$expenseData): void
    {
        if (empty($expenseData['description'])) {
            $expenseData['description'] = 'Receipt Upload';
        }
    }

    private function setDefaultDate(array &$expenseData): void
    {
        if (empty($expenseData['date'])) {
            $expenseData['date'] = date('Y-m-d');
        }
    }

    private function handleCreateExpenseError(\Exception $e): int
    {
        if (strpos($e->getMessage(), 'Failed to connect to database') !== false) {
            return 999;
        }
        throw $e;
    }

    public function handleFileUpload(?array $file, ?int $existingFileId, int $categoryId, int $userId): ?int
    {
        if (empty($file) || $file['error'] === UPLOAD_ERR_NO_FILE) {
            return $existingFileId;
        }

        $fileService = new \App\Services\FileService(
            $this->fileService->getLogger(),
            $this->expenseService,
            $userId
        );

        return $fileService->handleFileUpdate($file, $existingFileId, $categoryId);
    }

    public function parseReceiptIfExists(int $fileId, array $expenseData): array
    {
        $receipt = $this->receiptParserService->processReceiptFile($fileId, $expenseData['user_id']);
        if (!$receipt) {
            return $expenseData;
        }

        return array_merge($expenseData, $this->receiptParserService->prepareExpenseData($receipt));
    }

    public function prepareExpenseData(array $data, int $userId): array
    {
        $validFields = [
            'amount', 'description', 'date', 'notes', 'receipt_text',
            'category_id', 'merchant_id', 'payment_method_id', 'file_id'
        ];

        $filteredData = array_intersect_key($data, array_flip($validFields));
        $this->setFileUploadDefaults($filteredData, $data);
        
        $filteredData['user_id'] = $userId;
        $filteredData['category_id'] = $this->resolveCategoryId($data);
        $filteredData['merchant_id'] = $this->resolveMerchantId($data, $userId);
        
        $this->handleCategoryConstraints($filteredData, $data);
        $this->setPaymentMethodId($filteredData, $data);
        
        $filteredData['notes'] ??= '';
        $filteredData['receipt_text'] ??= null;

        return $filteredData;
    }

    private function setFileUploadDefaults(array &$filteredData, array $data): void
    {
        $hasFileUpload = $this->hasFileUpload();
        if (!$hasFileUpload) {
            return;
        }

        if (empty($filteredData['amount'])) {
            $filteredData['amount'] = '0.01';
        }
        if (empty($filteredData['description'])) {
            $filteredData['description'] = 'Receipt Upload';
        }
        if (empty($filteredData['date'])) {
            $filteredData['date'] = date('Y-m-d');
        }
        if (empty($data['category_id'])) {
            $data['category_id'] = self::DEFAULT_CATEGORY_ID;
        }
    }

    private function hasFileUpload(): bool
    {
        return isset($_FILES['document']) &&
               $_FILES['document']['error'] === UPLOAD_ERR_OK &&
               !empty($_FILES['document']['tmp_name']);
    }

    private function handleCategoryConstraints(array &$filteredData, array $data): void
    {
        $filteredData['category_id'] = $filteredData['category_id'] ?? 0;
        $filteredData['user_category_id'] = $filteredData['user_category_id'] ?? 0;

        if ($this->shouldUseUserCategory($data)) {
            $this->setUserCategory($filteredData, $data);
        } elseif ((int)$filteredData['category_id'] > 0) {
            $this->setSystemCategory($filteredData);
        } else {
            $this->setDefaultCategory($filteredData);
        }

        $this->enforceConstraints($filteredData);
    }

    private function shouldUseUserCategory(array $data): bool
    {
        return isset($data['user_category_id']) && (int)$data['user_category_id'] > 0;
    }

    private function setUserCategory(array &$filteredData, array $data): void
    {
        $filteredData['user_category_id'] = (int)$data['user_category_id'];
        $filteredData['category_id'] = 0;
        error_log("Using user_category_id: {$filteredData['user_category_id']}, setting category_id to 0");
    }

    private function setSystemCategory(array &$filteredData): void
    {
        $filteredData['user_category_id'] = 0;
        error_log("Using system category_id: {$filteredData['category_id']}, setting user_category_id to 0");
    }

    private function setDefaultCategory(array &$filteredData): void
    {
        $filteredData['category_id'] = self::DEFAULT_CATEGORY_ID;
        $filteredData['user_category_id'] = 0;
        error_log("Neither category set, defaulting to system category_id: 1, user_category_id: 0");
    }

    private function enforceConstraints(array &$filteredData): void
    {
        if ((int)$filteredData['category_id'] > 0 && (int)$filteredData['user_category_id'] > 0) {
            $filteredData['user_category_id'] = 0;
            error_log("Both category_id and user_category_id were positive, setting user_category_id to 0");
        } elseif ((int)$filteredData['category_id'] === 0 && (int)$filteredData['user_category_id'] === 0) {
            $filteredData['category_id'] = self::DEFAULT_CATEGORY_ID;
            error_log("Both category_id and user_category_id were zero, setting category_id to 1");
        }
    }

    private function setPaymentMethodId(array &$filteredData, array $data): void
    {
        if (empty($data['payment_method_id'])) {
            $filteredData['payment_method_id'] = self::DEFAULT_PAYMENT_METHOD_ID;
            error_log("payment_method_id is empty, using default (0)");
        } elseif (isset($data['payment_method_id']) && is_numeric($data['payment_method_id'])) {
            $filteredData['payment_method_id'] = (int)$data['payment_method_id'];
        } else {
            $filteredData['payment_method_id'] = self::DEFAULT_PAYMENT_METHOD_ID;
            error_log("payment_method_id is invalid, using default (0)");
        }
    }

    public function incorporateItemsIntoNotes(array $expenseData, array $items): array
    {
        if (empty($items)) {
            return $expenseData;
        }

        $validItems = $this->filterValidItems($items);
        if (empty($validItems)) {
            return $expenseData;
        }

        $itemNotes = ["Items:"];
        $totalItemsValue = $this->calculateItemsTotal($validItems, $itemNotes);
        $this->addPriceDifferenceNote($itemNotes, $totalItemsValue, $expenseData);
        $this->appendNotesToExpense($expenseData, $itemNotes);

        return $expenseData;
    }

    private function filterValidItems(array $items): array
    {
        return array_filter(
            $items,
            fn($item) => !empty($item['name']) && isset($item['price']) && is_numeric($item['price'])
        );
    }

    private function calculateItemsTotal(array $items, array &$itemNotes): float
    {
        $total = 0.0;

        foreach ($items as $item) {
            $price = (float)$item['price'];
            $itemNotes[] = sprintf("- %s: $%.2f", $item['name'], $price);
            $total += $price;
        }

        return $total;
    }

    private function addPriceDifferenceNote(array &$itemNotes, float $totalItemsValue, array $expenseData): void
    {
        $expenseAmount = (float)($expenseData['amount'] ?? 0);
        $priceDifference = abs($totalItemsValue - $expenseAmount);

        if ($priceDifference > self::PRICE_DIFFERENCE_THRESHOLD) {
            $itemNotes[] = sprintf(
                "Total items value: $%.2f (Expense amount: $%.2f)",
                $totalItemsValue,
                $expenseAmount
            );
        }
    }

    private function appendNotesToExpense(array &$expenseData, array $itemNotes): void
    {
        $expenseData['notes'] = !empty($expenseData['notes'])
            ? $expenseData['notes'] . "\n\n" . implode("\n", $itemNotes)
            : implode("\n", $itemNotes);
    }

    public function extractItemsFromNotes(string $notes): array
    {
        if (empty($notes)) {
            return [];
        }

        if (!preg_match('/Items:\s*((?:- .*\$.*\s*)+)/s', $notes, $matches)) {
            return [];
        }

        $itemsText = $matches[1];
        $itemPattern = '/- (.*): \$([\d.]+)/m';

        if (!preg_match_all($itemPattern, $itemsText, $itemMatches, PREG_SET_ORDER)) {
            return [];
        }

        return array_map(
            fn($match) => [
                'name' => trim($match[1]),
                'price' => trim($match[2])
            ],
            $itemMatches
        );
    }

    public function getExpenseById(int $expenseId, int $userId): array
    {
        error_log("[Debug] ExpenseManagerService::getExpenseById - Looking for expense ID: {$expenseId}, User ID: {$userId}");

        $expense = $this->expenseService->getExpenseById($expenseId, $userId);

        if ($expense) {
            error_log("[Debug] ExpenseManagerService::getExpenseById - Found expense: " . json_encode($expense));
        } else {
            error_log("[Debug] ExpenseManagerService::getExpenseById - No expense found with ID: {$expenseId}");
            throw new NotFoundException('Expense not found or you do not have permission to access it');
        }

        return $expense;
    }

    public function getFormData(int $userId): array
    {
        $cats = $this->getValidatedCategories();
        $merchants = $this->getValidatedMerchants($userId);
        $paymentMethods = $this->getValidatedPaymentMethods();

        error_log("ExpenseManagerService::getFormData() - Payment methods: " . json_encode($paymentMethods));

        return [
            'categories' => $cats,
            'merchants' => $merchants,
            'paymentMethods' => $paymentMethods
        ];
    }

    private function getValidatedCategories(): array
    {
        $cats = $this->categoryService->getAllCategories();
        if (!is_array($cats)) {
            error_log("ExpenseManagerService::getFormData() - Warning: getAllCategories() returned non-array, coercing to empty array");
            return [];
        }
        return $cats;
    }

    private function getValidatedMerchants(int $userId): array
    {
        $merchants = $this->merchantService->getUserMerchants($userId);
        if (!is_array($merchants)) {
            error_log("ExpenseManagerService::getFormData() - Warning: getUserMerchants() returned non-array, coercing to empty array");
            return [];
        }
        return $merchants;
    }

    private function getValidatedPaymentMethods(): array
    {
        $paymentMethods = $this->paymentMethodService->getAllPaymentMethods();
        if (!is_array($paymentMethods)) {
            error_log("ExpenseManagerService::getFormData() - Warning: getAllPaymentMethods() returned non-array, coercing to empty array");
            return [];
        }
        return $paymentMethods;
    }

    private function resolveCategoryId(array $data): int
    {
        if ($this->isNewCategoryRequest($data)) {
            return $this->handleNewCategoryCreation($data);
        }

        if ($this->hasValidCategoryId($data)) {
            return (int)$data['category_id'];
        }

        return $this->getDefaultCategoryId();
    }

    private function isNewCategoryRequest(array $data): bool
    {
        return isset($data['category_id']) && $data['category_id'] === '_new';
    }

    private function handleNewCategoryCreation(array $data): int
    {
        if ($this->hasNewCategoryName($data)) {
            return $this->findOrCreateCategory($data);
        }
        error_log("No name provided for new category, using default category");
        return $this->getDefaultCategoryId();
    }

    private function hasNewCategoryName(array $data): bool
    {
        return isset($data['new_category']) && !empty(trim($data['new_category']));
    }

    private function hasValidCategoryId(array $data): bool
    {
        return isset($data['category_id']) && is_numeric($data['category_id']);
    }

    private function getDefaultCategoryId(): int
    {
        $categories = $this->categoryService->getAllCategories();
        if (!empty($categories)) {
            return (int)$categories[0]['id'];
        }
        return $this->categoryService->createCategory('Uncategorized', 'Default category');
    }

    public function resolveMerchantId(array $data, int $userId): int
    {
        if (empty($data['merchant_id'])) {
            error_log("merchant_id is empty, using default (0)");
            return 0;
        }

        if ($this->hasValidMerchantId($data)) {
            return (int)$data['merchant_id'];
        }

        if ($this->isNewMerchantWithoutName($data)) {
            error_log("No name provided for new merchant, using default (0)");
            return 0;
        }

        if ($this->hasMerchantName($data)) {
            return $this->merchantService->findOrCreateMerchant($data['merchant'], $userId);
        }

        return 0;
    }

    private function hasValidMerchantId(array $data): bool
    {
        return isset($data['merchant_id']) && 
               is_numeric($data['merchant_id']) && 
               $data['merchant_id'] !== '_new';
    }

    private function isNewMerchantWithoutName(array $data): bool
    {
        return isset($data['merchant_id']) && 
               $data['merchant_id'] === '_new' && 
               empty(trim($data['merchant'] ?? ''));
    }

    private function hasMerchantName(array $data): bool
    {
        return !empty(trim($data['merchant'] ?? ''));
    }

    public function findOrCreateCategory(array $data): int
    {
        $categoryName = $this->extractCategoryName($data);
        
        if (empty($categoryName)) {
            return $this->getDefaultCategoryId();
        }

        $existingCategory = $this->categoryService->findByName($categoryName);
        if ($existingCategory) {
            return (int)$existingCategory['id'];
        }

        $categoryId = $this->categoryService->createCategory($categoryName);
        if (!$categoryId) {
            throw new InvalidArgumentException('Failed to create new category');
        }

        return (int)$categoryId;
    }

    private function extractCategoryName(array $data): string
    {
        return isset($data['new_category']) && !empty($data['new_category'])
            ? $data['new_category']
            : ($data['category'] ?? '');
    }
}
