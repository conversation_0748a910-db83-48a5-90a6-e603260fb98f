/* ===== TABLE COMPONENTS ===== */
.table-container {
    overflow-x: auto;
    margin-bottom: var(--spacing-lg);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--box-shadow);
  }

  table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    background-color: white;
  }

  table th, table td {
    padding: var(--spacing);
    text-align: left;
  }

  table th {
    background-color: var(--primary-color);
    color: white;
    font-weight: var(--font-weight-medium);
    position: sticky;
    top: 0;
    z-index: 10;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  }

  table th:first-child {
    border-top-left-radius: var(--border-radius);
  }

  table th:last-child {
    border-top-right-radius: var(--border-radius);
  }

  table tr:last-child td:first-child {
    border-bottom-left-radius: var(--border-radius);
  }

  table tr:last-child td:last-child {
    border-bottom-right-radius: var(--border-radius);
  }

  table tr:nth-child(even) {
    background-color: rgba(0, 0, 0, 0.02);
  }

  table tr {
    transition: var(--transition-fast);
  }

  table tr:hover {
    background-color: rgba(58, 123, 213, 0.08);
  }

  /* Interactive table rows */
  table.interactive tr {
    cursor: pointer;
  }

  table.interactive tr:hover {
    transform: translateY(-2px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    position: relative;
    z-index: 5;
  }

  @media (max-width: 768px) {
    table,
    caption {
      display: block;
    }

    caption {
      width: 100%;
      white-space: normal;
      word-wrap: break-word;
      margin-top: 0.5rem;
    }
  }
