document.addEventListener('DOMContentLoaded', initializeAuthPages);

function initializeAuthPages() {
  setupPasswordVisibilityToggle();
  setupPasswordStrengthValidation();
  setupPasswordConfirmationValidation();
  enhanceFormSubmission();
}

function setupPasswordVisibilityToggle() {
  document.querySelectorAll('input[type="password"]').forEach(passwordField => {
    if (passwordField.parentElement.querySelector('.password-toggle')) return;

    addToggleButtonToPasswordField(passwordField);
  });
}

function addToggleButtonToPasswordField(passwordField) {
  const toggleButton = createToggleButton();
  addPasswordToggleStyles();
  wrapPasswordFieldWithToggle(passwordField, toggleButton);
  setupToggleButtonBehavior(toggleButton, passwordField);
}

function createToggleButton() {
  const toggleButton = document.createElement('button');
  toggleButton.type = 'button';
  toggleButton.className = 'password-toggle';
  toggleButton.setAttribute('aria-label', 'Toggle password visibility');
  toggleButton.innerHTML = getVisibleEyeIcon();
  return toggleButton;
}

function getVisibleEyeIcon() {
  return '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18" height="18"><path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/></svg>';
}

function getHiddenEyeIcon() {
  return '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18" height="18"><path d="M12 7c2.76 0 5 2.24 5 5 0 .65-.13 1.26-.36 1.83l2.92 2.92c1.51-1.26 2.7-2.89 3.43-4.75-1.73-4.39-6-7.5-11-7.5-1.4 0-2.74.25-3.98.7l2.16 2.16C10.74 7.13 11.35 7 12 7zM2 4.27l2.28 2.28.46.46C3.08 8.3 1.78 10.02 1 12c1.73 4.39 6 7.5 11 7.5 1.55 0 3.03-.3 4.38-.84l.42.42L19.73 22 21 20.73 3.27 3 2 4.27zM7.53 9.8l1.55 1.55c-.05.21-.08.43-.08.65 0 1.66 1.34 3 3 3 .22 0 .44-.03.65-.08l1.55 1.55c-.67.33-1.41.53-2.2.53-2.76 0-5-2.24-5-5 0-.79.2-1.53.53-2.2zm4.31-.78l3.15 3.15.02-.16c0-1.66-1.34-3-3-3l-.17.01z"/></svg>';
}

function addPasswordToggleStyles() {
  if (document.getElementById('password-toggle-styles')) return;

  const style = document.createElement('style');
  style.id = 'password-toggle-styles';
  style.textContent = `
    .password-toggle {
      position: absolute;
      right: 10px;
      top: 50%;
      transform: translateY(-50%);
      background: none;
      border: none;
      cursor: pointer;
      color: var(--text-muted);
      padding: 5px;
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0.7;
      transition: opacity 0.2s;
    }
    .password-toggle:hover {
      opacity: 1;
    }
    .password-field-wrapper {
      position: relative;
    }
  `;
  document.head.appendChild(style);
}

function wrapPasswordFieldWithToggle(passwordField, toggleButton) {
  const wrapper = document.createElement('div');
  wrapper.className = 'password-field-wrapper';
  passwordField.parentNode.insertBefore(wrapper, passwordField);
  wrapper.appendChild(passwordField);
  wrapper.appendChild(toggleButton);
}

function setupToggleButtonBehavior(toggleButton, passwordField) {
  toggleButton.addEventListener('click', () => {
    const newType = passwordField.type === 'password' ? 'text' : 'password';
    passwordField.type = newType;
    toggleButton.innerHTML = newType === 'text' ? getHiddenEyeIcon() : getVisibleEyeIcon();
  });
}

function setupPasswordStrengthValidation() {
  const passwordField = document.querySelector('input[name="password"]');
  if (!passwordField) return;

  if (!isPasswordPage()) return;

  const strengthIndicator = createStrengthIndicator();
  addStrengthIndicatorStyles();

  const formGroup = passwordField.closest('.form-group');
  formGroup.appendChild(strengthIndicator);

  passwordField.addEventListener('input', () => {
    const strength = calculatePasswordStrength(passwordField.value);
    updateStrengthIndicator(strength, strengthIndicator);
  });
}

function isPasswordPage() {
  const isAuthPage = document.querySelector('.form-container') !== null;
  const isPasswordPage = window.location.pathname.includes('register') ||
                         window.location.pathname.includes('password');
  return isAuthPage && isPasswordPage;
}

function createStrengthIndicator() {
  const strengthIndicator = document.createElement('div');
  strengthIndicator.className = 'password-strength';
  strengthIndicator.innerHTML = `
    <div class="strength-meter">
      <div class="strength-meter-fill"></div>
    </div>
    <div class="strength-text"></div>
  `;
  return strengthIndicator;
}

function addStrengthIndicatorStyles() {
  if (document.getElementById('strength-indicator-styles')) return;

  const style = document.createElement('style');
  style.id = 'strength-indicator-styles';
  style.textContent = `
    .password-strength {
      margin-top: 8px;
    }
    .strength-meter {
      height: 4px;
      background-color: #e0e0e0;
      border-radius: 2px;
      margin-bottom: 8px;
    }
    .strength-meter-fill {
      height: 100%;
      border-radius: 2px;
      transition: width 0.3s ease, background-color 0.3s ease;
      width: 0;
    }
    .strength-text {
      font-size: 12px;
      color: var(--text-muted);
    }
  `;
  document.head.appendChild(style);
}

function calculatePasswordStrength(password) {
  if (!password) return 0;

  let score = 0;

  if (password.length >= 8) score += 20;
  if (password.length >= 12) score += 10;

  if (/[a-z]/.test(password)) score += 10;
  if (/[A-Z]/.test(password)) score += 15;
  if (/[0-9]/.test(password)) score += 15;
  if (/[^a-zA-Z0-9]/.test(password)) score += 20;

  const uniqueChars = new Set(password).size;
  score += Math.min(uniqueChars * 2, 10);

  return Math.min(score, 100);
}

function updateStrengthIndicator(strength, indicator) {
  const fill = indicator.querySelector('.strength-meter-fill');
  const text = indicator.querySelector('.strength-text');

  fill.style.width = `${strength}%`;

  if (strength < 30) {
    setStrengthLevel(fill, text, '#ff4d4d', 'Weak password');
  } else if (strength < 60) {
    setStrengthLevel(fill, text, '#ffa64d', 'Moderate password');
  } else if (strength < 80) {
    setStrengthLevel(fill, text, '#99cc00', 'Strong password');
  } else {
    setStrengthLevel(fill, text, '#00cc66', 'Very strong password');
  }
}

function setStrengthLevel(fill, text, color, message) {
  fill.style.backgroundColor = color;
  text.textContent = message;
  text.style.color = color;
}

function setupPasswordConfirmationValidation() {
  const passwordField = document.querySelector('input[name="password"]');
  const confirmField = document.querySelector('input[name="password_confirm"]');

  if (!passwordField || !confirmField) return;

  setupPasswordMatchValidation(passwordField, confirmField);
}

function setupPasswordMatchValidation(passwordField, confirmField) {
  confirmField.addEventListener('input', () => {
    validatePasswordMatch(passwordField, confirmField);
  });

  passwordField.addEventListener('input', () => {
    if (confirmField.value) {
      validatePasswordMatch(passwordField, confirmField);
    }
  });
}

function validatePasswordMatch(passwordField, confirmField) {
  const formGroup = confirmField.closest('.form-group');
  const matchIndicator = formGroup.querySelector('.password-match') ||
                        createMatchIndicator(formGroup);

  if (confirmField.value === '') {
    matchIndicator.style.display = 'none';
    return;
  }

  matchIndicator.style.display = 'block';

  if (passwordField.value === confirmField.value) {
    matchIndicator.textContent = 'Passwords match';
    matchIndicator.className = 'password-match match';
  } else {
    matchIndicator.textContent = 'Passwords do not match';
    matchIndicator.className = 'password-match no-match';
  }
}

function createMatchIndicator(formGroup) {
  const matchIndicator = document.createElement('div');
  matchIndicator.className = 'password-match';
  matchIndicator.style.display = 'none';
  formGroup.appendChild(matchIndicator);

  addMatchIndicatorStyles();

  return matchIndicator;
}

function addMatchIndicatorStyles() {
  if (document.getElementById('match-indicator-styles')) return;

  const style = document.createElement('style');
  style.id = 'match-indicator-styles';
  style.textContent = `
    .password-match {
      font-size: 12px;
      margin-top: 8px;
      transition: all 0.3s ease;
    }
    .password-match.match {
      color: var(--success-color);
    }
    .password-match.no-match {
      color: var(--danger-color);
    }
  `;
  document.head.appendChild(style);
}

function enhanceFormSubmission() {
  const authForms = document.querySelectorAll('.form-container form');

  authForms.forEach(form => {
    form.addEventListener('submit', e => {
      if (form.classList.contains('submitting')) {
        e.preventDefault();
        return;
      }

      addLoadingState(form);
    });
  });
}

function addLoadingState(form) {
  const submitButton = form.querySelector('button[type="submit"]');
  if (!submitButton) return;

  submitButton.classList.add('loading');

  if (!submitButton.querySelector('.loading-indicator')) {
    addLoadingIndicator(submitButton);
  }

  form.classList.add('submitting');
}

function addLoadingIndicator(button) {
  const loadingIndicator = document.createElement('span');
  loadingIndicator.className = 'loading-indicator';
  button.appendChild(loadingIndicator);

  addLoadingIndicatorStyles();
}

function addLoadingIndicatorStyles() {
  if (document.getElementById('loading-indicator-styles')) return;

  const style = document.createElement('style');
  style.id = 'loading-indicator-styles';
  style.textContent = `
    .loading-indicator {
      display: inline-block;
      width: 12px;
      height: 12px;
      border: 2px solid rgba(255, 255, 255, 0.3);
      border-radius: 50%;
      border-top-color: #fff;
      animation: spin 1s ease-in-out infinite;
      margin-left: 8px;
    }
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
    .button.loading {
      opacity: 0.8;
      pointer-events: none;
    }
  `;
  document.head.appendChild(style);
}
