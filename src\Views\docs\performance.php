<div class="container">
    <div class="page-header">
        <h1>Understanding Performance Metrics</h1>
    </div>

    <div class="content">
        <div class="notice-box">
            <div class="notice-content">
                <div class="notice-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <circle cx="12" cy="12" r="10"></circle>
                        <line x1="12" y1="16" x2="12" y2="12"></line>
                        <line x1="12" y1="8" x2="12.01" y2="8"></line>
                    </svg>
                </div>
                <div class="notice-text">
                    <p><strong>What is this?</strong> At the bottom of each page, you'll see a small box showing performance information. This guide explains what those numbers mean and why they matter to you.</p>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-8">
                <h2>Why Performance Matters</h2>
                <p>
                    Faster pages mean a better experience for you. When pages load quickly:
                </p>
                <ul>
                    <li>You spend less time waiting</li>
                    <li>The application feels more responsive</li>
                    <li>You can get your work done more efficiently</li>
                </ul>

                <h2>What You're Seeing</h2>
                <p>
                    The performance metrics box shows how quickly the page loaded and how efficiently it's running. Here's a simple explanation of what each number means for you:
                </p>
            </div>
            <div class="col-md-4">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">Sample Metrics Display</h3>
                    </div>
                    <div class="panel-body">
                        <img src="/assets/images/metrics-example.png" alt="Example of performance metrics" class="img-responsive" onerror="this.style.display='none'">
                        <div style="background:#f8f9fa; border:1px solid #ddd; padding:10px; font-size:12px; font-family:monospace;">
                            <p style="margin:0 0 5px 0;"><span style="color:#555;">Total Time:</span> <span style="font-weight:bold;">47.83 ms</span></p>
                            <p style="margin:0 0 5px 0;"><span style="color:#555;">Memory Usage:</span> <span style="font-weight:bold;">34.38 KB</span></p>
                            <p style="margin:0 0 5px 0;"><span style="color:#555;">Peak Memory:</span> <span style="font-weight:bold;">708.67 KB</span></p>
                            <p style="margin:0 0 5px 0;"><span style="color:#555;">Route Matching:</span> <span style="font-weight:bold;">0.42 ms</span></p>
                            <p style="margin:0 0 5px 0;"><span style="color:#555;">Route Processing:</span> <span style="font-weight:bold;">12.56 ms</span></p>
                            <p style="margin:0 0 5px 0;"><span style="color:#555;">Response Preparation:</span> <span style="font-weight:bold;">28.64 ms</span></p>
                            <p style="margin:0 0 5px 0;"><span style="color:#555;">Response Sending:</span> <span style="font-weight:bold;">1.81 ms (est.)</span></p>
                            <p style="margin:0 0 5px 0;"><span style="color:#555;">Response Size:</span> <span style="font-weight:bold;">18.07 KB</span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="metrics-guide">
            <div class="metric-item">
                <h3>Total Time <span class="label label-success">Most Important</span></h3>
                <p>
                    This is how long it took for the page to load, from the moment you clicked a link or button until the page appeared.
                </p>
                <div class="performance-scale">
                    <div class="scale-item good">
                        <span class="scale-label">Fast</span>
                        <span class="scale-value">&lt; 100ms</span>
                    </div>
                    <div class="scale-item acceptable">
                        <span class="scale-label">Acceptable</span>
                        <span class="scale-value">100-300ms</span>
                    </div>
                    <div class="scale-item poor">
                        <span class="scale-label">Slow</span>
                        <span class="scale-value">&gt; 300ms</span>
                    </div>
                </div>
                <p class="metric-tip">
                    <strong>What it means for you:</strong> Lower numbers mean faster page loads and a more responsive application.
                </p>
            </div>

            <div class="metric-item">
                <h3>Memory Usage</h3>
                <p>
                    This shows how much computer memory the page is using while you're viewing it.
                </p>
                <div class="performance-scale">
                    <div class="scale-item good">
                        <span class="scale-label">Efficient</span>
                        <span class="scale-value">&lt; 1MB</span>
                    </div>
                    <div class="scale-item acceptable">
                        <span class="scale-label">Normal</span>
                        <span class="scale-value">1-5MB</span>
                    </div>
                    <div class="scale-item poor">
                        <span class="scale-label">Heavy</span>
                        <span class="scale-value">&gt; 5MB</span>
                    </div>
                </div>
                <p class="metric-tip">
                    <strong>What it means for you:</strong> Lower memory usage means the application can handle more users simultaneously without slowing down.
                </p>
            </div>

            <div class="metric-item">
                <h3>Peak Memory</h3>
                <p>
                    This shows the maximum amount of computer memory used during page generation.
                </p>
                <div class="performance-scale">
                    <div class="scale-item good">
                        <span class="scale-label">Efficient</span>
                        <span class="scale-value">&lt; 2MB</span>
                    </div>
                    <div class="scale-item acceptable">
                        <span class="scale-label">Normal</span>
                        <span class="scale-value">2-10MB</span>
                    </div>
                    <div class="scale-item poor">
                        <span class="scale-label">Heavy</span>
                        <span class="scale-value">&gt; 10MB</span>
                    </div>
                </div>
                <p class="metric-tip">
                    <strong>What it means for you:</strong> Lower peak memory usage indicates the application is handling complex operations efficiently.
                </p>
            </div>

            <div class="metric-item">
                <h3>Response Size</h3>
                <p>
                    This is how much data was downloaded to display this page.
                </p>
                <div class="performance-scale">
                    <div class="scale-item good">
                        <span class="scale-label">Light</span>
                        <span class="scale-value">&lt; 100KB</span>
                    </div>
                    <div class="scale-item acceptable">
                        <span class="scale-label">Medium</span>
                        <span class="scale-value">100-500KB</span>
                    </div>
                    <div class="scale-item poor">
                        <span class="scale-label">Heavy</span>
                        <span class="scale-value">&gt; 500KB</span>
                    </div>
                </div>
                <p class="metric-tip">
                    <strong>What it means for you:</strong> Smaller page sizes load faster, especially on mobile devices or slower internet connections.
                </p>
            </div>

            <div class="metric-item">
                <h3>Route Matching</h3>
                <p>
                    This is how long it took to determine which page to show based on the URL you requested.
                </p>
                <div class="performance-scale">
                    <div class="scale-item good">
                        <span class="scale-label">Fast</span>
                        <span class="scale-value">&lt; 1ms</span>
                    </div>
                    <div class="scale-item acceptable">
                        <span class="scale-label">Normal</span>
                        <span class="scale-value">1-5ms</span>
                    </div>
                    <div class="scale-item poor">
                        <span class="scale-label">Slow</span>
                        <span class="scale-value">&gt; 5ms</span>
                    </div>
                </div>
                <p class="metric-tip">
                    <strong>What it means for you:</strong> This is typically very fast and shouldn't impact your experience much.
                </p>
            </div>

            <div class="metric-item">
                <h3>Route Processing</h3>
                <p>
                    This is how long it took to gather all the data needed for the page (like fetching expenses from the database).
                </p>
                <div class="performance-scale">
                    <div class="scale-item good">
                        <span class="scale-label">Fast</span>
                        <span class="scale-value">&lt; 50ms</span>
                    </div>
                    <div class="scale-item acceptable">
                        <span class="scale-label">Normal</span>
                        <span class="scale-value">50-150ms</span>
                    </div>
                    <div class="scale-item poor">
                        <span class="scale-label">Slow</span>
                        <span class="scale-value">&gt; 150ms</span>
                    </div>
                </div>
                <p class="metric-tip">
                    <strong>What it means for you:</strong> This often represents database queries and business logic. Lower values mean faster data retrieval.
                </p>
            </div>

            <div class="metric-item">
                <h3>Response Preparation</h3>
                <p>
                    This is how long it took to build the HTML for the page. Sometimes you'll see two values (total/final) - the total is the overall time, and final is just the last part of preparation.
                </p>
                <div class="performance-scale">
                    <div class="scale-item good">
                        <span class="scale-label">Fast</span>
                        <span class="scale-value">&lt; 50ms</span>
                    </div>
                    <div class="scale-item acceptable">
                        <span class="scale-label">Normal</span>
                        <span class="scale-value">50-150ms</span>
                    </div>
                    <div class="scale-item poor">
                        <span class="scale-label">Slow</span>
                        <span class="scale-value">&gt; 150ms</span>
                    </div>
                </div>
                <p class="metric-tip">
                    <strong>What it means for you:</strong> This represents how quickly the application can generate the HTML you see. Lower values mean faster page rendering.
                </p>
            </div>

            <div class="metric-item">
                <h3>Response Sending <span class="label label-info">Estimated</span></h3>
                <p>
                    This is an estimate of how long it takes to send the page from the server to your browser. You'll see "(est.)" next to this value because it's calculated rather than directly measured.
                </p>
                <div class="performance-scale">
                    <div class="scale-item good">
                        <span class="scale-label">Fast</span>
                        <span class="scale-value">&lt; 5ms</span>
                    </div>
                    <div class="scale-item acceptable">
                        <span class="scale-label">Normal</span>
                        <span class="scale-value">5-20ms</span>
                    </div>
                    <div class="scale-item poor">
                        <span class="scale-label">Slow</span>
                        <span class="scale-value">&gt; 20ms</span>
                    </div>
                </div>
                <p class="metric-tip">
                    <strong>What it means for you:</strong> This is affected by the size of the page and your internet connection speed. Smaller pages send faster.
                </p>
            </div>
        </div>

        <div class="panel panel-default">
            <div class="panel-heading">
                <h3 class="panel-title">Understanding Estimated Values and Raw Data</h3>
            </div>
            <div class="panel-body">
                <h4>Estimated Values</h4>
                <p>
                    Some metrics in the performance display are marked with "(est.)" to indicate they are estimated rather than directly measured.
                    This happens because:
                </p>
                <ul>
                    <li>Some operations happen after the measurement code has finished</li>
                    <li>Some values are calculated based on other metrics</li>
                    <li>Direct measurement would impact the performance being measured</li>
                </ul>
                <p>
                    Estimated values are still useful indicators but may not be as precise as directly measured values.
                </p>

                <h4>Raw Metrics Data</h4>
                <p>
                    At the bottom of the performance metrics display, you'll see a collapsible section labeled "Raw Metrics Data".
                    Clicking this reveals the complete JSON data structure of all collected metrics.
                </p>
                <p>
                    This raw data can be helpful when:
                </p>
                <ul>
                    <li>Troubleshooting performance issues</li>
                    <li>Looking for more detailed timing information</li>
                    <li>Reporting problems to technical support</li>
                </ul>
                <p>
                    The raw data includes additional technical details not shown in the main display, such as:
                </p>
                <ul>
                    <li>Number of routes checked during matching</li>
                    <li>Handler type used for processing</li>
                    <li>Parameter counts and response types</li>
                    <li>Detailed timing breakdowns</li>
                </ul>
            </div>
        </div>

        <div class="well">
            <h3>What to Do If Pages Are Loading Slowly</h3>
            <ol>
                <li>Check your internet connection</li>
                <li>Try refreshing the page</li>
                <li>Clear your browser cache</li>
                <li>If problems persist, contact support and mention the performance metrics you're seeing</li>
            </ol>
        </div>
    </div>
</div>

<link rel="stylesheet" href="/assets/css/main.css?v=<?= time() ?>">
<link rel="stylesheet" href="/assets/css/icons.css?v=<?= time() ?>">
<style>
    body {
        display: block;
    }
    /* Notice box styling */
    .notice-box {
        background-color: #d9edf7;
        border: 1px solid #bce8f1;
        border-radius: 4px;
        color: #31708f;
        padding: 15px;
        margin-bottom: 20px;
    }
    .notice-content {
        display: flex;
        align-items: flex-start;
    }
    .notice-icon {
        flex: 0 0 auto;
        margin-right: 15px;
        margin-top: 2px;
    }
    .notice-text {
        flex: 1 1 auto;
    }
    .notice-box p {
        margin: 0;
    }

    /* Metrics guide styling */
    .metrics-guide {
        margin: 30px 0;
    }
    .metric-item {
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 1px solid #eee;
    }
    .metric-item h3 {
        margin-top: 0;
        color: #333;
    }
    .performance-scale {
        display: flex;
        margin: 15px 0;
        border-radius: 4px;
        overflow: hidden;
    }
    .scale-item {
        flex: 1;
        padding: 10px;
        text-align: center;
        color: white;
    }
    .scale-item.good {
        background-color: #5cb85c;
    }
    .scale-item.acceptable {
        background-color: #f0ad4e;
    }
    .scale-item.poor {
        background-color: #d9534f;
    }
    .scale-label {
        display: block;
        font-weight: bold;
        margin-bottom: 5px;
    }
    .scale-value {
        font-size: 0.9em;
    }
    .metric-tip {
        margin-top: 10px;
        font-style: italic;
    }
    .label-success {
        background-color: #5cb85c;
        font-size: 0.6em;
        vertical-align: middle;
        margin-left: 5px;
    }
    .label-info {
        background-color: #5bc0de;
        font-size: 0.6em;
        vertical-align: middle;
        margin-left: 5px;
    }

    /* Panel styling */
    .panel-default {
        border-color: #ddd;
        margin-bottom: 20px;
    }
    .panel-heading {
        background-color: #f5f5f5;
        border-color: #ddd;
        padding: 10px 15px;
    }
    .panel-title {
        margin-top: 0;
        margin-bottom: 0;
        font-size: 16px;
        color: inherit;
    }
    .panel-body {
        padding: 15px;
    }
    .panel-body h4 {
        margin-top: 0;
        margin-bottom: 10px;
    }
</style>
