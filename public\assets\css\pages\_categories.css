/* ===== CATEGORY PAGES STYLES ===== */

/* Category Grid Layout */
.category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-md);
  margin: var(--spacing-md) 0;
}

/* Category Card Styling */
.category-card {
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
  display: flex;
  flex-direction: column;
}

.category-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--box-shadow-lg);
}

.system-category {
  border-left: 4px solid var(--primary-color);
}

.user-category {
  border-left: 4px solid var(--success-color);
}

/* Card Header */
.card-header {
  padding: var(--spacing) var(--spacing-md);
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid var(--grey-200);
}

.category-name {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--dark-color);
}

.badge {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  text-transform: uppercase;
}

.badge.system {
  background-color: var(--primary-light);
  color: var(--primary-dark);
}

.badge.user {
  background-color: var(--success-light);
  color: var(--success-dark);
}

/* Card Body */
.card-body {
  padding: var(--spacing-md);
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.description {
  color: var(--text-color);
  margin-top: 0;
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-sm);
  line-height: 1.5;
}

.description.empty {
  color: var(--text-muted);
  font-style: italic;
}

/* Category Stats */
.category-stats {
  display: flex;
  justify-content: space-between;
  margin-top: auto;
  padding-top: var(--spacing);
  border-top: 1px dashed var(--grey-200);
}

.stat-item {
  text-align: center;
  flex-basis: 50%;
}

.stat-label {
  display: block;
  font-size: var(--font-size-xs);
  color: var(--text-muted);
  margin-bottom: 0.25rem;
}

.stat-value {
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-semibold);
  color: var(--dark-color);
}

/* Card Footer */
.card-footer {
  background-color: var(--grey-100);
  padding: var(--spacing) var(--spacing-md);
  border-top: 1px solid var(--grey-200);
}

.category-actions {
  display: flex;
  gap: var(--spacing-xs);
  flex-wrap: wrap;
}

.system-notice {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
  font-style: italic;
  text-align: center;
}

/* Statistics Section */
.category-statistics {
  margin-top: var(--spacing-xl);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--grey-200);
}

.stats-container {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-lg);
  margin-top: var(--spacing-md);
}

.chart-wrapper {
  flex: 0 0 300px;
  max-width: 100%;
}

.stats-breakdown {
  flex: 1;
  min-width: 300px;
}

.distribution-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.distribution-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid var(--grey-200);
}

.distribution-item:last-child {
  border-bottom: none;
}

.color-indicator {
  width: 12px;
  height: 12px;
  border-radius: 3px;
  margin-right: var(--spacing-sm);
}

.amount-info {
  margin-left: auto;
  display: flex;
  gap: var(--spacing);
  align-items: center;
}

.amount {
  font-weight: var(--font-weight-semibold);
}

.percentage {
  color: var(--text-muted);
  font-size: var(--font-size-xs);
}

/* Category Form Styles */
.category-form-container {
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: var(--spacing-lg);
  max-width: 800px;
  margin: var(--spacing-lg) auto;
}

/* Statistics Page Styles */
.statistics-container {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

.category-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.stats-section {
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: var(--spacing-md);
}

.stats-section h3 {
  margin-top: 0;
  margin-bottom: var(--spacing);
  color: var(--dark-color);
  font-size: var(--font-size-lg);
}

.chart-container {
  height: 300px;
  position: relative;
}

.stats-summary {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  margin-top: auto;
  padding-top: var(--spacing);
  border-top: 1px dashed var(--grey-200);
}

.stat-group {
  display: flex;
  justify-content: space-between;
}

.statistics-charts {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.chart-section {
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: var(--spacing-md);
}

.recent-expenses {
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: var(--spacing-md);
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .statistics-container {
    grid-template-columns: 1fr;
  }

  .category-details {
    order: 1;
  }
}

@media (max-width: 768px) {
  .category-grid {
    grid-template-columns: 1fr;
  }

  .stats-container {
    flex-direction: column;
  }

  .chart-wrapper {
    margin: 0 auto;
  }

  .actions {
    flex-direction: column;
  }

  .button {
    width: 100%;
    justify-content: center;
  }
}
