<?php

/**
 * Document viewer for a specific expense
 * Used by the viewDocument() controller action
 */

// Include shared helper functions
include_once __DIR__ . '/../../../shared/helpers.php';

// Ensure we have the required data
if (empty($expense) || empty($fileInfo)) {
    echo '<div class="not-found">
            <p>Document not found or cannot be displayed.</p>
            <a href="/expenses" class="button secondary">Back to Expenses</a>
          </div>';
    return;
}
?>

<?php
// Set page header parameters
$title = 'Document Viewer';
$backUrl = '/expenses/' . htmlspecialchars($expense['id']);
$backText = 'Back to Expense';
include __DIR__ . '/../../../shared/components/page_header.php';
?>

<?php include __DIR__ . '/../../../shared/messages.php'; ?>

<div class="document-header">
    <h3>
        Document for: <?= htmlspecialchars($expense['description']) ?>
        <span class="document-amount">(<?= formatCurrency((float)$expense['amount']) ?>)</span>
    </h3>

    <div class="document-meta">
        <?php if (isset($expense['category_id'])) : ?>
            <p><strong>Category:</strong> <?= htmlspecialchars($category['name'] ?? 'Uncategorized') ?></p>
        <?php endif; ?>
        <p><strong>Date:</strong> <?= formatDate($expense['date'], 'F j, Y') ?></p>
        <?php if (!empty($fileInfo['created_at'])) : ?>
            <p><strong>Uploaded:</strong> <?= formatDate($fileInfo['created_at'], 'M d, Y g:i A') ?></p>
        <?php endif; ?>
    </div>

    <div class="document-actions">
        <?php
        // Define action buttons
        $actions = [
            [
                'url' => "/expenses/" . htmlspecialchars($expense['id']),
                'text' => "Expense Details",
                'icon' => "expense"
            ],
            [
                'url' => "/expenses/" . htmlspecialchars($expense['id']) . "/document/download",
                'text' => "Download",
                'icon' => "download"
            ]
        ];

        // Render each action button
        foreach ($actions as $action) {
            $url = $action['url'];
            $text = $action['text'];
            $icon = $action['icon'];
            $title = $action['text']; // Use text as title for accessibility
            include __DIR__ . '/../../../shared/components/action_button.php';
        }
        ?>
        <?php
        // Check if the file is a receipt that can be processed
        $isReceiptFile = false;
        if (!empty($fileInfo['original_name'])) {
            $extension = strtolower(pathinfo($fileInfo['original_name'], PATHINFO_EXTENSION));
            $isReceiptFile = in_array($extension, ['txt', 'pdf']);
        }
        ?>
        <?php if ($isReceiptFile) : ?>
            <form method="POST"
                  action="/expenses/<?= htmlspecialchars($expense['id']) ?>/receipt/process"
                  class="inline-form">
                <input type="hidden"
                       name="csrf_token"
                       value="<?= htmlspecialchars($csrf_token) ?>">
                <button type="submit" class="button special"
                        onclick="return confirm('This will update expense details based on the receipt. ' +
                                'Continue?');">
                    <svg class="icon icon-sm"><use xlink:href="/assets/icons.svg#icon-settings"></use></svg> Process Receipt
                </button>
            </form>
            <a href="/expenses/<?= htmlspecialchars($expense['id']) ?>/receipt/download" class="button">
                <svg class="icon icon-sm"><use xlink:href="/assets/icons.svg#icon-file-text"></use></svg> Download Receipt Text
            </a>
        <?php endif; ?>
    </div>
</div>

<div class="document-container">
    <?php
    $extension = '';
    $mimeType = '';

    if (!empty($fileInfo['original_name'])) {
        $extension = strtolower(pathinfo($fileInfo['original_name'], PATHINFO_EXTENSION));
    }

    if (!empty($fileInfo['mime_type'])) {
        $mimeType = $fileInfo['mime_type'];
    }

    if ($extension === 'pdf' || strpos($mimeType, 'application/pdf') === 0) : ?>
        <div class="pdf-container">
            <p>PDF documents can't be displayed directly. Please download the file to view it.</p>
            <a href="/expenses/<?= htmlspecialchars($expense['id']) ?>/document/download" class="button large">
                Download PDF
            </a>
        </div>
    <?php elseif (in_array($extension, ['jpg', 'jpeg', 'png', 'gif']) || strpos($mimeType, 'image/') === 0) : ?>
        <div class="image-container">
            <p>This is an image file. Please download to view.</p>
            <a href="/expenses/<?= htmlspecialchars($expense['id']) ?>/document/download" class="button large">
                Download Image
            </a>
        </div>
    <?php else : ?>
        <div class="text-content">
            <pre><?= htmlspecialchars($content) ?></pre>
        </div>
    <?php endif; ?>
</div>

<div class="navigation-links">
    <?php
    // Define navigation buttons
    $actions = [
        [
            'url' => "/expenses",
            'text' => "Back to Expenses",
            'icon' => "arrow-back",
            'type' => "secondary"
        ]
    ];

    // Render each navigation button
    foreach ($actions as $action) {
        $url = $action['url'];
        $text = $action['text'];
        $icon = $action['icon'];
        $type = $action['type'];
        $title = $action['text']; // Use text as title for accessibility
        include __DIR__ . '/../../../shared/components/action_button.php';
    }
    ?>
</div>


