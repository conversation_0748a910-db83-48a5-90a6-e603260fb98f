<?php

/**
 * Standardized form component
 *
 * Parameters:
 * @param string $action Form action URL
 * @param string $method Form method (GET, POST, PUT, DELETE)
 * @param string $content Form content (fields, etc.)
 * @param bool $multipart Whether form includes file uploads
 * @param string $submitText Text for submit button
 * @param string $cancelUrl URL for cancel button
 * @param string $cancelText Text for cancel button
 * @param string $id Form ID
 * @param string $class Additional CSS classes
 * @param string $csrf_token CSRF token (if already generated)
 */

// Set defaults
$method = $method ?? 'POST';
$multipart = $multipart ?? false;
$id = $id ?? '';
$class = $class ?? '';
$submitText = $submitText ?? 'Submit';
$cancelText = $cancelText ?? 'Cancel';
$formClass = trim('form ' . ($class ?? ''));

// Determine if we need method spoofing
$realMethod = strtoupper($method);
$formMethod = in_array($realMethod, ['GET', 'POST']) ? $realMethod : 'POST';
$needsMethodField = !in_array($realMethod, ['GET', 'POST']);

// Set enctype for file uploads
$enctype = $multipart ? ' enctype="multipart/form-data"' : '';

// Set form ID
$formId = $id ? ' id="' . htmlspecialchars($id) . '"' : '';

// Set form attributes
$attributes = [
    'method' => $formMethod,
    'action' => $action,
    'class' => $formClass,
    'id' => $id ?: null,
    'enctype' => $multipart ? 'multipart/form-data' : null,
    'novalidate' => true
];

// Filter out null attributes
$attributes = array_filter($attributes, fn($value) => $value !== null);

// Build attributes string
$attributesStr = '';
foreach ($attributes as $key => $value) {
    if ($value === true) {
        $attributesStr .= ' ' . htmlspecialchars($key);
    } else {
        $attributesStr .= ' ' . htmlspecialchars($key) . '="' . htmlspecialchars($value) . '"';
    }
}
?>

<form<?= $attributesStr ?>>
    <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($csrf_token ?? '') ?>">

    <?php if ($needsMethodField) : ?>
    <input type="hidden" name="_method" value="<?= htmlspecialchars($realMethod) ?>">
    <?php endif; ?>

    <?= $content ?>

    <?php if ((empty($hideFormActions) || !$hideFormActions) && ($submitText !== null || isset($cancelUrl))) : ?>
    <div class="form-actions">
        <?php if ($submitText !== null) : ?>
        <button type="submit" class="button primary">
            <?= htmlspecialchars($submitText) ?>
        </button>
        <?php endif; ?>

        <?php if (isset($cancelUrl)) : ?>
        <a href="<?= htmlspecialchars($cancelUrl) ?>" class="button secondary">
            <?= htmlspecialchars($cancelText) ?>
        </a>
        <?php endif; ?>
    </div>
    <?php endif; ?>
</form>
