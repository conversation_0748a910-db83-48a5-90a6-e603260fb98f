/* ====== FORM CONTAINER ===== */
.form-container {
  max-width: 480px;
  margin: 2rem auto;
  padding: var(--spacing-lg);
  background-color: white;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-inline: 10px solid;
  animation: formAppear 0.5s ease-out;
}

@keyframes formAppear {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.form-container:has(
  input:invalid:not(:focus):not(:placeholder-shown),
  select:invalid:not(:focus),
  textarea:invalid:not(:focus):not(:placeholder-shown)
) {
  background: linear-gradient(
    to bottom right,
    rgba(255, 247, 247, 0.973) ,
    white
  );
}

.form-container:hover {
  transform: translateY(-3px);
  box-shadow: var(--box-shadow-lg);
}

.form-group {
  margin-bottom: var(--spacing-md);
  position: relative;
  transition: transform 0.2s ease;
}

.form-group:focus-within {
  transform: scale(1.01);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
  color: var(--dark-color);
  transition: color 0.2s ease;
}

.form-group:focus-within label {
  color: var(--primary-color);
  font-weight: var(--font-weight-semibold);
}

/* Form hint text */
.form-hint {
  display: block;
  font-size: var(--font-size-xs);
  color: var(--text-muted);
  margin-top: var(--spacing-xs);
  transition: all 0.2s ease;
}

.form-group:focus-within .form-hint {
  color: var(--primary-dark);
}

  /* Helper for multi-column forms */
  .form-row {
    display: flex;
    gap: var(--spacing);
    margin-bottom: var(--spacing);
  }

  .form-column {
    flex: 1;
  }

  /* Form links */
.form-links {
  display: flex;
  justify-content: space-between;
  margin-top: var(--spacing-md);
  font-size: var(--font-size-sm);
}

.form-links a {
  color: var(--primary-color);
  text-decoration: none;
  transition: all 0.2s ease;
  position: relative;
}

.form-links a:hover {
  color: var(--primary-dark);
}

.form-links a::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 1px;
  background-color: var(--primary-dark);
  transition: width 0.3s ease;
}

.form-links a:hover::after {
  width: 100%;
}

  @media (max-width: 992px) {
    .form-container {
      padding: var(--spacing);
    }
  }

  @media (max-width: 768px) {
    .form-row {
      flex-direction: column;
      gap: 0;
    }
  }

  @media (max-width: 600px) {
    .form-container {
      padding: var(--spacing);
      margin: var(--spacing) auto;
      width: 95%;
    }

    .form-links {
      flex-direction: column;
      align-items: center;
      gap: var(--spacing-sm);
    }
  }

  @media (max-width: 576px) {
    .form-container {
      padding: var(--spacing-sm);
    }

    .form-group {
      margin-bottom: var(--spacing);
    }
  }