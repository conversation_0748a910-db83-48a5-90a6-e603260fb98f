<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Expense;
use App\Models\Category;
use App\Models\Merchant;
use App\Models\PaymentMethod;
use App\Exceptions\InvalidArgumentException;
use App\Exceptions\NotFoundException;
use App\Exceptions\DatabaseException;

class ExpenseService extends BaseService
{
    protected function getModelClass(): string
    {
        return Expense::class;
    }

    public function getExpenseById(int $id, ?int $userId = null): ?array
    {
        error_log("[Debug] ExpenseService::getExpenseById - Looking for expense ID: {$id}, User ID: " . ($userId ?? 'null'));

        $result = Expense::find($id, $userId);

        if ($result) {
            error_log("[Debug] ExpenseService::getExpenseById - Found expense: " . json_encode($result));
            return (array)$result;
        } else {
            error_log("[Debug] ExpenseService::getExpenseById - No expense found with ID: {$id}");
            return null;
        }
    }

    /**
     * Get user expenses with support for both exact-match and range-based filters
     *
     * @param int $userId The user ID
     * @param array $filters Filters to apply including:
     *   - category_id: Single category ID for backward compatibility
     *   - category_ids: Array of category IDs for multi-select
     *   - user_category_id: Exact user category match
     *   - dateFrom: Start date for range filter (YYYY-MM-DD)
     *   - dateTo: End date for range filter (YYYY-MM-DD)
     *   - minAmount: Minimum amount for range filter
     *   - maxAmount: Maximum amount for range filter
     *   - query: Text search in description, notes, receipt_text, merchant name
     *   - hasDocument: '1' for expenses with documents, '0' for without
     *   - _sort: Sort field (date, description, amount, category_id, category_name)
     *   - _order: Sort order (ASC, DESC)
     * @return array Array of expenses
     */
    public function getUserExpenses(int $userId, array $filters = []): array
    {
        error_log("[Debug] ExpenseService::getUserExpenses - User ID: {$userId}");
        error_log("[Debug] ExpenseService::getUserExpenses - Filters: " . json_encode($filters));

        // Map filter parameters to search method parameters
        $query = $filters['query'] ?? '';
        $dateFrom = $filters['dateFrom'] ?? $filters['date_from'] ?? '';
        $dateTo = $filters['dateTo'] ?? $filters['date_to'] ?? '';

        // Handle both single category_id and multiple category_ids
        $categoryIds = [];
        if (isset($filters['category_ids']) && is_array($filters['category_ids'])) {
            $categoryIds = array_map('intval', array_filter($filters['category_ids'], function ($id) {
                return is_numeric($id) && (int)$id > 0;
            }));
        } elseif (isset($filters['category_id']) && (int)$filters['category_id'] > 0) {
            $categoryIds = [(int)$filters['category_id']];
        }

        $minAmount = (float)($filters['minAmount'] ?? $filters['min_amount'] ?? 0);
        $maxAmount = (float)($filters['maxAmount'] ?? $filters['max_amount'] ?? 0);
        $hasDocument = $filters['hasDocument'] ?? $filters['has_document'] ?? '';
        $limit = (int)($filters['limit'] ?? 50);
        $offset = (int)($filters['offset'] ?? 0);

        $expenses = Expense::search(
            $userId,
            $query,
            $dateFrom,
            $dateTo,
            $categoryIds,
            $minAmount,
            $maxAmount,
            $hasDocument,
            $limit,
            $offset
        );

        error_log("[Debug] ExpenseService::getUserExpenses - Expenses count: " . count($expenses));
        if (!empty($expenses)) {
            // Log the first expense to see what data is being returned
            error_log("[Debug] ExpenseService::getUserExpenses - First expense: " . json_encode([
                'id' => $expenses[0]['id'] ?? 'not set',
                'category_id' => $expenses[0]['category_id'] ?? 'not set',
                'user_category_id' => $expenses[0]['user_category_id'] ?? 'not set',
                'description' => $expenses[0]['description'] ?? 'not set'
            ]));
        }

        return $expenses;
    }

    public function getRecentExpenses(int $userId, int $limit = 5): array
    {
        return Expense::getRecent($userId, $limit);
    }

    public function findByMerchantName(string $merchantName, ?int $userId = null, int $limit = 10): array
    {
        return Expense::findByMerchantName($merchantName, $userId, $limit);
    }

    public function createExpense(array $expenseData): int
    {
        try {
            error_log("[Debug] ExpenseService::createExpense - Creating expense with data: " . json_encode($expenseData));

            $this->validateExpenseData($expenseData);
            error_log("[Debug] ExpenseService::createExpense - Validation passed");

            $expenseData = $this->normalizeCategoryFields($expenseData);
            error_log("[Debug] ExpenseService::createExpense - Normalized data: " . json_encode($expenseData));

            $expenseId = Expense::create($expenseData);
            error_log("[Debug] ExpenseService::createExpense - Created expense with ID: {$expenseId}");

            return $expenseId;
        } catch (\Exception $e) {
            error_log("[Error] ExpenseService::createExpense - Error: " . $e->getMessage());
            error_log("[Error] ExpenseService::createExpense - Trace: " . $e->getTraceAsString());
            throw $e;
        }
    }

    private function normalizeCategoryFields(array $expenseData): array
    {
        error_log("[Debug] ExpenseService::normalizeCategoryFields - Input data: " . json_encode([
            'category_id' => $expenseData['category_id'] ?? 'not set',
            'user_category_id' => $expenseData['user_category_id'] ?? 'not set'
        ]));

        // Special handling for '_new' value from the form
        if (isset($expenseData['category_id']) && $expenseData['category_id'] === '_new') {
            error_log("[Debug] ExpenseService::normalizeCategoryFields - Found '_new' category_id, will handle new category creation separately");
            // For now, we'll default to Housing since we don't have the new category creation logic here
            $expenseData['category_id'] = 1;
            $expenseData['user_category_id'] = null;
            return $expenseData;
        }

        // Check if category_id is a string and convert it to int for proper comparison
        if (isset($expenseData['category_id']) && is_string($expenseData['category_id'])) {
            $expenseData['category_id'] = (int)$expenseData['category_id'];
            error_log("[Debug] ExpenseService::normalizeCategoryFields - Converted category_id from string to int: " . $expenseData['category_id']);
        }

        // Check if user_category_id is a string and convert it to int for proper comparison
        if (isset($expenseData['user_category_id']) && is_string($expenseData['user_category_id'])) {
            $expenseData['user_category_id'] = (int)$expenseData['user_category_id'];
            error_log("[Debug] ExpenseService::normalizeCategoryFields - Converted user_category_id from string to int: " . $expenseData['user_category_id']);
        }

        // If user_category_id is set and positive, use it and set category_id to null
        if (isset($expenseData['user_category_id']) && $expenseData['user_category_id'] > 0) {
            error_log("[Debug] ExpenseService::normalizeCategoryFields - Using user_category_id: " . $expenseData['user_category_id']);
            $expenseData['category_id'] = null;
            return $expenseData;
        }

        // If category_id is set and positive, use it and set user_category_id to null
        if (isset($expenseData['category_id']) && $expenseData['category_id'] > 0) {
            error_log("[Debug] ExpenseService::normalizeCategoryFields - Using category_id: " . $expenseData['category_id']);
            $expenseData['user_category_id'] = null;
            return $expenseData;
        }

        // Only default to Housing if no category is provided at all
        if (
            (!isset($expenseData['category_id']) || $expenseData['category_id'] === null || $expenseData['category_id'] === 0 || $expenseData['category_id'] === '') &&
            (!isset($expenseData['user_category_id']) || $expenseData['user_category_id'] === null || $expenseData['user_category_id'] === 0 || $expenseData['user_category_id'] === '')
        ) {
            error_log("[Debug] ExpenseService::normalizeCategoryFields - No valid category found, defaulting to Housing (category_id=1)");
            $expenseData['category_id'] = 1;
            $expenseData['user_category_id'] = null;
        }

        return $expenseData;
    }

    public function createExpenseWithFile(int $fileId, int $categoryId, array $expenseData): int
    {
        $expenseData['file_id'] = $fileId;
        $expenseData['category_id'] = $categoryId;
        return $this->createExpense($expenseData);
    }

    public function updateExpense(int $id, array $expenseData, ?int $userId = null): bool
    {
        $expense = $this->getExpenseById($id, $userId);
        if (!$expense) {
            throw new NotFoundException("Expense not found");
        }

        if (!empty($expenseData)) {
            $this->validateExpenseData($expenseData, false);
        }

        return Expense::update($id, $expenseData, $userId);
    }

    public function deleteExpense(int $id, ?int $userId = null): bool
    {
        $expense = $this->getExpenseById($id, $userId);
        if (!$expense) {
            throw new NotFoundException("Expense not found");
        }

        return Expense::delete($id, $userId);
    }

    public function removeFileAssociation(int $fileId): bool
    {
        return Expense::removeFileAssociation($fileId);
    }

    public function searchExpenses(
        int $userId,
        string $query = '',
        string $dateFrom = '',
        string $dateTo = '',
        $categoryIds = [],
        float $minAmount = 0,
        float $maxAmount = 0,
        string $hasDocument = '',
        int $limit = 50,
        int $offset = 0
    ): array {
        // Handle backward compatibility - convert single int to array
        if (is_int($categoryIds) && $categoryIds > 0) {
            $categoryIds = [$categoryIds];
        } elseif (!is_array($categoryIds)) {
            $categoryIds = [];
        }

        return Expense::search(
            $userId,
            $query,
            $dateFrom,
            $dateTo,
            $categoryIds,
            $minAmount,
            $maxAmount,
            $hasDocument,
            $limit,
            $offset
        );
    }

    public function searchExpensesWithParams(int $userId, array $params): array
    {
        // Handle both single categoryId and multiple categoryIds
        $categoryIds = [];
        if (isset($params['categoryIds']) && is_array($params['categoryIds'])) {
            $categoryIds = $params['categoryIds'];
        } elseif (isset($params['categoryId']) && (int)$params['categoryId'] > 0) {
            $categoryIds = [(int)$params['categoryId']];
        }

        return $this->searchExpenses(
            $userId,
            $params['query'] ?? '',
            $params['dateFrom'] ?? '',
            $params['dateTo'] ?? '',
            $categoryIds,
            $params['minAmount'] ?? 0,
            $params['maxAmount'] ?? 0,
            $params['hasDocument'] ?? '',
            $params['limit'] ?? 50,
            $params['offset'] ?? 0
        );
    }

    public function countSearchResults(
        int $userId,
        string $query = '',
        string $dateFrom = '',
        string $dateTo = '',
        $categoryIds = [],
        float $minAmount = 0,
        float $maxAmount = 0,
        string $hasDocument = ''
    ): int {
        // Handle backward compatibility - convert single int to array
        if (is_int($categoryIds) && $categoryIds > 0) {
            $categoryIds = [$categoryIds];
        } elseif (!is_array($categoryIds)) {
            $categoryIds = [];
        }

        return Expense::countSearchResults(
            $userId,
            $query,
            $dateFrom,
            $dateTo,
            $categoryIds,
            $minAmount,
            $maxAmount,
            $hasDocument
        );
    }

    public function countSearchResultsWithParams(int $userId, array $params): int
    {
        // Handle both single categoryId and multiple categoryIds
        $categoryIds = [];
        if (isset($params['categoryIds']) && is_array($params['categoryIds'])) {
            $categoryIds = $params['categoryIds'];
        } elseif (isset($params['categoryId']) && (int)$params['categoryId'] > 0) {
            $categoryIds = [(int)$params['categoryId']];
        }

        return $this->countSearchResults(
            $userId,
            $params['query'] ?? '',
            $params['dateFrom'] ?? '',
            $params['dateTo'] ?? '',
            $categoryIds,
            $params['minAmount'] ?? 0,
            $params['maxAmount'] ?? 0,
            $params['hasDocument'] ?? ''
        );
    }

    public function getExpenseCount(int $userId, int $days = 30): int
    {
        return Expense::getExpenseCount($userId, $days);
    }

    public function getWeeklyTotal(int $userId): float
    {
        return Expense::getWeeklyTotal($userId);
    }

    public function getMonthlyTotal(int $userId): float
    {
        return Expense::getMonthlyTotal($userId);
    }

    public function getYearlyTotal(int $userId): float
    {
        return Expense::getYearlyTotal($userId);
    }

    public function getPeriodTotal(
        int $userId,
        string $period,
        ?string $year = null,
        ?string $month = null
    ): float {
        return Expense::getPeriodTotal($userId, $period, $year, $month);
    }

    public function getMonthlyExpenses(int $userId, string $year, string $month): array
    {
        return Expense::getMonthlyExpenses($userId, $year, $month);
    }

    public function getMonthlyTotalsForYear(int $userId, string $year): array
    {
        return Expense::getMonthlyTotalsForYear($userId, $year);
    }

    public function getCategoryTotals(int $userId, string $period, string $year, ?string $month = null): array
    {
        return Expense::getCategoryTotals($userId, $period, $year, $month);
    }

    public function getCategoryTotalsForMonth(int $userId, string $year, string $month): array
    {
        return Expense::getCategoryTotalsForMonth($userId, $year, $month);
    }

    public function getCategoryTotalsForYear(int $userId, string $year): array
    {
        return Expense::getCategoryTotalsForYear($userId, $year);
    }

    public function getUserCategoryTotals(
        int $userId,
        string $period,
        string $year,
        ?string $month = null
    ): array {
        return Expense::getUserCategoryTotals($userId, $period, $year, $month);
    }

    public function getMerchantTotalsForYear(int $userId, string $year): array
    {
        return Expense::getMerchantTotalsForYear($userId, $year);
    }

    public function getDocumentCountsByCategory(int $userId): array
    {
        return Expense::getDocumentCountsByCategory($userId);
    }

    public function clearCache(): void
    {
        Expense::clearCache();
    }

    public function cleanExpiredCacheItems(): void
    {
        Expense::cleanExpiredCacheItems();
    }

    /**
     * Get the minimum and maximum amounts for a user's expenses
     *
     * @param int $userId The user ID
     * @return array Array with 'min' and 'max' keys
     */
    public function getAmountBounds(int $userId): array
    {
        try {
            return Expense::getAmountBoundsForUser($userId);
        } catch (DatabaseException $e) {
            error_log('[ExpenseService::getAmountBounds] ' . $e->getMessage());
            error_log('User ID: ' . $userId);
            throw $e;
        }
    }

    /**
     * Get expense counts per category for a user
     *
     * @param int $userId The user ID
     * @return array Array of category counts with category_id as key and count as value
     */
    public function getCategoryCountsForUser(int $userId): array
    {
        try {
            $results = Category::getUserExpenseCounts($userId);

            $counts = [];
            foreach ($results as $categoryId => $data) {
                $counts[$categoryId] = [
                    'name' => $data['category_name'],
                    'count' => $data['expense_count']
                ];
            }

            return $counts;
        } catch (\App\Exceptions\DatabaseException $e) {
            error_log("ExpenseService::getCategoryCountsForUser() - Database error: " . $e->getMessage());
            error_log("ExpenseService::getCategoryCountsForUser() - User ID: " . $userId);
            throw $e;
        }
    }

    private function validateExpenseData(array $expenseData, bool $requireAllFields = true): void
    {
        if ($requireAllFields) {
            $this->validateRequiredFields($expenseData);
            $this->validateCategoryFields($expenseData);
        }

        $this->validateAmountIfPresent($expenseData);
        $this->validateDateIfPresent($expenseData);
        $this->validateForeignKeysIfPresent($expenseData);
    }

    private function validateRequiredFields(array $expenseData): void
    {
        $requiredFields = ['user_id', 'amount', 'description', 'date'];
        $missingFields = $this->findMissingFields($expenseData, $requiredFields);

        if (!empty($missingFields)) {
            throw new InvalidArgumentException("Missing required fields: " . implode(', ', $missingFields));
        }
    }

    private function validateCategoryFields(array $expenseData): void
    {
        $hasCategoryId = isset($expenseData['category_id']) &&
                         $expenseData['category_id'] !== null &&
                         (int)$expenseData['category_id'] !== 0;

        $hasUserCategoryId = isset($expenseData['user_category_id']) &&
                             $expenseData['user_category_id'] !== null &&
                             (int)$expenseData['user_category_id'] !== 0;

        if (!$hasCategoryId && !$hasUserCategoryId) {
            throw new InvalidArgumentException(
                "Missing required category: either category_id or user_category_id must be set"
            );
        }
    }

    private function findMissingFields(array $data, array $requiredFields): array
    {
        return array_filter($requiredFields, function ($field) use ($data) {
            return !isset($data[$field]) || $data[$field] === '';
        });
    }

    private function validateAmountIfPresent(array $expenseData): void
    {
        $amount = $expenseData['amount'] ?? null;

        if ($amount === null) {
            return;
        }

        if (!is_numeric($amount)) {
            throw new InvalidArgumentException("Amount must be a number");
        }

        if ((float)$amount <= 0) {
            throw new InvalidArgumentException("Amount must be positive");
        }
    }

    private function validateDateIfPresent(array $expenseData): void
    {
        $date = $expenseData['date'] ?? null;

        if ($date === null) {
            return;
        }

        if (!$this->isValidDateFormat($date) || !$this->isValidDate($date)) {
            throw new InvalidArgumentException("Date must be a valid date in YYYY-MM-DD format");
        }
    }

    private function isValidDateFormat(string $date): bool
    {
        return preg_match('/^\d{4}-\d{2}-\d{2}$/', $date) === 1;
    }

    private function isValidDate(string $date): bool
    {
        $dateTime = \DateTime::createFromFormat('Y-m-d', $date);
        return $dateTime && $dateTime->format('Y-m-d') === $date;
    }

    private function validateForeignKeysIfPresent(array $expenseData): void
    {
        $foreignKeys = [
            'category_id' => [Category::class, "Invalid category ID"],
            'merchant_id' => [Merchant::class, "Invalid merchant ID"],
            'payment_method_id' => [PaymentMethod::class, "Invalid payment method ID"]
        ];

        foreach ($foreignKeys as $key => [$modelClass, $errorMessage]) {
            $this->validateForeignKey($expenseData, $key, $modelClass, $errorMessage);
        }
    }

    private function validateForeignKey(array $data, string $key, string $modelClass, string $errorMessage): void
    {
        $id = $data[$key] ?? null;

        if ($this->isEmptyForeignKey($id)) {
            return;
        }

        if (!$modelClass::find((int)$id)) {
            throw new InvalidArgumentException($errorMessage . ": {$id}");
        }
    }

    private function isEmptyForeignKey($value): bool
    {
        return $value === null || $value === 0 || $value === '0' || $value === '';
    }

    /**
     * Bulk delete multiple expenses
     *
     * @param array $expenseIds Array of expense IDs to delete
     * @param int|null $userId User ID for authorization
     * @return array Result with success and error counts
     */
    public function bulkDeleteExpenses(array $expenseIds, ?int $userId = null): array
    {
        $success = [];
        $errors = [];

        foreach ($expenseIds as $id) {
            try {
                $result = $this->deleteExpense((int)$id, $userId);
                if ($result) {
                    $success[] = $id;
                } else {
                    $errors[$id] = 'Failed to delete expense';
                }
            } catch (\Exception $e) {
                $errors[$id] = $e->getMessage();
            }
        }

        return [
            'success' => true,
            'deleted' => count($success),
            'failed' => count($errors),
            'errors' => $errors
        ];
    }
}
