/* ===== REPORTS PAGES STYLES ===== */

/* Report Dashboard Layout */
.report-dashboard {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.report-cards {
  flex: 1;
  min-width: 300px;
  display: flex;
  flex-direction: column;
  gap: var(--spacing);
}

.report-card {
  background-color: var(--card-bg);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--box-shadow-sm);
  transition: var(--transition);
}

.report-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

.report-card h3 {
  margin-top: 0;
  margin-bottom: var(--spacing-sm);
  color: var(--dark-color);
  font-weight: var(--font-weight-semibold);
}

.report-card p {
  margin-bottom: var(--spacing-lg);
  color: var(--text-muted);
}

.report-summary {
  flex: 1;
  min-width: 300px;
  background-color: var(--card-bg);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--box-shadow-sm);
}

.report-summary h3 {
  margin-top: 0;
  margin-bottom: var(--spacing);
  color: var(--dark-color);
  font-weight: var(--font-weight-semibold);
}

/* Report Filters */
.report-filters {
  background-color: var(--grey-100);
  padding: var(--spacing);
  border-radius: var(--border-radius-lg);
  margin-bottom: var(--spacing-lg);
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing);
}

.filter-group {
  display: flex;
  flex-direction: column;
  min-width: 150px;
}

.filter-group label {
  margin-bottom: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

/* Summary Stats */
.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: var(--spacing);
  margin-bottom: var(--spacing-lg);
}

.stat-item {
  background-color: var(--grey-100);
  padding: var(--spacing);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-sm);
  transition: var(--transition);
}

.stat-item:hover {
  background-color: var(--grey-200);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  display: block;
  margin-bottom: var(--spacing-xs);
}

.stat-value {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--dark-color);
  display: block;
}

.stat-subvalue {
  font-size: var(--font-size);
  color: var(--text-muted);
  display: block;
  margin-top: var(--spacing-xs);
}

/* Change Indicators */
.change {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  display: inline-block;
  margin-top: var(--spacing-xs);
}

.change.up {
  background-color: var(--danger-light);
  color: var(--danger-dark);
}

.change.down {
  background-color: var(--success-light);
  color: var(--success-dark);
}

.change.neutral {
  background-color: var(--grey-200);
  color: var(--grey-700);
}

/* Category List */
.category-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.category-list li {
  margin-bottom: var(--spacing-sm);
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.category-name {
  flex: 2;
  font-weight: var(--font-weight-medium);
}

.category-amount {
  flex: 1;
  text-align: right;
}

.category-percentage {
  width: 50px;
  text-align: right;
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

.progress-bar {
  height: 6px;
  width: 100%;
  background-color: var(--grey-200);
  border-radius: 3px;
  margin-top: var(--spacing-xs);
  overflow: hidden;
}

.progress {
  height: 100%;
  background-color: var(--primary-color);
  border-radius: 3px;
}

/* Chart Containers */
.chart-container {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.pie-chart-container,
.bar-chart-container {
  background-color: var(--card-bg);
  padding: var(--spacing);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-sm);
}

.pie-chart-container {
  flex: 1;
  min-width: 300px;
  max-width: 400px;
}

.bar-chart-container {
  flex: 2;
  min-width: 400px;
}

/* Recent Transactions */
.recent-transactions {
  margin-top: var(--spacing-xl);
}

.recent-transactions h3 {
  margin-bottom: var(--spacing);
}

/* Download Section */
.download-section {
  margin-top: var(--spacing-xl);
  background-color: var(--grey-100);
  padding: var(--spacing);
  border-radius: var(--border-radius-lg);
}

.download-section h3 {
  margin-top: 0;
  margin-bottom: var(--spacing-sm);
}

.download-options {
  display: flex;
  gap: var(--spacing);
  margin-top: var(--spacing);
}

/* Navigation Links */
.navigation-links {
  margin-top: var(--spacing-xl);
}

.view-all {
  margin-top: var(--spacing-lg);
  text-align: center;
}

/* Notice */
.notice {
  padding: var(--spacing);
  background-color: var(--grey-100);
  border-radius: var(--border-radius);
  color: var(--text-muted);
  text-align: center;
  margin: var(--spacing) 0;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .report-dashboard {
    flex-direction: column;
  }

  .summary-stats {
    grid-template-columns: 1fr;
  }

  .chart-container {
    flex-direction: column;
  }

  .pie-chart-container,
  .bar-chart-container {
    max-width: none;
  }

  .download-options {
    flex-direction: column;
  }

  .download-options .button {
    width: 100%;
    justify-content: center;
  }
}
