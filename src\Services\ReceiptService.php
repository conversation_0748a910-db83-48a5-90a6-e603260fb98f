<?php

declare(strict_types=1);

namespace App\Services;

use App\Core\Database;
use App\Core\LogManager;
use PDO;
use PDOException;

class ReceiptService
{
    private ?PDO $db = null;


    public function __construct(?PDO $connection = null)
    {
        try {
            $this->db = $connection ?? Database::getInstance();
        } catch (\Throwable $e) {
            // Log the error but don't throw - we'll use fallback methods if DB fails
            LogManager::logException($e, "Failed to initialize database in ReceiptService");
        }
    }

    public function getExpenseDataForReceipt(int $expenseId, int $userId): ?array
    {
        if (!$this->db) {
            return $this->getBasicExpenseData($expenseId);
        }

        try {
            return $this->fetchExpenseDataFromDb($expenseId, $userId);
        } catch (PDOException $e) {
            LogManager::logException($e, "Database error in getExpenseDataForReceipt");
            return $this->getBasicExpenseData($expenseId);
        } catch (\Throwable $e) {
            LogManager::logException($e, "Unexpected error in getExpenseDataForReceipt");
            return $this->getBasicExpenseData($expenseId);
        }
    }

    private function fetchExpenseDataFromDb(int $expenseId, int $userId): ?array
    {
        try {
            // First, try a simpler query without the payment_methods join
            $sql = "
                SELECT 
                    e.amount, 
                    e.date, 
                    e.description,
                    e.merchant_id,
                    e.payment_method_id,
                    e.user_id,
                    m.name as merchant_name,
                    COALESCE(c.name, uc.name) as category_name
                FROM expenses e
                LEFT JOIN categories c ON e.category_id = c.id
                LEFT JOIN user_categories uc ON e.user_category_id = uc.id
                LEFT JOIN merchants m ON e.merchant_id = m.id
                WHERE e.id = ? AND e.user_id = ?
            ";
        } catch (\Exception $e) {
            // Log the error for debugging
            error_log("SQL Error in fetchExpenseDataFromDb: " . $e->getMessage());
            throw $e;
        }

        try {
            $stmt = $this->db->prepare($sql);
            $stmt->execute([$expenseId, $userId]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$result) {
                return $this->getBasicExpenseData($expenseId);
            }

            if (isset($result['amount'])) {
                $result['amount'] = (float)$result['amount'];
            }

            // If payment method is not found, set a default value
            if (!isset($result['payment_method']) || empty($result['payment_method'])) {
                // Try to get the payment method name from the payment_methods table directly
                if (isset($result['payment_method_id']) && $result['payment_method_id'] > 0) {
                    try {
                        $paymentMethodSql = "SELECT method FROM payment_methods WHERE id = ?";
                        $paymentMethodStmt = $this->db->prepare($paymentMethodSql);
                        $paymentMethodStmt->execute([$result['payment_method_id']]);
                        $paymentMethod = $paymentMethodStmt->fetchColumn();

                        if ($paymentMethod) {
                            $result['payment_method'] = $paymentMethod;
                        } else {
                            $result['payment_method'] = 'Unknown';
                        }
                    } catch (\Exception $e) {
                        // If there's an error, use a default value
                        error_log("Error getting payment method: " . $e->getMessage());
                        $result['payment_method'] = 'Unknown';
                    }
                } else {
                    $result['payment_method'] = 'Unknown';
                }
            }

            return $result;
        } catch (\Exception $e) {
            // Log the error for debugging
            error_log("Error executing SQL in fetchExpenseDataFromDb: " . $e->getMessage());
            return $this->getBasicExpenseData($expenseId);
        }
    }


    private function getBasicExpenseData(int $expenseId): array
    {
        return [
            'id' => $expenseId,
            'amount' => 0.00,
            'date' => date('Y-m-d'),
            'description' => 'Expense #' . $expenseId,
            'merchant_name' => null,
            'category_name' => null,
            'payment_method' => 'Unknown'
        ];
    }

    public function generateReceiptContent(array $expense, int $expenseId): string
    {
        try {
            return $this->assembleReceiptSections($expense, $expenseId);
        } catch (\Throwable $e) {
            LogManager::logException($e, "Error generating receipt content");
            return $this->generateFallbackReceipt($expenseId);
        }
    }

    private function assembleReceiptSections(array $expense, int $expenseId): string
    {
        $headerText = $this->getReceiptHeaderText($expense, $expenseId);
        $detailsText = $this->getReceiptDetailsText($expense);
        $footerText = $this->getReceiptFooterText();

        return $headerText . $detailsText . $footerText;
    }


    public function generateFallbackReceipt(int $expenseId): string
    {
        $currentDate = date('F j, Y');

        return "========================================\n" .
               "RECEIPT - EXPENSE #{$expenseId}\n" .
               "========================================\n" .
               "Date: {$currentDate}\n" .
               "========================================\n\n" .
               "Thank you for your business!\n";
    }

    public function createReceiptFilename(array $expense, int $expenseId): string
    {
        try {
            return $this->buildFilenameWithCategory($expense, $expenseId);
        } catch (\Throwable $e) {
            return $this->buildBasicFilename($expenseId);
        }
    }

    private function buildFilenameWithCategory(array $expense, int $expenseId): string
    {
        $dateFormatted = date('Y-m-d');

        if (empty($expense['category_name'])) {
            return $this->buildBasicFilename($expenseId);
        }

        $categoryName = ucfirst($this->createSlug($expense['category_name']));
        return "Receipt_{$dateFormatted}_{$categoryName}_{$expenseId}.txt";
    }

    private function buildBasicFilename(int $expenseId): string
    {
        $dateFormatted = date('Y-m-d');
        return "Receipt_{$dateFormatted}_Expense_{$expenseId}.txt";
    }


    private function getReceiptHeaderText(array $expense, int $expenseId): string
    {
        $title = !empty($expense['merchant_name'])
            ? "RECEIPT - {$expense['merchant_name']}"
            : "RECEIPT - EXPENSE #{$expenseId}";

        return "========================================\n" .
               "{$title}\n" .
               "========================================\n";
    }

    private function getReceiptDetailsText(array $expense): string
    {
        $details = $this->formatDateSection($expense);
        $details .= $this->formatCategorySection($expense);
        $details .= $this->formatPaymentMethodSection($expense);
        $details .= $this->formatDescriptionSection($expense);
        $details .= "========================================\n";
        $details .= $this->formatAmountSection($expense);

        return $details;
    }

    private function formatPaymentMethodSection(array $expense): string
    {
        return !empty($expense['payment_method'])
            ? "Payment Method: {$expense['payment_method']}\n"
            : "";
    }

    private function formatDateSection(array $expense): string
    {
        $dateStr = isset($expense['date']) && !empty($expense['date'])
            ? date('F j, Y', strtotime($expense['date']))
            : date('F j, Y');

        return "Date: {$dateStr}\n";
    }

    private function formatCategorySection(array $expense): string
    {
        return !empty($expense['category_name'])
            ? "Category: {$expense['category_name']}\n"
            : "";
    }

    private function formatDescriptionSection(array $expense): string
    {
        return !empty($expense['description'])
            ? "Description: {$expense['description']}\n"
            : "";
    }

    private function formatAmountSection(array $expense): string
    {
        $amount = isset($expense['amount']) ? (float)$expense['amount'] : 0.00;
        return "TOTAL: $" . number_format($amount, 2) . "\n";
    }


    private function getReceiptFooterText(): string
    {
        return "========================================\n\n" .
               "Thank you for your business!\n";
    }


    private function createSlug(string $text): string
    {
        $slug = preg_replace('/[^A-Za-z0-9]+/', '_', $text);
        $slug = strtolower(trim($slug, '_'));

        return substr($slug, 0, 30) ?: 'expense';
    }


    public function outputDirectReceipt(int $expenseId, ?int $userId = null, string $format = 'text'): void
    {
        $receipt = $this->generateFallbackReceipt($expenseId);
        $filename = $this->buildBasicFilename($expenseId);

        $this->_sendReceiptToClient($receipt, $filename, $format);
    }

    /**
     * Send a receipt to the client in the specified format
     */
    public function sendReceiptToClient(string $receipt, string $filename, string $format = 'text'): void
    {
        if ($format === 'pdf') {
            $this->sendPdfReceipt($receipt, $filename);
        } else {
            // Default to text format
            header('Content-Type: text/plain');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            echo $receipt;
            exit;
        }
    }

    // This is a private helper method for the public sendReceiptToClient
    private function _sendReceiptToClient(string $receipt, string $filename, string $format = 'text'): void
    {
        if ($format === 'pdf') {
            $this->sendPdfReceipt($receipt, $filename);
        } else {
            // Default to text format
            header('Content-Type: text/plain');
            header('Content-Disposition: attachment; filename="' . $filename . '"');
            echo $receipt;
            exit;
        }
    }

    /**
     * Generate and send a PDF receipt to the client
     */
    private function sendPdfReceipt(string $receiptText, string $filename): void
    {
        // Convert text filename to PDF
        $filename = str_replace('.txt', '.pdf', $filename);

        // Create a simple HTML version of the receipt
        $html = $this->generateHtmlReceipt($receiptText);

        // Use PHP's built-in output buffering to capture the HTML
        ob_start();
        echo $html;
        $content = ob_get_clean();

        // Set header for inline display (not attachment)
        header('Content-Disposition: inline; filename="' . $filename . '"');

        // Output the PDF-like HTML directly to the browser
        echo $this->generatePdfFromHtml($content);
        exit;
    }

    /**
     * Generate HTML receipt from plain text
     */
    private function generateHtmlReceipt(string $receiptText): string
    {
        // Convert plain text to HTML with proper formatting
        $formattedText = str_replace(
            ['========================================', '\n'],
            ['<hr class="receipt-divider">', '<br>'],
            htmlspecialchars($receiptText)
        );

        // Use the receipt template
        ob_start();
        $receiptText = $formattedText;
        $title = 'Receipt';
        include __DIR__ . '/../Views/shared/receipt_template.php';
        return ob_get_clean();
    }

    /**
     * Generate a PDF from HTML content
     * This is a simple implementation using HTML to PDF conversion
     */
    private function generatePdfFromHtml(string $html): string
    {
        // Use the PDF receipt template
        ob_start();
        $receiptContent = $html;
        $title = 'Receipt PDF';
        include __DIR__ . '/../Views/shared/receipt_pdf_template.php';
        $printableHtml = ob_get_clean();

        // Change the content type header to HTML
        header('Content-Type: text/html');

        return $printableHtml;
    }
}
