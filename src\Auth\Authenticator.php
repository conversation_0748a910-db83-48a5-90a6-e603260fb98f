<?php

declare(strict_types=1);

namespace App\Auth;

use App\Models\User;
use App\Core\Security\Session;
use App\Exceptions\AuthenticationException;
use App\Exceptions\AuthorizationException;

final class Authenticator
{
    private const SESSION_USER_ID = 'user_id';
    private const SESSION_USER_EMAIL = 'user_email';
    private const SESSION_USER_NAME = 'user_name';
    private const SESSION_USER_STATUS = 'user_status';
    private const SESSION_TOKEN_EXPIRY = 'token_expiry';
    private const SESSION_IP = 'user_ip';
    private const TOKEN_LIFETIME = 7200; // 2 hours
    private const REMEMBER_COOKIE_NAME = 'remember_token';
    private const REMEMBER_COOKIE_DAYS = 30;

    public function attemptLogin(string $email, string $password, bool $remember = false): bool
    {
        $user = User::findByEmail($email);

        if (
            !$user ||
            !password_verify($password, $user['password']) ||
            $user['user_status_id'] !== User::STATUS_ACTIVE
        ) {
            return false;
        }

        $this->startSession($user);

        if ($remember) {
            $this->createRememberToken($user['id']);
        }

        return true;
    }

    public function register(string $name, string $email, string $password): bool
    {
        return User::registerUser($name, $email, $password);
    }

    public function logout(): void
    {
        // Delete remember-me token if it exists
        if (isset($_COOKIE[self::REMEMBER_COOKIE_NAME])) {
            $token = $_COOKIE[self::REMEMBER_COOKIE_NAME];
            User::deleteRememberToken($token);

            // Clear the cookie
            setcookie(
                self::REMEMBER_COOKIE_NAME,
                '',
                [
                    'expires' => time() - 3600,
                    'path' => '/',
                    'domain' => '',
                    'secure' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on',
                    'httponly' => true,
                    'samesite' => 'Lax'
                ]
            );
        }

        // Clear session and cookies
        session_unset();
        session_destroy();
        $this->clearSessionCookie();
    }

    public function isAuthenticated(): bool
    {
        // First check if user ID exists in session and session is valid
        if (!isset($_SESSION[self::SESSION_USER_ID]) || !$this->validateSession()) {
            // If not authenticated via session, try remember-me cookie
            if (isset($_COOKIE[self::REMEMBER_COOKIE_NAME])) {
                error_log("No valid session, but remember token found, attempting authentication");
                return $this->attemptRememberMeAuthentication();
            }
            error_log("No valid session or remember token, not authenticated");
            return false;
        }

        // For non-persistent sessions (no remember me), check for session timeout
        if (!isset($_SESSION['remember_me']) || $_SESSION['remember_me'] === false) {
            error_log("Non-persistent session detected, checking for timeout");
            $lastActivity = $_SESSION['last_activity'] ?? 0;
            $timeSinceLastActivity = time() - $lastActivity;
            $sessionTimeout = 1800; // 30 minutes

            // Session timeout after 30 minutes of inactivity
            if ($timeSinceLastActivity > $sessionTimeout) {
                error_log("Session timeout detected ({$timeSinceLastActivity}s > {$sessionTimeout}s), logging out");
                $this->logout();
                return false;
            }

            // Update last activity time
            $_SESSION['last_activity'] = time();
            error_log("Updated last activity time for non-persistent session");
        } else {
            error_log("Persistent session detected (remember_me=true), no timeout check needed");
        }

        return true;
    }

    public function getCurrentUserId(): ?int
    {
        return $this->isAuthenticated() ? (int)$_SESSION[self::SESSION_USER_ID] : null;
    }

    public function getCurrentUser(): ?array
    {
        $userId = $this->getCurrentUserId();
        return $userId ? User::findById($userId) : null;
    }

    public function requireAuthentication(): void
    {
        if (!$this->isAuthenticated()) {
            throw new AuthenticationException('Authentication required');
        }
    }

    public function requireStatus(int $statusId): void
    {
        $this->requireAuthentication();
        if ($_SESSION[self::SESSION_USER_STATUS] !== $statusId) {
            throw new AuthorizationException('Account status insufficient');
        }
    }

    public function requestPasswordReset(string $email): bool
    {
        $user = User::findByEmail($email);
        if (!$user) {
            return false;
        }

        $token = bin2hex(random_bytes(32));
        return User::storeResetToken($email, $token);
    }

    public function validateResetToken(string $token): ?array
    {
        return User::findByResetToken($token);
    }

    public function resetPassword(int $userId, string $password): bool
    {
        return User::updatePassword($userId, $password);
    }

    private function startSession(array $user): void
    {
        session_regenerate_id(true);
        $_SESSION[self::SESSION_USER_ID] = $user['id'];
        $_SESSION[self::SESSION_USER_EMAIL] = $user['email'];
        $_SESSION[self::SESSION_USER_NAME] = $user['name'];
        $_SESSION[self::SESSION_USER_STATUS] = $user['user_status_id'];
        $_SESSION[self::SESSION_TOKEN_EXPIRY] = time() + self::TOKEN_LIFETIME;
        $_SESSION[self::SESSION_IP] = $_SERVER['REMOTE_ADDR'];

        // Set the last activity timestamp for session expiration tracking
        $_SESSION['last_activity'] = time();

        // Set a timestamp to identify when this session was created
        $_SESSION['session_start_time'] = time();

        // Only set remember_me to true if we're authenticating via a remember token
        // and there's no explicit remember_me flag already set
        if (!isset($_SESSION['remember_me']) && isset($_COOKIE[self::REMEMBER_COOKIE_NAME])) {
            $_SESSION['remember_me'] = true;
        }
    }

    private function validateSession(): bool
    {
        // Check if token has expired
        if (!isset($_SESSION[self::SESSION_TOKEN_EXPIRY]) || $_SESSION[self::SESSION_TOKEN_EXPIRY] < time()) {
            $this->logout();
            return false;
        }

        // For non-persistent sessions (no remember me), initialize or update activity time
        $nonPersistentSession = isset($_SESSION['remember_me']) && $_SESSION['remember_me'] === false;
        if ($nonPersistentSession && !isset($_SESSION['last_activity'])) {
            $_SESSION['last_activity'] = time();
        }

        if (isset($_SESSION[self::SESSION_IP])) {
            $sessionIp = $_SESSION[self::SESSION_IP];
            $currentIp = $_SERVER['REMOTE_ADDR'];
            $sessionIpParts = explode('.', $sessionIp);
            $currentIpParts = explode('.', $currentIp);

            if (count($sessionIpParts) === 4 && count($currentIpParts) === 4) {
                $sessionIpNetwork = "{$sessionIpParts[0]}.{$sessionIpParts[1]}.{$sessionIpParts[2]}";
                $currentIpNetwork = "{$currentIpParts[0]}.{$currentIpParts[1]}.{$currentIpParts[2]}";

                if ($sessionIpNetwork !== $currentIpNetwork) {
                    $this->logout();
                    return false;
                }
            } elseif ($sessionIp !== $currentIp) {
                $this->logout();
                return false;
            }
        }

        $user = User::findById((int)$_SESSION[self::SESSION_USER_ID]);
        if (!$user || $user['user_status_id'] !== User::STATUS_ACTIVE) {
            $this->logout();
            return false;
        }

        $this->refreshToken();
        return true;
    }

    private function refreshToken(): void
    {
        $_SESSION[self::SESSION_TOKEN_EXPIRY] = time() + self::TOKEN_LIFETIME;
    }

    private function clearSessionCookie(): void
    {
        // Clear session cookie
        if (isset($_COOKIE[session_name()])) {
            $params = session_get_cookie_params();
            setcookie(
                session_name(),
                '',
                [
                    'expires' => time() - 3600,
                    'path' => $params['path'],
                    'domain' => $params['domain'],
                    'secure' => $params['secure'],
                    'httponly' => $params['httponly'],
                    'samesite' => $params['samesite'] ?? 'Lax'
                ]
            );
        }

        // Also clear remember-me cookie
        $this->clearRememberMeCookie();
    }

    /**
     * Create a remember-me token for the user
     *
     * @param int $userId The user ID
     * @return void
     */
    private function createRememberToken(int $userId): void
    {
        $tokenData = User::createRememberToken($userId, self::REMEMBER_COOKIE_DAYS);

        // Calculate expiry in seconds from now
        $expiry = strtotime($tokenData['expires_at']) - time();

        // Use Session class to set the cookie
        Session::setRememberCookie(
            self::REMEMBER_COOKIE_NAME,
            $tokenData['token'],
            $expiry
        );
    }

    /**
     * Attempt to authenticate using remember-me token
     *
     * @return bool True if authentication was successful
     */
    public function attemptRememberMeAuthentication(): bool
    {
        if (!isset($_COOKIE[self::REMEMBER_COOKIE_NAME])) {
            return false;
        }

        $token = $_COOKIE[self::REMEMBER_COOKIE_NAME];

        $tokenData = User::findRememberToken($token);

        if (!$tokenData) {
            $this->clearRememberMeCookie();
            return false;
        }

        $user = User::findById($tokenData['user_id']);

        if (!$user || $user['user_status_id'] !== User::STATUS_ACTIVE) {
            $this->clearRememberMeCookie();
            User::deleteRememberToken($token);
            return false;
        }

        // Start session for the user
        $this->startSession($user);

        // Explicitly set remember_me flag to true since we're authenticating via remember token
        $_SESSION['remember_me'] = true;

        // Rotate the remember-me token for security
        $this->rotateRememberToken($tokenData['user_id'], $token);

        // Update the session cookie to persist
        $params = session_get_cookie_params();
        $expiryTime = time() + 86400 * self::REMEMBER_COOKIE_DAYS;
        setcookie(
            session_name(),
            session_id(),
            [
                'expires' => $expiryTime,
                'path' => $params['path'],
                'domain' => $params['domain'],
                'secure' => $params['secure'],
                'httponly' => $params['httponly'],
                'samesite' => $params['samesite'] ?? 'Lax'
            ]
        );
        error_log("Updated session cookie to persist for " . self::REMEMBER_COOKIE_DAYS . " days");

        return true;
    }

    /**
     * Rotate the remember-me token for security
     *
     * @param int $userId The user ID
     * @param string $oldToken The old token to delete
     * @return void
     */
    private function rotateRememberToken(int $userId, string $oldToken): void
    {
        // Delete the old token
        User::deleteRememberToken($oldToken);

        // Create a new token
        $this->createRememberToken($userId);
    }

    /**
     * Clear the remember-me cookie
     *
     * @return void
     */
    private function clearRememberMeCookie(): void
    {
        // Use Session class to delete the cookie
        Session::deleteRememberCookie(self::REMEMBER_COOKIE_NAME);
    }
}
