<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PET - Personal Expense Tracker</title>
    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
:root {
    --primary-hue: 216;
    --primary-color: hsl(var(--primary-hue), 59%, 52%);
    --primary-light: hsl(var(--primary-hue), 72%, 61%);
    --primary-dark: hsl(var(--primary-hue), 59%, 40%);
    --accent-color: hsl(187, 100%, 68%);
    --dark-color: hsl(210, 29%, 24%);
    --light-color: hsl(210, 17%, 98%);
    --text-color: hsl(0, 0%, 20%);
    --font-main: 'Nunito', sans-serif;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    overflow: hidden;
    width: 100%;
}

body {
    font-family: var(--font-main);
    background: #000;
    color: white;
    line-height: 1.6;
    display: flex;
    flex-direction: column;
    position: relative;
    margin: 0;
    padding: 0;
}

/* Background Gradient */
.bg-gradient {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: linear-gradient(to bottom, #0a0a1e 0%, #1e1e32 100%);
    will-change: transform;
}

/* Stars */
.stars {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
    opacity: 1;
}

.star {
    position: absolute;
    width: 2px;
    height: 2px;
    background-color: white;
    border-radius: 50%;
    opacity: 0.8;
    animation: twinkle 4s infinite;
}

@keyframes twinkle {
    0%, 100% { opacity: 0.2; transform: scale(0.8); }
    50% { opacity: 1; transform: scale(1); }
}

/* Interactive Background with Coins */
.background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 10;
}

.coin-container {
    position: absolute;
    cursor: pointer;
    z-index: 20;
    transform: translate3d(0,0,0);
    will-change: transform;
    pointer-events: auto;
}

.floating-coin {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #ffd700, #daa520);
    border-radius: 50%;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
    opacity: 0.9;
    transition: all 0.3s ease;
    will-change: transform;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    backface-visibility: visible;
    transform-style: preserve-3d;
}

.coin-container:hover .floating-coin,
.coin-container:focus .floating-coin {
    opacity: 1;
    transform: scale(1.1);
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.8);
}

.feature-popup {
    position: absolute;
    background: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 15px;
    border-radius: 12px;
    width: 250px;
    opacity: 0;
    pointer-events: none;
    transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
    z-index: 1000;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.6);
    white-space: normal;
    bottom: 70px;
    left: 50%;
    transform: translateX(-50%) scale(0);
}

.feature-popup h4 {
    color: var(--accent-color);
    margin-bottom: 8px;
    font-size: 1.1rem;
}

.feature-popup p {
    font-size: 0.9rem;
    opacity: 0.9;
    line-height: 1.5;
}

/* Main Content */
.container {
    max-width: 1200px;
    width: 100%;
    margin: 0 auto;
    position: relative;
    z-index: 5;
    flex: 1;
    display: flex;
    flex-direction: column;
    height: 100%;
}

header {
    padding-top: 2rem;
    margin-bottom: 1rem;
    text-align: center;
}

.logo {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    letter-spacing: -1px;
    background: linear-gradient(135deg, var(--accent-color), var(--primary-light));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.tagline {
    font-size: 0.9rem;
    opacity: 0.8;
    max-width: 600px;
    margin: 0 auto 1rem;
}

/* Hero Section */
.hero {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    position: relative;
    padding: 0 2rem;
}

.hero h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    line-height: 1.2;
    background: linear-gradient(135deg, #ffffff, #aaaaaa);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.highlight {
    color: var(--accent-color);
}

.hero p {
    font-size: 1.25rem;
    margin-bottom: 2rem;
    max-width: 700px;
    opacity: 0.9;
}

.buttons {
    display: flex;
    gap: 1.5rem;
    margin-top: 1rem;
    flex-wrap: wrap;
    justify-content: center;
}

.btn {
    display: inline-block;
    padding: 0.8rem 1.5rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 700;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    box-shadow: 0 4px 15px rgba(58, 123, 213, 0.4);
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid rgba(255,255,255,0.3);
}

/* Accessibility improvements */
.btn:focus, .coin-container:focus {
    outline: 2px solid var(--accent-color);
    outline-offset: 2px;
}

/* Footer */
footer {
    text-align: center;
    padding: 1rem 0;
    opacity: 0.7;
    font-size: 0.9rem;
}

/* Responsive */
@media (max-width: 768px) {
    .hero h1 {
        font-size: 2rem;
    }
    
    .hero p {
        font-size: 1rem;
    }
    
    .buttons {
        flex-direction: column;
        gap: 1rem;
    }
    
    .tagline {
        font-size: 0.9rem;
    }

    .feature-popup {
        width: 200px;
        font-size: 0.9rem;
    }
    
    /* Optimize coin size for mobile */
    .floating-coin {
        width: 32px;
        height: 32px;
        font-size: 14px;
    }
}

/* For very small screens */
@media (max-width: 480px) {
    .hero h1 {
        font-size: 1.8rem;
    }
    
    .floating-coin {
        width: 28px;
        height: 28px;
        font-size: 12px;
    }
    
    .feature-popup {
        width: 180px;
        padding: 10px;
    }
    
    .feature-popup h4 {
        font-size: 0.9rem;
    }
    
    .feature-popup p {
        font-size: 0.8rem;
    }
}

/* Print styles */
@media print {
    .stars, .background, .coin-container {
        display: none;
    }
    
    body, .bg-gradient {
        background: white !important;
        color: black !important;
    }
    
    .hero h1, .logo {
        background: none !important;
        -webkit-text-fill-color: black !important;
        color: black !important;
    }
}
    </style>
</head>
<body>
    <!-- Static background gradient -->
    <div class="bg-gradient" id="bgGradient"></div>
    
    <!-- Stars background -->
    <div class="stars" id="starsContainer"></div>

    <!-- Separate interactive layer for coins -->
    <div class="background">
        <!-- Dynamically generated floating coins using JavaScript -->
    </div>

    <div class="container">
        <header>
            <div class="logo">PET</div>
            <p class="tagline">A new way to understand your spending habits</p>
        </header>

        <section class="hero">
            <h1>Transform Your <span class="highlight">Financial Journey</span></h1>
            <p>PET empowers you to track expenses, visualize spending patterns, and take control of your financial future with an intuitive, artistic interface.</p>
            
            <div class="buttons">
                <a href="/login" class="btn btn-primary">Get Started</a>
                <a href="/how" class="btn btn-secondary">How It Works</a>
            </div>
        </section>

        <footer>
            <p>Personal Expense Tracker &copy; 2025</p>
        </footer>
    </div>

    <script>
// Feature data for coins
const features = [
    {
        title: "Expense Tracking",
        description: "Effortlessly record and categorize your daily expenses. Add, edit, and organize with just a few clicks.",
        icon: "📊"
    },
    {
        title: "Receipt Storage",
        description: "Upload and store digital copies of your receipts, making tax season and expense validation simple.",
        icon: "📁"
    },
    {
        title: "Visual Reports",
        description: "Transform your spending data into beautiful, insightful visual reports that help you understand your habits.",
        icon: "📈"
    },
    {
        title: "Secure Access",
        description: "Your financial data stays protected with our secure authentication system and data encryption.",
        icon: "🔒"
    },
    {
        title: "Budget Planning",
        description: "Set spending limits and track your progress throughout the month to stay on top of your financial goals.",
        icon: "💰"
    },
    {
        title: "Financial Goals",
        description: "Define saving targets and monitor your progress with motivating visual feedback.",
        icon: "🎯"
    },
    {
        title: "Smart Alerts",
        description: "Receive notifications about unusual spending patterns or when you're approaching budget limits.",
        icon: "🔔"
    },
    {
        title: "Multi-Currency",
        description: "Track expenses in different currencies with automatic conversion to your primary currency.",
        icon: "💱"
    }
];

// Physics parameters
const PHYSICS = {
    gravity: 0.15 + (Math.random() * 0.1),
    bounce: 0.65 + (Math.random() * 0.15),
    damping: 0.97 + (Math.random() * 0.02),
    terminalVelocity: 12
};

// Animation timing
const ANIMATION_DURATION = 40000; // 1 minute in milliseconds

// Application state
const PET = {
    animations: new Map(), // Store animation IDs and cleanup functions
    isInitialized: false,
    resizeTimer: null,
    animationTimeout: null,
    
    // Initialize the application
    init() {
        if (this.isInitialized) return;
        
        this.initBackground();
        this.createStars();
        this.createFallingCoins();
        
        // Add event listeners
        window.addEventListener('resize', this.handleResize.bind(this));
        window.addEventListener('keydown', this.handleKeyPress.bind(this));
        
        // Set timeout to stop animations after 1 minute
        this.animationTimeout = setTimeout(() => {
            this.stopAnimations();
        }, ANIMATION_DURATION);
        
        this.isInitialized = true;
    },
    
    // Stop all animations after timeout
    stopAnimations() {
        // Stop coin animations but keep them visible
        this.animations.forEach((animInfo, container) => {
            if (animInfo.cleanup) {
                animInfo.cleanup();
            }
            // Keep coins visible but static
            container.style.animation = 'none';
            container.querySelector('.floating-coin').style.animation = 'none';
        });
        
        // Stop star animations but keep them visible
        const stars = document.querySelectorAll('.star');
        stars.forEach(star => {
            star.style.animation = 'none';
        });
    },
    
    // Initialize background gradient based on time of day
    initBackground() {
        const bgGradient = document.getElementById('bgGradient');
        
        // Use a time-based gradient (morning to night)
        const hour = new Date().getHours();
        const timeNormalized = (hour % 24) / 24; // 0-1 range
        
        // Night sky colors (deep blue)
        const nightSkyStart = 'rgba(10, 10, 30, 1)';
        const nightSkyEnd = 'rgba(30, 30, 50, 1)';
        
        // Day sky colors (light blue)
        const daySkyStart = 'rgba(100, 150, 255, 1)';
        const daySkyEnd = 'rgba(180, 220, 255, 1)';
        
        // Interpolate between night and day colors
        const skyStart = this.interpolateColor(nightSkyStart, daySkyStart, timeNormalized);
        const skyEnd = this.interpolateColor(nightSkyEnd, daySkyEnd, timeNormalized);
        
        // Set background gradient
        bgGradient.style.background = `linear-gradient(to bottom, ${skyStart} 0%, ${skyEnd} 100%)`;
        
        // Set stars visibility (more visible at night)
        const starsContainer = document.getElementById('starsContainer');
        starsContainer.style.opacity = 1 - timeNormalized;
    },
    
    // Color interpolation helper
    interpolateColor(color1, color2, factor) {
        // Parse colors from rgba
        const parseColor = (color) => {
            const match = color.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*([0-9.]+))?\)/);
            return {
                r: parseInt(match[1], 10),
                g: parseInt(match[2], 10),
                b: parseInt(match[3], 10),
                a: match[4] ? parseFloat(match[4]) : 1
            };
        };
        
        const c1 = parseColor(color1);
        const c2 = parseColor(color2);
        
        // RGB interpolation
        const r = Math.round(c1.r + factor * (c2.r - c1.r));
        const g = Math.round(c1.g + factor * (c2.g - c1.g));
        const b = Math.round(c1.b + factor * (c2.b - c1.b));
        const a = c1.a + factor * (c2.a - c1.a);
        
        return `rgba(${r}, ${g}, ${b}, ${a})`;
    },
    
    // Generate stars with improved distribution
    createStars() {
        const starsContainer = document.getElementById('starsContainer');
        const numStars = Math.min(200, Math.floor(window.innerWidth * window.innerHeight / 2000));
        
        // Clear existing stars
        starsContainer.innerHTML = '';
        
        // Use document fragment for better performance
        const fragment = document.createDocumentFragment();
        
        // Golden ratio for better distribution
        const phi = 0.618033988749895;
        
        for (let i = 0; i < numStars; i++) {
            const star = document.createElement('div');
            star.classList.add('star');
            
            // Use Fibonacci lattice distribution for more natural look
            let x = (i * phi) % 1;
            let y = ((i + 0.5) * phi) % 1;
            
            // Add subtle randomness
            x = (x + Math.random() * 0.05) % 1;
            y = (y + Math.random() * 0.05) % 1;
            
            star.style.left = `${x * 100}%`;
            star.style.top = `${y * 100}%`;
            
            // Random size with better distribution
            const sizeClass = Math.random();
            const size = sizeClass < 0.7 ? 1 : sizeClass < 0.9 ? 2 : 3;
            star.style.width = `${size}px`;
            star.style.height = `${size}px`;
            
            // Adjust animation timing for more natural twinkling
            const twinkleSpeed = size === 1 ? 4 : size === 2 ? 3 : 2;
            star.style.animationDuration = `${twinkleSpeed + Math.random() * 2}s`;
            star.style.animationDelay = `${Math.random() * 4}s`;
            
            fragment.appendChild(star);
        }
        
        starsContainer.appendChild(fragment);
    },
    
    // Create falling coins with feature popups
    createFallingCoins() {
        const background = document.querySelector('.background');
        const numberOfCoins = Math.min(8, features.length);
        
        // Clean up existing coins
        this.cleanupCoins();
        
        // Get viewport dimensions
        const vw = Math.max(document.documentElement.clientWidth || 0, window.innerWidth || 0);
        const vh = Math.max(document.documentElement.clientHeight || 0, window.innerHeight || 0);
        
        // Edge padding to keep coins visible
        const edgePadding = Math.min(50, vw * 0.05);
        const usableWidth = vw - (edgePadding * 2);
        
        // Create coins spread across viewport
        for (let i = 0; i < numberOfCoins; i++) {
            // Create container and set attributes for accessibility
            const container = document.createElement('div');
            container.classList.add('coin-container');
            container.tabIndex = 0; // Make focusable
            container.setAttribute('role', 'button');
            container.setAttribute('aria-label', `${features[i].title} Feature`);
            
            // Create the coin element
            const coin = document.createElement('div');
            coin.classList.add('floating-coin');
            coin.textContent = features[i].icon;
            coin.setAttribute('aria-hidden', 'true');
            
            // Calculate responsive position
            const segmentWidth = usableWidth / numberOfCoins;
            const posX = edgePadding + (i * segmentWidth) + (segmentWidth / 2);
            const posY = -50; // Start above viewport
            
            // Set initial position
            container.style.transform = `translate3d(${posX}px, ${posY}px, 0)`;
            
            // Size based on viewport
            const baseSize = Math.min(35, vw * 0.025);
            const size = baseSize + Math.random() * 15;
            coin.style.width = `${size}px`;
            coin.style.height = `${size}px`;
            coin.style.fontSize = `${size * 0.7}px`;
            
            // Add feature popup
            const popup = document.createElement('div');
            popup.classList.add('feature-popup');
            popup.id = `feature-popup-${i}`;
            popup.setAttribute('role', 'tooltip');
            popup.setAttribute('aria-hidden', 'true');
            
            const title = document.createElement('h4');
            title.textContent = features[i].title;
            
            const description = document.createElement('p');
            description.textContent = features[i].description;
            
            popup.appendChild(title);
            popup.appendChild(description);
            
            // Connect popup for accessibility
            container.setAttribute('aria-describedby', popup.id);
            
            // Append elements
            container.appendChild(coin);
            container.appendChild(popup);
            background.appendChild(container);
            
            // Add event listeners
            container.addEventListener('mouseenter', this.handleCoinHover.bind(this));
            container.addEventListener('mouseleave', this.handleCoinLeave.bind(this));
            container.addEventListener('focus', this.handleCoinHover.bind(this));
            container.addEventListener('blur', this.handleCoinLeave.bind(this));
            
            // Start animation with delay
            const delay = i * 1000 + Math.random() * 9000;
            setTimeout(() => {
                const cleanup = this.animateCoin(container, posX, posY, size);
                // Store animation info for cleanup
                this.animations.set(container, cleanup);
            }, delay);
        }
    },
    
    // Animation for coin falling
    animateCoin(container, startX, startY, coinSize) {
        const coin = container.querySelector('.floating-coin');
        
        // Physics state
        const state = {
            posX: startX,
            posY: startY,
            velocityX: (Math.random() - 0.5) * 1.2,
            velocityY: 1 + Math.random() * 0.8,
            rotationX: Math.random() * 360,
            rotationY: Math.random() * 360,
            rotationZ: Math.random() * 360,
            rotationSpeedX: (Math.random() - 0.5) * 3,
            rotationSpeedY: (Math.random() - 0.5) * 3,
            rotationSpeedZ: (Math.random() - 0.5) * 1,
            lastTime: performance.now(),
            isHovering: false,
            isResting: false,
            restCounter: 0,
            animationId: null
        };
        
        // Animation update function
        function update(currentTime) {
            // Calculate delta time for frame-rate independent physics
            const deltaTime = Math.min(currentTime - state.lastTime, 32);
            const timeMultiplier = deltaTime / 16.667;
            state.lastTime = currentTime;
            
            // Only update if not hovering
            if (!state.isHovering && !state.isResting) {
                // Apply gravity with terminal velocity
                state.velocityY = Math.min(state.velocityY + PHYSICS.gravity * timeMultiplier, PHYSICS.terminalVelocity);
                
                // Apply air resistance (subtle)
                state.velocityX *= 0.99;
                
                // Update position
                state.posX += state.velocityX * timeMultiplier;
                state.posY += state.velocityY * timeMultiplier;
                
                // Update rotation for more natural movement
                state.rotationX += state.rotationSpeedX * timeMultiplier;
                state.rotationY += state.rotationSpeedY * timeMultiplier;
                state.rotationZ += state.rotationSpeedZ * timeMultiplier;
                
                // Get viewport dimensions
                const viewportWidth = window.innerWidth;
                const viewportHeight = window.innerHeight;
                
                // Boundary checks with bounce
                if (state.posX < coinSize/2) {
                    state.posX = coinSize/2;
                    state.velocityX = Math.abs(state.velocityX) * PHYSICS.bounce;
                    state.rotationSpeedX *= -0.8;
                } else if (state.posX > viewportWidth - coinSize/2) {
                    state.posX = viewportWidth - coinSize/2;
                    state.velocityX = -Math.abs(state.velocityX) * PHYSICS.bounce;
                    state.rotationSpeedX *= -0.8;
                }
                
                // Bottom bounce
                if (state.posY > viewportHeight - coinSize) {
                    state.posY = viewportHeight - coinSize;
                    
                    // Only bounce if velocity is significant
                    if (Math.abs(state.velocityY) > 0.5) {
                        state.velocityY = -Math.abs(state.velocityY) * PHYSICS.bounce;
                        
                        // Apply damping after bounce
                        state.velocityX *= PHYSICS.damping;
                        state.velocityY *= PHYSICS.damping;
                        
                        // Adjust rotation speeds on bounce
                        state.rotationSpeedX *= PHYSICS.damping;
                        state.rotationSpeedY *= PHYSICS.damping;
                        state.rotationSpeedZ *= PHYSICS.damping;
                    } else {
                        // Stop vertical movement if very slow
                        state.velocityY = 0;
                        
                        state.isResting = true;
                    }
                }
                
                // Apply movement
                container.style.transform = `translate3d(${state.posX}px, ${state.posY}px, 0)`;
                coin.style.transform = `rotateX(${state.rotationX}deg) rotateY(${state.rotationY}deg) rotateZ(${state.rotationZ}deg)`;
            }
            
            // Continue animation unless at rest
            if (!state.isResting) {
                state.animationId = requestAnimationFrame(update);
            } else {
                // Apply subtle hover effect when at rest
                const hoverEffect = () => {
                    if (!state.isHovering) {
                        const t = Date.now() / 2000;
                        const floatY = Math.sin(t) * 5;
                        container.style.transform = `translate3d(${state.posX}px, ${state.posY + floatY}px, 0)`;
                        state.animationId = requestAnimationFrame(hoverEffect);
                    }
                };
                state.animationId = requestAnimationFrame(hoverEffect);
            }
        }
        
        // Start animation
        state.animationId = requestAnimationFrame(update);
        
        // Return cleanup function
        return {
            state: state,
            cleanup: () => {
                cancelAnimationFrame(state.animationId);
                state.animationId = null;
            }
        };
    },
    
    // Event handler for coin hover
    handleCoinHover(event) {
        const container = event.currentTarget;
        const coin = container.querySelector('.floating-coin');
        const popup = container.querySelector('.feature-popup');
        
        // Mark as hovering to pause physics
        const animInfo = this.animations.get(container);
        if (animInfo) {
            animInfo.state.isHovering = true;
        }
        
        // Show popup with improved positioning
        if (popup) {
            popup.style.opacity = '1';
            popup.style.transform = 'translateX(-50%) scale(1)';
            popup.setAttribute('aria-hidden', 'false');
            
            // Adjust popup position for viewport visibility
            setTimeout(() => {
                const containerRect = container.getBoundingClientRect();
                const popupRect = popup.getBoundingClientRect();
                const viewportWidth = window.innerWidth;
                const viewportHeight = window.innerHeight;
                
                // Check horizontal boundaries
                if (popupRect.right > viewportWidth - 10) {
                    popup.style.left = 'auto';
                    popup.style.right = '0';
                    popup.style.transform = 'translateX(0) scale(1)';
                } else if (popupRect.left < 10) {
                    popup.style.left = '0';
                    popup.style.transform = 'translateX(0) scale(1)';
                }
                
                // Check vertical boundaries
                if (containerRect.top - popupRect.height < 10) {
                    popup.style.bottom = 'auto';
                    popup.style.top = '100%';
                    popup.style.marginTop = '10px';
                } else if (containerRect.bottom + popupRect.height > viewportHeight - 10) {
                    popup.style.top = 'auto';
                    popup.style.bottom = '100%';
                    popup.style.marginBottom = '10px';
                }
            }, 10);
        }
        
        // Enhance coin appearance
        coin.style.transform = 'scale(1.1)';
        coin.style.boxShadow = '0 0 15px rgba(255, 215, 0, 0.8)';
    },
    
    // Event handler for coin leave
    handleCoinLeave(event) {
        const container = event.currentTarget;
        const coin = container.querySelector('.floating-coin');
        const popup = container.querySelector('.feature-popup');
        
        // Resume physics
        const animInfo = this.animations.get(container);
        if (animInfo) {
            animInfo.state.isHovering = false;
        }
        
        // Hide popup
        if (popup) {
            popup.style.opacity = '0';
            popup.style.transform = 'translateX(-50%) scale(0.8)';
            popup.setAttribute('aria-hidden', 'true');
        }
        
        // Reset coin appearance
        coin.style.transform = '';
        coin.style.boxShadow = '';
    },
    
    // Handle keyboard interaction
    handleKeyPress(event) {
        if (event.key === 'Escape') {
            // Close all popups when ESC is pressed
            document.querySelectorAll('.feature-popup').forEach(popup => {
                popup.style.opacity = '0';
                popup.style.transform = 'translateX(-50%) scale(0.8)';
                popup.setAttribute('aria-hidden', 'true');
            });
        }
    },
    
    // Debounced resize handler
    handleResize() {
        clearTimeout(this.resizeTimer);
        this.resizeTimer = setTimeout(() => {
            this.createStars();
            this.createFallingCoins();
        }, 3000);
    },
    
    // Clean up animations
    cleanupCoins() {
        document.querySelectorAll('.coin-container').forEach(container => {
            // Get cleanup function
            const cleanup = this.animations.get(container);
            if (cleanup && typeof cleanup.cleanup === 'function') {
                cleanup.cleanup();
            }
            
            // Remove event listeners
            container.removeEventListener('mouseenter', this.handleCoinHover);
            container.removeEventListener('mouseleave', this.handleCoinLeave);
            container.removeEventListener('focus', this.handleCoinHover);
            container.removeEventListener('blur', this.handleCoinLeave);
            
            // Remove element
            container.remove();
        });
        
        // Clear animations map
        this.animations.clear();
    }
};

// Initialize on document ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize the application
    PET.init();
    
    // Preload relevant fonts and assets
    if ('fonts' in document) {
        Promise.all([
            document.fonts.load('400 1em Nunito'),
            document.fonts.load('700 1em Nunito')
        ]).then(() => {
            // Fonts loaded
        });
    }
});
    </script>
</body>
</html>