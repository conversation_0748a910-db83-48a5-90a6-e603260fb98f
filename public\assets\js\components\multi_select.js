/*
 * Module to manage multi-select operations in expense table.
 */
(function () {
    const SELECT_MODE_CLASS = 'select-mode';
    const TABLE_SELECTOR = '.modern-table';
    const BULK_DELETE_URL = '/expenses/bulk-delete';

    let tableContainer;
    let table;
    let headerCheckbox;
    let rowSelectors = [];
    let selectedIds = new Set();
    let toolbar;

    function init()
    {
        // Initialize immediately if DOM is already loaded
        if (document.readyState === 'complete' || document.readyState === 'interactive') {
            initializeModule();
        } else {
            // Otherwise wait for DOMContentLoaded
            document.addEventListener('DOMContentLoaded', initializeModule);
        }
    }

    function initializeModule()
    {
        tableContainer = document.querySelector('.modern-table, .table');
        if (!tableContainer) {
            return;
        }

        table = tableContainer.querySelector('table');

        // Set up event listeners
        document.addEventListener('expense:enter-select', (e) => {
            const expenseId = e.detail && e.detail.expenseId;
            enterSelectMode(expenseId);
        });

        document.addEventListener('keydown', onKeyDown);
    }

    function enterSelectMode(expenseId)
    {
        const alreadyInSelectMode = tableContainer.classList.contains(SELECT_MODE_CLASS);

        if (!alreadyInSelectMode) {
            tableContainer.classList.add(SELECT_MODE_CLASS);
            injectHeaderCheckbox();
            injectRowCheckboxes();
            injectToolbar();
        }

        // Select the specified expense if provided (whether already in select mode or not)
        if (expenseId) {
            const targetCheckbox = table.querySelector(`input[data - expense - id = "${expenseId}"]`);
            if (targetCheckbox) {
                targetCheckbox.checked = true;
                toggleSelection(expenseId, true);
                updateHeaderState();
            }
        }
    }

    function injectHeaderCheckbox()
    {
        const thead = table.tHead;
        if (!thead) {
            return;
        }

        const headerRow = thead.rows[0];

        // First, check if checkbox already exists but is hidden
        const existingCheckbox = table.querySelector('.select-all');

        if (existingCheckbox) {
            // Clone the checkbox to remove all event listeners
            const newCheckbox = existingCheckbox.cloneNode(true);
            existingCheckbox.parentNode.replaceChild(newCheckbox, existingCheckbox);

            // Make the new checkbox visible and add event listener
            newCheckbox.hidden = false;
            newCheckbox.addEventListener('change', onSelectAllChange);
            headerCheckbox = newCheckbox;
            return;
        }

        // If no existing checkbox, create a new one
        const th = document.createElement('th');
        th.classList.add('selector-column');
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.classList.add('select-all');
        checkbox.setAttribute('aria-label', 'Select all expenses');
        checkbox.addEventListener('change', onSelectAllChange);
        th.appendChild(checkbox);
        headerRow.insertBefore(th, headerRow.firstChild);
        headerCheckbox = checkbox;
    }

    function injectRowCheckboxes()
    {
        const tbody = table.tBodies[0];
        if (!tbody) {
            return;
        }

        // First, check if checkboxes already exist but are hidden
        const existingCheckboxes = table.querySelectorAll('.row-selector');

        if (existingCheckboxes.length > 0) {
            // Use existing checkboxes instead of creating new ones
            existingCheckboxes.forEach(checkbox => {
                // Clone the checkbox to remove all event listeners
                const newCheckbox = checkbox.cloneNode(true);
                checkbox.parentNode.replaceChild(newCheckbox, checkbox);

                // Make the new checkbox visible and add event listener
                newCheckbox.hidden = false;
                newCheckbox.addEventListener('change', onRowSelectorChange);
                rowSelectors.push(newCheckbox);

                // Add row click delegation for existing checkboxes
                const row = newCheckbox.closest('tr');
                if (row) {
                    row.addEventListener('click', function (e) {
                        // Ignore clicks on interactive elements
                        if (e.target.tagName === 'INPUT' ||
                            e.target.tagName === 'BUTTON' ||
                            e.target.tagName === 'A' ||
                            e.target.closest('button') ||
                            e.target.closest('a')) {
                            return;
                        }

                        // Toggle the checkbox state
                        newCheckbox.checked = !newCheckbox.checked;

                        // Dispatch change event to reuse existing logic
                        const changeEvent = new Event('change', { bubbles: true });
                        newCheckbox.dispatchEvent(changeEvent);
                    });
                }
            });

            return;
        }

        // If no existing checkboxes, create new ones
        Array.from(tbody.rows).forEach(row => {
            const td = document.createElement('td');
            td.classList.add('selector-column');
            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.classList.add('row-selector');
            const expenseId = row.getAttribute('data-expense-id');
            checkbox.dataset.expenseId = expenseId;
            checkbox.setAttribute('aria-label', `Select expense ${expenseId}`);
            checkbox.addEventListener('change', onRowSelectorChange);
            td.appendChild(checkbox);
            row.insertBefore(td, row.firstChild);
            rowSelectors.push(checkbox);

            // Add row click delegation
            row.addEventListener('click', function (e) {
                // Ignore clicks on interactive elements
                if (e.target.tagName === 'INPUT' ||
                    e.target.tagName === 'BUTTON' ||
                    e.target.tagName === 'A' ||
                    e.target.closest('button') ||
                    e.target.closest('a')) {
                    return;
                }

                // Toggle the checkbox state
                checkbox.checked = !checkbox.checked;

                // Dispatch change event to reuse existing logic
                const changeEvent = new Event('change', { bubbles: true });
                checkbox.dispatchEvent(changeEvent);
            });
        });
    }

    function injectToolbar()
    {
        // Check if toolbar already exists
        const existingToolbar = document.querySelector('.multi-select-toolbar');
        if (existingToolbar) {
            toolbar = existingToolbar;
            return;
        }

        toolbar = document.createElement('div');
        toolbar.classList.add('multi-select-toolbar');

        // Selection counter
        const selectionCounter = document.createElement('div');
        selectionCounter.classList.add('selection-counter');
        selectionCounter.innerHTML = '<span class="count">0</span> rows selected';
        toolbar.appendChild(selectionCounter);

        // Spacer to push buttons to the right
        const spacer = document.createElement('div');
        spacer.classList.add('toolbar-spacer');
        toolbar.appendChild(spacer);

        // Bulk delete button
        const deleteBtn = document.createElement('button');
        deleteBtn.type = 'button';
        deleteBtn.classList.add('btn', 'bulk-delete');
        deleteBtn.textContent = 'Delete Selected';
        deleteBtn.addEventListener('click', bulkDelete);
        toolbar.appendChild(deleteBtn);

        // Cancel button
        const cancelBtn = document.createElement('button');
        cancelBtn.type = 'button';
        cancelBtn.classList.add('btn', 'cancel-select');
        cancelBtn.textContent = 'Cancel';
        cancelBtn.addEventListener('click', exitSelectMode);
        toolbar.appendChild(cancelBtn);

        // Insert toolbar before the table
        const table = tableContainer.querySelector('table');
        if (table) {
            tableContainer.insertBefore(toolbar, table);
        } else {
            tableContainer.appendChild(toolbar);
        }
    }

    function onSelectAllChange(e)
    {
        const checked = e.target.checked;

        // Update all checkboxes and toggle selection
        rowSelectors.forEach(cb => {
            cb.checked = checked;
            toggleSelection(cb.dataset.expenseId, checked);
        });

        // Update header checkbox state
        updateHeaderState();
    }

    function onRowSelectorChange(e)
    {
        const cb = e.target;
        const id = cb.dataset.expenseId;
        toggleSelection(id, cb.checked);
        updateHeaderState();
    }

    function toggleSelection(id, isSelected)
    {
        // Update the selectedIds Set
        if (isSelected) {
            selectedIds.add(id);
        } else {
            selectedIds.delete(id);
        }

        // Find the row and update its visual state
        const row = table.querySelector(`tr[data - expense - id = "${id}"]`);
        if (row) {
            if (isSelected) {
                row.classList.add('selected');
            } else {
                row.classList.remove('selected');
            }
        }

        // Update the selection counter
        updateSelectionCounter();
    }

    function updateSelectionCounter()
    {
        if (toolbar) {
            const counter = toolbar.querySelector('.selection-counter .count');
            if (counter) {
                counter.textContent = selectedIds.size;
            }
        }
    }

    function updateHeaderState()
    {
        if (!headerCheckbox) {
            return;
        }
        const total = rowSelectors.length;
        const selectedCount = selectedIds.size;
        if (selectedCount === 0) {
            headerCheckbox.checked = false;
            headerCheckbox.indeterminate = false;
        } else if (selectedCount === total) {
            headerCheckbox.checked = true;
            headerCheckbox.indeterminate = false;
        } else {
            headerCheckbox.checked = false;
            headerCheckbox.indeterminate = true;
        }
    }

    function bulkDelete()
    {
        if (selectedIds.size === 0) {
            alert('No expenses selected.');
            return;
        }
        if (!confirm('Are you sure you want to delete the selected expenses?')) {
            return;
        }

        // Try to get CSRF token from meta tag first
        let csrfTokenElement = document.querySelector('meta[name="csrf-token"]');
        let csrfToken = csrfTokenElement ? csrfTokenElement.getAttribute('content') : null;

        // If not found in meta tag, try to get it from a hidden input field
        if (!csrfToken) {
            csrfTokenElement = document.querySelector('input[name="csrf_token"]');
            csrfToken = csrfTokenElement ? csrfTokenElement.value : null;
        }

        const form = document.createElement('form');
        form.method = 'POST';
        form.action = BULK_DELETE_URL;
        form.style.display = 'none';

        // CSRF token
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = 'csrf_token';
            csrfInput.value = csrfToken;
            form.appendChild(csrfInput);
        }

        // Selected IDs
        // Convert the Set to an array and join with commas
        const idsArray = Array.from(selectedIds);
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'expenseIds';
        input.value = idsArray.join(',');
        form.appendChild(input);

        document.body.appendChild(form);
        form.submit();
    }

    function exitSelectMode()
    {
        if (!tableContainer.classList.contains(SELECT_MODE_CLASS)) {
            return;
        }

        tableContainer.classList.remove(SELECT_MODE_CLASS);

        // Hide header checkbox instead of removing it
        if (headerCheckbox) {
            headerCheckbox.hidden = true;
            headerCheckbox.checked = false;
        }
        headerCheckbox = null;

        // Hide row selectors instead of removing them
        rowSelectors.forEach(cb => {
            cb.hidden = true;
            cb.checked = false;
        });
        rowSelectors = [];

        // Clear selection
        selectedIds.clear();

        // Remove selected class from all rows
        const selectedRows = table.querySelectorAll('tr.selected');
        selectedRows.forEach(row => {
            row.classList.remove('selected');
        });

        // Remove toolbar
        if (toolbar && toolbar.parentElement) {
            toolbar.parentElement.removeChild(toolbar);
        }
        toolbar = null;
    }

    function onKeyDown(e)
    {
        if (e.key === 'Escape' && tableContainer.classList.contains(SELECT_MODE_CLASS)) {
            exitSelectMode();
        }
    }

    // Initialize module
    init();
})();
