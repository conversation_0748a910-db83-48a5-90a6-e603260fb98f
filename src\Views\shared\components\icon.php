<?php

/**
 * SVG icon component
 * Displays an SVG icon from the icon sprite
 *
 * @param string $icon The icon ID (without the prefix)
 * @param string $size Icon size (sm, md, lg, xl) - default: md
 * @param string $class Additional CSS classes
 * @param string $title Optional title for accessibility
 * @param bool $ariaHidden Whether to hide the icon from screen readers (default: true)
 */

// Default parameters
$icon = $icon ?? '';
$size = $size ?? 'md';
$class = $class ?? '';
$title = $title ?? '';
$ariaHidden = $ariaHidden ?? true;

// Determine size class
$sizeClass = match ($size) {
    'sm' => 'icon-sm',
    'lg' => 'icon-lg',
    'xl' => 'icon-xl',
    default => ''
};

// Build classes
$classes = ['icon'];
if ($sizeClass) {
    $classes[] = $sizeClass;
}
if ($class) {
    $classes[] = $class;
}
$classAttr = implode(' ', $classes);

// Accessibility attributes
$ariaAttr = $ariaHidden ? ' aria-hidden="true"' : '';
$titleElem = $title && !$ariaHidden ? "<title>" . htmlspecialchars($title) . "</title>" : '';
?>

<svg class="<?= $classAttr ?>"<?= $ariaAttr ?>>
    <?= $titleElem ?>
    <use xlink:href="/assets/icons.svg#icon-<?= htmlspecialchars($icon) ?>"></use>
</svg>
