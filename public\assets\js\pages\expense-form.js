document.addEventListener('DOMContentLoaded', initializeExpenseForm);

function initializeExpenseForm()
{
    setupMerchantSelection();
    setupCategorySelection();
    setupItemsManagement();
    setupFileUpload();
    setupFormValidation();
    checkInitialState();
}

function setupMerchantSelection()
{
    const merchantSelect = document.getElementById('merchant_id');
    const newMerchantGroup = document.querySelector('.new-merchant-group');
    const merchantInput = document.getElementById('merchant');

    if (!merchantSelect || !newMerchantGroup || !merchantInput) {
        return;
    }

    merchantSelect.addEventListener('change', () => {
        const isNewMerchant = merchantSelect.value === '_new';
        toggleNewItemField(isNewMerchant, newMerchantGroup, merchantInput);
    });
}

function setupCategorySelection()
{
    const categorySelect = document.getElementById('category_id');
    const newCategoryGroup = document.querySelector('.new-category-group');
    const newCategoryInput = document.getElementById('new_category');

    if (!categorySelect || !newCategoryGroup || !newCategoryInput) {
        return;
    }

    categorySelect.addEventListener('change', () => {
        const isNewCategory = categorySelect.value === '_new';
        toggleNewItemField(isNewCategory, newCategoryGroup, newCategoryInput);
    });
}

function toggleNewItemField(showField, fieldGroup, inputElement)
{
    if (showField) {
        fieldGroup.classList.remove('hidden');
        inputElement.setAttribute('required', 'required');
        inputElement.focus();
    } else {
        fieldGroup.classList.add('hidden');
        inputElement.removeAttribute('required');
    }
}

function setupItemsManagement()
{
    const itemsList = document.getElementById('items-list');
    const addItemBtn = document.getElementById('add-item');

    if (!itemsList || !addItemBtn) {
        return;
    }

    let itemCount = itemsList.querySelectorAll('.item-row').length;

    addItemBtn.addEventListener('click', () => addNewItemRow(itemsList, itemCount++));
    setupExistingItemRows();
}

function addNewItemRow(itemsList, index)
{
    const newRow = document.createElement('div');
    newRow.className = 'item-row';
    newRow.innerHTML = `
        < input type = "text" name = "items[${index}][name]" placeholder = "Item name" class = "item-name" >
        < input type = "number" name = "items[${index}][price]" placeholder = "Price" step = "0.01" min = "0" class = "item-price" >
        < button type = "button" class = "remove-item btn-icon" aria - label = "Remove item" > × < / button >
    `;

    itemsList.appendChild(newRow);

    newRow.querySelector('.remove-item').addEventListener('click', () => {
        itemsList.removeChild(newRow);
    });

    newRow.querySelector('.item-name').focus();
}

function setupExistingItemRows()
{
    document.querySelectorAll('.remove-item, .btn-icon').forEach(button => {
        button.addEventListener('click', () => {
            const parentRow = button.closest('.item-row');
            if (parentRow && parentRow.parentElement) {
                parentRow.parentElement.removeChild(parentRow);
            }
        });
    });
}

function setupFileUpload()
{
    const fileInput = document.getElementById('document');
    const fileInfo = document.querySelector('.file-info');

    if (!fileInput) {
        return;
    }

    fileInput.addEventListener('change', () => {
        if (fileInput.files.length === 0) {
            return;
        }

        const file = fileInput.files[0];
        if (!validateFile(file)) {
            fileInput.value = '';
            return;
        }

        updateFileInfo(file, fileInfo);
    });
}

function updateFileInfo(file, fileInfo)
{
    if (!fileInfo) {
        return;
    }

    const existingNotice = fileInfo.querySelector('.parse-notice');
    if (existingNotice) {
        existingNotice.remove();
    }

    const fileSize = (file.size / 1024).toFixed(1);
    fileInfo.innerHTML = `
        < div class = "file-details" >
            < span class = "file-name" > ${file.name} < / span >
            < span class = "file-size" > (${fileSize} KB) < / span >
        <  / div >
        < div class = "parse-notice" >
            File uploaded. We'll attempt to automatically extract receipt information on submission.
        < / div >
    `;
    }

    function validateFile(file)
    {
        const allowedTypes = ['txt', 'csv', 'pdf', 'log', 'jpg', 'jpeg', 'png'];
        const maxSizeMB = 2;
        const fileType = file.name.split('.').pop().toLowerCase();
        const fileSizeMB = file.size / 1024 / 1024;

        if (!allowedTypes.includes(fileType)) {
            alert(`Unsupported file type. Please upload one of the following: ${allowedTypes.join(', ')}`);
            return false;
        }

        if (fileSizeMB > maxSizeMB) {
            alert(`File is too large. Maximum size is ${maxSizeMB}MB.`);
            return false;
        }

        return true;
    }

    function setupFormValidation()
    {
        const form = document.querySelector('form');
        if (!form) {
            return;
        }

        form.addEventListener('submit', e => {
            form.classList.add('was-validated');

            if (!validateRequiredFields(form)) {
                e.preventDefault();
                form.querySelector('.is-invalid').focus();
            } else {
                showLoadingState(form);
            }
        });
    }

    function validateRequiredFields(form)
    {
        const requiredFields = form.querySelectorAll('[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                markFieldAsInvalid(field);
                isValid = false;
            } else {
                markFieldAsValid(field);
            }
        });

        return isValid;
    }

    function markFieldAsInvalid(field)
    {
        field.classList.add('is-invalid');

        let errorMessage = field.nextElementSibling;
        if (!errorMessage || !errorMessage.classList.contains('invalid-feedback')) {
            errorMessage = document.createElement('div');
            errorMessage.className = 'invalid-feedback';
            field.parentNode.insertBefore(errorMessage, field.nextSibling);
        }

        const fieldName = field.getAttribute('placeholder') || 'This field';
        errorMessage.textContent = `${fieldName} is required`;
    }

    function markFieldAsValid(field)
    {
        field.classList.remove('is-invalid');
        field.classList.add('is-valid');

        const errorMessage = field.nextElementSibling;
        if (errorMessage && errorMessage.classList.contains('invalid-feedback')) {
            errorMessage.remove();
        }
    }

    function showLoadingState(form)
    {
        const submitButton = form.querySelector('button[type="submit"]');
        if (submitButton) {
            submitButton.disabled = true;
            submitButton.innerHTML = '<span class="spinner"></span> Saving...';
        }
    }

    function checkInitialState()
    {
        checkInitialCategoryState();
        checkInitialMerchantState();
    }

    function checkInitialCategoryState()
    {
        const categorySelect = document.getElementById('category_id');
        const newCategoryGroup = document.querySelector('.new-category-group');
        const newCategoryInput = document.getElementById('new_category');

        if (categorySelect && categorySelect.value === '_new' && newCategoryGroup && newCategoryInput) {
            newCategoryGroup.classList.remove('hidden');
            newCategoryInput.setAttribute('required', 'required');
        }
    }

    function checkInitialMerchantState()
    {
        const merchantSelect = document.getElementById('merchant_id');
        const newMerchantGroup = document.querySelector('.new-merchant-group');
        const merchantInput = document.getElementById('merchant');

        if (merchantSelect && merchantSelect.value === '_new' && newMerchantGroup && merchantInput) {
            newMerchantGroup.classList.remove('hidden');
            merchantInput.setAttribute('required', 'required');
        }
    }
