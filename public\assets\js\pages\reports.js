document.addEventListener('DOMContentLoaded', initializeReportsPage);

function initializeReportsPage()
{
    setupMonthlyChart();
    setupCategoryCharts();
    enhanceReportCards();
    setupFilterSubmission();
    setupExportButtons();
}

function setupMonthlyChart()
{
    const chartElement = document.getElementById('monthlyChart');
    if (!chartElement) {
        return;
    }

    const ctx = chartElement.getContext('2d');
    const chartData = getChartData('chart-months-data', 'chart-amounts-data');
    if (!chartData) {
        return;
    }

    createMonthlyChart(ctx, chartData.labels, chartData.values);
}

function getChartData(labelsElementId, valuesElementId)
{
    const labelsElement = document.getElementById(labelsElementId);
    const valuesElement = document.getElementById(valuesElementId);

    if (!labelsElement || !valuesElement) {
        return null;
    }

    return {
        labels: JSON.parse(labelsElement.textContent),
        values: JSON.parse(valuesElement.textContent)
    };
}

function createMonthlyChart(ctx, months, amounts)
{
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: months,
            datasets: [{
                label: 'Monthly Expenses',
                data: amounts,
                backgroundColor: 'rgba(54, 162, 235, 0.5)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: value => '$' + value.toLocaleString()
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: context => '$' + context.raw.toLocaleString()
                    }
                }
            }
        }
    });
}

function setupCategoryCharts()
{
    setupCategoryPieChart();
    setupCategoryBarChart();
}

function setupCategoryPieChart()
{
    const chartElement = document.getElementById('categoryPieChart');
    if (!chartElement) {
        return;
    }

    const ctx = chartElement.getContext('2d');
    const chartData = getChartData('chart-categories-data', 'chart-percentages-data');
    if (!chartData) {
        return;
    }

    createCategoryPieChart(ctx, chartData.labels, chartData.values);
}

function createCategoryPieChart(ctx, categories, percentages)
{
    const backgroundColors = getChartColors();

    new Chart(ctx, {
        type: 'pie',
        data: {
            labels: categories,
            datasets: [{
                data: percentages,
                backgroundColor: backgroundColors,
                borderColor: backgroundColors.map(color => color.replace('0.5', '1')),
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'right',
                },
                tooltip: {
                    callbacks: {
                        label: context => `${context.label}: ${context.raw} % `
                    }
                }
            }
        }
    });
}

function setupCategoryBarChart()
{
    const chartElement = document.getElementById('categoryBarChart');
    if (!chartElement) {
        return;
    }

    const ctx = chartElement.getContext('2d');
    const chartData = getChartData('chart-categories-data', 'chart-amounts-data');
    if (!chartData) {
        return;
    }

    createCategoryBarChart(ctx, chartData.labels, chartData.values);
}

function createCategoryBarChart(ctx, categories, amounts)
{
    const backgroundColors = getChartColors();

    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: categories,
            datasets: [{
                label: 'Category Expenses',
                data: amounts,
                backgroundColor: backgroundColors,
                borderColor: backgroundColors.map(color => color.replace('0.5', '1')),
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: value => '$' + value.toLocaleString()
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: context => '$' + context.raw.toLocaleString()
                    }
                }
            }
        }
    });
}

function getChartColors()
{
    return [
        'rgba(54, 162, 235, 0.5)',
        'rgba(255, 99, 132, 0.5)',
        'rgba(255, 206, 86, 0.5)',
        'rgba(75, 192, 192, 0.5)',
        'rgba(153, 102, 255, 0.5)',
        'rgba(255, 159, 64, 0.5)',
        'rgba(199, 199, 199, 0.5)',
        'rgba(83, 102, 255, 0.5)',
        'rgba(40, 159, 64, 0.5)',
        'rgba(255, 99, 255, 0.5)'
    ];
}

function enhanceReportCards()
{
    document.querySelectorAll('.report-card').forEach(card => {
        card.addEventListener('mouseenter', () => {
            card.style.transform = 'translateY(-5px)';
            card.style.boxShadow = 'var(--box-shadow-lg)';
        });

        card.addEventListener('mouseleave', () => {
            card.style.transform = '';
            card.style.boxShadow = '';
        });
    });
}

function setupFilterSubmission()
{
    const filterSelectors = '.report-filters select, .report-filters input[type="month"]';

    document.querySelectorAll(filterSelectors).forEach(element => {
        element.addEventListener('change', () => {
            element.closest('form').submit();
        });
    });
}

function setupExportButtons()
{
    document.querySelectorAll('.download-options .button').forEach(button => {
        button.addEventListener('click', () => {
            const originalText = button.innerHTML;
            button.innerHTML = '<span class="loading-spinner"></span> Preparing...';

            setTimeout(() => {
                button.innerHTML = originalText;
            }, 2000);
        });
    });
}
