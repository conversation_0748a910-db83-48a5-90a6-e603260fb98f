/* ===== CHECKBOX & RADIO INPUTS ===== */
.checkbox-group,
.radio-group {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing);
  cursor: pointer;
  user-select: none;
}

.checkbox-group input[type="checkbox"] {
  width: 18px;
  height: 18px;
  margin-right: var(--spacing-sm);
  cursor: pointer;
  accent-color: var(--primary-color);
  transition: all 0.2s ease;
}

.checkbox-group:hover input[type="checkbox"] {
  transform: scale(1.05);
}

.checkbox-group label {
  margin: 0;
  cursor: pointer;
  font-weight: var(--font-weight-normal);
}

.checkbox-item,
.radio-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  cursor: pointer;
}

.checkbox-item input,
.radio-item input {
  width: auto;
  margin: 0;
  cursor: pointer;
}

.checkbox-item label,
.radio-item label {
  margin: 0;
  cursor: pointer;
  font-weight: var(--font-weight-normal);
}

@media (max-width: 768px) {
  .checkbox-group,
  .radio-group {
    flex-direction: column;
    gap: var(--spacing-xs);
  }
}