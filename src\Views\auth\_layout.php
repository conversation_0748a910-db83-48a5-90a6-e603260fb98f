<?php

/**
 * Shared layout for authentication pages
 *
 * Variables that should be passed to this layout:
 * - $title: Page title
 * - $content: The main content to display
 */

// Common session handling for all auth pages
$csrf_token ??= '';
$errors = $_SESSION['errors'] ?? null;
$success = $_SESSION['success'] ?? null;
$old = $_SESSION['old'] ?? [];

// Clean up session after retrieving values
if (isset($_SESSION['errors'])) {
    unset($_SESSION['errors']);
}
if (isset($_SESSION['success'])) {
    unset($_SESSION['success']);
}
if (isset($_SESSION['old'])) {
    unset($_SESSION['old']);
}
?>

<h2 class="auth-title"><?= htmlspecialchars($title) ?></h2>

<div class="form-container">
    <?php if ($errors) : ?>
        <div class="alert alert-danger" role="alert">
            <?php if (is_array($errors)) : ?>
                <ul>
                    <?php foreach ($errors as $error) : ?>
                        <li><?= htmlspecialchars($error) ?></li>
                    <?php endforeach; ?>
                </ul>
            <?php else : ?>
                <?= htmlspecialchars($errors) ?>
            <?php endif; ?>
        </div>
    <?php endif; ?>

    <?php if ($success) : ?>
        <div class="alert alert-success" role="alert">
            <?= htmlspecialchars($success) ?>
        </div>
    <?php endif; ?>

    <?= $content ?>
</div>

<script src="/assets/js/pages/auth.js" defer></script>
