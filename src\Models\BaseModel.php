<?php

declare(strict_types=1);

namespace App\Models;

use PDO;
use App\Core\Database;
use App\Core\Security\PreparedStatement;
use App\Core\Security\SecurityLogger;
use App\Exceptions\DatabaseException;

abstract class BaseModel
{
    abstract protected static function getTableName(): string;

    // Database access
    public static function getDb(): PDO
    {
        try {
            $db = Database::getInstance();
            error_log("BaseModel::getDb() - Successfully got database instance");
            return $db;
        } catch (\Exception $e) {
            error_log("BaseModel::getDb() - Error getting database instance: " . $e->getMessage());
            throw $e;
        }
    }

    protected static function getPreparedStatement(): PreparedStatement
    {
        static $preparedStatement = null;
        
        if ($preparedStatement === null) {
            $preparedStatement = new PreparedStatement(
                self::getDb(),
                new SecurityLogger(),
                (bool)(getenv('LOG_QUERIES') ?? false)
            );
        }
        
        return $preparedStatement;
    }

    // CRUD operations
    public static function find(int $id): ?array
    {
        $table = static::getTableName();
        $sql = "SELECT * FROM `{$table}` WHERE id = ? AND deleted_at IS NULL LIMIT 1";
        return static::getPreparedStatement()->fetchOne($sql, [$id]);
    }

    public static function all(): array
    {
        $table = static::getTableName();
        $sql = "SELECT * FROM `{$table}` WHERE deleted_at IS NULL ORDER BY id DESC";
        return static::getPreparedStatement()->fetchMany($sql);
    }

    public static function create(array $data): int
    {
        // Add created_at timestamp if not present
        if (!isset($data['created_at'])) {
            $data['created_at'] = date('Y-m-d H:i:s');
        }
        
        return static::getPreparedStatement()->insert(static::getTableName(), $data);
    }

    public static function update(int $id, array $data): bool
    {
        // Add updated_at timestamp
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        $rowsAffected = static::getPreparedStatement()->update(
            static::getTableName(),
            $data,
            ['id' => $id]
        );
        
        return $rowsAffected > 0;
    }

    public static function delete(int $id): bool
    {
        $rowsAffected = static::getPreparedStatement()->update(
            static::getTableName(),
            ['deleted_at' => date('Y-m-d H:i:s')],
            ['id' => $id]
        );
        
        return $rowsAffected > 0;
    }

    // Database query execution (deprecated - use getPreparedStatement() instead)
    protected static function executeSql(string $sql, array $params = []): bool
    {
        try {
            $stmt = static::getPreparedStatement()->execute($sql, $params);
            return true;
        } catch (DatabaseException $e) {
            throw $e;
        }
    }

    // Data retrieval methods (deprecated - use getPreparedStatement() instead)
    protected static function fetchOne(string $sql, array $params = []): ?array
    {
        try {
            error_log("[Debug] BaseModel::fetchOne - SQL: {$sql}");
            error_log("[Debug] BaseModel::fetchOne - Params: " . json_encode($params));

            $result = static::getPreparedStatement()->fetchOne($sql, $params);
            error_log("[Debug] BaseModel::fetchOne - Result: " . json_encode($result));
            return $result;
        } catch (DatabaseException $e) {
            throw $e;
        }
    }

    protected static function fetchMany(string $sql, array $params = []): array
    {
        try {
            return static::getPreparedStatement()->fetchMany($sql, $params);
        } catch (DatabaseException $e) {
            throw $e;
        }
    }

    protected static function fetchScalar(string $sql, array $params = []): mixed
    {
        try {
            $stmt = self::getDb()->prepare($sql);
            $stmt->execute($params);
            return $stmt->fetchColumn();
        } catch (\PDOException $e) {
            throw new DatabaseException("FetchScalar failed: {$sql}", $e->getCode(), $e);
        }
    }

    // Error handling
    protected static function handleUniqueConstraintViolation(DatabaseException $e): bool
    {
        if (str_contains($e->getMessage(), '23000')) {
            return false;
        }
        throw $e;
    }

    // Transaction management
    protected static function transaction(callable $callback)
    {
        $db = self::getDb();
        $db->beginTransaction();

        try {
            $result = $callback();
            $db->commit();
            return $result;
        } catch (\Exception $e) {
            $db->rollBack();
            throw $e;
        }
    }
}
