# Personal Expense Tracker

A comprehensive personal expense tracking application built with PHP, MySQL, and a component-based architecture.

## Features

- **User Authentication**: Register, login, and logout functionality.
- **Expense Management**: Add, edit, delete, and categorize expenses.
- **File Uploads**: Upload and manage receipts for expenses.
- **Reports**: Generate monthly and category-wise expense reports with visualizations.
- **Export**: Export data in various formats (CSV, JSON, PDF).
- **Responsive Design**: Works on all screen sizes using a custom component-based UI.

## Component-Based Architecture

The application uses a component-based architecture to ensure consistency, maintainability, and accessibility. Components are reusable UI elements that can be composed to create complex views.

### Component Types

- **UI Components**: Alert, Badge, Breadcrumbs, Button, Card, Modal, Pagination, Tabs
- **Form Components**: Form, Form Field, Form Group, Form Actions
- **Data Components**: Table, Chart, List, Stat
- **Layout Components**: Base, Header, Footer, Sidebar, Container

### Documentation

For more information about the component-based architecture, see the documentation in the `docs` directory:

- [Component Architecture](docs/component-architecture.md): Overview of the component-based architecture
- [Style Guide](docs/style-guide.md): Style guide for using the component-based architecture

## Installation

1. **Clone the repository**:
   ```bash
   git clone https://github.com/yourusername/personal-expense-tracker.git
   cd personal-expense-tracker