<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Duha Test</title>
</head>
<body>
    <h1>Duha Prayer Time Calculation Test</h1>
    <div id="output"></div>

    <script>
        // Test the time parsing and duha calculation
        function parseTimeString(timeString) {
          if (!timeString || typeof timeString !== 'string' || timeString === '--:--') {
            return null;
          }
          
          const cleanTime = timeString.replace(/\s*(AM|PM)\s*/i, '').trim();
          const timeParts = cleanTime.split(':');
          
          if (timeParts.length < 2) return null;
          
          const hours = parseInt(timeParts[0], 10);
          const minutes = parseInt(timeParts[1], 10);
          const seconds = timeParts.length > 2 ? parseInt(timeParts[2], 10) : 0;
          
          if (!Number.isInteger(hours) || !Number.isInteger(minutes) || 
              hours < 0 || hours > 23 || minutes < 0 || minutes > 59 ||
              seconds < 0 || seconds > 59) {
            return null;
          }

          let adjustedHours = hours;
          const isPM = timeString.toLowerCase().includes('pm');
          const isAM = timeString.toLowerCase().includes('am');
          
          if (isPM && hours !== 12) adjustedHours += 12;
          if (isAM && hours === 12) adjustedHours = 0;

          return {
            hours: adjustedHours,
            minutes,
            seconds,
            totalSeconds: adjustedHours * 3600 + minutes * 60 + seconds,
            totalMinutes: adjustedHours * 60 + minutes + seconds / 60
          };
        }

        function timeToSeconds(timeStr) {
          if (!timeStr) return 0;
          
          // Handle different time formats
          const parsed = parseTimeString(timeStr);
          if (parsed) {
            return parsed.totalSeconds;
          }
          
          // Fallback to original parsing
          const [hours, minutes, seconds] = timeStr.split(':').map(Number);
          return (hours || 0) * 3600 + (minutes || 0) * 60 + (seconds || 0);
        }

        function secondsToTime(totalSeconds) {
          const hours = Math.floor(totalSeconds / 3600);
          const minutes = Math.floor((totalSeconds % 3600) / 60);
          const seconds = totalSeconds % 60;
          
          const pad = (num) => num.toString().padStart(2, '0');
          return `${pad(hours)}:${pad(minutes)}:${pad(seconds)}`;
        }

        function formatDisplayTime(timeString) {
          const parsed = parseTimeString(timeString);
          if (!parsed) return "Invalid Time";
          
          const displayHours = parsed.hours % 12 || 12;
          const period = parsed.hours >= 12 ? 'PM' : 'AM';
          
          return `${displayHours}:${String(parsed.minutes).padStart(2, '0')} ${period}`;
        }

        function testDuhaCalculation() {
            // Test with sample times
            const testCases = [
                { sunrise: "06:30 AM", dhuhr: "12:15 PM" },
                { sunrise: "06:45", dhuhr: "12:30" },
                { sunrise: "05:30 AM", dhuhr: "11:45 AM" }
            ];

            let output = document.getElementById('output');
            
            testCases.forEach((test, index) => {
                const sunriseSeconds = timeToSeconds(test.sunrise);
                const dhuhrSeconds = timeToSeconds(test.dhuhr);
                
                const duhaStart = secondsToTime(sunriseSeconds + (20 * 60));
                const duhaMid = secondsToTime(sunriseSeconds + Math.round((dhuhrSeconds - sunriseSeconds) / 2));
                const duhaEnd = secondsToTime(dhuhrSeconds - (10 * 60));
                
                output.innerHTML += `
                    <h3>Test Case ${index + 1}</h3>
                    <p><strong>Sunrise:</strong> ${test.sunrise} (${sunriseSeconds} seconds)</p>
                    <p><strong>Dhuhr:</strong> ${test.dhuhr} (${dhuhrSeconds} seconds)</p>
                    <p><strong>Duha Start:</strong> ${duhaStart} (${formatDisplayTime(duhaStart)})</p>
                    <p><strong>Duha Mid:</strong> ${duhaMid} (${formatDisplayTime(duhaMid)})</p>
                    <p><strong>Duha End:</strong> ${duhaEnd} (${formatDisplayTime(duhaEnd)})</p>
                    <hr>
                `;
            });
        }

        // Run test when page loads
        window.onload = function() {
            testDuhaCalculation();
        };
    </script>
</body>
</html>