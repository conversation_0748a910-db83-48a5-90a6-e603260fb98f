<h2>Category Statistics: <?= htmlspecialchars($category['name']) ?></h2>

<div class="actions">
    <a href="/categories" class="button secondary">Back to Categories</a>
    <a href="/categories/<?= htmlspecialchars($category['id']) ?>/edit" class="button">Edit Category</a>
</div>

<div class="statistics-container">
    <div class="category-details">
        <div class="category-card">
            <div class="card-header">
                <h3 class="category-name"><?= htmlspecialchars($category['name']) ?></h3>
            </div>

            <div class="card-body">
                <p class="description <?= empty($category['description']) ? 'empty' : '' ?>">
                    <?= !empty($category['description'])
                        ? htmlspecialchars($category['description'])
                        : 'No description'
                    ?>
                </p>

                <div class="stats-summary">
                    <div class="stat-group">
                        <div class="stat-item">
                            <span class="stat-label">Total Expenses</span>
                            <span class="stat-value"><?= $stats['expense_count'] ?? 0 ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Total Amount</span>
                            <span class="stat-value">$<?= number_format($stats['total_expenses'] ?? 0, 2) ?></span>
                        </div>
                    </div>

                    <?php if (isset($stats['file_count'])) : ?>
                    <div class="stat-group">
                        <div class="stat-item">
                            <span class="stat-label">Attached Files</span>
                            <span class="stat-value"><?= $stats['file_count'] ?></span>
                        </div>
                        <div class="stat-item">
                            <span class="stat-label">Total File Size</span>
                            <span class="stat-value"><?= formatFileSize($stats['file_size'] ?? 0) ?></span>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <div class="statistics-charts">
        <div class="chart-section">
            <h3>Monthly Expense Trend</h3>
            <div class="chart-container">
                <canvas id="monthlyTrendChart" width="600" height="300"></canvas>
            </div>
        </div>

        <?php if (!empty($stats['recent_expenses'] ?? [])) : ?>
        <div class="recent-expenses">
            <h3>Recent Expenses</h3>
            <div class="table-responsive">
                <table>
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Description</th>
                            <th>Amount</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($stats['recent_expenses'] ?? [] as $expense) : ?>
                        <tr>
                            <td><?= date('M j, Y', strtotime($expense['date'])) ?></td>
                            <td><?= htmlspecialchars($expense['description']) ?></td>
                            <td class="amount">$<?= number_format($expense['amount'], 2) ?></td>
                            <td>
                                <a href="/expenses/<?= $expense['id'] ?>" class="button small">View</a>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
        <?php endif; ?>
    </div>
</div>

<?php
// Add Chart.js and categories.js to the scripts array
$scripts ??= [];
$scripts[] = 'https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js';
$scripts[] = '/assets/js/pages/categories.js';

// Define the monthly data for the chart
$monthlyData = [
    'labels' => ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    'datasets' => [[
        'label' => 'Monthly Expenses',
        'data' => array_map(
            function () {
                return rand(50, 500);
            },
            range(0, 11)
        ),
        'backgroundColor' => 'rgba(54, 162, 235, 0.2)',
        'borderColor' => 'rgba(54, 162, 235, 1)',
        'borderWidth' => 2,
        'tension' => 0.3,
        'fill' => true
    ]]
];
?>

<script>
// Make the monthly data available to the categories.js script
const monthlyData = <?= json_encode($monthlyData) ?>;
</script>

<!-- Category styles are now included in main.css -->
<?php
// Helper function to format file size
function formatFileSize($bytes)
{
    if ($bytes < 1024) {
        return $bytes . ' B';
    } elseif ($bytes < 1048576) {
        return round($bytes / 1024, 2) . ' KB';
    } elseif ($bytes < 1073741824) {
        return round($bytes / 1048576, 2) . ' MB';
    } else {
        return round($bytes / 1073741824, 2) . ' GB';
    }
}
?>
