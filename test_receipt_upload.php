<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Models\Receipt;
use App\Services\ReceiptParserService;
use App\Services\FileService;
use App\Services\ExpenseService;

// Test receipt content
$content = <<<EOT
Cinema: MovieMax
Date: 2023-11-01
Items:
  - Movie Ticket: $12.00
  - Popcorn: $5.50
  - Soda: $3.00
Total: $20.50
Payment Method: Cash
EOT;

// Parse the receipt content
$parsedData = Receipt::parseContent($content);

// Add user-specific data
$parsedData['user_id'] = 1; // Assuming user ID 1 exists
$parsedData['filename'] = 'test_receipt.txt';
$parsedData['file_id'] = null;
$parsedData['content'] = $content;

// Create services
$fileService = new FileService(
    new \Psr\Log\NullLogger(),
    new ExpenseService(),
    1
);

$receiptParserService = new ReceiptParserService(
    $fileService,
    new ExpenseService()
);

// Prepare expense data
$expenseData = $receiptParserService->prepareExpenseData($parsedData);

// Output the result
echo "Parsed Receipt Data:\n";
echo "-------------------\n";
echo "Merchant: " . $parsedData['merchant'] . "\n";
echo "Date: " . $parsedData['date'] . "\n";
echo "Amount: $" . number_format($parsedData['amount'], 2) . "\n";
echo "Payment Method: " . $parsedData['payment_method'] . "\n";
echo "Category: " . $parsedData['category'] . "\n";
echo "Items:\n";

if (!empty($parsedData['items'])) {
    foreach ($parsedData['items'] as $item) {
        echo "  - " . $item['name'] . ": $" . number_format($item['price'], 2) . "\n";
    }
} else {
    echo "  No items found\n";
}

echo "\nPrepared Expense Data:\n";
echo "-------------------\n";
echo "User ID: " . $expenseData['user_id'] . "\n";
echo "Category ID: " . $expenseData['category_id'] . "\n";
echo "Merchant ID: " . $expenseData['merchant_id'] . "\n";
echo "Amount: $" . number_format($expenseData['amount'], 2) . "\n";
echo "Description: " . $expenseData['description'] . "\n";
echo "Date: " . $expenseData['date'] . "\n";
echo "Payment Method ID: " . $expenseData['payment_method_id'] . "\n";
echo "Notes: " . $expenseData['notes'] . "\n";
