/**
 * Tab Session Manager
 *
 * This script handles browser session tracking and ensures that sessions
 * are properly invalidated when the user closes all tabs/windows.
 *
 * It works by:
 * 1. Setting a unique ID in sessionStorage (which is cleared when all tabs are closed)
 * 2. Checking on page load if this is a new browser session
 * 3. If it's a new session and remember_me is false, invalidating the session and redirecting to home page
 */

// IMPORTANT: This script runs immediately when loaded, before DOMContentLoaded
// This allows us to check for new browser sessions and redirect if necessary
// before any content is displayed

// Set a flag to prevent infinite redirects
// This is stored in localStorage so it persists across page loads in the same tab
if (!localStorage.getItem('redirectAttempts')) {
    localStorage.setItem('redirectAttempts', '0');
}

// Prevent infinite redirects by tracking redirect attempts
const redirectAttempts = parseInt(localStorage.getItem('redirectAttempts') || '0', 10);
if (redirectAttempts > 3) {
    console.warn('Too many redirect attempts detected, resetting counter');
    localStorage.setItem('redirectAttempts', '0');
    // Clear any problematic session data
    sessionStorage.removeItem('browserSessionId');
    localStorage.removeItem('lastBrowserSessionId');
}

// Immediate check for new browser session
// This runs as soon as the script loads
(function () {
    if (isPublicPage()) {
        return;
    }

    if (isLoginToDashboard()) {
        handleLoginToDashboardImmediate();
        return;
    }

    if (isNewBrowserSession()) {
        handleNewBrowserSessionImmediate();
    }
})();

function isPublicPage()
{
    const currentPath = window.location.pathname;
    const publicPaths = ['/', '/login', '/register', '/password/reset', '/password/new', '/how'];

    const isPublic = publicPaths.includes(currentPath);
    if (isPublic) {
        console.log('On public page, skipping session check');
    }

    return isPublic;
}

function isLoginToDashboard()
{
    const currentPath = window.location.pathname;
    return currentPath === '/dashboard' && document.referrer.includes('/login');
}

function handleLoginToDashboardImmediate()
{
    console.log('Coming from login page to dashboard, skipping session check');
    const browserSessionId = generateUniqueId();
    sessionStorage.setItem('browserSessionId', browserSessionId);
}

function isNewBrowserSession()
{
    return !sessionStorage.getItem('browserSessionId');
}

function handleNewBrowserSessionImmediate()
{
    console.log('New browser session detected, checking remember_me setting');

    const browserSessionId = generateUniqueId();
    sessionStorage.setItem('browserSessionId', browserSessionId);

    checkSessionWithServer();
}

document.addEventListener('DOMContentLoaded', initializeTabSessionManager);

function initializeTabSessionManager()
{
    console.log('Tab session manager initialized');

    if (isPublicPage()) {
        console.log('On public page, no need to check session');
        ensureTabSessionId();
        return;
    }

    const hasExistingSession = checkForExistingSession();
    storeRememberMeSetting();

    if (!hasExistingSession) {
        handlePotentialNewSession();
    } else {
        console.log('Existing browser session detected, no need to check validity');
    }

    ensureTabSessionId();
}

function storeRememberMeSetting()
{
    const rememberMe = document.body.getAttribute('data-remember-me');
    console.log('Remember me setting:', rememberMe);
    localStorage.setItem('remember_me', rememberMe);
    return rememberMe;
}

function handlePotentialNewSession()
{
    console.log('New browser session detected');

    const rememberMe = document.body.getAttribute('data-remember-me');
    if (rememberMe === 'false') {
        console.log('Remember me is not enabled, checking session validity');
        checkSessionValidity();
    } else {
        console.log('Remember me is enabled, session should persist');
    }
}

function ensureTabSessionId()
{
    if (!sessionStorage.getItem('tab_session_id')) {
        const newTabSessionId = generateUniqueId();
        sessionStorage.setItem('tab_session_id', newTabSessionId);
        console.log('New tab session started: ' + newTabSessionId);
    }
}

/**
 * Check if this is a new browser session
 *
 * @returns {boolean} True if an existing session was found, false if this is a new session
 */
function checkForExistingSession()
{
    // Increment the redirect attempts counter to prevent infinite loops
    const attempts = parseInt(localStorage.getItem('redirectAttempts') || '0', 10);
    localStorage.setItem('redirectAttempts', (attempts + 1).toString());

    // Handle too many redirect attempts
    if (attempts > 2) {
        console.warn('Multiple redirect attempts detected, treating as existing session');
        localStorage.setItem('redirectAttempts', '0');
        return true;
    }

    // Handle coming from login page to dashboard
    const currentPath = window.location.pathname;
    if (currentPath === '/dashboard' && document.referrer.includes('/login')) {
        return handleLoginToDashboard();
    }

    // Handle new browser session
    if (!sessionStorage.getItem('browserSessionId')) {
        return handleNewBrowserSession();
    }

    // Handle existing browser session
    return handleExistingBrowserSession();
}

function handleLoginToDashboard()
{
    console.log('Coming from login page to dashboard, treating as existing session');
    const browserSessionId = generateUniqueId();
    sessionStorage.setItem('browserSessionId', browserSessionId);
    localStorage.setItem('redirectAttempts', '0');
    return true;
}

function handleNewBrowserSession()
{
    const browserSessionId = generateUniqueId();
    sessionStorage.setItem('browserSessionId', browserSessionId);

    console.log('New browser session detected');
    localStorage.setItem('lastBrowserSessionId', browserSessionId);
    localStorage.setItem('redirectAttempts', '0');

    const rememberMe = document.body.getAttribute('data-remember-me');
    console.log('Remember me setting from data attribute:', rememberMe);

    if (rememberMe === 'false') {
        console.log('Remember me is not enabled, this is a new session');
        return false;
    }

    console.log('Remember me is enabled, treating as existing session');
    return true;
}

function handleExistingBrowserSession()
{
    console.log('Existing browser session detected');
    localStorage.setItem('lastBrowserSessionId', sessionStorage.getItem('browserSessionId'));
    localStorage.setItem('redirectAttempts', '0');
    return true;
}

/**
 * Check session validity for a new browser session
 * This is used when a user has closed all tabs/windows and reopened the browser
 */
function checkSessionValidity()
{
    console.log('Checking session validity for new browser session');

    if (hasTooManyRedirects()) {
        return;
    }

    incrementRedirectAttempts();

    if (hasRememberMeEnabled()) {
        return;
    }

    hideContentForSessionCheck();
    checkNonPersistentSession();
}

function hasTooManyRedirects()
{
    const attempts = parseInt(localStorage.getItem('redirectAttempts') || '0', 10);
    if (attempts > 2) {
        console.warn('Too many redirect attempts, skipping session validity check');
        localStorage.setItem('redirectAttempts', '0');
        return true;
    }
    return false;
}

function incrementRedirectAttempts()
{
    const attempts = parseInt(localStorage.getItem('redirectAttempts') || '0', 10);
    localStorage.setItem('redirectAttempts', (attempts + 1).toString());
}

function hasRememberMeEnabled()
{
    const rememberMe = document.body.getAttribute('data-remember-me');
    console.log('Remember me setting from data attribute:', rememberMe);

    if (rememberMe === 'true') {
        console.log('Remember me is enabled, skipping session validity check');
        localStorage.setItem('redirectAttempts', '0');
        return true;
    }
    return false;
}

function hideContentForSessionCheck()
{
    document.documentElement.style.visibility = 'hidden';

    const style = document.createElement('style');
    style.id = 'session-check-style';
    style.textContent = 'html { visibility: hidden !important; }';
    document.head.appendChild(style);
}

function checkNonPersistentSession()
{
    console.log('Non-persistent session detected, checking with server');
    validateSessionWithServer();
}

/**
 * Invalidate the session by calling the server
 */
function invalidateSession()
{
    console.log('Explicitly invalidating session');

    const payload = {
        action: 'invalidate',
        browserSessionId: sessionStorage.getItem('browserSessionId')
    };

    sendInvalidationRequest(payload);
}

/**
 * Generate a unique ID
 *
 * @returns {string} A unique ID
 */
function generateUniqueId()
{
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
}

/**
 * Set up the tab close handler
 *
 * This function sets up an event listener for when a tab is closed.
 * It sends a notification to the server so it can track which tabs are still open.
 * When all tabs are closed, sessionStorage is automatically cleared by the browser,
 * which allows us to detect a new browser session when the user returns.
 */
function setupTabCloseHandler()
{
    ensureTabId();
    window.addEventListener('beforeunload', handleTabClose);
}

function ensureTabId()
{
    if (!sessionStorage.getItem('tabId')) {
        const tabId = generateUniqueId();
        sessionStorage.setItem('tabId', tabId);
    }
}

function handleTabClose()
{
    const tabId = sessionStorage.getItem('tabId');
    const browserSessionId = sessionStorage.getItem('browserSessionId');

    // Store the current timestamp in localStorage
    localStorage.setItem('browserLastClosed', Date.now().toString());

    const rememberMe = document.body.getAttribute('data-remember-me') === 'true';

    const payload = JSON.stringify({
        tabId,
        browserSessionId,
        action: 'close',
        remember_me: rememberMe
    });

    sendTabCloseNotification(payload);
}

function sendTabCloseNotification(payload)
{
    if (navigator.sendBeacon) {
        navigator.sendBeacon('/invalidate-session', payload);
    } else {
        const xhr = new XMLHttpRequest();
        xhr.open('POST', '/invalidate-session', false);
        xhr.setRequestHeader('Content-Type', 'application/json');
        xhr.send(payload);
    }
}

/**
 * Check session with server and handle the response
 */
function checkSessionWithServer()
{
    fetch('/check-session', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(handleSessionCheckResponse)
    .catch(handleSessionCheckError);
}

/**
 * Handle session check response
 */
function handleSessionCheckResponse(data)
{
    console.log('Session check response:', data);

    // If session is invalid or this is a new session without remember_me, redirect to home
    if (!data.valid || (data.new_session && !data.remember_me)) {
        console.log('Session invalid or new session without remember_me, redirecting to home');
        window.location.href = '/';
    } else {
        showContent();
    }
}

/**
 * Handle session check error
 */
function handleSessionCheckError(error)
{
    console.error('Error checking session:', error);
    showContent();
}

/**
 * Validate session with server
 */
function validateSessionWithServer()
{
    fetch('/check-session', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(handleValidationResponse)
    .catch(handleValidationError);
}

/**
 * Handle validation response
 */
function handleValidationResponse(data)
{
    console.log('Session validity check response:', data);

    // Reset redirect attempts counter
    localStorage.setItem('redirectAttempts', '0');

    // If the session is invalid or this is a new session without remember_me, redirect to home
    if (!data.valid || (data.new_session && !data.remember_me)) {
        console.log('Session invalid or new session without remember_me, redirecting to home');
        window.location.href = data.redirect || '/';
    } else {
        console.log('Session is valid or persistent (remember me enabled)');
        showContent();
    }
}

/**
 * Handle validation error
 */
function handleValidationError(error)
{
    console.error('Error checking session validity:', error);
    // Reset redirect attempts counter on error
    localStorage.setItem('redirectAttempts', '0');
    showContent();
}

/**
 * Show content by removing visibility restrictions
 */
function showContent()
{
    document.documentElement.style.visibility = '';
    // Remove the style tag if it exists
    const styleTag = document.getElementById('session-check-style');
    if (styleTag) {
        styleTag.remove();
    }
    console.log('Session is valid, showing content');
}

/**
 * Send invalidation request to server
 */
function sendInvalidationRequest(payload)
{
    fetch('/invalidate-session', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
        credentials: 'same-origin'
    })
    .then(response => response.json())
    .then(handleInvalidationResponse)
    .catch(handleInvalidationError);
}

/**
 * Handle invalidation response
 */
function handleInvalidationResponse(data)
{
    console.log('Session invalidation response:', data);

    // If the server says we should redirect, do it
    if (data.action === 'logout') {
        // Check if the server specified a redirect URL
        const redirectUrl = data.redirect || '/';
        console.log(`Session invalidated, redirecting to ${redirectUrl}`);
        window.location.href = redirectUrl;
    }
}

/**
 * Handle invalidation error
 */
function handleInvalidationError(error)
{
    console.error('Error invalidating session:', error);
}

// Initialize the tab close handler
setupTabCloseHandler();
