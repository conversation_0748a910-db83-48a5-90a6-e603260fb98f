/* ===== EXPENSE FORM STYLES ===== */

/* Form Controls & Enhanced Inputs */
.form-control {
  width: 100%;
  height: var(--input-height, 2.75rem);
  padding: 0.8rem;
  border: 1px solid var(--grey-400);
  border-radius: var(--radius-sm);
  transition: var(--transition-fast);
  background-color: #fcfcfc;
  font-family: var(--font-main);
  font-size: var(--font-size);
  color: var(--text-color);
}

.form-control:focus {
  border-color: var(--color-primary);
  outline: none;
  box-shadow: 0 0 0 3px var(--primary-light);
  background-color: white;
}

.form-hint {
  display: block;
  margin-top: var(--spacing-xs);
  color: var(--text-muted);
  font-size: var(--font-size-sm);
}

.hidden {
  display: none !important;
}

/* File Upload Drop Zone */
.file-upload {
  border: 2px dashed var(--grey-400);
  border-radius: var(--radius-sm);
  padding: var(--spacing-lg);
  text-align: center;
  background-color: var(--grey-100);
  transition: var(--transition-fast);
  cursor: pointer;
}

.file-upload:hover {
  border-color: var(--color-primary);
  background-color: var(--primary-light);
}

.file-upload.dragover {
  border-color: var(--color-primary);
  background-color: var(--primary-light);
}

/* Input groups */
.input-group {
  display: flex;
  position: relative;
}

.input-group-text {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.8rem;
  background-color: var(--grey-200);
  border: 1px solid var(--grey-400);
  border-right: none;
  border-radius: var(--radius-sm) 0 0 var(--radius-sm);
  font-weight: var(--font-weight-medium);
  color: var(--dark-color);
  min-width: 42px;
  transition: var(--transition-fast);
}

.form-group:focus-within .form-control,
.form-group:focus-within .input-group-text {
  border-color: var(--color-primary);
  background-color: var(--primary-light);
}

.input-group .form-control {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  flex: 1;
}

/* File input styling */
.file-input {
  display: block;
  width: 100%;
  height: var(--input-height, 2.75rem);
  padding: 0.8rem;
  border: 1px solid var(--grey-400);
  border-radius: var(--radius-sm);
  background-color: #fcfcfc;
  font-family: var(--font-main);
  font-size: var(--font-size);
  color: var(--text-color);
  cursor: pointer;
}

.file-input:focus {
  border-color: var(--color-primary);
  outline: none;
  box-shadow: 0 0 0 3px var(--primary-light);
}

.file-info {
  margin-top: var(--spacing-xs);
}

.existing-file-info {
  margin-top: var(--spacing-sm);
  padding: var(--spacing-sm);
  background-color: var(--primary-light);
  border-radius: var(--radius-sm);
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  align-items: center;
}

.view-document,
.download-document {
  color: var(--color-primary);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.view-document:hover,
.download-document:hover {
  text-decoration: underline;
}

/* Select styling */
select.form-control {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%236c757d' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 1rem;
  padding-right: 2.5rem;
}

/* Items section */
.items-container {
  border: 1px solid var(--grey-400);
  border-radius: var(--radius-sm);
  padding: var(--spacing);
  background-color: var(--light-color);
  margin-bottom: var(--spacing-md);
}

.item-row {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
  align-items: center;
}

.item-name {
  flex: 2;
  padding: 0.5rem;
  border-radius: var(--radius-sm);
  transition: var(--transition-fast);
}

.item-price {
  padding: 0.5rem;
  border-radius: var(--radius-sm);
  transition: var(--transition-fast);
  width: 70px; /* Fixed width to ensure alignment */
  min-width: 70px; /* Ensure minimum width for price column */
  flex-shrink: 0; /* Prevent shrinking */
  text-align: right;
}

.btn-icon {
  background: var(--danger-color);
  color: white;
  border: none;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 18px;
  padding: 0;
  line-height: 1;
  transition: var(--transition-fast);
  box-shadow: var(--box-shadow-sm);
}

.btn-icon:hover {
  background-color: hsl(6, 78%, 47%);
  transform: translateY(-1px);
  box-shadow: var(--box-shadow);
}

.items-summary {
  margin: var(--spacing-sm) 0;
  padding: var(--spacing);
  background-color: white;
  border-radius: var(--radius-sm);
  font-weight: var(--font-weight-medium);
  box-shadow: var(--box-shadow-sm);
}

/* For highlighting validation errors */
.has-error input,
.has-error .form-control {
  border-color: var(--danger-color);
  background-color: var(--danger-light);
}

.has-error label {
  color: var(--danger-color);
}

.error-message {
  color: var(--danger-color);
  font-size: var(--font-size-sm);
  margin-top: var(--spacing-xs);
  display: block;
}

.text-warning {
  color: var(--warning-color);
}

/* New merchant/category fields */
.new-merchant-group,
.new-category-group {
  margin-top: var(--spacing-sm);
  padding-top: var(--spacing-sm);
  border-top: 1px dashed var(--grey-400);
}

/* Receipt upload section */
.receipt-upload-section {
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--grey-300);
}

.receipt-form {
  margin-top: var(--spacing);
}

/* Form container */
.form-container {
  background-color: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--box-shadow);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.form-group {
  margin-bottom: var(--spacing-md);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
  color: var(--text-color);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing);
  margin-top: var(--spacing-lg);
}

/* Responsive adjustments for items */
@media (max-width: 768px) {
  .item-row {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .item-name {
    width: 100%;
  }

  .item-price {
    width: 70px; /* Maintain fixed width even on mobile */
    min-width: 70px;
  }

  .btn-icon {
    align-self: flex-end;
  }

  .form-actions {
    flex-direction: column;
  }

  .form-actions .button {
    width: 100%;
  }
}
