<?php

declare(strict_types=1);

namespace App\Models;

use App\Exceptions\InvalidArgumentException;
use App\Exceptions\DatabaseException;

class File extends BaseModel
{
    private const MAX_FILENAME_LENGTH = 255;

    protected static function getTableName(): string
    {
        return 'files';
    }

    private static function getColumnList(): string
    {
        return "id, user_id, filename, original_name, file_text, filesize, deleted_at, created_at, updated_at";
    }

    // Basic CRUD Operations

    public static function all(bool $includeDeleted = false, ?int $userId = null): array
    {
        $conditions = $includeDeleted ? [] : ['deleted_at IS NULL'];
        $params = [];

        if ($userId !== null) {
            $conditions[] = '(user_id = ? OR user_id IS NULL)';
            $params[] = $userId;
        }

        $whereClause = empty($conditions) ? '' : 'WHERE ' . implode(' AND ', $conditions);

        $sql = sprintf(
            "SELECT %s FROM %s %s ORDER BY created_at DESC",
            self::getColumnList(),
            self::getTableName(),
            $whereClause
        );

        return self::fetchMany($sql, $params);
    }

    public static function find(int $id, ?int $userId = null): ?array
    {
        return self::findBy('id', $id, false, $userId);
    }

    public static function findByFilename(string $filename, ?int $userId = null): ?array
    {
        return self::findBy('filename', $filename, false, $userId);
    }

    public static function findDeleted(int $id, ?int $userId = null): ?array
    {
        return self::findBy('id', $id, true, $userId);
    }

    private static function findBy(string $field, $value, bool $deleted = false, ?int $userId = null): ?array
    {
        $conditions = $deleted ? ['deleted_at IS NOT NULL'] : ['deleted_at IS NULL'];
        $params = [];

        if ($userId !== null) {
            $conditions[] = '(user_id = ? OR user_id IS NULL)';
            $params[] = $userId;
        }

        $conditions[] = "$field = ?";
        $params[] = $value;

        $whereClause = 'WHERE ' . implode(' AND ', $conditions);

        $sql = sprintf(
            "SELECT %s FROM %s %s LIMIT 1",
            self::getColumnList(),
            self::getTableName(),
            $whereClause
        );

        return self::fetchOne($sql, $params);
    }

    // File Creation and Management

    public static function createFile(
        string $filename,
        string $originalName,
        int $filesize,
        ?string $fileText = null,
        ?int $userId = null
    ): int {
        self::validateFilename($filename);
        self::ensureUniqueFilename($filename, $userId);

        $data = [
            'filename' => $filename,
            'original_name' => $originalName,
            'filesize' => $filesize,
            'file_text' => $fileText,
            'user_id' => $userId,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        return self::create($data);
    }

    public static function create(array $data): int
    {
        if (isset($data['filename'])) {
            self::validateFilename($data['filename']);
            self::ensureUniqueFilename($data['filename'], $data['user_id'] ?? null);
        }

        return self::transaction(function () use ($data) {
            $columns = implode(', ', array_keys($data));
            $placeholders = implode(', ', array_fill(0, count($data), '?'));

            $sql = sprintf(
                "INSERT INTO %s (%s) VALUES (%s)",
                self::getTableName(),
                $columns,
                $placeholders
            );

            self::executeSql($sql, array_values($data));
            return (int)self::getDb()->lastInsertId();
        });
    }

    public static function updateFile(
        int $id,
        ?string $filename = null,
        ?string $originalName = null,
        ?string $fileText = null,
        ?int $userId = null
    ): bool {
        $file = self::find($id);
        if (!$file) {
            return false;
        }

        // Convert to array if it's an object
        $fileArray = is_array($file) ? $file : ($file ? (array)$file : []);

        // Check if user has permission to update this file
        if ($userId !== null) {
            if (isset($fileArray['user_id']) && $fileArray['user_id'] !== null && $fileArray['user_id'] != $userId) {
                return false;
            }
        }

        $data = [];

        if ($filename) {
            self::validateFilename($filename);
            self::ensureFilenameNotUsedByOthers($id, $filename, $userId);
            $data['filename'] = $filename;
        }

        if ($originalName !== null) {
            $data['original_name'] = $originalName;
        }

        if ($fileText !== null) {
            $data['file_text'] = $fileText;
        }

        if (empty($data)) {
            return false;
        }

        $data['updated_at'] = date('Y-m-d H:i:s');

        return self::update($id, $data);
    }

    public static function update(int $id, array $data): bool
    {
        $file = self::find($id);
        if (!$file) {
            return false;
        }

        // Convert to array if it's an object
        $fileArray = is_array($file) ? $file : ($file ? (array)$file : []);

        $userId = $data['user_id'] ?? null;
        // Check if user has permission to update this file
        if ($userId !== null) {
            if (isset($fileArray['user_id']) && $fileArray['user_id'] !== null && $fileArray['user_id'] != $userId) {
                return false;
            }
        }

        if (isset($data['filename'])) {
            self::validateFilename($data['filename']);
            self::ensureFilenameNotUsedByOthers($id, $data['filename'], $userId);
        }

        if (empty($data)) {
            return false;
        }

        $data['updated_at'] = date('Y-m-d H:i:s');

        return self::transaction(function () use ($id, $data) {
            $setClause = implode(', ', array_map(fn($col) => "{$col} = :{$col}", array_keys($data)));

            $sql = sprintf(
                "UPDATE %s SET %s WHERE id = :id",
                self::getTableName(),
                $setClause
            );

            $data['id'] = $id;
            return self::executeSql($sql, $data);
        });
    }

    public static function delete(int $id, bool $hardDelete = false, ?int $userId = null): bool
    {
        $file = self::find($id, $userId);
        if (!$file) {
            return false;
        }

        // Convert to array if it's an object
        $fileArray = is_array($file) ? $file : (array)$file;

        // Check if user has permission to delete this file
        if ($userId !== null) {
            if (isset($fileArray['user_id']) && $fileArray['user_id'] !== null && $fileArray['user_id'] != $userId) {
                return false;
            }
        }

        return $hardDelete ? self::hardDelete($id) : self::softDelete($id);
    }

    private static function hardDelete(int $id): bool
    {
        return self::transaction(function () use ($id) {
            $sql = sprintf("DELETE FROM %s WHERE id = ?", self::getTableName());
            return self::executeSql($sql, [$id]);
        });
    }

    private static function softDelete(int $id): bool
    {
        return self::transaction(function () use ($id) {
            $sql = sprintf(
                "UPDATE %s SET deleted_at = NOW(), updated_at = NOW() WHERE id = ?",
                self::getTableName()
            );
            return self::executeSql($sql, [$id]);
        });
    }

    public static function restore(int $id, ?int $userId = null): bool
    {
        $file = self::findDeleted($id, $userId);
        if (!$file) {
            return false;
        }

        // Convert to array if it's an object
        $fileArray = is_array($file) ? $file : (array)$file;

        // Check if user has permission to restore this file
        if ($userId !== null) {
            if (isset($fileArray['user_id']) && $fileArray['user_id'] !== null && $fileArray['user_id'] != $userId) {
                return false;
            }
        }

        self::ensureFilenameNotUsedByOthers($id, $fileArray['filename'], $userId);

        return self::transaction(function () use ($id) {
            $sql = sprintf(
                "UPDATE %s SET deleted_at = NULL, updated_at = NOW() WHERE id = ?",
                self::getTableName()
            );

            return self::executeSql($sql, [$id]);
        });
    }

    // File Utility Methods

    public static function getTotalSize(?int $userId = null): int
    {
        $conditions = ['deleted_at IS NULL'];
        $params = [];

        if ($userId !== null) {
            $conditions[] = '(user_id = ? OR user_id IS NULL)';
            $params[] = $userId;
        }

        $whereClause = 'WHERE ' . implode(' AND ', $conditions);

        $sql = sprintf(
            "SELECT COALESCE(SUM(filesize), 0) FROM %s %s",
            self::getTableName(),
            $whereClause
        );

        return (int)self::fetchScalar($sql, $params);
    }

    public static function getMimeType(string $filePath): string
    {
        if (function_exists('mime_content_type')) {
            $mimeType = mime_content_type($filePath);
            if ($mimeType !== false) {
                return $mimeType;
            }
        }

        // Fallback to extension-based detection
        $extension = strtolower(pathinfo($filePath, PATHINFO_EXTENSION));
        $mimeTypes = [
            'txt' => 'text/plain',
            'pdf' => 'application/pdf',
            'doc' => 'application/msword',
            'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'xls' => 'application/vnd.ms-excel',
            'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'csv' => 'text/csv',
        ];

        return $mimeTypes[$extension] ?? 'application/octet-stream';
    }

    public static function isValidFileType(string $filename, array $allowedTypes): bool
    {
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        return isset($allowedTypes[$extension]);
    }

    public static function isValidFileSize(int $size, int $maxSize): bool
    {
        return $size <= $maxSize;
    }

    public static function extractTextFromFile(string $filePath, int $maxLength): ?string
    {
        $mimeType = mime_content_type($filePath);
        $isTextFile = in_array($mimeType, ['text/plain', 'text/csv', 'text/x-log']);

        if (!$isTextFile) {
            return null;
        }

        // Use fopen and fread to avoid loading entire file into memory
        $handle = fopen($filePath, 'r');
        if ($handle === false) {
            return null;
        }

        try {
            // Read only the amount we need plus a buffer for multibyte characters
            $bufferSize = min($maxLength * 4, filesize($filePath)); // 4x for worst-case multibyte
            $content = fread($handle, $bufferSize);
            
            if ($content === false) {
                return null;
            }

            // Truncate to the actual max length, respecting multibyte characters
            return mb_substr($content, 0, $maxLength);
        } finally {
            fclose($handle);
        }
    }

    public static function validateCategory(int $categoryId, ?int $userId = null): ?array
    {
        return \App\Models\Category::validateCategory($categoryId, $userId);
    }

    public static function validateMimeType(string $filePath, array $allowedTypes): bool
    {
        $mimeType = self::getMimeType($filePath);
        return in_array($mimeType, array_values($allowedTypes));
    }

    public static function validateFilePath(string $filePath): bool
    {
        return file_exists($filePath) && is_readable($filePath);
    }

    public static function validateUploadDirectory(string $directory): bool
    {
        return file_exists($directory)
            ? (is_dir($directory) && is_writable($directory))
            : mkdir($directory, 0755, true);
    }

    // Private Helper Methods

    private static function ensureFilenameNotUsedByOthers(int $currentId, string $filename, ?int $userId = null): void
    {
        $existing = self::findByFilename($filename, $userId);
        if ($existing && $existing['id'] !== $currentId) {
            throw new InvalidArgumentException('Filename already in use');
        }
    }

    private static function validateFilename(string $filename): void
    {
        $trimmed = trim($filename);
        if (empty($trimmed) || strlen($trimmed) > self::MAX_FILENAME_LENGTH) {
            throw new InvalidArgumentException(
                sprintf("Filename must be 1-%d characters", self::MAX_FILENAME_LENGTH)
            );
        }
    }

    private static function ensureUniqueFilename(string $filename, ?int $userId = null): void
    {
        $existing = self::findByFilename($filename, $userId);
        if ($existing) {
            throw new InvalidArgumentException('File with this name already exists');
        }
    }

    public static function findByUserId(int $userId): array
    {
        try {
            $db = self::getDb();
            $stmt = $db->prepare("
                SELECT
                    id, user_id, filename, original_name, filesize,
                    file_text, created_at, updated_at
                FROM files
                WHERE user_id = ?
                AND deleted_at IS NULL
                ORDER BY created_at DESC
            ");
            $stmt->execute([$userId]);

            return $stmt->fetchAll(\PDO::FETCH_ASSOC);
        } catch (\PDOException $e) {
            throw new DatabaseException("Error finding files by user ID: " . $e->getMessage(), (int)$e->getCode());
        }
    }

    public static function findByFilter(array $filters, bool $includeDeleted = false, ?int $userId = null): array
    {
        // Check if category_id filter is present and remove it
        $categoryId = null;
        if (isset($filters['category_id'])) {
            $categoryId = $filters['category_id'];
            unset($filters['category_id']);
        }

        $conditions = $includeDeleted ? [] : ['deleted_at IS NULL'];
        $params = [];

        // Most restrictive filter first - user ID
        if ($userId !== null) {
            $conditions[] = '(user_id = ? OR user_id IS NULL)';
            $params[] = $userId;
        }

        // Add other filters (excluding category_id which we handled separately)
        foreach ($filters as $column => $value) {
            $conditions[] = "$column = ?";
            $params[] = $value;
        }

        $whereClause = empty($conditions) ? '' : 'WHERE ' . implode(' AND ', $conditions);

        // Explicit column selection instead of using getColumnList()
        $sql = "SELECT
                id, user_id, filename, original_name, filesize,
                file_text, created_at, updated_at
            FROM files
            $whereClause
            ORDER BY created_at DESC";

        // For now, we'll just return all files since we can't filter by category_id
        // In a real implementation, you would need to add a category_id column to the files table
        return self::fetchMany($sql, $params);
    }
}
