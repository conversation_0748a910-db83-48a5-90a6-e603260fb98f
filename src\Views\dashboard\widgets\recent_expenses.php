<?php

/**
 * Recent expenses widget
 * Displays the most recent expenses added by the user
 */

// Set widget parameters
$title = 'Recent Expenses';
$icon = '<svg class="icon icon-expense"><use xlink:href="/assets/icons.svg#icon-expense"></use></svg>';
$widgetId = 'recent-expenses';
$widgetClass = 'recent-expenses-widget';
$hasData = !empty($recentExpenses);
$emptyMessage = 'No recent expenses found. <a href="/expenses/create">Add your first expense</a>.';

// Prepare widget content
ob_start();
?>

<?php if (!empty($recentExpenses)) : ?>
    <ul class="expense-list">
        <?php foreach ($recentExpenses as $expense) : ?>
            <li class="expense-item">
                <div class="expense-info">
                    <div class="expense-date"><?= date('M d', strtotime($expense['date'])) ?></div>
                    <div class="expense-details">
                        <span class="expense-description"><?= htmlspecialchars($expense['description']) ?></span>
                        <span class="expense-category"><?= htmlspecialchars($expense['category_name'] ?? 'Uncategorized') ?></span>
                    </div>
                </div>
                <div class="expense-amount">$<?= number_format($expense['amount'], 2) ?></div>
            </li>
        <?php endforeach; ?>
    </ul>
    <div class="widget-footer">
        <a href="/expenses" class="button small">View All Expenses</a>
    </div>
<?php endif; ?>

<?php
$content = ob_get_clean();

// Include the widget template
include __DIR__ . '/widget_template.php';
?>