<?php

declare(strict_types=1);

namespace App\Core;

use PDO;
use PDOException;
use App\Exceptions\DatabaseException;

final class Database
{
    private static ?PDO $connection = null;

    private const CONNECTION_OPTIONS = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::MYSQL_ATTR_INIT_COMMAND => 'SET NAMES utf8mb4',
    ];

    private function __construct()
    {
        // Private constructor to prevent instantiation
    }

    private function __clone()
    {
        // Private clone method to prevent cloning
    }

    public function __wakeup()
    {
        throw new DatabaseException('Database connection cannot be unserialized.');
    }

    public static function initialize(array $config): void
    {
        if (self::$connection !== null) {
            return;
        }

        $dsn = sprintf(
            '%s:host=%s;dbname=%s;charset=%s',
            $config['driver'] ?? 'mysql',
            $config['host'] ?? '127.0.0.1',
            $config['database'] ?? '',
            $config['charset'] ?? 'utf8mb4'
        );

        try {
            $options = $config['options'] ?? self::CONNECTION_OPTIONS;

            self::$connection = new PDO(
                $dsn,
                $config['username'] ?? '',
                $config['password'] ?? '',
                $options
            );

            register_shutdown_function(fn() => self::closeConnection());
        } catch (PDOException $exception) {
            $message = sprintf(
                'Failed to connect to database: [Driver: %s | Host: %s | Database: %s]',
                $config['driver'] ?? 'unknown',
                $config['host'] ?? 'unknown',
                $config['database'] ?? 'unknown'
            );

            throw new DatabaseException(
                $message,
                (int) $exception->getCode(),
                $exception
            );
        }
    }

    /**
     * Get the database connection instance
     *
     * @param bool $forceNew Whether to force a new connection
     * @return PDO The database connection
     * @throws DatabaseException If the connection is not initialized
     */
    public static function getInstance(bool $forceNew = false): PDO
    {
        if (self::$connection === null || $forceNew) {
            // If we need to force a new connection or the connection is null,
            // try to initialize it using environment variables or config
            if ($forceNew && self::$connection !== null) {
                // Close the existing connection first
                self::closeConnection();
            }

            // Get database configuration from environment or config
            $config = [
                'driver' => getenv('DB_DRIVER') ?: 'mysql',
                'host' => getenv('DB_HOST') ?: '127.0.0.1',
                'database' => getenv('DB_NAME') ?: 'expense_tracker',
                'username' => getenv('DB_USER') ?: 'root',
                'password' => getenv('DB_PASS') ?: '',
                'charset' => getenv('DB_CHARSET') ?: 'utf8mb4',
                'options' => self::CONNECTION_OPTIONS
            ];

            // Initialize the connection
            self::initialize($config);
        }

        if (self::$connection === null) {
            throw new DatabaseException('Database connection is not initialized.');
        }

        return self::$connection;
    }

    public static function closeConnection(): void
    {
        self::$connection = null;
    }

    public static function beginTransaction(): bool
    {
        return self::getInstance()->beginTransaction();
    }

    public static function commit(): bool
    {
        return self::getInstance()->commit();
    }

    public static function rollBack(): bool
    {
        return self::getInstance()->rollBack();
    }

    public static function query(string $sql, array $params = []): \PDOStatement
    {
        $statement = self::getInstance()->prepare($sql);
        $statement->execute($params);
        return $statement;
    }
}
