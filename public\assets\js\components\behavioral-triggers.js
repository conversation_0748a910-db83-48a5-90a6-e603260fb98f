function initializeBehavioralTriggers()
{
    setupProgressBars();
    setupProgressSteps();
    setupUrgencyCountdowns();
    setupAchievementBadges();
    setupNudges();
}

function setupProgressBars()
{
    const progressBars = document.querySelectorAll('.progress-bar');

    progressBars.forEach(progressBar => {
        const progressFill = progressBar.querySelector('.progress-bar-fill');
        if (!progressFill) {
            return;
        }

        const targetPercentage = progressFill.getAttribute('data-percentage') || '0';
        observeProgressBar(progressBar, progressFill, targetPercentage);
    });
}

function observeProgressBar(progressBar, progressFill, targetPercentage)
{
    const observer = new IntersectionObserver(entries => {
        entries.forEach(entry => {
            if (!entry.isIntersecting) {
                return;
            }

            animateProgressBar(progressBar, progressFill, targetPercentage);
            observer.unobserve(entry.target);
        });
    }, { threshold: 0.2 });

    observer.observe(progressBar);
}

function animateProgressBar(progressBar, progressFill, targetPercentage)
{
    setTimeout(() => {
        progressFill.style.width = `${targetPercentage} % `;

        if (parseInt(targetPercentage) >= 100) {
            progressFill.classList.add('success');
            triggerProgressComplete(progressBar);
        }
    }, 300);
}

function triggerProgressComplete(progressBar)
{
    const completionEvent = new CustomEvent('progress-complete', {
        bubbles: true,
        detail: { progressBar }
    });
    progressBar.dispatchEvent(completionEvent);
}

function setupProgressSteps()
{
    const progressStepsContainers = document.querySelectorAll('.progress-steps');

    progressStepsContainers.forEach(container => {
        const steps = container.querySelectorAll('.progress-step');
        if (steps.length === 0) {
            return;
        }

        const activeStepIndex = parseInt(container.getAttribute('data-active-step') || '0');
        updateStepStates(steps, activeStepIndex);

        if (container.classList.contains('interactive')) {
            setupInteractiveSteps(container, steps, activeStepIndex);
        }
    });
}

function updateStepStates(steps, activeIndex)
{
    steps.forEach((step, index) => {
        if (index < activeIndex) {
            step.classList.add('completed');
            step.classList.remove('active');
        } else if (index === activeIndex) {
            step.classList.add('active');
            step.classList.remove('completed');
        } else {
            step.classList.remove('active', 'completed');
        }
    });
}

function setupInteractiveSteps(container, steps, activeStepIndex)
{
    steps.forEach((step, index) => {
        step.addEventListener('click', () => {
            if (index > activeStepIndex + 1) {
                return;
            }

            container.setAttribute('data-active-step', index.toString());
            updateStepStates(steps, index);
            triggerStepChange(container, index);
        });
    });
}

function triggerStepChange(container, currentStep)
{
    const stepChangeEvent = new CustomEvent('step-change', {
        bubbles: true,
        detail: { container, currentStep }
    });
    container.dispatchEvent(stepChangeEvent);
}

function setupUrgencyCountdowns()
{
    const countdowns = document.querySelectorAll('.urgency-countdown');

    countdowns.forEach(countdown => {
        const targetDateStr = countdown.getAttribute('data-target-date');
        if (!targetDateStr) {
            return;
        }

        const targetDate = new Date(targetDateStr).getTime();
        startCountdown(countdown, targetDate);
    });
}

function startCountdown(countdown, targetDate)
{
    const countdownInterval = setInterval(() => {
        const now = new Date().getTime();
        const distance = targetDate - now;

        if (distance < 0) {
            handleExpiredCountdown(countdown, countdownInterval);
            return;
        }

        updateCountdownDisplay(countdown, distance);

        if (distance < (1000 * 60 * 60)) {
            countdown.classList.add('urgent');
        }
    }, 1000);

    countdown.setAttribute('data-interval-id', countdownInterval);
}

function handleExpiredCountdown(countdown, intervalId)
{
    clearInterval(intervalId);
    countdown.innerHTML = '<span class="urgency-expired">Expired</span>';

    const expiryEvent = new CustomEvent('countdown-expired', {
        bubbles: true,
        detail: { countdown }
    });
    countdown.dispatchEvent(expiryEvent);
}

function updateCountdownDisplay(countdown, timeRemaining)
{
    const days = Math.floor(timeRemaining / (1000 * 60 * 60 * 24));
    const hours = Math.floor((timeRemaining % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
    const minutes = Math.floor((timeRemaining % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((timeRemaining % (1000 * 60)) / 1000);

    countdown.innerHTML = `
        < div class = "urgency-countdown-unit" >
            < span class = "urgency-countdown-value" > ${days} < / span >
            < span class = "urgency-countdown-label" > days < / span >
        <  / div >
        < div class = "urgency-countdown-unit" >
            < span class = "urgency-countdown-value" > ${hours} < / span >
            < span class = "urgency-countdown-label" > hrs < / span >
        <  / div >
        < div class = "urgency-countdown-unit" >
            < span class = "urgency-countdown-value" > ${minutes} < / span >
            < span class = "urgency-countdown-label" > min < / span >
        <  / div >
        < div class = "urgency-countdown-unit" >
            < span class = "urgency-countdown-value" > ${seconds} < / span >
            < span class = "urgency-countdown-label" > sec < / span >
        <  / div >
    `;
}

function setupAchievementBadges()
{
    const badges = document.querySelectorAll('.achievement-badge');

    badges.forEach(badge => {
        setupBadgeHoverEffects(badge);

        if (badge.getAttribute('data-achievement-details')) {
            setupBadgeDetailsModal(badge);
        }
    });
}

function setupBadgeHoverEffects(badge)
{
    badge.addEventListener('mouseenter', () => {
        badge.style.transform = 'translateY(-3px) rotate(2deg)';
    });

    badge.addEventListener('mouseleave', () => {
        badge.style.transform = 'translateY(0) rotate(0)';
    });
}

function setupBadgeDetailsModal(badge)
{
    badge.style.cursor = 'pointer';

    badge.addEventListener('click', () => {
        const details = badge.getAttribute('data-achievement-details');
        showAchievementModal(details);
    });
}

function showAchievementModal(details)
{
    const modal = createAchievementModal(details);
    document.body.appendChild(modal);
    modal.style.display = 'block';

    setupModalCloseHandlers(modal);
}

function createAchievementModal(details)
{
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.innerHTML = `
        < div class = "modal-content" >
            < span class = "close" > & times; < / span >
            < h3 > Achievement Details < / h3 >
            < p > ${details} < / p >
        <  / div >
    `;
    return modal;
}

function setupModalCloseHandlers(modal)
{
    const closeButton = modal.querySelector('.close');

    closeButton.addEventListener('click', () => {
        closeAndRemoveModal(modal);
    });

    window.addEventListener('click', (event) => {
        if (event.target === modal) {
            closeAndRemoveModal(modal);
        }
    });
}

function closeAndRemoveModal(modal)
{
    modal.style.display = 'none';
    setTimeout(() => modal.remove(), 300);
}

function setupNudges()
{
    const nudges = document.querySelectorAll('.nudge');

    nudges.forEach(nudge => {
        if (nudge.classList.contains('dismissible')) {
            addNudgeCloseButton(nudge);
        }

        const autoHideDelay = parseInt(nudge.getAttribute('data-auto-hide') || '0');
        if (autoHideDelay > 0) {
            setupAutoHideNudge(nudge, autoHideDelay);
        }
    });
}

function addNudgeCloseButton(nudge)
{
    const closeButton = document.createElement('button');
    closeButton.className = 'nudge-close';
    closeButton.innerHTML = '&times;';
    closeButton.setAttribute('aria-label', 'Close');

    closeButton.addEventListener('click', () => {
        hideNudge(nudge);
    });

    nudge.appendChild(closeButton);
}

function setupAutoHideNudge(nudge, delay)
{
    setTimeout(() => hideNudge(nudge), delay);
}

function hideNudge(nudge)
{
    nudge.style.opacity = '0';
    setTimeout(() => nudge.style.display = 'none', 300);
}

export {
    initializeBehavioralTriggers,
    setupProgressBars,
    setupProgressSteps,
    setupUrgencyCountdowns
};
