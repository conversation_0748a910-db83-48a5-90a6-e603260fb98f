<h2>Change Password</h2>

<div class="form-container">
    <form method="POST" action="/profile/password">
        <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($csrf_token) ?>">

        <?php include __DIR__ . '/components/messages.php'; ?>

        <!-- Current Password Field -->
        <?php
        $currentPasswordField = [
            'type' => 'password',
            'name' => 'current_password',
            'label' => 'Current Password',
            'required' => true
        ];
        extract($currentPasswordField);
        include __DIR__ . '/components/form_field.php';
        ?>

        <!-- New Password Field -->
        <?php
        $newPasswordField = [
            'type' => 'password',
            'name' => 'new_password',
            'label' => 'New Password',
            'required' => true,
            'attributes' => [
                'pattern' => '(?=.*\d)(?=.*[a-z])(?=.*[A-Z]).{8,}',
                'aria-describedby' => 'password-requirements'
            ]
        ];
        extract($newPasswordField);
        include __DIR__ . '/components/form_field.php';
        ?>
        <div id="password-requirements" class="password-requirements">
            Password must:
            <ul>
                <li class="requirement">Be at least 8 characters long</li>
                <li class="requirement">Include at least one uppercase letter</li>
                <li class="requirement">Include at least one lowercase letter</li>
                <li class="requirement">Include at least one number</li>
            </ul>
        </div>

        <!-- Confirm Password Field -->
        <?php
        $confirmPasswordField = [
            'type' => 'password',
            'name' => 'confirm_password',
            'label' => 'Confirm New Password',
            'required' => true
        ];
        extract($confirmPasswordField);
        include __DIR__ . '/components/form_field.php';
        ?>

        <!-- Password Toggle -->
        <?php
        $showPasswordField = [
            'type' => 'checkbox',
            'name' => 'show_password',
            'label' => 'Show password',
            'id' => 'show_password'
        ];
        extract($showPasswordField);
        include __DIR__ . '/components/form_field.php';
        ?>

        <!-- Form Actions -->
        <div class="form-actions">
            <button type="submit" class="button">Update Password</button>
            <a href="/profile" class="button secondary">Back to Profile</a>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Password visibility toggle
    const showPasswordCheckbox = document.getElementById('show_password');
    const passwordFields = [
        document.getElementById('current_password'),
        document.getElementById('new_password'),
        document.getElementById('confirm_password')
    ];

    showPasswordCheckbox.addEventListener('change', function() {
        const type = this.checked ? 'text' : 'password';
        passwordFields.forEach(field => {
            if (field) field.type = type;
        });
    });

    // Password strength validation
    const newPassword = document.getElementById('new_password');
    const requirements = document.querySelectorAll('.requirement');

    newPassword.addEventListener('input', function() {
        const value = this.value;

        // Length check
        if (value.length >= 8) {
            requirements[0].classList.add('met');
        } else {
            requirements[0].classList.remove('met');
        }

        // Uppercase check
        if (/[A-Z]/.test(value)) {
            requirements[1].classList.add('met');
        } else {
            requirements[1].classList.remove('met');
        }

        // Lowercase check
        if (/[a-z]/.test(value)) {
            requirements[2].classList.add('met');
        } else {
            requirements[2].classList.remove('met');
        }

        // Number check
        if (/\d/.test(value)) {
            requirements[3].classList.add('met');
        } else {
            requirements[3].classList.remove('met');
        }
    });

    // Password confirmation validation
    const confirmPassword = document.getElementById('confirm_password');

    function validatePasswordMatch() {
        if (newPassword.value === confirmPassword.value) {
            confirmPassword.setCustomValidity('');
        } else {
            confirmPassword.setCustomValidity('Passwords do not match');
        }
    }

    newPassword.addEventListener('change', validatePasswordMatch);
    confirmPassword.addEventListener('keyup', validatePasswordMatch);
});
</script>

<style>
.password-requirements {
    margin-top: 10px;
    font-size: 0.9em;
    color: #666;
}

.password-requirements ul {
    margin-top: 5px;
    padding-left: 20px;
}

.requirement {
    margin-bottom: 3px;
    transition: color 0.3s;
}

.requirement.met {
    color: #4CAF50;
}

.requirement.met::before {
    content: "✓ ";
}
</style>
