<?php

declare(strict_types=1);

namespace App\Controllers;

use App\Core\Response;
use App\Core\Security\Validator;
use App\Services\AuthService;
use App\Services\FileService;
use App\Services\UserService;
use InvalidArgumentException;

class ProfileController extends BaseAuthController
{
    private const PASSWORD_RULES = [
        'current_password' => ['required'],
        'new_password' => ['required', 'min:8', 'contains_digit', 'contains_special'],
        'password_confirm' => ['required', 'matches:new_password']
    ];

    private const PROFILE_RULES = [
        'name' => ['required', 'string', 'min:2', 'max:100'],
        'email' => ['required', 'email'],
        'currency' => ['string', 'max:3'],
        'date_format' => ['string', 'max:20'],
        'remember_me_default' => ['boolean']
    ];

    private Validator $validator;

    public function __construct(
        private readonly FileService $fileService,
        private readonly UserService $userService,
        private readonly AuthService $authService
    ) {
        $this->validator = new Validator(self::PROFILE_RULES);
    }



    public function index(): Response
    {
        // Use the requireAuthentication method from BaseAuthController
        $authResponse = $this->requireAuthentication('Please login to view your profile');
        if ($authResponse !== null) {
            return $authResponse;
        }

        try {
            $userId = $this->getCurrentUserId();
            $user = $this->userService->getUserById($userId);

            if (!$user) {
                return $this->redirect('/login');
            }

            // Check for profile update result from cookie
            $message = null;
            $messageType = null;
            $formData = null;

            if (isset($_COOKIE['profile_update_result'])) {
                $tempFileName = $_COOKIE['profile_update_result'];

                // Validate the temp file name for security - only allow alphanumeric and specific characters
                if (preg_match('/^[a-zA-Z0-9_-]+\.tmp$/', $tempFileName)) {
                    $tempDir = sys_get_temp_dir();
                    $tempFile = $tempDir . DIRECTORY_SEPARATOR . $tempFileName;
                    
                    // Ensure the file is within the temp directory (prevent directory traversal)
                    $realTempFile = realpath($tempFile);
                    $realTempDir = realpath($tempDir);
                    
                    if ($realTempFile && $realTempDir && 
                        strpos($realTempFile, $realTempDir . DIRECTORY_SEPARATOR) === 0 &&
                        file_exists($realTempFile) && is_readable($realTempFile)) {
                        
                        $fileContent = file_get_contents($realTempFile);
                        if ($fileContent !== false) {
                            $updateResult = json_decode($fileContent, true);
                            
                            // Verify the user ID matches to prevent cross-user data leakage
                            if (is_array($updateResult) && isset($updateResult['user_id']) && 
                                $updateResult['user_id'] == $userId) {
                                if ($updateResult['success']) {
                                    $message = $updateResult['message'] ?? 'Profile updated successfully';
                                    $messageType = 'success';
                                } else {
                                    $message = $updateResult['message'] ?? 'An error occurred';
                                    $messageType = 'error';
                                    $formData = $updateResult['form_data'] ?? null;
                                }
                            }
                        }

                        // Delete the temp file after reading it
                        if (file_exists($realTempFile)) {
                            unlink($realTempFile);
                        }
                    }
                }

                // Clear the cookie
                setcookie('profile_update_result', '', [
                    'expires' => time() - 3600,
                    'path' => '/',
                    'secure' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on',
                    'httponly' => false,
                    'samesite' => 'Lax'
                ]);
            }

            // If we have a message from the temp file, set it in the session
            if ($message) {
                if ($messageType === 'success') {
                    $_SESSION['success'] = $message;

                    // Ensure the user data is refreshed
                    $user = $this->userService->getUserById($userId);
                    error_log(
                        "ProfileController::index - User data refreshed after successful update: " .
                        json_encode($user)
                    );
                    error_log(
                        "ProfileController::index - remember_me_default: " .
                        ($user['remember_me_default'] ?? 'not set')
                    );
                } else {
                    $_SESSION['errors'] = [$message];
                    if ($formData) {
                        // Ensure remember_me_default is properly set in the form data
                        if (isset($formData['remember_me_default'])) {
                            $formData['remember_me_default'] = (int)$formData['remember_me_default'];
                        }
                        $_SESSION['old'] = $formData;
                    }
                }
            }

            return $this->view('profile/index', [
                'user' => $user
            ]);
        } catch (\Exception $exception) {
            error_log("Error in ProfileController::index: " . $exception->getMessage());
            return $this->redirect('/login', 'Please log in to view your profile');
        }
    }

    public function edit(): Response
    {
        // Use the requireAuthentication method from BaseAuthController
        $authResponse = $this->requireAuthentication('Please login to edit your profile');
        if ($authResponse !== null) {
            return $authResponse;
        }

        try {
            $userId = $this->getCurrentUserId();
            $user = $this->userService->getUserById($userId);

            if (!$user) {
                return $this->redirect('/login');
            }

            return $this->view('profile/edit', [
                'user' => $user,
                'currencies' => $this->getSupportedCurrencies(),
                'dateFormats' => $this->getSupportedDateFormats(),
                'csrf_token' => $this->getCsrfToken()
            ]);
        } catch (\Exception $e) {
            error_log("Error in ProfileController::edit: " . $e->getMessage());
            return $this->redirect('/login', 'Please log in to edit your profile');
        }
    }

    public function update(): Response
    {
        error_log("ProfileController::update - Starting profile update");

        // Store the original session ID for debugging
        $originalSessionId = session_id();
        error_log("ProfileController::update - Original Session ID: " . $originalSessionId);
        error_log("ProfileController::update - SESSION: " . json_encode($_SESSION));
        error_log("ProfileController::update - POST: " . json_encode($_POST));

        // Ensure session is active
        if (session_status() !== PHP_SESSION_ACTIVE) {
            error_log("ProfileController::update - Session not active, starting session");
            session_start();
        }

        // Use the requireAuthentication method from BaseAuthController
        $authResponse = $this->requireAuthentication('Please login to update your profile');
        if ($authResponse !== null) {
            error_log("ProfileController::update - Authentication required, redirecting to login");
            return $authResponse;
        }

        // Store user ID for later use
        $userId = $this->getCurrentUserId();
        error_log("ProfileController::update - Current user ID: {$userId}");

        // Validate CSRF token
        $csrfResponse = $this->validateCsrfWithRedirect(
            $this->getPostValue('csrf_token'),
            '/profile',
            'Invalid security token'
        );
        if ($csrfResponse !== null) {
            error_log("ProfileController::update - CSRF validation failed");
            return $csrfResponse;
        }

        try {
            // Validate form data
            $data = $this->validator->validate($_POST);

            // Get user data
            $user = $this->userService->getUserById($userId);
            if (!$user) {
                error_log("ProfileController::update - User not found");
                throw new InvalidArgumentException('User not found');
            }

            // Handle profile image upload if present
            if (!empty($_FILES['profile_image']['name'])) {
                $data['profile_image'] = $this->handleProfileImageUpload($_FILES['profile_image']);
            }

            // Debug logging for remember_me_default
            error_log("ProfileController::update - POST data: " . json_encode($_POST));

            // Handle remember_me_default checkbox
            // First check if we have the hidden input value
            if (isset($_POST['remember_me_default_hidden'])) {
                $rememberMeDefault = (int)$_POST['remember_me_default_hidden'];
                error_log("ProfileController::update - Using remember_me_default_hidden value: {$rememberMeDefault}");
            } else {
                // Fall back to the checkbox value
                $rememberMeDefault = isset($_POST['remember_me_default'])
                    ? 1
                    : 0;
                error_log("ProfileController::update - Using checkbox value for remember_me_default: " .
                    $rememberMeDefault);
            }

            $data['remember_me_default'] = $rememberMeDefault;
            error_log("ProfileController::update - Final remember_me_default value: {$rememberMeDefault}");

            // Handle show_performance_metrics checkbox
            // First check if we have the hidden input value
            if (isset($_POST['show_performance_metrics_hidden'])) {
                $showPerformanceMetrics = (int) $_POST['show_performance_metrics_hidden'];
                error_log(
                    "ProfileController::update - Using show_performance_metrics_hidden value: " .
                    $showPerformanceMetrics
                );
            } else {
                // Fall back to the checkbox value
                $showPerformanceMetrics = isset($_POST['show_performance_metrics']) ? 1 : 0;
                error_log("ProfileController::update - Using checkbox value for show_performance_metrics: " .
                    $showPerformanceMetrics);
            }

            $data['show_performance_metrics'] = $showPerformanceMetrics;
            error_log("ProfileController::update - show_performance_metrics value: {$showPerformanceMetrics}");

            // Debug the data before update
            error_log("ProfileController::update - Data before update: " . json_encode($data));

            // Ensure remember_me_default is properly set (force it to be an integer)
            $rememberMeValue = (int)$rememberMeDefault;
            $data['remember_me_default'] = $rememberMeValue;
            error_log(
                "ProfileController::update - Forced remember_me_default to integer: " .
                $data['remember_me_default']
            );

            // Ensure show_performance_metrics is properly set (force it to be an integer)
            $data['show_performance_metrics'] = (int)$showPerformanceMetrics;
            error_log(
                "ProfileController::update - Forced show_performance_metrics to integer: " .
                $data['show_performance_metrics']
            );

            // Update the user's preferences
            $updateResult = $this->userService->updateUser($userId, $data);

            // Verify the update by fetching the user again
            $updatedUser = $this->userService->getUserById($userId);
            error_log("ProfileController::update - User data after update: " . json_encode($updatedUser));
            error_log(
                "ProfileController::update - remember_me_default after update: " .
                ($updatedUser['remember_me_default'] ?? 'not set')
            );

            // Update session with new performance metrics setting
            if (isset($data['show_performance_metrics'])) {
                $_SESSION['show_performance_metrics'] = (bool)$data['show_performance_metrics'];
                error_log(
                    "ProfileController::update - Updated session show_performance_metrics: " .
                    $_SESSION['show_performance_metrics']
                );
            }

            // Update the remember_me cookie to match the user's preference
            $rememberMe = $rememberMeDefault ? 'true' : 'false';
            setcookie(
                'remember_me',
                $rememberMe,
                [
                    'expires' => time() + 86400 * 30, // 30 days
                    'path' => '/',
                    'secure' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on',
                    'httponly' => false,
                    'samesite' => 'Lax'
                ]
            );

            // COMPREHENSIVE FIX: Create a completely new approach to handle the redirect

            // 1. Store success message in a temporary file with secure naming
            $tempFileName = 'profile_update_' . $userId . '_' . time() . '_' . bin2hex(random_bytes(8)) . '.tmp';
            $tempFile = sys_get_temp_dir() . DIRECTORY_SEPARATOR . $tempFileName;
            
            $tempData = json_encode([
                'success' => true,
                'message' => 'Profile updated successfully',
                'user_id' => $userId,
                'session_id' => session_id(),
                'time' => time(),
                'form_data' => $data,
                'remember_me_default' => $rememberMeDefault
            ]);
            
            if (file_put_contents($tempFile, $tempData) === false) {
                error_log("Failed to create temporary file for profile update result");
                $_SESSION['success'] = 'Profile updated successfully';
            } else {
                // 2. Store only the temp file name (not full path) in a cookie
                setcookie(
                    'profile_update_result',
                    $tempFileName,
                    [
                        'expires' => time() + 300, // 5 minutes
                        'path' => '/',
                        'secure' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on',
                        'httponly' => false,
                        'samesite' => 'Lax'
                    ]
                );
            }

            // 3. Ensure session data is written to storage
            $_SESSION['last_activity'] = time();
            $_SESSION['authenticated'] = true;

            // 4. Regenerate session ID to prevent session fixation
            $oldSessionId = session_id();
            session_regenerate_id(false); // Keep old session data
            $newSessionId = session_id();
            error_log("ProfileController::update - Regenerated session ID from {$oldSessionId} to {$newSessionId}");

            // 5. Ensure the session cookie is properly set with the new session ID
            $params = session_get_cookie_params();
            $cookieLifetime = isset($_SESSION['remember_me']) && $_SESSION['remember_me'] === true
                ? time() + 86400 * 30  // 30 days for remember me
                : 0;                   // Session cookie (expires when browser closes)

            setcookie(
                session_name(),
                session_id(),
                [
                    'expires' => $cookieLifetime,
                    'path' => $params['path'],
                    'domain' => $params['domain'],
                    'secure' => $params['secure'],
                    'httponly' => $params['httponly'],
                    'samesite' => $params['samesite'] ?? 'Lax'
                ]
            );

            // 6. Write session data to storage
            session_write_close();

            // 7. Use a direct header redirect instead of JavaScript
            error_log("ProfileController::update - Using direct header redirect to /profile");

            // Set headers for redirect
            header('Location: /profile');
            header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
            header('Pragma: no-cache');
            header('Expires: 0');

            // Exit to prevent further execution
            exit('Redirecting to profile page...');
        } catch (\Exception $e) {
            error_log("Error in ProfileController::update: " . $e->getMessage());

            // COMPREHENSIVE FIX: Create a completely new approach to handle the error redirect

            // Debug the error
            error_log("ProfileController::update - Error: " . $e->getMessage());

            // Ensure remember_me_default is properly set in the form data
            $formData = $this->sanitizeFormData($_POST);

            // First check if we have the hidden input value
            if (isset($_POST['remember_me_default_hidden'])) {
                $formData['remember_me_default'] = (int)$_POST['remember_me_default_hidden'];
                error_log("ProfileController::update - Error case: Using remember_me_default_hidden value: " .
                    "{$formData['remember_me_default']}");
            } else {
                // Fall back to the checkbox value
                $formData['remember_me_default'] =
                    isset($_POST['remember_me_default'])
                        ? 1
                        : 0;
                error_log("ProfileController::update - Error case: Using checkbox value for remember_me_default: "
                    . $formData['remember_me_default']);
            }

            error_log("ProfileController::update - Error case: Final remember_me_default in form data: "
                . $formData['remember_me_default']);

            // 1. Store error message in a temporary file with secure naming
            $tempFileName = 'profile_update_error_' . $userId . '_' . time() . '_' . bin2hex(random_bytes(8)) . '.tmp';
            $tempFile = sys_get_temp_dir() . DIRECTORY_SEPARATOR . $tempFileName;
            
            $tempData = json_encode([
                'success' => false,
                'message' => $e->getMessage(),
                'user_id' => $userId,
                'session_id' => session_id(),
                'time' => time(),
                'form_data' => $formData
            ]);
            
            if (file_put_contents($tempFile, $tempData) === false) {
                error_log("Failed to create temporary file for profile update error");
                $_SESSION['errors'] = [$e->getMessage()];
                $_SESSION['old'] = $formData;
            } else {
                // 2. Store only the temp file name (not full path) in a cookie
                setcookie(
                    'profile_update_result',
                    $tempFileName,
                    [
                        'expires' => time() + 300, // 5 minutes
                        'path' => '/',
                        'secure' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on',
                        'httponly' => false,
                        'samesite' => 'Lax'
                    ]
                );
            }

            // 3. Ensure session data is written to storage
            $_SESSION['last_activity'] = time();
            $_SESSION['authenticated'] = true;

            // 4. Regenerate session ID to prevent session fixation
            $oldSessionId = session_id();
            session_regenerate_id(false); // Keep old session data
            $newSessionId = session_id();
            error_log("ProfileController::update - Error case: Regenerated session ID from "
                . "{$oldSessionId} to {$newSessionId}");

            // 5. Ensure the session cookie is properly set with the new session ID
            $params = session_get_cookie_params();
            $cookieLifetime = isset($_SESSION['remember_me']) && $_SESSION['remember_me'] === true
                ? time() + 86400 * 30  // 30 days for remember me
                : 0;                   // Session cookie (expires when browser closes)

            setcookie(
                session_name(),
                session_id(),
                [
                    'expires' => $cookieLifetime,
                    'path' => $params['path'],
                    'domain' => $params['domain'],
                    'secure' => $params['secure'],
                    'httponly' => $params['httponly'],
                    'samesite' => $params['samesite'] ?? 'Lax'
                ]
            );

            // 6. Write session data to storage
            session_write_close();

            // 7. Use a direct header redirect instead of JavaScript
            error_log("ProfileController::update - Error case: Using direct header redirect to /profile");

            // Set headers for redirect
            header('Location: /profile');
            header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
            header('Pragma: no-cache');
            header('Expires: 0');

            // Exit to prevent further execution
            exit('Redirecting to profile page...');
        }
    }

    public function changePassword(): Response
    {
        // Use the requireAuthentication method from BaseAuthController
        $authResponse = $this->requireAuthentication('Please login to change your password');
        if ($authResponse !== null) {
            return $authResponse;
        }

        try {
            return $this->view('profile/change_password', [
                'csrf_token' => $this->getCsrfToken()
            ]);
        } catch (\Exception $e) {
            error_log("Error in ProfileController::changePassword: " . $e->getMessage());
            return $this->redirect('/login', 'Please log in to change your password');
        }
    }

    public function updateAjax(): Response
    {
        error_log("ProfileController::updateAjax - Starting AJAX profile update");
        error_log("ProfileController::updateAjax - POST: " . json_encode($_POST));

        // Ensure session is active
        if (session_status() !== PHP_SESSION_ACTIVE) {
            error_log("ProfileController::updateAjax - Session not active, starting session");
            session_start();
        }

        // Check if the user is authenticated
        if (!isset($_SESSION['user_id'])) {
            error_log("ProfileController::updateAjax - User not authenticated");
            return Response::json([
                'success' => false,
                'error' => 'Authentication required',
                'redirect' => '/login'
            ], 401);
        }

        // Get the current user ID from the session
        $userId = $_SESSION['user_id'];
        error_log("ProfileController::updateAjax - User ID: {$userId}");

        // Get the current user data
        $user = $this->userService->getUserById($userId);
        if (!$user) {
            error_log("ProfileController::updateAjax - User not found");
            return Response::json([
                'success' => false,
                'error' => 'User not found',
                'redirect' => '/login'
            ], 404);
        }

        // Validate the CSRF token
        try {
            $this->validateCsrf($_POST['csrf_token'] ?? null);
            error_log("ProfileController::updateAjax - CSRF token validated successfully");
        } catch (\Exception $e) {
            error_log("ProfileController::updateAjax - Invalid CSRF token: " . $e->getMessage());
            return Response::json([
                'success' => false,
                'error' => 'Invalid CSRF token. Please try again.'
            ], 403);
        }

        try {
            // Prepare the data for update
            $data = [];

            // Handle basic fields
            $fields = ['name', 'email', 'currency', 'date_format', 'monthly_budget'];
            foreach ($fields as $field) {
                if (isset($_POST[$field])) {
                    $data[$field] = trim($_POST[$field]);
                }
            }

            // Handle remember_me_default checkbox
            $rememberMeDefault = isset($_POST['remember_me_default']) && $_POST['remember_me_default'] === '1' ? 1 : 0;
            error_log("ProfileController::updateAjax - remember_me_default value: {$rememberMeDefault}");
            $data['remember_me_default'] = $rememberMeDefault;

            // Handle show_performance_metrics checkbox
            $showPerformanceMetrics = isset($_POST['show_performance_metrics']) &&
                $_POST['show_performance_metrics'] === '1'
                ? 1
                : 0;
            error_log("ProfileController::updateAjax - show_performance_metrics value: {$showPerformanceMetrics}");
            $data['show_performance_metrics'] = $showPerformanceMetrics;

            // Skip profile image handling for AJAX updates
            // This prevents issues with file uploads in AJAX requests

            // Debug the data before update
            error_log("ProfileController::updateAjax - Data before update: " . json_encode($data));

            // Update the user's preferences
            $updateResult = $this->userService->updateUser($userId, $data);

            // Verify the update by fetching the user again
            $updatedUser = $this->userService->getUserById($userId);
            error_log("ProfileController::updateAjax - User data after update: " . json_encode($updatedUser));

            // Update session with new performance metrics preference
            if (isset($data['show_performance_metrics'])) {
                $_SESSION['show_performance_metrics'] = (bool)$data['show_performance_metrics'];
                error_log(
                    "ProfileController::updateAjax - Updated session show_performance_metrics: " .
                    $_SESSION['show_performance_metrics']
                );
            }

            // Return a JSON response
            return Response::json([
                'success' => true,
                'message' => 'Profile updated successfully.',
                // Reload if performance metrics setting changed
                'reload' => $data['show_performance_metrics'] != $user['show_performance_metrics'],
                'user' => [
                    'id' => $updatedUser['id'],
                    'name' => $updatedUser['name'],
                    'email' => $updatedUser['email'],
                    'remember_me_default' => (bool)$updatedUser['remember_me_default'],
                    'show_performance_metrics' => (bool)$updatedUser['show_performance_metrics']
                ]
            ]);
        } catch (\Exception $e) {
            error_log("ProfileController::updateAjax - Error: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return Response::json([
                'success' => false,
                'error' => 'An error occurred while updating your profile: ' . $e->getMessage()
            ], 500);
        }
    }

    public function updatePassword(): Response
    {
        // Use the requireAuthentication method from BaseAuthController
        $authResponse = $this->requireAuthentication('Please login to update your password');
        if ($authResponse !== null) {
            return $authResponse;
        }

        // Validate CSRF token
        $csrfResponse = $this->validateCsrfWithRedirect(
            $_POST['csrf_token'] ?? null,
            '/profile/password',
            'Invalid security token'
        );
        if ($csrfResponse !== null) {
            return $csrfResponse;
        }

        try {
            $data = (new Validator(self::PASSWORD_RULES))->validate($_POST);

            $userId = $this->getCurrentUserId();

            // Validate current password and update to new password
            if (!$this->userService->validateCurrentPassword($userId, $data['current_password'])) {
                throw new InvalidArgumentException('Current password is incorrect');
            }

            $this->userService->updateUserPassword($userId, $data['new_password']);
            return $this->redirect('/profile', 'Password updated successfully');
        } catch (\Exception $e) {
            error_log("Error in ProfileController::updatePassword: " . $e->getMessage());
            return $this->redirectWithErrors('/profile/password', $e->getMessage());
        }
    }



    private function handleProfileImageUpload(array $file): string
    {
        try {
            return $this->fileService->saveProfileImage($file);
        } catch (\Exception $e) {
            throw new InvalidArgumentException($e->getMessage());
        }
    }

    private function sanitizeFormData(array $data): array
    {
        $sanitized = [];
        foreach ($data as $key => $value) {
            $sanitized[$key] = is_string($value)
                ? htmlspecialchars($value, ENT_QUOTES, 'UTF-8')
                : $value;
        }
        return $sanitized;
    }


    private function getSupportedCurrencies(): array
    {
        return [
            'USD' => 'US Dollar',
            'EUR' => 'Euro',
            'GBP' => 'British Pound',
            // Add more currencies as needed
        ];
    }

    private function getSupportedDateFormats(): array
    {
        return [
            'Y-m-d' => 'YYYY-MM-DD',
            'd/m/Y' => 'DD/MM/YYYY',
            'm/d/Y' => 'MM/DD/YYYY',
            // Add more formats as needed
        ];
    }
}
