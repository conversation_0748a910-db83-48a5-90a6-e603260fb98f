/* ===== MODAL COMPONENTS ===== */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    overflow: auto;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .modal.show {
    opacity: 1;
  }

  .modal-content {
    background-color: white;
    margin: 10vh auto;
    padding: var(--spacing-lg);
    width: 90%;
    max-width: 600px;
    border-radius: var(--border-radius-lg);
    box-shadow: 0 5px 30px rgba(0, 0, 0, 0.3);
    animation: modalFadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateY(0);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
  }

  .modal.closing .modal-content {
    transform: translateY(-30px) scale(0.95);
    opacity: 0;
  }

  .modal-content h3 {
    margin-bottom: var(--spacing-md);
    color: var(--primary-color);
    padding-right: 2rem;
  }

  .modal-body {
    margin-bottom: var(--spacing-md);
  }

  .modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing);
    margin-top: var(--spacing-lg);
  }

  .close {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    color: var(--text-muted);
    font-size: 1.5rem;
    font-weight: bold;
    cursor: pointer;
    transition: var(--transition-fast);
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
  }

  .close:hover {
    color: var(--danger-color);
    background-color: rgba(0, 0, 0, 0.05);
  }

  @media (max-width: 768px) {
    .modal-content {
      width: 95%;
      margin: 5% auto;
    }
  }