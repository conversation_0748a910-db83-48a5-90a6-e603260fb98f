<?php
/**
 * @var array $expenses
 */

// Include shared helper functions
include_once __DIR__ . '/../../shared/helpers.php';
?>

<div class="expense-data" role="region" aria-label="Expense Records">
    <div class="table-responsive container u-margin-top-md">
        <table class="table table-striped">
            <thead>
                <tr>
                    <th class="select-all-column">
                        <input type="checkbox" class="select-all" hidden>
                    </th>
                    <th scope="col" class="date-column" aria-sort="none">
                        <div class="th-content">
                            <span>Date</span>
                            <button class="sort-button" data-sort="date" aria-label="Sort by date ascending">
                                <svg class="sort-icon" aria-hidden="true" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                  <path d="M16 17.01V10h-2v7.01h-3L15 21l4-3.99h-3zM9 3L5 6.99h3V14h2V6.99h3L9 3z"/>
                                </svg>
                                <span class="direction-indicator"></span>
                            </button>
                        </div>
                    </th>
                    <th scope="col" class="description-column" aria-sort="none">
                        <div class="th-content">
                            <span>Description</span>
                            <button class="sort-button" data-sort="description" aria-label="Sort by description ascending">
                                <svg class="sort-icon" aria-hidden="true" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                  <path d="M16 17.01V10h-2v7.01h-3L15 21l4-3.99h-3zM9 3L5 6.99h3V14h2V6.99h3L9 3z"/>
                                </svg>
                                <span class="direction-indicator"></span>
                            </button>
                        </div>
                    </th>
                    <th scope="col" class="category-column" aria-sort="none">
                        <div class="th-content">
                            <span>Category</span>
                            <button class="sort-button" data-sort="category_name" aria-label="Sort by category ascending">
                                <svg class="sort-icon" aria-hidden="true" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                  <path d="M16 17.01V10h-2v7.01h-3L15 21l4-3.99h-3zM9 3L5 6.99h3V14h2V6.99h3L9 3z"/>
                                </svg>
                                <span class="direction-indicator"></span>
                            </button>
                        </div>
                    </th>
                    <th scope="col" class="amount-column" aria-sort="none">
                        <div class="th-content">
                            <span>Amount</span>
                            <button class="sort-button" data-sort="amount" aria-label="Sort by amount ascending">
                                <svg class="sort-icon" aria-hidden="true" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                  <path d="M16 17.01V10h-2v7.01h-3L15 21l4-3.99h-3zM9 3L5 6.99h3V14h2V6.99h3L9 3z"/>
                                </svg>
                                <span class="direction-indicator"></span>
                            </button>
                        </div>
                    </th>
                    <th scope="col" class="actions-column">
                        <span class="sr-only">Actions</span>
                    </th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($expenses as $expense) : ?>
                    <?php include __DIR__ . '/_table_row.php'; ?>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</div>
