<?php

declare(strict_types=1);

namespace App\Controllers;

use App\Core\Response;
use App\Core\Security\Validator;
use App\Services\CategoryService;
use App\Exceptions\AuthorizationException;
use App\Exceptions\InvalidArgumentException;

class CategoryController extends BaseAuthController
{
    private Validator $validator;

    public function __construct(
        private readonly CategoryService $categoryService
    ) {
        $this->validator = new Validator([
            'name' => ['required', 'string', 'min:2', 'max:50'],
            'description' => ['string', 'max:255']
        ]);
    }



    public function index(): Response
    {
        // Use the requireAuthentication method from BaseAuthController
        $authResponse = $this->requireAuthentication('Please login to view your categories');
        if ($authResponse !== null) {
            return $authResponse;
        }

        try {
            $userId = $this->getCurrentUserId();
            return $this->renderCategoriesIndex($userId);
        } catch (\InvalidArgumentException $exception) {
            return $this->redirect('/login', 'Please log in to view your categories');
        } catch (\Exception $exception) {
            error_log("Error in CategoryController::index: " . $exception->getMessage());
            return $this->redirect('/dashboard', 'An error occurred while loading categories', 'error');
        }
    }

    private function renderCategoriesIndex(int $userId): Response
    {
        $categories = $this->getCombinedCategories($userId);
        $categories = $this->enhanceCategoriesWithStats($categories, $userId);
        $expenseBreakdown = $this->categoryService->getCategoryExpenseBreakdown($userId);

        return $this->view('categories/index', [
            'categories' => $categories,
            'userId' => $userId,
            'expenseBreakdown' => $expenseBreakdown,
            'csrf_token' => $this->getCsrfToken()
        ]);
    }

    private function getCombinedCategories(int $userId): array
    {
        $systemCategories = $this->categoryService->getAllCategories();
        $userCategories = $this->categoryService->getUserCategories($userId);
        $categories = array_merge($systemCategories, $userCategories);

        usort($categories, fn($a, $b) => strcasecmp($a['name'], $b['name']));

        if (empty($categories)) {
            $this->createDefaultCategory($userId);
            $userCategories = $this->categoryService->getUserCategories($userId);
            $categories = array_merge($systemCategories, $userCategories);
        }

        return $categories;
    }

    private function createDefaultCategory(int $userId): void
    {
        try {
            $this->categoryService->createUserCategory(
                $userId,
                'General',
                'Default category for your expenses'
            );
        } catch (\Exception $exception) {
            error_log("Error creating default category: " . $exception->getMessage());
        }
    }

    private function enhanceCategoriesWithStats(array $categories, int $userId): array
    {
        foreach ($categories as &$category) {
            $categoryId = $category['id'];
            $category['expense_count'] = $this->categoryService->expenseCount($categoryId, $userId);
            $category['total_expenses'] = $this->categoryService->totalExpenses($categoryId, $userId);
            $category['is_in_use'] = $this->categoryService->isInUse($categoryId);
        }

        return $categories;
    }

    public function create(): Response
    {
        // Use the requireAuthentication method from BaseAuthController
        $authResponse = $this->requireAuthentication('Please login to create categories');
        if ($authResponse !== null) {
            return $authResponse;
        }

        try {
            return $this->renderCategoryForm();
        } catch (\Exception $exception) {
            error_log("Error in CategoryController::create: " . $exception->getMessage());
            return $this->redirect('/categories', 'An error occurred', 'error');
        }
    }

    private function renderCategoryForm(): Response
    {
        return $this->view('categories/form', [
            'csrf_token' => $this->getCsrfToken()
        ]);
    }

    public function store(): Response
    {
        // Use the requireAuthentication method from BaseAuthController
        $authResponse = $this->requireAuthentication('Please login to create categories');
        if ($authResponse !== null) {
            return $authResponse;
        }

        // Validate CSRF token
        $csrfResponse = $this->validateCsrfWithRedirect(
            $_POST['csrf_token'] ?? null,
            '/categories/create',
            'Invalid security token'
        );
        if ($csrfResponse !== null) {
            return $csrfResponse;
        }

        try {
            $data = $this->validator->validate($_POST);
            $userId = $this->getCurrentUserId();

            return $this->createCategory($userId, $data);
        } catch (\Exception $e) {
            return $this->redirectWithErrors('/categories/create', $e->getMessage(), $_POST);
        }
    }

    private function createCategory(int $userId, array $data): Response
    {
        $name = $data['name'];
        $description = $data['description'] ?? null;

        $categoryId = $this->categoryService->createUserCategory($userId, $name, $description);

        if (!$categoryId) {
            throw new InvalidArgumentException('Failed to create category');
        }

        return $this->redirect('/categories', 'Category created successfully');
    }

    public function edit(int $id): Response
    {
        // Use the requireAuthentication method from BaseAuthController
        $authResponse = $this->requireAuthentication('Please login to edit categories');
        if ($authResponse !== null) {
            return $authResponse;
        }

        try {
            $userId = $this->getCurrentUserId();
            $category = $this->findUserCategory($id, $userId);
            return $this->renderEditForm($category);
        } catch (AuthorizationException $e) {
            return $this->redirect('/categories', $e->getMessage(), 'error');
        } catch (\Exception $e) {
            error_log("Error in CategoryController::edit: " . $e->getMessage());
            return $this->redirect('/categories', 'An error occurred', 'error');
        }
    }

    private function findUserCategory(int $id, int $userId): array
    {
        $category = $this->categoryService->getUserCategoryById($id, $userId);

        if (!$category) {
            throw new AuthorizationException('Category not found or access denied');
        }

        return $category;
    }

    private function renderEditForm(array $category): Response
    {
        return $this->view('categories/form', ['category' => $category]);
    }

    public function update(int $id): Response
    {
        // Use the requireAuthentication method from BaseAuthController
        $authResponse = $this->requireAuthentication('Please login to update categories');
        if ($authResponse !== null) {
            return $authResponse;
        }

        // Validate CSRF token
        $csrfResponse = $this->validateCsrfWithRedirect(
            $_POST['csrf_token'] ?? null,
            "/categories/{$id}/edit",
            'Invalid security token'
        );
        if ($csrfResponse !== null) {
            return $csrfResponse;
        }

        try {
            $data = $this->validator->validate($_POST);
            $userId = $this->getCurrentUserId();

            $this->findUserCategory($id, $userId);
            return $this->updateCategory($id, $userId, $data);
        } catch (\Exception $e) {
            error_log("Error in CategoryController::update: " . $e->getMessage());
            return $this->redirect("/categories/{$id}/edit", $e->getMessage(), 'error');
        }
    }

    private function updateCategory(int $id, int $userId, array $data): Response
    {
        $name = $data['name'];
        $description = $data['description'] ?? null;

        if (!$this->categoryService->updateUserCategory($id, $userId, $name, $description)) {
            throw new InvalidArgumentException('Failed to update category');
        }

        return $this->redirect('/categories', 'Category updated successfully');
    }

    public function delete(int $id): Response
    {
        // Use the requireAuthentication method from BaseAuthController
        $authResponse = $this->requireAuthentication('Please login to delete categories');
        if ($authResponse !== null) {
            return $authResponse;
        }

        // Validate CSRF token
        $csrfResponse = $this->validateCsrfWithRedirect(
            $_POST['csrf_token'] ?? null,
            '/categories',
            'Invalid security token'
        );
        if ($csrfResponse !== null) {
            return $csrfResponse;
        }

        try {
            $userId = $this->getCurrentUserId();

            $this->findUserCategory($id, $userId);
            return $this->deleteCategory($id, $userId);
        } catch (\Exception $e) {
            error_log("Error in CategoryController::delete: " . $e->getMessage());
            return $this->redirect('/categories', $e->getMessage(), 'error');
        }
    }

    private function deleteCategory(int $id, int $userId): Response
    {
        if (!$this->categoryService->deleteUserCategory($id, $userId)) {
            throw new InvalidArgumentException('Failed to delete category');
        }

        return $this->redirect('/categories', 'Category deleted successfully');
    }

    public function statistics(int $id): Response
    {
        // Use the requireAuthentication method from BaseAuthController
        $authResponse = $this->requireAuthentication('Please login to view category statistics');
        if ($authResponse !== null) {
            return $authResponse;
        }

        try {
            $userId = $this->getCurrentUserId();
            $category = $this->validateAndGetCategory($id, $userId);

            return $this->renderCategoryStatistics($category, $id, $userId);
        } catch (\Exception $e) {
            error_log("Error in CategoryController::statistics: " . $e->getMessage());
            return $this->redirect('/categories', $e->getMessage(), 'error');
        }
    }

    private function validateAndGetCategory(int $id, int $userId): array
    {
        $category = $this->findCategory($id, $userId);

        if (!$category) {
            throw new AuthorizationException('Category not found or access denied');
        }

        return $category;
    }

    private function renderCategoryStatistics(array $category, int $id, int $userId): Response
    {
        $isSystemCategory = isset($category['is_system']) && $category['is_system'];
        $stats = $this->getCategoryStatistics($id, $userId, $isSystemCategory);

        return $this->view('categories/statistics', [
            'category' => $category,
            'stats' => $stats
        ]);
    }

    private function findCategory(int $id, int $userId): ?array
    {
        $category = $this->categoryService->getCategoryById($id);

        if (!$category) {
            $category = $this->categoryService->getUserCategoryById($id, $userId);
        }

        return $category;
    }

    private function getCategoryStatistics(int $id, int $userId, bool $isSystemCategory): array
    {
        return $isSystemCategory
            ? $this->categoryService->getCategoryStatistics($id, $userId)
            : $this->categoryService->getUserCategoryStatistics($id, $userId);
    }
}
