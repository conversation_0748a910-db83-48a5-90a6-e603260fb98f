/* ===== PROFILE PAGES STYLES ===== */

/* Profile Container */
.profile-container {
  background-color: white;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow);
  padding: var(--spacing-lg);
  margin-top: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
}

/* Profile Header */
.profile-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--grey-200);
}

.profile-image {
  margin-right: var(--spacing-md);
}

.profile-avatar {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--grey-200);
  box-shadow: var(--box-shadow-sm);
}

.profile-info h3 {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: var(--font-size-lg);
  color: var(--dark-color);
}

.profile-email {
  color: var(--text-muted);
  margin: 0;
  font-size: var(--font-size);
}

/* Profile Details */
.profile-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.detail-section {
  margin-bottom: var(--spacing-lg);
}

.detail-section h4 {
  margin-top: 0;
  margin-bottom: var(--spacing);
  padding-bottom: var(--spacing-xs);
  border-bottom: 1px solid var(--grey-200);
  color: var(--dark-color);
  font-weight: var(--font-weight-semibold);
}

.detail-row {
  display: flex;
  margin-bottom: var(--spacing-sm);
}

.detail-label {
  flex: 0 0 150px;
  font-weight: var(--font-weight-medium);
  color: var(--text-muted);
}

.detail-value {
  flex: 1;
  color: var(--text-color);
}

/* Notification List */
.notification-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.notification-list li {
  padding: var(--spacing-sm) 0;
  position: relative;
  padding-left: var(--spacing-md);
}

.notification-list li:before {
  content: "";
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
}

.notification-list li.enabled:before {
  background-color: var(--success-color);
}

.notification-list li.disabled:before {
  background-color: var(--grey-400);
}

/* Profile Actions */
.profile-actions {
  display: flex;
  gap: var(--spacing);
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--grey-200);
}

/* Profile Edit Form */
.current-image {
  display: flex;
  align-items: center;
  gap: var(--spacing);
  margin-bottom: var(--spacing);
  padding: var(--spacing);
  background-color: var(--grey-100);
  border-radius: var(--border-radius);
}

.profile-thumbnail {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--grey-200);
}

.image-actions {
  flex: 1;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  cursor: pointer;
}

.file-info {
  margin-top: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

.change-password-link {
  text-align: center;
  margin-top: var(--spacing);
}

.change-password-link a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
}

.change-password-link a:hover {
  text-decoration: underline;
}

/* Profile Image Preview */
.image-preview {
  margin-top: var(--spacing);
  margin-bottom: var(--spacing);
}

.preview-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--spacing);
  background-color: var(--grey-100);
  border-radius: var(--border-radius);
  border: 1px dashed var(--grey-400);
}

.profile-preview {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--grey-200);
  box-shadow: var(--box-shadow-sm);
}

.preview-info {
  margin-top: var(--spacing-sm);
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

/* Notification Toggles */
.notification-toggle {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing);
  background-color: var(--grey-100);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-sm);
  transition: var(--transition);
}

.notification-toggle:hover {
  background-color: var(--grey-200);
}

.toggle-status {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
}

.toggle-status.enabled {
  background-color: var(--success-light);
  color: var(--success-dark);
}

.toggle-status.disabled {
  background-color: var(--grey-300);
  color: var(--grey-700);
}

/* Alert Container */
.alert-container {
  margin-bottom: var(--spacing);
}

/* Disabled File Input */
.file-input.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .profile-details {
    grid-template-columns: 1fr;
    gap: var(--spacing);
  }

  .profile-header {
    flex-direction: column;
    align-items: flex-start;
    text-align: center;
  }

  .profile-image {
    margin-right: 0;
    margin-bottom: var(--spacing);
    align-self: center;
  }

  .profile-info {
    align-self: center;
  }

  .detail-row {
    flex-direction: column;
  }

  .detail-label {
    margin-bottom: var(--spacing-xs);
  }

  .profile-actions {
    flex-direction: column;
  }

  .profile-actions .button {
    width: 100%;
    justify-content: center;
  }
}
