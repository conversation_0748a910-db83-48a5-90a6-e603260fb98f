<?php
// Get token from query parameter
$token ??= '';

// Prepare content for the layout
$title = 'Set New Password';
ob_start();
?>

<form method="POST" action="/password/update">
    <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($csrf_token) ?>">
    <input type="hidden" name="token" value="<?= htmlspecialchars($token) ?>">
    
    <!-- New Password Field -->
    <div class="form-group">
        <label for="password">New Password</label>
        <input type="password" 
               name="password" 
               id="password" 
               required
               aria-describedby="password-help" 
               autofocus>
        <small id="password-help" class="form-hint">
            Must be at least 8 characters with a number and special character
        </small>
    </div>
   
    <!-- Confirm Password Field -->
    <div class="form-group">
        <label for="password_confirm">Confirm New Password</label>
        <input type="password" name="password_confirm" id="password_confirm" required>
    </div>
   
    <!-- Form Actions -->
    <div class="form-actions">
        <button type="submit" class="button">Reset Password</button>
    </div>
</form>

<div class="form-links">
    <a href="/login">Remember your password? Login</a>
</div>

<?php
$content = ob_get_clean();
require __DIR__ . '/../_layout.php';
?>
