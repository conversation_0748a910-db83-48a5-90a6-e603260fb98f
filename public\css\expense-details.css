/* Premium Expense Header Styles */
.expense-premium-header {
    display: flex;
    justify-content: space-between;
    background: linear-gradient(135deg, #2c3e50, #4ca1af);
    color: white;
    border-radius: 12px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.expense-premium-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('/images/pattern.svg');
    opacity: 0.05;
    pointer-events: none;
}

.expense-details-column {
    flex: 1;
}

.expense-title-wrapper {
    margin-bottom: 16px;
}

.expense-title {
    font-size: 28px;
    font-weight: 600;
    margin: 0;
    line-height: 1.2;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.expense-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
}

.expense-date-badge,
.expense-merchant-badge {
    display: flex;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.15);
    border-radius: 20px;
    padding: 6px 12px;
    font-size: 14px;
}

.expense-date-badge .icon,
.expense-merchant-badge .icon {
    width: 16px;
    height: 16px;
    margin-right: 6px;
    fill: white;
}

.expense-amount-column {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-end;
    padding-left: 24px;
}

.amount-wrapper {
    text-align: right;
}

.amount-label {
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 1px;
    opacity: 0.8;
    margin-bottom: 4px;
}

.amount-value {
    font-size: 32px;
    font-weight: 700;
    color: white;
}

/* Feature Badges */
.feature-badges {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
    margin-bottom: 24px;
}

.feature-badge {
    display: flex;
    align-items: center;
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 12px 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s, box-shadow 0.2s;
}

.feature-badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.feature-badge.category {
    border-left: 4px solid #4caf50;
}

.feature-badge.payment {
    border-left: 4px solid #2196f3;
}

.feature-badge.document {
    border-left: 4px solid #ff9800;
}

.badge-icon {
    width: 24px;
    height: 24px;
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.badge-icon svg {
    width: 20px;
    height: 20px;
    fill: #555;
}

.feature-badge.category .badge-icon svg {
    fill: #4caf50;
}

.feature-badge.payment .badge-icon svg {
    fill: #2196f3;
}

.feature-badge.document .badge-icon svg {
    fill: #ff9800;
}

.badge-text {
    font-weight: 500;
    color: #333;
}

/* Floating Action Buttons */
.floating-action-buttons {
    display: flex;
    gap: 12px;
    margin-bottom: 32px;
}

.fab {
    display: flex;
    align-items: center;
    padding: 10px 20px;
    border-radius: 50px;
    font-weight: 500;
    text-decoration: none;
    color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
    font-size: 14px;
}

.fab svg {
    width: 18px;
    height: 18px;
    margin-right: 8px;
    fill: currentColor;
}

.fab.edit {
    background-color: #2196f3;
}

.fab.view {
    background-color: #4caf50;
}

.fab.delete {
    background-color: #f44336;
}

.fab:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

.fab:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .expense-premium-header {
        flex-direction: column;
    }

    .expense-amount-column {
        align-items: flex-start;
        padding-left: 0;
        margin-top: 16px;
    }

    .amount-wrapper {
        text-align: left;
    }

    .feature-badges {
        flex-direction: column;
    }

    .floating-action-buttons {
        flex-wrap: wrap;
    }
}

/* Receipt Preview Styles */
.receipt-preview {
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    margin-bottom: 24px;
}

.receipt-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #eee;
}

.receipt-header h4 {
    display: flex;
    align-items: center;
    margin: 0;
    font-size: 16px;
    color: #333;
}

.receipt-header svg {
    width: 18px;
    height: 18px;
    margin-right: 8px;
    fill: #555;
}

.view-full-receipt {
    display: flex;
    align-items: center;
    color: #2196f3;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
}

.view-full-receipt svg {
    width: 16px;
    height: 16px;
    margin-left: 4px;
    fill: currentColor;
}

.receipt-preview-content {
    padding: 20px;
}

.receipt-merchant-preview {
    font-size: 18px;
    font-weight: 600;
    text-align: center;
    margin-bottom: 16px;
    padding-bottom: 16px;
    border-bottom: 1px dashed #ddd;
}

.receipt-details-preview {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.receipt-row {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
}

.receipt-label {
    color: #666;
    font-weight: 500;
}

.receipt-value {
    font-weight: 500;
    color: #333;
}

.total-row {
    margin-top: 8px;
    padding-top: 12px;
    border-top: 1px solid #eee;
    font-size: 16px;
    font-weight: 600;
}

.total-row .receipt-value {
    color: #2196f3;
}

.extraction-summary {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 16px;
    padding: 8px;
    background-color: #e8f5e9;
    border-radius: 4px;
    color: #4caf50;
    font-size: 14px;
    font-weight: 500;
}

.extraction-summary svg {
    width: 16px;
    height: 16px;
    margin-right: 6px;
    fill: currentColor;
}

/* Document thumbnail */
.document-thumbnail {
    margin-top: 16px;
}

.thumbnail-container {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.thumbnail-image {
    width: 100%;
    height: auto;
    display: block;
}