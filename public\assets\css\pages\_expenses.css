/* ===== EXPENSES PAGES STYLES ===== */

/* Page Title */
.page-title {
  font-size: var(--font-size-xl);
  margin-bottom: var(--spacing-md);
}

/* Expense Page Header */
.page-header {
  margin-top: 80px; /* Increased margin to prevent header obstruction */
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing);
  border-bottom: 1px solid var(--border-color);
}

.header-content h1 {
  font-size: var(--font-size-xxl);
  margin-bottom: var(--spacing-xs);
  color: var(--text-color);
  font-weight: var(--font-weight-bold);
}

.expense-summary {
  display: flex;
  align-items: center;
  gap: var(--spacing);
  color: var(--text-muted);
  font-size: var(--font-size);
}

.expense-total {
  font-weight: var(--font-weight-semibold);
  color: var(--text-color);
}

.expense-count {
  color: var(--text-muted);
}

/* Expense Controls */
.expense-controls {
  background-color: var(--card-bg);
  padding: var(--spacing);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-sm);
  margin-bottom: var(--spacing-xl);
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: var(--spacing);
  align-items: center;
}

/* Filter Bar Container */
.filter-bar-container {
  background-color: var(--grey-100);
  padding: var(--spacing);
  border-radius: var(--border-radius-lg);
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--box-shadow-sm);
}

/* Enhanced Filter Bar */
.filter-bar {
  height: 72px;
  background: #FAFBFC;
  border-bottom: 1px solid #E1E8ED;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
  padding: 16px 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: var(--spacing-lg);
  flex-wrap: nowrap;
  overflow-x: auto;
}

/* Filter Item Variants */
.filter-item--date {
  width: 180px;
}

.filter-item--category {
  width: 160px;
}

.filter-item--amount {
  width: 200px;
}

.filter-item--search {
  width: 240px;
}

.filter-item--tags {
  flex: 1;
  min-width: 200px;
}

.filter-item {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.filter-item label {
  font-weight: var(--font-weight-medium);
  color: var(--text-color);
  font-size: var(--font-size-sm);
  margin-bottom: 4px;
}

.filter-item input,
.filter-item select,
.filter-item button {
  height: 40px;
  padding: 8px 12px;
  border: 1.5px solid #CBD6E2;
  border-radius: 8px;
  font-family: var(--font-main);
  font-size: var(--font-size);
  background-color: var(--card-bg);
  color: var(--text-color);
  transition: all 150ms ease;
  cursor: pointer;
}

.filter-item input:hover,
.filter-item select:hover,
.filter-item button:hover {
  border-color: #4A90E2;
}

.filter-item input:focus,
.filter-item select:focus,
.filter-item button:focus {
  outline: none;
  border-color: #4A90E2;
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

.filter-item.active input,
.filter-item.active select,
.filter-item.active button {
  border-color: #4A90E2;
  background-color: rgba(74, 144, 226, 0.05);
  font-weight: var(--font-weight-medium);
  box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.1);
}

/* Dropdown and Slider Panels */
.dropdown-panel,
.slider-panel {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid #E1E8ED;
  z-index: var(--z-index-dropdown);
  display: none;
  margin-top: 4px;
  padding: 12px;
  transition: all 200ms ease;
}

.dropdown-panel.show,
.slider-panel.show {
  display: block;
}

.dropdown-panel {
  max-height: 200px;
  overflow-y: auto;
}

.dropdown-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 150ms ease;
}

.dropdown-option:hover {
  background-color: #F8F9FA;
}

.dropdown-option.selected {
  background-color: rgba(74, 144, 226, 0.1);
  color: #4A90E2;
  font-weight: var(--font-weight-medium);
}

.dropdown-option-count {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  background-color: #E9ECEF;
  padding: 2px 6px;
  border-radius: 12px;
  min-width: 20px;
  text-align: center;
}

/* Active Filter Tags */
.active-filter-tag {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  background-color: #4A90E2;
  color: white;
  padding: 4px 8px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: var(--font-weight-medium);
  margin-right: 6px;
  margin-bottom: 4px;
  transition: all 150ms ease;
}

.active-filter-tag:hover {
  background-color: #357ABD;
  transform: translateY(-1px);
}

.active-filter-tag .remove {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0;
  margin-left: 2px;
  border-radius: 50%;
  width: 14px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  line-height: 1;
  transition: all 150ms ease;
}

.active-filter-tag .remove:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

/* Clear Filters Button */
.clear-filters-btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  color: #6C757D;
  text-decoration: none;
  font-size: 13px;
  font-weight: var(--font-weight-medium);
  border-radius: 6px;
  transition: all 150ms ease;
  background-color: transparent;
  border: 1px solid #DEE2E6;
  cursor: pointer;
  white-space: nowrap;
}

.clear-filters-btn:hover {
  color: #495057;
  background-color: #F8F9FA;
  border-color: #ADB5BD;
  text-decoration: none;
  transform: translateY(-1px);
}

.clear-filters-btn:focus {
  outline: none;
  border-color: #4A90E2;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.clear-filters-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* Results Counter */
#results-counter {
  font-size: 13px;
  color: #6C757D;
  font-weight: var(--font-weight-medium);
  margin-left: auto;
  white-space: nowrap;
  padding: 6px 12px;
  background-color: #F8F9FA;
  border-radius: 6px;
  border: 1px solid #E9ECEF;
}

#results-counter.loading {
  opacity: 0.6;
}

#results-counter .count-number {
  font-weight: var(--font-weight-bold);
  color: #4A90E2;
}

.action-links {
  display: flex;
  gap: var(--spacing);
  align-items: center;
}

.text-link {
  display: inline-flex;
  align-items: center;
  color: var(--primary-color);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: var(--transition-fast);
}

.text-link:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: var(--spacing-xl);
  background-color: var(--grey-100);
  border-radius: var(--border-radius-lg);
  margin-bottom: var(--spacing-xl);
}

.empty-state-icon {
  margin-bottom: var(--spacing);
  color: var(--text-muted);
}

.empty-state h2 {
  margin-bottom: var(--spacing);
  color: var(--text-color);
}

.empty-state p {
  margin-bottom: var(--spacing-lg);
  color: var(--text-muted);
}

/* Expense Data Table */
.expense-data {
  background-color: var(--card-bg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-sm);
  margin-bottom: var(--spacing-xl);
  overflow: hidden;
}

.table-responsive {
  width: 100%;
  overflow: hidden;
}

table {
  width: 100%;
  border-collapse: collapse;
}

th, td {
  padding: var(--spacing);
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

th {
  background-color: var(--grey-100);
  font-weight: var(--font-weight-semibold);
}

tr:hover {
  background-color: var(--grey-100);
}

/* Modern Table Styles */
.modern-table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--card-bg);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--box-shadow-sm);
}

.modern-table th,
.modern-table td {
  padding: var(--spacing-md);
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

.modern-table th {
  background-color: var(--grey-100);
  font-weight: var(--font-weight-semibold);
  color: var(--text-color);
}

.modern-table tbody tr:nth-child(even) {
  background-color: var(--grey-50);
}

.modern-table tbody tr:hover {
  background-color: var(--primary-light);
  transition: var(--transition-fast);
}

.modern-table tbody tr:last-child td {
  border-bottom: none;
}

.expense-description {
  color: var(--text-color);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
}

.expense-description:hover {
  text-decoration: underline;
  color: var(--primary-color);
}

.category-badge {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-sm);
  background-color: var(--grey-200);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-sm);
  color: var(--text-color);
}

.amount-column {
  font-weight: var(--font-weight-semibold);
  white-space: nowrap;
}

.icon-button {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--border-radius-sm);
  transition: var(--transition-fast);
}

.icon-button:hover {
  background-color: var(--grey-200);
  color: var(--text-color);
}

.action-group {
  display: flex;
  gap: var(--spacing-xs);
  justify-content: flex-end;
}

/* Dropdown Menu */
.dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-toggle {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.dropdown-menu {
  position: absolute;
  right: 0;
  top: 100%;
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  min-width: 160px;
  z-index: 10;
  display: none;
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.dropdown-menu.show {
  display: block;
}

.dropdown-item {
  display: block;
  padding: var(--spacing-sm) var(--spacing);
  color: var(--text-color);
  text-decoration: none;
  transition: var(--transition-fast);
  white-space: nowrap;
}

.dropdown-item:hover {
  background-color: var(--grey-100);
}

.dropdown-item.danger {
  color: var(--danger-color);
}

.dropdown-item.danger:hover {
  background-color: hsl(6, 75%, 97%);
}

.dropdown-divider {
  height: 1px;
  background-color: var(--border-color);
  margin: var(--spacing-xs) 0;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
}

.pagination-controls {
  display: flex;
  gap: var(--spacing-sm);
}

.pagination-button {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing);
  border-radius: var(--border-radius);
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  color: var(--text-color);
  cursor: pointer;
  transition: var(--transition-fast);
}

.pagination-button:hover {
  background-color: var(--primary-light);
}

.pagination-button.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

.pagination-info {
  padding: var(--spacing-sm) var(--spacing);
  color: var(--text-muted);
}

/* Insights Section */
.insights-section {
  background-color: var(--card-bg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-sm);
  overflow: hidden;
}

.insights-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
}

.tab-button {
  padding: var(--spacing) var(--spacing-lg);
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  cursor: pointer;
  font-weight: var(--font-weight-medium);
  color: var(--text-muted);
  transition: var(--transition-fast);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.tab-button:hover {
  color: var(--text-color);
  background-color: var(--grey-100);
}

.tab-button.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

.tab-panel {
  padding: var(--spacing-lg);
  display: none;
}

.tab-panel.active {
  display: block;
}

.insight-links {
  list-style: none;
  padding: 0;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing);
}

.insight-link {
  display: flex;
  align-items: center;
  gap: var(--spacing);
  padding: var(--spacing);
  border-radius: var(--border-radius);
  background-color: var(--grey-100);
  text-decoration: none;
  color: var(--text-color);
  transition: var(--transition);
}

.insight-link:hover {
  background-color: var(--primary-light);
  transform: translateY(-2px);
}

.insight-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  color: var(--primary-color);
}

.insight-content h3 {
  margin: 0 0 var(--spacing-xs) 0;
  font-size: var(--font-size-md);
}

.insight-content p {
  margin: 0;
  color: var(--text-muted);
  font-size: var(--font-size-sm);
}

.document-category-list {
  list-style: none;
  padding: 0;
}

.document-category {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing);
  border-radius: var(--border-radius);
  background-color: var(--grey-100);
  text-decoration: none;
  color: var(--text-color);
  margin-bottom: var(--spacing-sm);
  transition: var(--transition-fast);
}

.document-category:hover {
  background-color: var(--primary-light);
}

.document-count {
  background-color: var(--card-bg);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

.empty-documents-message {
  padding: var(--spacing);
  color: var(--text-muted);
  text-align: center;
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--modal-overlay);
  z-index: 100;
  align-items: center;
  justify-content: center;
}

.modal.show {
  display: flex;
}

.modal-content {
  background-color: var(--card-bg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-lg);
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  padding: var(--spacing) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  font-size: var(--font-size-lg);
}

.close-button {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: var(--spacing-xs);
  border-radius: var(--border-radius-sm);
  transition: var(--transition-fast);
}

.close-button:hover {
  background-color: var(--grey-200);
  color: var(--text-color);
}

.modal-body {
  padding: var(--spacing-lg);
  overflow-y: auto;
  flex: 1;
}

.modal-footer {
  padding: var(--spacing) var(--spacing-lg);
  border-top: 1px solid var(--border-color);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing);
}

.document-preview-container {
  width: 100%;
  min-height: 300px;
  background-color: var(--grey-100);
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Expense Detail Page */
.expense-details {
  margin-bottom: var(--spacing-xl);
}

/* Header Section */
.expense-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.header-left {
  flex: 1;
}

.title-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: calc(var(--spacing-sm) * 0.25);
  margin-bottom: var(--spacing-xs);
  flex-wrap: wrap;
}

.expense-title {
  margin: 0;
  margin-bottom: 0;
  padding-bottom: 0;
  font-size: var(--font-size-xl);
  color: var(--text-color);
  line-height: 1.1;
  flex-shrink: 0;
  margin-right: var(--spacing-sm);
}

.title-row .category-badge {
  margin-top: 0;
  align-self: flex-start;
}

/* Responsive adjustments for expense details */
@media (max-width: 768px) {
  .expense-header {
    flex-direction: column;
  }

  .expense-actions {
    margin-top: var(--spacing-md);
    width: 100%;
    display: flex;
    justify-content: flex-start;
    gap: var(--spacing-sm);
  }

  .title-row {
    width: 100%;
    flex-wrap: wrap;
  }

  .expense-title {
    margin-right: var(--spacing-sm);
    margin-bottom: 0;
  }

  .title-row .category-badge {
    align-self: center;
  }

  .expense-date {
    margin-top: var(--spacing-xs);
  }
}

/* Title styling moved up to be with title-row */

.expense-date {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-top: 0;
  margin-bottom: var(--spacing-sm);
  color: var(--text-muted);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-normal);
  max-width: 150px;
  flex-basis: 100%;
  order: 2;
}

.category-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  background-color: var(--primary-light);
  color: var(--grey-100);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size-sm);
  white-space: nowrap;
}

.expense-actions {
  display: flex;
  gap: var(--spacing-sm);
}

/* Card Grid for Expense Details */
.expense-card-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

/* Expense Card Styles */
.expense-card {
  border-left: 4px solid var(--primary-color);
  height: 100%;
}

.expense-card.primary-details {
  border-left-color: var(--primary-color);
}

.expense-card.notes-card {
  border-left-color: var(--accent-calm);
}

.expense-card.document-card {
  border-left-color: var(--accent-energy);
}

.expense-card.empty-document {
  border-left-color: var(--grey-400);
}

/* Amount Highlight */
.amount-highlight {
  background-color: rgba(var(--primary-rgb), 0.1);
  padding: var(--spacing);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-lg);
  text-align: center;
}

.amount-label {
  display: block;
  color: var(--text-muted);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-xs);
}

.amount-value {
  display: block;
  font-size: var(--font-size-xxl);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
}

/* Document Info */
.document-info {
  margin-bottom: var(--spacing-lg);
}

/* No Document Message */
.no-document-message {
  text-align: center;
  padding: var(--spacing);
  color: var(--text-muted);
}

.no-document-message p {
  margin-bottom: var(--spacing);
}

/* Metadata Section */
.metadata-section {
  margin-top: var(--spacing-lg);
  border-top: 1px solid var(--grey-200);
  padding-top: var(--spacing);
}

.metadata-section details {
  width: 100%;
}

.metadata-section summary {
  cursor: pointer;
  color: var(--text-muted);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-sm);
  border-radius: var(--border-radius);
  transition: var(--transition-fast);
}

.metadata-section summary:hover {
  background-color: var(--grey-100);
  color: var(--text-color);
}

.metadata-content {
  margin-top: var(--spacing);
  background-color: var(--grey-100);
  border-radius: var(--border-radius);
  padding: var(--spacing);
}

/* Notes Content */
.notes-content {
  white-space: pre-wrap;
  line-height: 1.5;
}

/* Document Actions */
.document-actions {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing);
}

/* SVG Icons */
.svg-icon {
  width: 20px;
  height: 20px;
  fill: currentColor;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .expense-header {
    flex-direction: column;
    gap: var(--spacing);
  }

  .title-row {
    flex-wrap: wrap;
    gap: var(--spacing-sm);
  }

  .expense-title {
    margin-bottom: var(--spacing-xs);
  }

  .expense-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .expense-card-grid {
    grid-template-columns: 1fr;
  }

  .document-actions {
    flex-direction: column;
  }
}

.expense-detail-row {
  display: flex;
  margin-bottom: var(--spacing);
  border-bottom: 1px solid var(--grey-200);
  padding-bottom: var(--spacing);
}

.expense-detail-row:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.expense-detail-label {
  flex: 0 0 200px;
  font-weight: var(--font-weight-medium);
  color: var(--text-muted);
}

.expense-detail-value {
  flex: 1;
}

.expense-notes, .expense-document {
  background-color: var(--card-bg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-sm);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.expense-notes h3, .expense-document h3 {
  margin-top: 0;
  margin-bottom: var(--spacing);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.notes-content {
  background-color: var(--grey-100);
  padding: var(--spacing);
  border-radius: var(--border-radius);
  white-space: pre-wrap;
}

.document-preview {
  background-color: var(--grey-100);
  border-radius: var(--border-radius);
  padding: var(--spacing);
  text-align: center;
}

.document-actions {
  display: flex;
  gap: var(--spacing);
  margin-top: var(--spacing);
}

/* Expense Form */
.expense-form {
  background-color: var(--card-bg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-sm);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.form-section {
  margin-bottom: var(--spacing-lg);
}

.form-section-title {
  margin-bottom: var(--spacing);
  padding-bottom: var(--spacing-xs);
  border-bottom: 1px solid var(--grey-200);
}

.form-row {
  display: flex;
  gap: var(--spacing);
  margin-bottom: var(--spacing);
}

.form-group {
  flex: 1;
  margin-bottom: var(--spacing);
}

.form-group label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
}

.form-control {
  width: 100%;
  padding: var(--spacing-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-family: var(--font-main);
  font-size: var(--font-size);
}

.form-hint {
  display: block;
  margin-top: var(--spacing-xs);
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing);
  margin-top: var(--spacing-lg);
}

/* Search Page */
.search-container {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: var(--spacing-lg);
}

.search-filters {
  background-color: var(--card-bg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-sm);
  padding: var(--spacing);
}

.search-filters h3 {
  margin-top: 0;
  margin-bottom: var(--spacing);
}

.filter-section {
  margin-bottom: var(--spacing);
}

.filter-section-title {
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-xs);
}

.filter-options {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.filter-option {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.filter-option label {
  flex: 1;
  cursor: pointer;
}

.active-filters {
  margin-top: var(--spacing);
}

.filter-item {
  background-color: var(--primary-light);
  color: var(--primary-dark);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing-xs);
  display: inline-block;
  margin-right: var(--spacing-xs);
}

.search-results {
  background-color: var(--card-bg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-sm);
  padding: var(--spacing-lg);
}

.no-results {
  padding: var(--spacing-lg);
  text-align: center;
  background-color: var(--grey-100);
  border-radius: var(--border-radius);
  margin-top: var(--spacing);
}

.document-indicator {
  margin-left: var(--spacing-xs);
  font-size: var(--font-size-sm);
}

.search-query {
  margin-top: var(--spacing-xs);
  font-size: var(--font-size);
  color: var(--text-muted);
}

/* Responsive Styles */
@media (max-width: 992px) {
  .search-container {
    grid-template-columns: 1fr;
  }

  .expense-header {
    flex-direction: column;
  }

  .expense-actions {
    margin-top: var(--spacing);
    width: 100%;
  }

  .form-row {
    flex-direction: column;
  }

  .insight-links {
    grid-template-columns: 1fr;
  }
}

/* Slider Specific Styles */
.slider-panel .slider-container {
  padding: 16px 0;
}

.slider-panel .slider-labels {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 12px;
  color: var(--text-muted);
}

.slider-panel .slider-inputs {
  display: flex;
  gap: 12px;
  margin-top: 12px;
}

.slider-panel .slider-input {
  flex: 1;
  height: 32px;
  padding: 4px 8px;
  border: 1px solid #DEE2E6;
  border-radius: 4px;
  font-size: 13px;
  text-align: center;
}

/* Filter Toggle for Mobile */
.filter-toggle {
  display: none;
  width: 100%;
  padding: 12px;
  background-color: #F8F9FA;
  border: 1px solid #DEE2E6;
  border-radius: 8px;
  cursor: pointer;
  text-align: center;
  font-weight: var(--font-weight-medium);
  color: var(--text-color);
  transition: all 150ms ease;
  margin-bottom: 16px;
}

.filter-toggle:hover {
  background-color: #E9ECEF;
  border-color: #ADB5BD;
}

.filter-toggle.active {
  background-color: #4A90E2;
  color: white;
  border-color: #4A90E2;
}

/* Responsive Design */
@media (max-width: 1024px) and (min-width: 769px) {
  .filter-bar {
    padding: 12px 16px;
    gap: 12px;
  }

  .filter-item--date {
    width: 160px;
  }

  .filter-item--category {
    width: 140px;
  }

  .filter-item--amount {
    width: 180px;
  }

  .filter-item--search {
    width: 200px;
  }

  .filter-item--tags {
    min-width: 160px;
  }
}

@media (max-width: 768px) {
  .filter-toggle {
    display: block;
  }

  .filter-bar {
    height: auto;
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    padding: 16px;
    display: none;
  }

  .filter-bar.show {
    display: flex;
  }

  .filter-bar .filter-item {
    width: 100%;
  }

  .filter-item--date,
  .filter-item--category,
  .filter-item--amount,
  .filter-item--search,
  .filter-item--tags {
    width: 100%;
    min-width: auto;
  }

  .filter-bar .filter-item input,
  .filter-bar .filter-item select,
  .filter-bar .filter-item button {
    width: 100%;
  }

  #results-counter {
    margin-left: 0;
    text-align: center;
    order: -1;
    margin-bottom: 12px;
  }

  .clear-filters-btn {
    width: 100%;
    justify-content: center;
  }

  .dropdown-panel,
  .slider-panel {
    position: static;
    margin-top: 8px;
    box-shadow: none;
    border: 1px solid #DEE2E6;
    background-color: #F8F9FA;
  }
}

@media (max-width: 768px) {
  .action-links {
    width: 100%;
    justify-content: space-between;
  }

  .pagination {
    flex-direction: column;
    gap: var(--spacing);
  }

  .pagination-controls {
    width: 100%;
    justify-content: space-between;
  }

  .expense-detail-row {
    flex-direction: column;
  }

  .expense-detail-label {
    margin-bottom: var(--spacing-xs);
  }

  .document-actions {
    flex-direction: column;
  }

  .document-actions .button {
    width: 100%;
  }

  .expense-data {
    width: 100%;
  }
}

@media (max-width: 576px) {
  .expense-content, .expense-header, .expense-notes, .expense-document {
    padding: var(--spacing);
  }

  .modal-content {
    width: 95%;
  }

  .expense-data {
    width: 100%;
  }

  /* Remove any overflow and set all table elements to block */
  .expense-data table,
  .expense-data thead,
  .expense-data tbody,
  .expense-data th,
  .expense-data td,
  .expense-data tr,
  .expense-data caption {
    display: block;
  }

  /* Hide the header row completely */
  .expense-data thead {
    position: absolute;
    top: -9999px;
    left: -9999px;
  }

  .expense-data caption {
    width: 100%;
    white-space: normal;
    word-wrap: break-word;
    margin-top: var(--spacing-sm);
  }

  /* Style each row as a card */
  .expense-data tr {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing);
    padding: var(--spacing);
    position: relative;
  }

  .expense-data tr:hover {
    background-color: var(--grey-50);
  }

  /* Style table cells with labels */
  .expense-data td {
    padding: var(--spacing-sm) 0;
    border: none;
    position: relative;
    padding-left: 140px;
    min-height: 24px;
  }

  .expense-data td::before {
    content: attr(data-label);
    font-weight: var(--font-weight-semibold);
    position: absolute;
    left: 0;
    top: var(--spacing-sm);
    width: 130px;
    color: var(--text-muted);
  }

  /* Specific column label overrides */
  .expense-data .date-column::before {
    content: "Date:";
  }

  .expense-data .description-column::before {
    content: "Description:";
  }

  .expense-data .category-column::before {
    content: "Category:";
  }

  .expense-data .amount-column::before {
    content: "Amount:";
  }

  .expense-data .amount-column {
    text-align: left;
  }

  /* Actions column positioning */
  .expense-data .actions-column {
    padding-left: 0;
    text-align: right;
    margin-top: var(--spacing-sm);
    border-top: 1px solid var(--border-color);
    padding-top: var(--spacing-sm);
  }

  .expense-data .actions-column::before {
    content: "";
    display: none;
  }

  .actions-column {
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-sm);
  }

  /* Modern Table Mobile Layout */
  .modern-table,
  .modern-table thead,
  .modern-table tbody,
  .modern-table th,
  .modern-table td,
  .modern-table tr,
  .modern-table caption {
    display: block;
  }

  .modern-table thead {
    position: absolute;
    top: -9999px;
    left: -9999px;
  }

  .modern-table tr {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing);
    padding: var(--spacing);
    position: relative;
    box-shadow: var(--box-shadow-sm);
  }

  .modern-table tr:hover {
    background-color: var(--grey-50);
    transform: translateY(-1px);
    box-shadow: var(--box-shadow);
  }

  .modern-table td {
    padding: var(--spacing-sm) 0;
    border: none;
    position: relative;
    padding-left: 140px;
    min-height: 24px;
  }

  .modern-table td::before {
    content: attr(data-label);
    font-weight: var(--font-weight-semibold);
    position: absolute;
    left: 0;
    top: var(--spacing-sm);
    width: 130px;
    color: var(--text-muted);
  }

  .modern-table .actions-column {
    padding-left: 0;
    text-align: right;
    margin-top: var(--spacing-sm);
    border-top: 1px solid var(--border-color);
    padding-top: var(--spacing-sm);
  }

  .modern-table .actions-column::before {
    content: "";
    display: none;
  }
}

/* Fade In Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}
