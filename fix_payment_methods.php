<?php

require_once __DIR__ . '/vendor/autoload.php';

use App\Core\Database;

// Enable error reporting
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

echo "Fixing payment_methods table issues...\n\n";

try {
    $db = Database::getInstance();

    // Check if the payment_methods table exists
    $stmt = $db->query("SHOW TABLES LIKE 'payment_methods'");
    $tableExists = $stmt->fetchColumn();

    if (!$tableExists) {
        echo "The payment_methods table does not exist! Creating it...\n";

        // Create the payment_methods table with the correct structure
        $db->exec("
            CREATE TABLE payment_methods (
                id INT AUTO_INCREMENT PRIMARY KEY,
                method VARCHAR(50) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        ");

        echo "Created payment_methods table.\n";

        // Insert some default payment methods
        $db->exec("
            INSERT INTO payment_methods (method) VALUES
            ('Cash'),
            ('Credit Card'),
            ('Debit Card'),
            ('Bank Transfer'),
            ('PayPal'),
            ('Check'),
            ('Mobile Payment')
        ");

        echo "Added default payment methods.\n";
    } else {
        echo "The payment_methods table exists. Checking its structure...\n";

        // Check if the 'name' column exists
        $stmt = $db->query("SHOW COLUMNS FROM payment_methods LIKE 'name'");
        $nameColumnExists = $stmt->fetchColumn();

        if ($nameColumnExists) {
            echo "The 'name' column exists. Renaming it to 'method'...\n";

            // Check if the 'method' column already exists
            $stmt = $db->query("SHOW COLUMNS FROM payment_methods LIKE 'method'");
            $methodColumnExists = $stmt->fetchColumn();

            if (!$methodColumnExists) {
                // Rename the 'name' column to 'method'
                $db->exec("ALTER TABLE payment_methods CHANGE name method VARCHAR(50) NOT NULL");
                echo "Renamed 'name' column to 'method'.\n";
            } else {
                echo "Both 'name' and 'method' columns exist. This is unexpected.\n";
                echo "Please check the database structure manually.\n";
            }
        } else {
            // Check if the 'method' column exists
            $stmt = $db->query("SHOW COLUMNS FROM payment_methods LIKE 'method'");
            $methodColumnExists = $stmt->fetchColumn();

            if (!$methodColumnExists) {
                echo "Neither 'name' nor 'method' column exists. Adding 'method' column...\n";
                $db->exec("ALTER TABLE payment_methods ADD method VARCHAR(50) NOT NULL");
                echo "Added 'method' column.\n";
            } else {
                echo "The 'method' column already exists. No changes needed.\n";
            }
        }
    }

    echo "\nDone fixing payment_methods table issues.\n";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
