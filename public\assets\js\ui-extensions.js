document.addEventListener('DOMContentLoaded', initializeUIExtensions);

function initializeUIExtensions()
{
    applyFadeInAnimations();
    enhanceButtonInteractions();
    improveFormFeedback();
    enhanceNavigationExperience();
    applyProgressiveDisclosure();
    setupTrustSignals();
}

function applyFadeInAnimations()
{
    animateListItems();
    animateMainContent();
}

function animateListItems()
{
    const listContainers = document.querySelectorAll('.expense-list, .category-list, .nav-list');

    listContainers.forEach(container => {
        const items = container.querySelectorAll('tr, .card, .nav-item');
        items.forEach((item, index) => {
            applyStaggeredAnimation(item, index);
        });
    });
}

function applyStaggeredAnimation(element, index)
{
    element.classList.add('stagger-item', 'fade-in-up');
    element.style.animationDelay = `${0.05 * index}s`;
    element.style.animationDuration = '0.4s';
}

function animateMainContent()
{
    const mainContent = document.querySelector('main.container');
    if (mainContent) {
        mainContent.classList.add('fade-in');
    }
}

function enhanceButtonInteractions()
{
    const buttons = document.querySelectorAll('.button');

    buttons.forEach(button => {
        addRippleEffect(button);
        setupSubmitButtonBehavior(button);
        applyButtonStyling(button);
    });
}

function addRippleEffect(button)
{
    button.classList.add('with-ripple');
}

function setupSubmitButtonBehavior(button)
{
    if (button.type !== 'submit') {
        return;
    }

    const form = button.closest('form');
    if (!form) {
        return;
    }

    form.addEventListener('submit', () => {
        if (form.classList.contains('submitting')) {
            return;
        }

        button.classList.add('loading');
        form.classList.add('submitting');
    });
}

function applyButtonStyling(button)
{
    const isPrimary = button.classList.contains('primary');
    const buttonText = button.textContent.trim().toLowerCase();
    const isSaveButton = buttonText.includes('save');
    const isCreateButton = buttonText.includes('create');

    if (isPrimary || isSaveButton || isCreateButton) {
        button.classList.add('primary-cta');
    }
}

function improveFormFeedback()
{
    const forms = document.querySelectorAll('form');

    forms.forEach(form => {
        setupFormInputs(form);
    });
}

function setupFormInputs(form)
{
    const inputs = form.querySelectorAll('input, select, textarea');

    inputs.forEach(input => {
        setupInputFocusEffects(input);
        setupInputValidation(input);
    });
}

function setupInputFocusEffects(input)
{
    input.addEventListener('focus', () => {
        input.parentElement.classList.add('focused');
    });

    input.addEventListener('blur', () => {
        input.parentElement.classList.remove('focused');
        updateInputValueState(input);
    });
}

function updateInputValueState(input)
{
    const hasValue = input.value.trim() !== '';
    input.parentElement.classList.toggle('has-value', hasValue);
}

function setupInputValidation(input)
{
    input.addEventListener('blur', () => {
        const isValid = input.checkValidity();
        input.parentElement.classList.toggle('has-error', !isValid);
    });
}

function enhanceNavigationExperience()
{
    highlightCurrentNavSection();
    setupSmoothScrolling();
}

function highlightCurrentNavSection()
{
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.nav-link');

    navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (!href || !currentPath.startsWith(href)) {
            return;
        }

        markLinkAsActive(link);
        expandParentAccordion(link);
    });
}

function markLinkAsActive(link)
{
    link.classList.add('active');
    link.setAttribute('aria-current', 'page');
}

function expandParentAccordion(link)
{
    const parentAccordion = link.closest('.nav-accordion');
    if (parentAccordion) {
        parentAccordion.classList.add('expanded');
    }
}

function setupSmoothScrolling()
{
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', handleAnchorClick);
    });
}

function handleAnchorClick(e)
{
    e.preventDefault();
    const targetId = this.getAttribute('href');
    const targetElement = document.querySelector(targetId);

    if (!targetElement) {
        return;
    }

    scrollToElement(targetElement);
    focusElement(targetElement);
}

function scrollToElement(element)
{
    window.scrollTo({
        top: element.offsetTop - 80,
        behavior: 'smooth'
    });
}

function focusElement(element)
{
    element.setAttribute('tabindex', '-1');
    element.focus();
}

function applyProgressiveDisclosure()
{
    setupCollapsibleSections();
    setupExpandableContent();
}

function setupCollapsibleSections()
{
    const collapsibleTriggers = document.querySelectorAll('.collapsible-trigger');

    collapsibleTriggers.forEach(trigger => {
        trigger.addEventListener('click', () => {
            const targetSelector = trigger.getAttribute('data-target');
            const target = document.querySelector(targetSelector);
            if (!target) {
                return;
            }

            toggleCollapsibleSection(trigger, target);
        });
    });
}

function toggleCollapsibleSection(trigger, target)
{
    const isExpanded = trigger.getAttribute('aria-expanded') === 'true';
    const newExpandedState = !isExpanded;

    trigger.setAttribute('aria-expanded', newExpandedState);
    target.classList.toggle('expanded');

    if (newExpandedState) {
        expandSection(target);
    } else {
        collapseSection(target);
    }
}

function expandSection(target)
{
    target.style.height = '0';
    const height = target.scrollHeight;
    target.style.height = height + 'px';

    setTimeout(() => {
        target.style.height = 'auto';
    }, 300);
}

function collapseSection(target)
{
    target.style.height = target.scrollHeight + 'px';

    setTimeout(() => {
        target.style.height = '0';
    }, 10);
}

function setupExpandableContent()
{
    const expandableContents = document.querySelectorAll('.expandable-content');

    expandableContents.forEach(content => {
        const height = content.scrollHeight;
        const maxHeight = parseInt(getComputedStyle(content).getPropertyValue('max-height'));

        if (height <= maxHeight) {
            return;
        }

        addExpandButtonToContent(content, height, maxHeight);
    });
}

function addExpandButtonToContent(content, height, maxHeight)
{
    const expandButton = createExpandButton();
    content.parentNode.insertBefore(expandButton, content.nextSibling);

    expandButton.addEventListener('click', () => {
        toggleContentExpansion(content, expandButton, height, maxHeight);
    });
}

function createExpandButton()
{
    const button = document.createElement('button');
    button.className = 'expand-trigger button small secondary';
    button.textContent = 'Show more';
    button.setAttribute('aria-expanded', 'false');
    return button;
}

function toggleContentExpansion(content, button, fullHeight, collapsedHeight)
{
    const isExpanded = button.getAttribute('aria-expanded') === 'true';
    const newExpandedState = !isExpanded;

    content.style.maxHeight = newExpandedState ? fullHeight + 'px' : collapsedHeight + 'px';
    button.textContent = newExpandedState ? 'Show less' : 'Show more';
    button.setAttribute('aria-expanded', newExpandedState);
}

function setupTrustSignals()
{
    enhanceSuccessMessages();
    setupDangerButtonConfirmation();
    setupAsyncFormLoading();
}

function enhanceSuccessMessages()
{
    document.querySelectorAll('.alert-success').forEach(message => {
        message.classList.add('success-pulse');
    });
}

function setupDangerButtonConfirmation()
{
    const dangerButtons = document.querySelectorAll('.button.danger, button[type="submit"][form*="delete"]');

    dangerButtons.forEach(button => {
        button.addEventListener('click', handleDangerButtonClick);
    });
}

function handleDangerButtonClick(e)
{
    if (this.hasAttribute('data-confirmed')) {
        return;
    }

    e.preventDefault();
    showConfirmationState(this);
}

function showConfirmationState(button)
{
    const originalText = button.textContent;
    button.textContent = 'Are you sure?';
    button.classList.add('confirming');
    button.setAttribute('data-confirmed', 'true');

    setTimeout(() => {
        resetButtonIfNotClicked(button, originalText);
    }, 3000);
}

function resetButtonIfNotClicked(button, originalText)
{
    if (!button.classList.contains('confirming')) {
        return;
    }

    button.textContent = originalText;
    button.classList.remove('confirming');
    button.removeAttribute('data-confirmed');
}

function setupAsyncFormLoading()
{
    const asyncForms = document.querySelectorAll('form[data-async="true"]');

    asyncForms.forEach(form => {
        form.addEventListener('submit', handleAsyncFormSubmit);
    });
}

function handleAsyncFormSubmit(e)
{
    e.preventDefault();

    const submitButton = this.querySelector('[type="submit"]');
    if (submitButton) {
        submitButton.classList.add('loading');
    }

    simulateAsyncRequest(this, submitButton);
}

function simulateAsyncRequest(form, submitButton)
{
    setTimeout(() => {
        if (submitButton) {
            submitButton.classList.remove('loading');
        }
        form.submit();
    }, 800);
}
