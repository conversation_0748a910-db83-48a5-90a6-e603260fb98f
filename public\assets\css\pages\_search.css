/* ===== SEARCH PAGES STYLES ===== */

/* Page Title Section */
.page-title-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xl);
  position: relative;
}

.page-title-section::after {
  content: '';
  position: absolute;
  bottom: calc(-1 * var(--spacing));
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(to right, var(--grey-300) 0%, rgba(229, 231, 235, 0.5) 50%, rgba(229, 231, 235, 0) 100%);
}

.title-wrapper h2 {
  margin: 0;
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--dark-color);
  letter-spacing: -0.025em;
}

.subtitle {
  margin: var(--spacing-xs) 0 0 0;
  color: var(--text-muted);
  font-size: var(--font-size);
}

.action-buttons {
  display: flex;
  gap: var(--spacing-sm);
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing);
  border-radius: var(--border-radius);
  font-weight: var(--font-weight-medium);
  font-size: var(--font-size);
  transition: var(--transition);
  text-decoration: none;
}

.btn-outline {
  border: 1px solid var(--grey-300);
  background-color: var(--card-bg);
  color: var(--text-muted);
}

.btn-outline:hover {
  background-color: var(--grey-100);
  border-color: var(--grey-400);
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--text-light);
  border: 1px solid transparent;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
}

.icon-arrow-left,
.icon-plus {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  margin-right: var(--spacing-xs);
  position: relative;
}

.icon-arrow-left::before {
  content: '←';
}

.icon-plus::before {
  content: '+';
}

/* Search Container Layout */
.search-container {
  display: grid;
  grid-template-columns: 320px 1fr;
  gap: var(--spacing-lg);
}

/* Search Filters Panel */
.search-filters {
  background-color: var(--card-bg);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--box-shadow-sm);
  border: 1px solid var(--grey-200);
  height: fit-content;
}

.search-filters h3 {
  margin-top: 0;
  margin-bottom: var(--spacing);
  color: var(--dark-color);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  position: relative;
  padding-bottom: var(--spacing-sm);
}

.search-filters h3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 3rem;
  height: 2px;
  background-color: var(--primary-color);
}

.search-form .form-group {
  margin-bottom: var(--spacing);
}

.search-form label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
  color: var(--text-color);
  font-size: var(--font-size);
}

.search-form input[type="text"],
.search-form input[type="date"],
.search-form input[type="number"],
.search-form select {
  width: 100%;
  padding: var(--spacing-sm);
  border: 1px solid var(--grey-300);
  border-radius: var(--border-radius);
  background-color: var(--card-bg);
  font-size: var(--font-size);
  transition: var(--transition-fast);
}

.search-form input[type="text"]:focus,
.search-form input[type="date"]:focus,
.search-form input[type="number"]:focus,
.search-form select:focus {
  outline: none;
  border-color: var(--primary-light);
  box-shadow: 0 0 0 3px rgba(var(--primary-hue), 59%, 52%, 0.1);
}

.search-form .form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing);
  margin-bottom: 0;
}

.currency-input {
  position: relative;
}

.currency-symbol {
  position: absolute;
  left: var(--spacing-sm);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  font-weight: var(--font-weight-medium);
}

.currency-input input {
  padding-left: var(--spacing-lg);
}

.form-actions {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-lg);
}

/* Search Instructions Panel */
.search-instructions {
  background-color: var(--card-bg);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--box-shadow-sm);
  border: 1px solid var(--grey-200);
}

.search-instructions h3 {
  margin-top: 0;
  margin-bottom: var(--spacing);
  color: var(--dark-color);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  position: relative;
  padding-bottom: var(--spacing-sm);
}

.search-instructions h3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 3rem;
  height: 2px;
  background-color: var(--primary-color);
}

.card {
  background-color: var(--grey-100);
  border-radius: var(--border-radius);
  padding: var(--spacing);
  margin-bottom: var(--spacing-lg);
  border: 1px solid var(--grey-200);
}

.search-instructions ul {
  margin: 0;
  padding-left: var(--spacing-lg);
  line-height: 1.7;
}

.search-instructions li {
  margin-bottom: var(--spacing-sm);
  color: var(--text-muted);
}

.text-muted {
  color: var(--text-muted);
}

/* Search Results Styles */
.search-summary {
  background-color: var(--primary-light);
  padding: var(--spacing) var(--spacing-lg);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-lg);
  color: var(--primary-dark);
  border-left: 4px solid var(--primary-color);
}

.search-summary p {
  margin: 0;
  font-size: var(--font-size);
}

.active-filters {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing);
}

.filter-tag {
  background-color: var(--primary-light);
  color: var(--primary-dark);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  display: inline-flex;
  align-items: center;
}

.table-responsive {
  overflow: hidden;
  margin-bottom: 0;
  background-color: var(--card-bg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-sm);
  border: 1px solid var(--grey-200);
}

table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

table th,
table td {
  padding: var(--spacing) var(--spacing-lg);
  text-align: left;
  border-bottom: 1px solid var(--grey-200);
}

table th {
  background-color: var(--grey-100);
  font-weight: var(--font-weight-semibold);
  color: var(--dark-color);
  font-size: var(--font-size);
  position: sticky;
  top: 0;
}

table th:first-child {
  border-top-left-radius: var(--border-radius-lg);
}

table th:last-child {
  border-top-right-radius: var(--border-radius-lg);
}

table tr:last-child td {
  border-bottom: none;
}

table tr:hover {
  background-color: var(--grey-100);
}

.meta-info {
  display: block;
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  margin-top: var(--spacing-xs);
}

.document-indicator {
  font-size: var(--font-size-lg);
}

.actions {
  white-space: nowrap;
  display: flex;
  gap: var(--spacing-xs);
}

.actions .button {
  padding: var(--spacing-xs) var(--spacing-sm);
  font-size: var(--font-size-sm);
  border-radius: var(--border-radius-sm);
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: var(--spacing-lg);
  gap: var(--spacing);
}

.pagination-button {
  padding: var(--spacing-xs) var(--spacing);
  border-radius: var(--border-radius);
  background-color: var(--card-bg);
  border: 1px solid var(--grey-300);
  color: var(--text-muted);
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  transition: var(--transition-fast);
}

.pagination-button:hover {
  background-color: var(--grey-100);
  border-color: var(--grey-400);
}

.pagination-info {
  color: var(--text-muted);
  font-size: var(--font-size);
}

.notice {
  padding: var(--spacing);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-lg);
  background-color: var(--danger-light);
  color: var(--danger-dark);
  border-left: 4px solid var(--danger-color);
}

.notice.info {
  background-color: var(--primary-light);
  color: var(--primary-dark);
  border-left: 4px solid var(--primary-color);
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  overflow: auto;
  background-color: var(--modal-overlay);
  backdrop-filter: blur(4px);
}

.modal-content {
  background-color: var(--card-bg);
  margin: 8% auto;
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  width: 90%;
  max-width: 600px;
  box-shadow: var(--box-shadow-lg);
  border: 1px solid var(--grey-200);
  position: relative;
}

.close {
  position: absolute;
  top: var(--spacing);
  right: var(--spacing);
  color: var(--text-muted);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  cursor: pointer;
  line-height: 1;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: var(--transition-fast);
}

.close:hover,
.close:focus {
  color: var(--dark-color);
  background-color: var(--grey-100);
}

#notesModalTitle {
  margin-top: 0;
  margin-bottom: var(--spacing);
  color: var(--dark-color);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  padding-right: var(--spacing-lg);
}

#notesContent {
  background-color: var(--grey-100);
  padding: var(--spacing);
  border-radius: var(--border-radius);
  margin-top: var(--spacing);
  white-space: pre-wrap;
  font-family: inherit;
  font-size: var(--font-size);
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid var(--grey-200);
  line-height: 1.6;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .page-title-section {
    flex-direction: column;
    align-items: flex-start;
  }

  .action-buttons {
    margin-top: var(--spacing);
    width: 100%;
    justify-content: space-between;
  }

  .search-container {
    grid-template-columns: 1fr;
  }

  .search-form .form-row {
    grid-template-columns: 1fr;
  }

  .actions {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .actions .button {
    margin-right: 0;
    text-align: center;
  }

  .table-responsive {
    margin-left: calc(-1 * var(--spacing));
    margin-right: calc(-1 * var(--spacing));
    width: calc(100% + 2 * var(--spacing));
    border-radius: 0;
  }

  table th:first-child,
  table th:last-child {
    border-radius: 0;
  }

  .modal-content {
    width: 95%;
    padding: var(--spacing);
    margin: 5% auto;
  }
}
