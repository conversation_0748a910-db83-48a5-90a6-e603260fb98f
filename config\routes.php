<?php

declare(strict_types=1);

namespace App\Config;

use App\Controllers\HomeController;
use App\Controllers\DashboardController;
use App\Controllers\DocsController;
use App\Controllers\ExpenseController;
use App\Controllers\CategoryController;
use App\Controllers\ProfileController;
use App\Controllers\ReportController;
use App\Controllers\AuthController;
use App\Controllers\SessionController;
use App\Core\Router;
use App\Services\FileService;
use App\Services\ReceiptParserService;
use App\Auth\Authenticator;
use Psr\Log\LoggerInterface;

// Ensure dependencies are available
if (!isset($container) || !isset($logger)) {
    throw new \RuntimeException('Container and Logger must be initialized before loading routes');
}

// Register services
$container->register('ExpenseService', fn() => new \App\Services\ExpenseService());
$container->register('CategoryService', fn() => new \App\Services\CategoryService());
$container->register('MerchantService', fn() => new \App\Services\MerchantService());
$container->register('PaymentMethodService', fn() => new \App\Services\PaymentMethodService());

// Register Authenticator
$container->register(Authenticator::class, fn() => new Authenticator());

// Register FileService with dependencies
$container->register(FileService::class, fn($c) => new FileService(
    $c->get(LoggerInterface::class),
    $c->get('ExpenseService'),
    null
));

// Register ReceiptParserService with dependencies
$container->register(
    ReceiptParserService::class,
    fn($c) => new ReceiptParserService(
        $c->get(FileService::class),
        $c->get('ExpenseService'),
        $c->get('MerchantService')
    )
);

// Register ReportingService with dependencies
$container->register(
    'ReportingService',
    fn($c) => new \App\Services\ReportingService(
        $c->get('ExpenseService'),
        $c->get('CategoryService'),
        $c->get('MerchantService')
    )
);

// Register ExpenseManagerService with dependencies
$container->register(
    'ExpenseManagerService',
    fn($c) => new \App\Services\ExpenseManagerService(
        $c->get('ExpenseService'),
        $c->get('CategoryService'),
        $c->get('MerchantService'),
        $c->get(FileService::class),
        $c->get(ReceiptParserService::class),
        $c->get('PaymentMethodService')
    )
);

// Register EmailService
$container->register(
    'EmailService',
    fn() => new \App\Services\EmailService()
);

// Register AuthService with dependencies
$container->register(
    'AuthService',
    fn($c) => new \App\Services\AuthService(
        $c->get(Authenticator::class),
        $c->get('UserService'),
        $c->get('EmailService')
    )
);

// Register UserService
$container->register(
    'UserService',
    fn() => new \App\Services\UserService()
);

// Initialize router with logger and container
$router = new Router($logger, $container);

// Configure error settings
$errorViewPath = __DIR__ . '/../src/Views/errors/';
$router->setErrorTemplatePath($errorViewPath);
$router->setShowErrorDetails(true);

// Enable performance metrics collection
$router->setCollectPerformanceMetrics(true);

// Configure error handlers
$router->setErrorHandlers([
    400 => function (array $context) use ($errorViewPath) {
        extract($context);
        require "{$errorViewPath}400.php";
    },
    401 => function (array $context) use ($errorViewPath) {
        extract($context);
        require "{$errorViewPath}401.php";
    },
    403 => function (array $context) use ($errorViewPath) {
        extract($context);
        require "{$errorViewPath}403.php";
    },
    404 => function (array $context) use ($errorViewPath) {
        extract($context);
        require "{$errorViewPath}404.php";
    },
    405 => function (array $context) use ($errorViewPath) {
        extract($context);
        require "{$errorViewPath}405.php";
    },
    500 => function (array $context) use ($errorViewPath) {
        extract($context);
        require "{$errorViewPath}500.php";
    },
    503 => function (array $context) use ($errorViewPath) {
        extract($context);
        require "{$errorViewPath}503.php";
    },
]);

// Define routes grouped by HTTP method and feature
$routes = [
    // GET routes
    'GET' => [
        // Home & Dashboard routes
        '/' => [HomeController::class, 'index'],
        '/how' => [HomeController::class, 'how'],
        '/dashboard' => [DashboardController::class, 'index'],

        // Authentication routes
        '/login' => [AuthController::class, 'login'],
        '/register' => [AuthController::class, 'register'],
        '/password/reset' => [AuthController::class, 'resetPassword'],
        '/password/new' => [AuthController::class, 'newPassword'],

        // Profile routes
        '/profile' => [ProfileController::class, 'index'],
        '/profile/edit' => [ProfileController::class, 'edit'],
        '/profile/password' => [ProfileController::class, 'changePassword'],

        // Expense routes
        '/expenses' => [ExpenseController::class, 'index'],
        '/expenses/create' => [ExpenseController::class, 'create'],
        '/expenses/new' => [ExpenseController::class, 'create'],
        '/expenses/{id}' => [ExpenseController::class, 'show'],
        '/expenses/{id}/edit' => [ExpenseController::class, 'edit'],
        '/expenses/{id}/document' => [ExpenseController::class, 'viewDocument'],
        '/expenses/{id}/document/download' => [ExpenseController::class, 'downloadDocument'],
        '/expenses/{id}/receipt/download' => [ExpenseController::class, 'downloadReceiptText'],
        '/expenses/{id}/receipt/original' => [ExpenseController::class, 'downloadOriginalReceiptText'],
        '/expenses/{id}/receipt/generate' => [ExpenseController::class, 'generateReceiptText'],
        '/expenses/{id}/receipt/generate-pdf' => [ExpenseController::class, 'generateReceiptPdf'],
        '/expenses/category/{categoryId}/documents' => [ExpenseController::class, 'listDocuments'],

        // Search route
        '/search' => [ExpenseController::class, 'search'],

        // Category routes
        '/categories' => [CategoryController::class, 'index'],
        '/categories/create' => [CategoryController::class, 'create'],
        '/categories/{id}/edit' => [CategoryController::class, 'edit'],

        // Report routes
        '/reports' => [ReportController::class, 'index'],
        '/reports/monthly' => [ReportController::class, 'monthlyReport'],
        '/reports/summary' => [ReportController::class, 'summaryReport'],
        '/reports/category' => [ReportController::class, 'categoryReport'],


        // Debug routes
        '/debug/remember-tokens' => [AuthController::class, 'debugRememberTokens'],
        '/debug/session' => [AuthController::class, 'debugSession'],
        '/debug/token-test' => [AuthController::class, 'debugRememberTokens'],
        '/debug/cookies' => [AuthController::class, 'debugSession'],

        // Documentation routes
        '/docs/performance' => [DocsController::class, 'performance'],
    ],

    // POST routes
    'POST' => [
        // Authentication routes
        '/authenticate' => [AuthController::class, 'authenticate'],
        '/store' => [AuthController::class, 'store'],
        '/logout' => [AuthController::class, 'logout'],
        '/password/reset/process' => [AuthController::class, 'processResetPassword'],
        '/password/update' => [AuthController::class, 'updatePassword'],

        // Profile routes
        '/profile' => [ProfileController::class, 'update'],
        '/profile/update-ajax' => [ProfileController::class, 'updateAjax'],
        '/profile/password' => [ProfileController::class, 'updatePassword'],

        // Expense routes
        '/expenses' => [ExpenseController::class, 'store'],
        '/expenses/bulk-delete' => [ExpenseController::class, 'bulkDelete'],
        '/expenses/receipt/upload' => [ExpenseController::class, 'uploadReceipt'],
        '/expenses/{id}' => [ExpenseController::class, 'update'],
        '/expenses/{id}/delete' => [ExpenseController::class, 'delete'],
        '/expenses/{id}/receipt/process' => [ExpenseController::class, 'processReceipt'],

        // Category routes
        '/categories' => [CategoryController::class, 'store'],
        '/categories/{id}' => [CategoryController::class, 'update'],
        '/categories/{id}/delete' => [CategoryController::class, 'delete'],

        // Report routes
        '/reports/export' => [ReportController::class, 'generateExport'],

        // Session management routes
        '/check-session' => [SessionController::class, 'checkSession'],
        '/invalidate-session' => [SessionController::class, 'invalidateSession']
    ],

    // DELETE routes
    'DELETE' => [
        // Expense routes
        '/expenses/{id}' => [ExpenseController::class, 'delete'],

        // Category routes
        '/categories/{id}' => [CategoryController::class, 'delete'],
    ]
];

// Register all routes
$router->defineRoutes($routes);
