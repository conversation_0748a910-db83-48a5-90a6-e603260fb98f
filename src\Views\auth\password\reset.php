<?php
// Get old input values
$oldEmail = $old['email'] ?? '';

// Prepare content for the layout
$title = 'Reset Password';
ob_start();
?>

<form method="POST" action="/password/reset/process">
    <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($csrf_token) ?>">

    <p class="form-hint">Enter your email address and we'll send you a link to reset your password.</p>

    <!-- Email Field -->
    <div class="form-group">
        <label for="email">Email Address</label>
        <input type="email" name="email" id="email"
               value="<?= htmlspecialchars($oldEmail) ?>"
               required autofocus>
    </div>

    <!-- Form Actions -->
    <div class="form-actions">
        <button type="submit" class="button">Send Reset Link</button>
    </div>
</form>

<div class="form-links">
    <a href="/login">Remember your password? Login</a>
</div>

<?php
$content = ob_get_clean();
require __DIR__ . '/../_layout.php';
?>
