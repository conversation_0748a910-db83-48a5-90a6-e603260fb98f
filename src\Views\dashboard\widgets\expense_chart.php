<?php

/**
 * Expense chart widget
 * Displays a chart of monthly expenses
 */

// Set widget parameters
$title = 'Monthly Expenses';
$icon = '<svg class="icon"><use xlink:href="/assets/icons.svg#icon-chart"></use></svg>';
$widgetId = 'expense-chart';
$widgetClass = 'expense-chart-widget';
$hasData = !empty($monthlyData) && count($monthlyData) > 0;
$emptyMessage = 'Not enough data to display a chart. Add more expenses to see trends.';

// Prepare chart data
$chartLabels = [];
$chartValues = [];

if (!empty($monthlyData)) {
    foreach ($monthlyData as $month) {
        $chartLabels[] = $month['month_name'] ?? '';
        $chartValues[] = $month['total'] ?? 0;
    }
}

$chartData = [
    'labels' => $chartLabels,
    'values' => $chartValues
];

// Prepare widget content
ob_start();
?>

<?php if ($hasData) : ?>
    <div class="chart-container">
        <canvas id="expenseChart" data-chart='<?= json_encode($chartData) ?>' width="400" height="200"></canvas>
    </div>
    <div class="widget-footer">
        <a href="/reports/monthly" class="button small">View Monthly Report</a>
    </div>
<?php endif; ?>

<?php
$content = ob_get_clean();

// Include the widget template
include __DIR__ . '/widget_template.php';
?>
