<?php

/**
 * Form logic for expense form
 * Contains JavaScript to handle form interactions
 */

?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle merchant selection
    const merchantSelect = document.getElementById('merchant_id');
    const newMerchantField = document.getElementById('merchant');
    const newMerchantContainer = newMerchantField ? newMerchantField.closest('.form-group') : null;
    
    if (merchantSelect && newMerchantContainer) {
        // Initially hide the custom merchant field unless "_new" is selected
        if (merchantSelect.value !== '_new') {
            newMerchantContainer.style.display = 'none';
        }
        
        // Show/hide custom merchant field based on selection
        merchantSelect.addEventListener('change', function() {
            if (this.value === '_new') {
                newMerchantContainer.style.display = 'block';
                newMerchantField.focus();
            } else {
                newMerchantContainer.style.display = 'none';
            }
        });
    }
    
    // Handle category selection
    const categorySelect = document.getElementById('category_id');
    const newCategoryField = document.getElementById('new_category');
    const newCategoryContainer = newCategoryField ? newCategoryField.closest('.form-group') : null;
    
    if (categorySelect && newCategoryContainer) {
        // Initially hide the new category field unless "_new" is selected
        if (categorySelect.value !== '_new') {
            newCategoryContainer.style.display = 'none';
        }
        
        // Show/hide new category field based on selection
        categorySelect.addEventListener('change', function() {
            if (this.value === '_new') {
                newCategoryContainer.style.display = 'block';
                newCategoryField.focus();
            } else {
                newCategoryContainer.style.display = 'none';
            }
        });
    }
    
    // Handle file upload
    const fileInput = document.getElementById('document');
    const fileHelp = document.getElementById('file-help');
    
    if (fileInput) {
        fileInput.addEventListener('change', function() {
            if (this.files.length > 0) {
                const file = this.files[0];
                const fileSize = file.size / 1024 / 1024; // Convert to MB
                
                // Check file size
                if (fileSize > 1) {
                    alert('File size exceeds 1MB limit. Please choose a smaller file.');
                    this.value = ''; // Clear the file input
                    return;
                }
                
                // Check file type
                const allowedTypes = ['text/plain', 'text/csv', 'application/pdf', 'text/x-log'];
                const allowedExtensions = ['.txt', '.csv', '.pdf', '.log'];
                
                let isValidType = false;
                
                // Check MIME type
                if (allowedTypes.includes(file.type)) {
                    isValidType = true;
                }
                
                // Check file extension as fallback
                if (!isValidType) {
                    const fileName = file.name.toLowerCase();
                    for (const ext of allowedExtensions) {
                        if (fileName.endsWith(ext)) {
                            isValidType = true;
                            break;
                        }
                    }
                }
                
                if (!isValidType) {
                    alert('Invalid file type. Please upload a TXT, CSV, PDF, or LOG file.');
                    this.value = ''; // Clear the file input
                }
            }
        });
    }
});
</script>
