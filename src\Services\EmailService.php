<?php

declare(strict_types=1);

namespace App\Services;

use App\Exceptions\EmailException;
use App\Models\File;
use App\Models\User;
use App\Models\EmailTemplate;
use App\Models\EmailLog;

class EmailService
{
    private string $fromEmail;
    private string $fromName;
    private array $config;
    private bool $useSmtp;
    private const MAX_ATTACHMENT_SIZE_MB = 10;

    public function __construct(array $config = [])
    {
        $this->config = $config ?: $this->getDefaultConfig();
        $this->fromEmail = $this->config['from_email'];
        $this->fromName = $this->config['from_name'];
        $this->useSmtp = $this->config['use_smtp'];
    }

    public function send(
        string $to,
        string $subject,
        string $message,
        array $attachments = []
    ): bool {
        if (!$this->validateEmailAddress($to)) {
            throw new EmailException("Invalid recipient email address: {$to}");
        }

        try {
            $this->validateAttachments($attachments);
            $result = $this->useSmtp
                ? $this->sendSmtp($to, $subject, $message, $attachments)
                : $this->sendMail($to, $subject, $message, $attachments);

            $this->logEmailSent($to, $subject, $result);
            return $result;
        } catch (\Exception $exception) {
            $this->logEmailSent($to, $subject, false, $exception->getMessage());
            $this->logError("Email sending failed", $exception);
            throw new EmailException("Failed to send email: {$exception->getMessage()}");
        }
    }

    public function sendHtml(
        string $to,
        string $subject,
        string $htmlContent,
        string $plainTextFallback = '',
        array $attachments = []
    ): bool {
        $boundary = $this->generateBoundary('alternative');
        $plainText = $plainTextFallback ?: $this->htmlToPlainText($htmlContent);
        
        $headers = $this->buildHtmlHeaders($boundary);
        $message = $this->buildAlternativeMessage($htmlContent, $plainText, $boundary);
        
        if (!empty($attachments)) {
            [$headers, $message] = $this->addAttachmentsToHtmlMessage($headers, $message, $attachments, $boundary);
        }
        
        return $this->sendEmailMessage($to, $subject, $message, $headers);
    }
    
    private function buildHtmlHeaders(string $boundary): array
    {
        $headers = $this->getBaseHeaders();
        $headers[] = "MIME-Version: 1.0";
        $headers[] = "Content-Type: multipart/alternative; boundary=\"{$boundary}\"";
        
        return $headers;
    }
    
    private function buildAlternativeMessage(string $htmlContent, string $plainText, string $boundary): string
    {
        $message = "--{$boundary}\r\n";
        $message .= "Content-Type: text/plain; charset=utf-8\r\n";
        $message .= "Content-Transfer-Encoding: 8bit\r\n\r\n";
        $message .= "{$plainText}\r\n\r\n";
        $message .= "--{$boundary}\r\n";
        $message .= "Content-Type: text/html; charset=utf-8\r\n";
        $message .= "Content-Transfer-Encoding: 8bit\r\n\r\n";
        $message .= "{$htmlContent}\r\n\r\n";
        $message .= "--{$boundary}--";
        
        return $message;
    }
    
    private function addAttachmentsToHtmlMessage(array $headers, string $message, array $attachments, string $boundary): array
    {
        $mixedBoundary = $this->generateBoundary('mixed');
        $headers = $this->updateHeadersForMixedContent($headers, $mixedBoundary);
        $message = $this->buildMixedMessage($message, $attachments, $boundary, $mixedBoundary);
        
        return [$headers, $message];
    }
    
    private function updateHeadersForMixedContent(array $headers, string $mixedBoundary): array
    {
        foreach ($headers as $key => $value) {
            if (strpos($value, 'Content-Type:') === 0) {
                $headers[$key] = "Content-Type: multipart/mixed; boundary=\"{$mixedBoundary}\"";
            }
        }
        
        return $headers;
    }
    
    private function buildMixedMessage(string $message, array $attachments, string $boundary, string $mixedBoundary): string
    {
        $mixedMessage = "--{$mixedBoundary}\r\n";
        $mixedMessage .= "Content-Type: multipart/alternative; boundary=\"{$boundary}\"\r\n\r\n";
        $mixedMessage .= "{$message}\r\n\r\n";
        
        $mixedMessage .= $this->appendAttachments($attachments, $mixedBoundary);
        $mixedMessage .= "--{$mixedBoundary}--";
        
        return $mixedMessage;
    }
    
    private function appendAttachments(array $attachments, string $mixedBoundary): string
    {
        $attachmentContent = '';
        
        foreach ($attachments as $attachment) {
            if (!file_exists($attachment['path'])) {
                continue;
            }
            
            $attachmentContent .= $this->buildAttachmentSection($attachment, $mixedBoundary);
        }
        
        return $attachmentContent;
    }
    
    private function buildAttachmentSection(array $attachment, string $mixedBoundary): string
    {
        $fileContent = chunk_split(base64_encode(file_get_contents($attachment['path'])));
        $mimeType = $attachment['type'] ?? $this->getMimeType($attachment['path']);
        $fileName = $attachment['name'] ?? basename($attachment['path']);
        
        $section = "--{$mixedBoundary}\r\n";
        $section .= "Content-Type: {$mimeType}; name=\"{$fileName}\"\r\n";
        $section .= "Content-Transfer-Encoding: base64\r\n";
        $section .= "Content-Disposition: attachment; filename=\"{$fileName}\"\r\n\r\n";
        $section .= "{$fileContent}\r\n\r\n";
        
        return $section;
    }
    
    private function sendEmailMessage(string $to, string $subject, string $message, array $headers): bool
    {
        return $this->useSmtp
            ? $this->sendSmtp($to, $subject, $message, [])
            : $this->sendMail($to, $subject, $message, [], implode("\r\n", $headers));
    }

    public function sendTemplate(
        string $to,
        string $subject,
        string $templateName,
        array $variables = [],
        array $attachments = []
    ): bool {
        $template = EmailTemplate::findByName($templateName);
        if ($template) {
            if (empty($subject)) {
                $subject = $template['subject'];
            }

            $htmlContent = EmailTemplate::render($template, $variables);
            return $this->sendHtml($to, $subject, $htmlContent, '', $attachments);
        }

        $templatePath = $this->resolveTemplatePath($templateName);
        if (!file_exists($templatePath)) {
            throw new EmailException("Email template not found: {$templateName}");
        }

        extract($variables, EXTR_SKIP);

        ob_start();
        include $templatePath;
        $htmlContent = ob_get_clean();

        if ($htmlContent === false) {
            throw new EmailException("Failed to process email template: {$templateName}");
        }

        return $this->sendHtml($to, $subject, $htmlContent, '', $attachments);
    }

    private function sendMail(
        string $to,
        string $subject,
        string $message,
        array $attachments = [],
        ?string $customHeaders = null
    ): bool {
        $headers = $customHeaders ?? implode("\r\n", $this->getBaseHeaders());

        if (!empty($attachments) && $customHeaders === null) {
            $boundary = $this->generateBoundary('mixed');
            $headers .= "\r\nMIME-Version: 1.0";
            $headers .= "\r\nContent-Type: multipart/mixed; boundary=\"{$boundary}\"";
            $message = $this->buildMultipartMessage($message, $attachments, $boundary);
        }

        $encodedSubject = $this->encodeEmailHeaderValue($subject);
        return mail($to, $encodedSubject, $message, $headers);
    }

    private function sendSmtp(
        string $to,
        string $subject,
        string $message = '',
        array $attachments = []
    ): bool {
        if (!extension_loaded('openssl')) {
            throw new EmailException('OpenSSL extension is required for SMTP emails');
        }

        $smtpConfig = $this->getSmtpConfig();
        if (
            empty($smtpConfig['host'])
            || empty($smtpConfig['port'])
            || empty($smtpConfig['user'])
            || empty($smtpConfig['pass'])
        ) {
            throw new EmailException('SMTP configuration is incomplete');
        }

        $this->logInfo(sprintf(
            "SMTP email would be sent to: %s with subject: %s, message length: %d, attachments: %d",
            $to,
            $subject,
            strlen($message),
            count($attachments)
        ));

        return true;
    }

    private function getBaseHeaders(): array
    {
        return [
            'From: ' . $this->getFromHeader(),
            'Reply-To: ' . $this->getFromHeader(),
            'X-Mailer: PHP/' . phpversion(),
            'X-Priority: 3',
            'Content-Transfer-Encoding: 8bit',
        ];
    }

    private function getFromHeader(): string
    {
        if (!$this->fromName) {
            return $this->fromEmail;
        }

        $encodedName = $this->encodeEmailHeaderValue($this->fromName);
        return "{$encodedName} <{$this->fromEmail}>";
    }

    /**
     * Build a multipart message with attachments
     *
     * @param string $message The message body
     * @param array $attachments The attachments to include
     * @param string $boundary The boundary string to use
     * @return string The multipart message
     */
    private function buildMultipartMessage(string $message, array $attachments, string $boundary): string
    {
        $emailBody = $this->buildMessageBody($message, $boundary);
        $emailBody .= $this->processAttachments($attachments, $boundary);
        $emailBody .= "--{$boundary}--";

        return $emailBody;
    }
    
    private function buildMessageBody(string $message, string $boundary): string
    {
        $body = "--{$boundary}\r\n";
        $body .= "Content-Type: text/plain; charset=utf-8\r\n";
        $body .= "Content-Transfer-Encoding: 8bit\r\n\r\n";
        $body .= "{$message}\r\n\r\n";
        
        return $body;
    }
    
    private function processAttachments(array $attachments, string $boundary): string
    {
        $attachmentContent = '';
        
        foreach ($attachments as $attachment) {
            if (!$this->isValidAttachment($attachment)) {
                continue;
            }
            
            $attachmentContent .= $this->buildAttachmentContent($attachment, $boundary);
        }
        
        return $attachmentContent;
    }
    
    private function isValidAttachment(array $attachment): bool
    {
        if (!file_exists($attachment['path'])) {
            return false;
        }
        
        $fileSize = filesize($attachment['path']);
        $maxSize = self::MAX_ATTACHMENT_SIZE_MB * 1024 * 1024;
        
        if ($fileSize === false || $fileSize > $maxSize) {
            $this->logWarning("Attachment too large: {$attachment['path']} ({$fileSize} bytes)");
            return false;
        }
        
        return true;
    }
    
    private function buildAttachmentContent(array $attachment, string $boundary): string
    {
        $mimeType = $attachment['type'] ?? $this->getMimeType($attachment['path']);
        $fileName = $attachment['name'] ?? basename($attachment['path']);
        
        $content = "--{$boundary}\r\n";
        $content .= "Content-Type: {$mimeType}; name=\"{$fileName}\"\r\n";
        $content .= "Content-Transfer-Encoding: base64\r\n";
        $content .= "Content-Disposition: attachment; filename=\"{$fileName}\"\r\n\r\n";
        
        $content .= $this->encodeAttachmentFile($attachment['path']);
        $content .= "\r\n";
        
        return $content;
    }
    
    private function encodeAttachmentFile(string $filePath): string
    {
        $handle = fopen($filePath, 'rb');
        
        if (!$handle) {
            $this->logWarning("Could not open attachment: {$filePath}");
            return '';
        }
        
        $encodedContent = '';
        $chunkSize = 76 * 1024; // Read 76KB at a time (base64 line length multiple)
        
        while (!feof($handle)) {
            $chunk = fread($handle, $chunkSize);
            $encodedContent .= chunk_split(base64_encode($chunk));
        }
        
        fclose($handle);
        return $encodedContent;
    }

    private function generateBoundary(string $prefix = 'boundary'): string
    {
        return $prefix . '_' . md5(uniqid((string)mt_rand(), true));
    }

    private function encodeEmailHeaderValue(string $value): string
    {
        if (preg_match('/[^\x00-\x7F]/', $value)) {
            return '=?UTF-8?B?' . base64_encode($value) . '?=';
        }

        return $value;
    }

    private function getMimeType(string $filePath): string
    {
        return File::getMimeType($filePath);
    }

    private function htmlToPlainText(string $html): string
    {
        $text = html_entity_decode($html, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        $text = preg_replace('/<br\s*\/?>/i', "\r\n", $text);
        $text = preg_replace('/<\/div>\s*<div/i', "\r\n<div", $text);
        $text = preg_replace('/<\/p>\s*<p/i', "\r\n\r\n<p", $text);

        $text = strip_tags($text);
        $text = html_entity_decode($text, ENT_QUOTES | ENT_HTML5, 'UTF-8');
        $text = preg_replace('/\s+/', ' ', $text);

        return trim($text);
    }

    private function validateEmailAddress(string $email): bool
    {
        return User::isValidEmail($email);
    }

    private function validateAttachments(array $attachments): void
    {
        $maxSizeBytes = self::MAX_ATTACHMENT_SIZE_MB * 1024 * 1024;

        foreach ($attachments as $attachment) {
            if (!isset($attachment['path']) || !file_exists($attachment['path'])) {
                $fileName = $attachment['name'] ?? $attachment['path'] ?? 'Unknown file';
                throw new EmailException("Attachment not found: {$fileName}");
            }

            $fileSize = filesize($attachment['path']);
            if ($fileSize === false || $fileSize > $maxSizeBytes) {
                $fileName = $attachment['name'] ?? basename($attachment['path']);
                throw new EmailException("Attachment too large: {$fileName}");
            }
        }
    }

    private function resolveTemplatePath(string $templateName): string
    {
        $templatePath = $this->config['template_path'] . '/' . $templateName;

        if (!str_ends_with($templatePath, '.php')) {
            $templatePath .= '.php';
        }

        return $templatePath;
    }

    private function getSmtpConfig(): array
    {
        return [
            'host' => $this->config['smtp_host'],
            'port' => $this->config['smtp_port'],
            'user' => $this->config['smtp_user'],
            'pass' => $this->config['smtp_pass'],
            'secure' => $this->config['smtp_secure'],
        ];
    }

    private function logEmailSent(string $to, string $subject, bool $success, ?string $error = null): void
    {
        try {
            EmailLog::log($to, $subject, $success, $error);
        } catch (\Exception $e) {
            $this->logError("Failed to log email to database", $e);
        }

        $status = $success ? 'sent' : 'failed';
        $logMessage = "Email to {$to} with subject '{$subject}' {$status}";

        if ($error) {
            $logMessage .= ": {$error}";
        }

        $this->logInfo($logMessage);
    }

    private function logError(string $message, ?\Exception $exception = null): void
    {
        $logMessage = $message;

        if ($exception) {
            $logMessage .= ': ' . $exception->getMessage();
            $logMessage .= ' in ' . $exception->getFile() . ':' . $exception->getLine();
        }

        error_log("[ERROR] {$logMessage}");
    }

    /**
     * Log an informational message
     *
     * @param string $message The message to log
     */
    private function logInfo(string $message): void
    {
        error_log("[INFO] {$message}");
    }

    /**
     * Log a warning message
     *
     * @param string $message The message to log
     */
    private function logWarning(string $message): void
    {
        error_log("[WARNING] {$message}");
    }

    /**
     * Get the default configuration for the email service
     *
     * @return array The default configuration
     */
    private function getDefaultConfig(): array
    {
        return [
            'from_email' => '<EMAIL>',
            'from_name' => 'Example Application',
            'use_smtp' => false,
            'smtp_host' => '',
            'smtp_port' => 587,
            'smtp_user' => '',
            'smtp_pass' => '',
            'smtp_secure' => 'tls',
            'template_path' => __DIR__ . '/../views/emails',
        ];
    }

    public function getTemplates(): array
    {
        return EmailTemplate::all();
    }

    public function getTemplate(int $id): ?array
    {
        $template = EmailTemplate::find($id);
        return $template ? (array)$template : null;
    }

    public function getTemplateByName(string $name): ?array
    {
        return EmailTemplate::findByName($name);
    }

    /**
     * Create a new email template
     *
     * @param string $name Template name
     * @param string $subject Email subject
     * @param string $content Template content (HTML)
     * @return int The ID of the created template
     * @throws EmailException If the template could not be created
     */
    public function createTemplate(string $name, string $subject, string $content): int
    {
        try {
            return EmailTemplate::createTemplate($name, $subject, $content);
        } catch (\Exception $e) {
            throw new EmailException("Failed to create email template: " . $e->getMessage());
        }
    }

    /**
     * Update an existing email template
     *
     * @param int $id Template ID
     * @param string|null $name New template name
     * @param string|null $subject New email subject
     * @param string|null $content New template content (HTML)
     * @return bool True if the template was updated successfully
     * @throws EmailException If the template could not be updated
     */
    public function updateTemplate(
        int $id,
        ?string $name = null,
        ?string $subject = null,
        ?string $content = null
    ): bool {
        try {
            $result = EmailTemplate::updateTemplate($id, $name, $subject, $content);
            if (!$result) {
                throw new EmailException("Email template not found or no changes made");
            }
            return $result;
        } catch (\Exception $e) {
            throw new EmailException("Failed to update email template: " . $e->getMessage());
        }
    }

    public function deleteTemplate(int $id): bool
    {
        try {
            $result = EmailTemplate::delete($id);
            if (!$result) {
                throw new EmailException("Email template not found");
            }
            return $result;
        } catch (\Exception $e) {
            throw new EmailException("Failed to delete email template: " . $e->getMessage());
        }
    }

    public function getEmailStats(): array
    {
        return EmailLog::getStats();
    }

    public function getRecentEmails(int $limit = 50): array
    {
        return EmailLog::getRecent($limit);
    }

    public function getEmailsForRecipient(string $recipient, int $limit = 50): array
    {
        return EmailLog::getByRecipient($recipient, $limit);
    }

    public function getFailedEmails(int $limit = 50): array
    {
        return EmailLog::getFailures($limit);
    }

    public function renderTemplate(string $templateName, array $variables = []): string
    {
        $template = $this->getTemplateByName($templateName);
        if (!$template) {
            throw new EmailException("Email template not found: {$templateName}");
        }

        return EmailTemplate::render($template, $variables);
    }

    public function previewTemplate(int $templateId, array $variables = []): array
    {
        $template = $this->getTemplate($templateId);
        if (!$template) {
            throw new EmailException("Email template not found");
        }

        $renderedContent = EmailTemplate::render($template, $variables);
        $plainText = $this->htmlToPlainText($renderedContent);

        return [
            'id' => $template['id'],
            'name' => $template['name'],
            'subject' => $template['subject'],
            'html_content' => $renderedContent,
            'plain_text' => $plainText,
            'variables' => $variables
        ];
    }

    public function sendTestEmail(string $to, int $templateId, array $variables = []): bool
    {
        $template = $this->getTemplate($templateId);
        if (!$template) {
            throw new EmailException("Email template not found");
        }

        $subject = "TEST: " . $template['subject'];
        $htmlContent = EmailTemplate::render($template, $variables);

        return $this->sendHtml($to, $subject, $htmlContent);
    }

    public function validateTemplate(string $name, string $subject, string $content): array
    {
        $errors = [];

        if (empty(trim($name))) {
            $errors[] = "Template name cannot be empty";
        } elseif (strlen($name) > 100) {
            $errors[] = "Template name cannot exceed 100 characters";
        }

        if (empty(trim($subject))) {
            $errors[] = "Subject cannot be empty";
        } elseif (strlen($subject) > 255) {
            $errors[] = "Subject cannot exceed 255 characters";
        }

        if (empty(trim($content))) {
            $errors[] = "Content cannot be empty";
        }

        if (!empty($content)) {
            // Suppress warnings and collect errors
            libxml_use_internal_errors(true);

            // Add proper HTML5 doctype and UTF-8 encoding
            $wrappedContent = "<!DOCTYPE html><html><head><meta charset=\"UTF-8\"></head>"
                . "<body>{$content}</body></html>";

            // Load the HTML content
            $doc = new \DOMDocument();
            $doc->loadHTML($wrappedContent);

            // Get any errors
            $htmlErrors = libxml_get_errors();
            libxml_clear_errors();

            // Check for serious errors only (ignore warnings about HTML5 elements)
            $seriousErrors = array_filter(
                $htmlErrors,
                fn($error) => $error->level === LIBXML_ERR_ERROR || $error->level === LIBXML_ERR_FATAL
            );

            if (!empty($seriousErrors)) {
                $errors[] = "HTML content contains serious errors";

                // Add detailed error information in debug mode
                if (filter_var($_ENV['APP_DEBUG'] ?? false, FILTER_VALIDATE_BOOLEAN)) {
                    foreach ($seriousErrors as $error) {
                        $errors[] = "Line {$error->line}: {$error->message}";
                    }
                }
            }
        }

        return $errors;
    }

    public function getConfiguration(): array
    {
        return [
            'from_email' => $this->fromEmail,
            'from_name' => $this->fromName,
            'use_smtp' => $this->useSmtp,
            'smtp_config' => $this->getSmtpConfig(),
            'template_path' => $this->config['template_path'],
            'max_attachment_size_mb' => self::MAX_ATTACHMENT_SIZE_MB
        ];
    }

    public function updateConfiguration(array $config): bool
    {
        $validKeys = [
            'from_email', 'from_name', 'use_smtp',
            'smtp_host', 'smtp_port', 'smtp_user',
            'smtp_pass', 'smtp_secure', 'template_path'
        ];

        $updatedConfig = $this->config;
        foreach ($config as $key => $value) {
            if (in_array($key, $validKeys)) {
                $updatedConfig[$key] = $value;
            }
        }

        if (isset($config['from_email']) && !$this->validateEmailAddress($config['from_email'])) {
            throw new EmailException("Invalid from email address");
        }

        $this->config = $updatedConfig;
        $this->fromEmail = $updatedConfig['from_email'];
        $this->fromName = $updatedConfig['from_name'];
        $this->useSmtp = $updatedConfig['use_smtp'];

        return true;
    }
}
