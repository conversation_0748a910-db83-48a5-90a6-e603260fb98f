# Views Directory Structure and Standards

## Overview

This directory contains all view templates for the Personal Expense Tracker application. The views follow a standardized structure to ensure consistency, accessibility, and maintainability across the application.

## Directory Structure

```
Views/
├── auth/                  # Authentication-related views (login, register, etc.)
├── categories/            # Category management views
├── dashboard/             # Dashboard views and widgets
│   └── widgets/           # Dashboard-specific widgets (summary_stats, expense_chart, etc.)
├── errors/                # Error pages (404, 500, etc.)
├── expenses/              # Expense management views
│   ├── components/        # Expense-specific components (pagination, messages, etc.)
│   └── documents/         # Document-related views (list, view, etc.)
├── home/                  # Home page and landing views (index, how, etc.)
├── layouts/               # Layout templates (main.php)
│   └── partials/          # Header, footer, sidebar components
├── profile/               # User profile management views
├── reports/               # Reporting and analytics views
│   └── components/        # Report-specific components
├── search/                # Search results views
└── shared/                # Shared components and partials used across all views
    ├── components/        # Reusable UI components
    └── examples/          # Example implementations of shared components
```

### Shared Directory Structure

The `shared/` directory contains standardized components, helpers, and utilities that should be used throughout the application to ensure consistency and maintainability.

```
shared/
├── components/            # Reusable UI components
│   ├── action_button.php  # Button with icon and text
│   ├── document_icon.php  # Icon for document types
│   ├── icon.php           # SVG icon component
│   └── page_header.php    # Page header with title and actions
├── form.php               # Standardized form container
├── form_field.php         # Standardized form field
├── helpers.php            # Shared helper functions
├── messages.php           # Standardized messages component
├── modal.php              # Standardized modal dialog
├── pagination.php         # Standardized pagination controls
├── table.php              # Standardized table layout
└── examples/              # Example implementations
```

### Assets Directory Structure

The `public/assets/` directory contains all static assets used by the views, including CSS, JavaScript, images, and icons.

```
public/assets/
├── css/                   # CSS files
│   ├── components/        # Component-specific CSS
│   │   ├── _alerts.css    # Alert component styles
│   │   ├── _buttons.css   # Button component styles
│   │   ├── _cards.css     # Card component styles
│   │   ├── _forms.css     # Form component styles
│   │   ├── _modals.css    # Modal component styles
│   │   ├── _tables.css    # Table component styles
│   │   └── _shared-components.css  # Shared component styles
│   ├── layouts/           # Layout-specific CSS
│   ├── pages/             # Page-specific CSS
│   ├── base.css           # Base styles (typography, colors, etc.)
│   ├── main.css           # Main stylesheet (imports all other CSS)
│   ├── normalize.css      # CSS normalization
│   ├── utilities.css      # Utility classes
│   └── variables.css      # CSS variables
├── js/                    # JavaScript files
│   ├── components/        # Component-specific JS
│   ├── pages/             # Page-specific JS
│   ├── utils/             # Utility functions
│   └── main.js            # Main JavaScript file
├── icons.svg              # SVG sprite for icons
└── images/                # Image assets
```

## Component Usage

The following components are available in the `shared/` directory and should be used throughout the application to ensure consistency, accessibility, and maintainability. Each component is designed to be self-contained and reusable.

### Available Components

1. **Form Component** (`shared/form.php`)
   - Standardized form container with CSRF protection and consistent styling
   - Handles method spoofing for PUT/DELETE requests
   - Provides consistent form actions layout

2. **Form Field Component** (`shared/form_field.php`)
   - Standardized form field rendering for all input types
   - Supports accessibility features (ARIA attributes, help text)
   - Handles validation states and error messages
   - Supports prepend/append elements (e.g., currency symbols)

3. **Messages Component** (`shared/messages.php`)
   - Standardized feedback messages (success, error, warning, info)
   - Auto-dismissible alerts with proper accessibility
   - Handles both single messages and validation error arrays

4. **Pagination Component** (`shared/pagination.php`)
   - Standardized pagination controls with proper accessibility
   - Responsive design with mobile-friendly controls
   - Preserves query parameters across pagination links

5. **Modal Component** (`shared/modal.php`)
   - Standardized modal dialogs with proper accessibility
   - Supports various sizes and configurations
   - Includes JavaScript for keyboard navigation and focus trapping

6. **Card Component** (`shared/card.php`)
   - Standardized card layout for content organization
   - Supports header actions and footer content
   - Consistent styling across the application

7. **Table Component** (`shared/table.php`)
   - Standardized table layout for data presentation
   - Supports responsive tables and custom column styling
   - Handles empty states and row actions

8. **Icon Component** (`shared/components/icon.php`)
   - Standardized SVG icon rendering from the icon sprite
   - Supports different sizes (sm, md, lg, xl)
   - Includes accessibility attributes
   - Consistent styling across the application

9. **Page Header Component** (`shared/components/page_header.php`)
   - Standardized page header with title and actions
   - Supports back button and custom actions
   - Consistent styling across the application

10. **Action Button Component** (`shared/components/action_button.php`)
    - Standardized action button with icon and text
    - Supports different sizes and types
    - Consistent styling across the application

11. **Document Icon Component** (`shared/components/document_icon.php`)
    - Displays appropriate icon based on file extension
    - Supports different sizes
    - Consistent styling across the application

## SVG Icon System

The application uses an SVG sprite system for icons, which provides several advantages:

1. **Performance**: All icons are loaded in a single request
2. **Consistency**: Icons have a consistent style and appearance
3. **Accessibility**: Icons can include proper accessibility attributes
4. **Flexibility**: Icons can be styled with CSS (color, size, etc.)

### Using Icons

Icons should be used via the `icon.php` component:

```php
<?php
$icon = 'expense';  // Icon ID without the 'icon-' prefix
$size = 'sm';       // Size: sm, md, lg, xl
include __DIR__ . '/../shared/components/icon.php';
?>
```

### Available Icons

The following icons are available in the SVG sprite (`public/assets/icons.svg`):

- **Navigation**: `menu`, `arrow-back`, `arrow-forward`, `close`
- **Actions**: `add`, `edit`, `delete`, `view`, `download`, `upload`, `search`
- **Status**: `success`, `warning`, `error`, `info`
- **Finance**: `expense`, `income`, `transfer`, `budget`
- **Documents**: `file-generic`, `file-pdf`, `file-text`, `file-csv`
- **UI Elements**: `settings`, `user`, `calendar`, `filter`, `sort`

## Helper Functions

The `shared/helpers.php` file contains utility functions that should be used throughout the application to ensure consistency and maintainability.

### Available Helper Functions

1. **formatFileSize(int $bytes): string**
   - Formats file size in human-readable format (bytes, KB, MB, GB)
   - Used for displaying file sizes consistently

2. **formatDate(string $date, string $format = 'M d, Y'): string**
   - Formats dates consistently across the application
   - Default format is 'M d, Y' (e.g., Jan 1, 2023)

3. **formatCurrency(float $amount, string $currency = '$'): string**
   - Formats currency amounts consistently
   - Default currency symbol is '$'

4. **getFileIconId(string $filename): string**
   - Returns the appropriate icon ID based on file extension
   - Used by the document icon component

## Best Practices

### General Guidelines

1. **Use Shared Components**
   - Always use the standardized components from the `shared/` directory
   - Avoid creating custom implementations of common UI patterns

2. **Use Helper Functions**
   - Always use the helper functions from `shared/helpers.php`
   - Include the helpers file at the top of each view file

3. **Maintain Consistency**
   - Follow the established patterns for each view type
   - Use consistent naming conventions for variables and files

4. **Prioritize Accessibility**
   - Include proper ARIA attributes and roles
   - Ensure keyboard navigation works for all interactive elements
   - Use semantic HTML elements appropriately

5. **Keep Views Simple**
   - Views should primarily handle presentation logic
   - Complex data manipulation should be done in controllers
   - Use helper functions for repetitive view logic

6. **Error Handling**
   - Always check for existence of variables before using them
   - Provide meaningful fallbacks for missing data
   - Use the standardized messages component for feedback

### Form Implementation

1. **Use the Form Component**
   ```php
   <?php
   // Set form parameters
   $formParams = [
       'action' => '/expenses',
       'method' => 'POST',
       'multipart' => true,  // For file uploads
       'submitText' => 'Save Expense',
       'cancelUrl' => '/expenses',
       'cancelText' => 'Cancel'
   ];

   // Start output buffering to capture form content
   ob_start();
   ?>

   <!-- Form fields go here using form_field component -->
   <?php
   $fieldParams = [
       'type' => 'text',
       'name' => 'description',
       'label' => 'Description',
       'required' => true
   ];
   extract($fieldParams);
   include __DIR__ . '/../shared/form_field.php';
   ?>

   <?php
   // Get the buffered content
   $formContent = ob_get_clean();

   // Include the form with the content
   extract($formParams);
   $content = $formContent;
   include __DIR__ . '/../shared/form.php';
   ?>
   ```

2. **Validation and Error Handling**
   - Use the `errorMessage` parameter in form_field for field-specific errors
   - Use the messages component for form-level errors
   - Always preserve user input on validation failure using `$_SESSION['old']`

### Layout Implementation

1. **Use the Main Layout**
   - All views should extend the main layout (`layouts/main.php`)
   - Set the `$content` variable to your view content
   - Set the `$scripts` array for page-specific JavaScript files

2. **Page Structure**
   - Use semantic headings (h1, h2, etc.) appropriately
   - Include a page header with contextual actions
   - Group related content in cards or sections

## Examples

See the `shared/examples/` directory for complete examples of how to implement these components in various contexts.

## Component Integration

### Including Components in Views

When including components in views, follow these guidelines:

1. **Set Parameters First**: Define all parameters before including the component
2. **Use Descriptive Variable Names**: Make parameter names clear and descriptive
3. **Provide Default Values**: Use the null coalescing operator (`??`) for optional parameters
4. **Document Component Usage**: Add a comment explaining the component's purpose

Example:
```php
<?php
// Define page header parameters
$title = 'Expense Details';
$subtitle = 'View and manage expense information';
$backUrl = '/expenses';
$backText = 'Back to Expenses';
$actions = [
    [
        'url' => "/expenses/{$expense['id']}/edit",
        'text' => 'Edit',
        'icon' => 'edit',
        'type' => 'primary'
    ],
    [
        'url' => "/expenses/{$expense['id']}/delete",
        'text' => 'Delete',
        'icon' => 'delete',
        'type' => 'danger'
    ]
];

// Include the page header component
include __DIR__ . '/../shared/components/page_header.php';
?>
```

### Component Composition

Components can be composed together to create more complex UI elements:

```php
<?php
// Start a card component
$cardTitle = 'Expense Details';
$cardActions = true;
include __DIR__ . '/../shared/card_start.php';
?>

<!-- Card content -->
<div class="expense-details">
    <?php
    // Include other components inside the card
    $icon = 'expense';
    $size = 'lg';
    include __DIR__ . '/../shared/components/icon.php';
    ?>

    <div class="expense-info">
        <h3><?= htmlspecialchars($expense['description']) ?></h3>
        <p><?= formatCurrency((float)$expense['amount']) ?></p>
    </div>
</div>

<?php
// End the card component
include __DIR__ . '/../shared/card_end.php';
?>
```

## Extending Components

If you need to extend or modify these components, please consider:

1. Whether the change should be made to the shared component for all uses
2. Whether a new parameter should be added to support the variation
3. Whether a new component should be created for the specific use case

Always prioritize consistency and maintainability over one-off customizations.

### Component Versioning

When making significant changes to a component:

1. Document the changes in a comment at the top of the component file
2. Update any examples in the `shared/examples/` directory
3. Consider backward compatibility for existing uses
4. Update the README.md with any new parameters or usage patterns