document.addEventListener('DOMContentLoaded', initializeDashboard);

function initializeDashboard()
{
    initializeExpenseChart();
    initializeCategoryChart();
    initializeQuickActions();
    setupRefreshButton();
    setupDateRangeFilter();
    setupWidgetToggle();
}

function initializeExpenseChart()
{
    const chartCanvas = document.getElementById('expenseChart');
    if (!chartCanvas) {
        return;
    }

    const ctx = chartCanvas.getContext('2d');
    const chartData = JSON.parse(chartCanvas.getAttribute('data-chart'));

    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: chartData.labels,
            datasets: [{
                label: 'Monthly Expenses',
                data: chartData.values,
                backgroundColor: 'rgba(59, 130, 246, 0.5)',
                borderColor: 'rgba(59, 130, 246, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: value => '$' + value.toLocaleString()
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: context => '$' + context.raw.toLocaleString()
                    }
                },
                legend: { display: false }
            }
        }
    });
}

function initializeCategoryChart()
{
    const chartCanvas = document.getElementById('categoryChart');
    if (!chartCanvas) {
        return;
    }

    const ctx = chartCanvas.getContext('2d');
    const chartData = JSON.parse(chartCanvas.getAttribute('data-chart'));
    const backgroundColors = generateChartColors(chartData.labels);

    createDoughnutChart(ctx, chartData, backgroundColors);
}

function generateChartColors(labels)
{
    return labels.map((_, index) => {
        const hue = (index * 137) % 360;
        return `hsla(${hue}, 70 % , 60 % , 0.7)`;
    });
}

function createDoughnutChart(ctx, chartData, backgroundColors)
{
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: chartData.labels,
            datasets: [{
                data: chartData.values,
                backgroundColor: backgroundColors,
                borderColor: backgroundColors.map(color => color.replace('0.7', '1')),
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                tooltip: {
                    callbacks: {
                        label: context => formatChartLabel(context)
                    }
                }
            }
        }
    });
}

function formatChartLabel(context)
{
    const value = context.raw;
    const total = context.dataset.data.reduce((a, b) => a + b, 0);
    const percentage = ((value / total) * 100).toFixed(1);
    return `${context.label}: $${value.toLocaleString()} (${percentage} % )`;
}

function initializeQuickActions()
{
    const quickActions = document.querySelector('.quick-actions');
    if (!quickActions) {
        return;
    }

    quickActions.querySelectorAll('.action-button').forEach(button => {
        button.addEventListener('mouseenter', () => {
            button.style.transform = 'translateY(-3px)';
        });

        button.addEventListener('mouseleave', () => {
            button.style.transform = '';
        });
    });
}

function setupRefreshButton()
{
    const refreshButton = document.getElementById('refreshDashboard');
    if (!refreshButton) {
        return;
    }

    refreshButton.addEventListener('click', () => {
        showLoadingState(refreshButton);
        setTimeout(() => window.location.reload(), 800);
    });
}

function showLoadingState(button)
{
    button.classList.add('loading');
    button.innerHTML = '<span class="spinner"></span> Refreshing...';
}

function setupDateRangeFilter()
{
    const dateFilter = document.getElementById('dashboardDateFilter');
    if (!dateFilter) {
        return;
    }

    dateFilter.addEventListener('change', () => {
        const form = dateFilter.closest('form');
        if (form) {
            form.submit();
        }
    });
}

function setupWidgetToggle()
{
    document.querySelectorAll('.widget-header .toggle-widget').forEach(toggle => {
        const widget = toggle.closest('.widget');
        const content = widget.querySelector('.widget-content');
        const widgetId = getWidgetId(widget);

        restoreWidgetState(toggle, content, widgetId);
        setupWidgetToggleEvents(toggle, content, widgetId);
    });
}

function getWidgetId(widget)
{
    return widget.id || widget.querySelector('h3').textContent.trim();
}

function restoreWidgetState(toggle, content, widgetId)
{
    const isCollapsed = localStorage.getItem(`widget_${widgetId}_collapsed`) === 'true';

    if (isCollapsed) {
        content.classList.add('collapsed');
        toggle.innerHTML = '↓';
    }
}

function setupWidgetToggleEvents(toggle, content, widgetId)
{
    toggle.addEventListener('click', () => {
        content.classList.toggle('collapsed');
        toggle.innerHTML = content.classList.contains('collapsed') ? '↓' : '↑';

        localStorage.setItem(
            `widget_${widgetId}_collapsed`,
            content.classList.contains('collapsed')
        );
    });
}
