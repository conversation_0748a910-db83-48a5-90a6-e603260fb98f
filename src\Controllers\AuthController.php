<?php

declare(strict_types=1);

namespace App\Controllers;

use App\Core\Response;
use App\Core\Security\CSRF;
use App\Core\Security\Validator;
use App\Services\AuthService;
use App\Core\LogManager;
use App\Models\User;
use InvalidArgumentException;

final class AuthController extends BaseAuthController
{
    private const PASSWORD_RULES = [
        'required',
        'string',
        'min:8',
        'contains_digit',
        'contains_special'
    ];
    private Validator $registerValidator;
    private Validator $loginValidator;

    public function __construct(
        private readonly AuthService $authService
    ) {
        $this->initializeValidators();
    }

    public function login(): Response
    {
        error_log("AuthController::login - Starting login page render");

        if ($this->authService->isAuthenticated()) {
            error_log("AuthController::login - User already authenticated, redirecting to dashboard");
            return $this->redirect('/dashboard');
        }

        // Get the remember_me_default preference from the database if user has a remember token
        $rememberMeDefault = false;

        // Check if there's a remember token cookie
        if (isset($_COOKIE['remember_token'])) {
            $token = $_COOKIE['remember_token'];
            error_log("AuthController::login - Found remember_token cookie: " . substr($token, 0, 8) . "...");

            $tokenData = $this->authService->validateRememberToken($token);

            if ($tokenData) {
                error_log("AuthController::login - Token is valid, user_id: " . $tokenData['user_id']);

                // Get the user's preference from the database
                $user = User::findById($tokenData['user_id']);
                error_log("AuthController::login - User found: " . ($user ? 'yes' : 'no'));

                if ($user) {
                    error_log(
                        "AuthController::login - User remember_me_default: " .
                        ($user['remember_me_default'] ?? 'not set')
                    );

                    if (!empty($user['remember_me_default'])) {
                        $rememberMeDefault = true;
                        error_log("AuthController::login - Setting remember_me_default to true based on user preference");
                    }
                }
            } else {
                error_log("AuthController::login - Token is invalid or expired");
            }
        } else {
            error_log("AuthController::login - No remember_token cookie found");

            // Check for remember_me cookie
            error_log("AuthController::login - Checking remember_me cookie");
            if (isset($_COOKIE['remember_me'])) {
                error_log("AuthController::login - Found remember_me cookie: " . $_COOKIE['remember_me']);
                $rememberMeDefault = $_COOKIE['remember_me'] === 'true';
                error_log("AuthController::login - Setting remember_me_default to "
                    . ($rememberMeDefault ? 'true' : 'false')
                    . " based on cookie");
            } else {
                error_log("AuthController::login - No remember_me cookie found");
            }
        }

        error_log("AuthController::login - Final remember_me_default value: " .
            ($rememberMeDefault ? 'true' : 'false'));

        return $this->view('auth/login', [
            'csrf_token' => $this->getCsrfToken(),
            'remember_me_default' => $rememberMeDefault
        ]);
    }

    public function authenticate(): Response
    {
        error_log("AuthController::authenticate() called");

        // Validate CSRF token
        $csrfToken = $this->getPostValue('csrf_token');

        $csrfResponse = $this->validateCsrfWithRedirect(
            $csrfToken,
            '/login',
            'Invalid security token'
        );
        if ($csrfResponse !== null) {
            error_log("CSRF validation failed, redirecting to login");
            return $csrfResponse;
        }

        try {
            $data = $this->collectLoginData();
            $remember = isset($_POST['remember']) && $_POST['remember'] === '1';
            error_log("Login attempt for {$data['email']}, remember me: " . ($remember ? 'yes' : 'no'));

            // Validate login data
            $this->loginValidator->validate($data);

            // Attempt login
            $user = User::findByEmail($data['email']);

            // Check if user exists and password is correct
            if (
                !$user
                || !password_verify($data['password'], $user['password'])
                || $user['user_status_id'] !== User::STATUS_ACTIVE
            ) {
                error_log("Login failed for {$data['email']} - invalid credentials or inactive user");
                return $this->redirectWithErrors('/login', 'Invalid credentials', ['email' => $data['email']]);
            }

            // Login successful - clear any existing session
            session_unset();

            // Start a new session
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['user_email'] = $user['email'];
            $_SESSION['user_name'] = $user['name'];
            $_SESSION['authenticated'] = true;
            $_SESSION['last_activity'] = time();
            $_SESSION['remember_me'] = $remember;

            // Regenerate session ID for security
            session_regenerate_id(true);

            error_log("Login successful for {$data['email']}, session data: " . json_encode($_SESSION));

            // Handle remember me functionality
            if ($remember) {
                error_log("Remember me checked, setting up persistent session");

                // Set the remember_me flag in the session
                $_SESSION['remember_me'] = true;

                // Delete any existing remember tokens for this user
                User::manageRememberToken($user['id'], 'delete');

                // Create a new remember token
                $expiryDays = 30; // Default to 30 days
                $tokenData = User::manageRememberToken($user['id'], 'create', null, $expiryDays);

                // Always update the user's remember_me_default preference to match their login choice
                error_log("Updating user's remember_me_default preference to match their login choice (checked)");
                User::update($user['id'], ['remember_me_default' => 1]);

                // Also update the remember_me cookie to match
                setcookie(
                    'remember_me',
                    'true',
                    [
                        'expires' => time() + 86400 * 30, // 30 days
                        'path' => '/',
                        'secure' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on',
                        'httponly' => false,
                        'samesite' => 'Lax'
                    ]
                );

                if (!isset($tokenData['error'])) {
                    // Set the remember token cookie
                    setcookie(
                        'remember_token',
                        $tokenData['token'],
                        [
                            'expires' => strtotime($tokenData['expires_at']),
                            'path' => '/',
                            'domain' => '',
                            'secure' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on',
                            'httponly' => true,
                            'samesite' => 'Lax'
                        ]
                    );
                    error_log("Remember token cookie set for user {$user['id']}");

                    // Set the session cookie to persist for the same duration
                    // This is critical for the "Remember me" functionality to work properly
                    $params = session_get_cookie_params();
                    setcookie(
                        session_name(),
                        session_id(),
                        [
                            'expires' => strtotime($tokenData['expires_at']),
                            'path' => $params['path'],
                            'domain' => $params['domain'],
                            'secure' => $params['secure'],
                            'httponly' => $params['httponly'],
                            'samesite' => $params['samesite'] ?? 'Lax'
                        ]
                    );
                    error_log("Session cookie set to persist for {$expiryDays} days");
                }
            } else {
                error_log("Remember me NOT checked, setting up non-persistent session");

                // Set the remember_me flag in the session to false
                $_SESSION['remember_me'] = false;

                // Clear any existing remember token cookie
                setcookie(
                    'remember_token',
                    '',
                    [
                        'expires' => time() - 3600,
                        'path' => '/',
                        'domain' => '',
                        'secure' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on',
                        'httponly' => true,
                        'samesite' => 'Lax'
                    ]
                );

                // Always update the user's remember_me_default preference to match their login choice
                error_log("Updating user's remember_me_default preference to match their login choice (unchecked)");
                User::update($user['id'], ['remember_me_default' => 0]);

                // Also update the remember_me cookie to match
                setcookie(
                    'remember_me',
                    'false',
                    [
                        'expires' => time() + 86400 * 30, // 30 days
                        'path' => '/',
                        'secure' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on',
                        'httponly' => false,
                        'samesite' => 'Lax'
                    ]
                );
                error_log("Remember token cookie cleared (remember me not checked)");

                // Ensure the session cookie is set to expire when browser is closed
                // This is critical - we need to explicitly set expires=0 to make the session cookie
                // expire when the browser is closed
                $params = session_get_cookie_params();
                setcookie(
                    session_name(),
                    session_id(),
                    [
                        'expires' => 0, // 0 means "until the browser is closed"
                        'path' => $params['path'],
                        'domain' => $params['domain'],
                        'secure' => $params['secure'],
                        'httponly' => true, // Force httponly for security
                        'samesite' => 'Lax'
                    ]
                );
                error_log("Session cookie set to expire when browser is closed");

                // Double-check that the session cookie was set correctly
                $sessionCookieParams = session_get_cookie_params();
                error_log("Session cookie params after setting: " . json_encode($sessionCookieParams));

                // Delete any existing remember tokens for this user
                User::manageRememberToken($user['id'], 'delete');
            }

            // Double-check that the session cookie was set correctly
            $sessionCookieParams = session_get_cookie_params();
            error_log("Session cookie params after setting: " . json_encode($sessionCookieParams));
            error_log("Remember me flag in session: " . ($_SESSION['remember_me'] ? 'true' : 'false'));

            // Check if there's a redirect URL stored in the session
            if (isset($_SESSION['redirect_after_login'])) {
                $redirectUrl = $_SESSION['redirect_after_login'];
                unset($_SESSION['redirect_after_login']);

                // Double-check that the URL is internal for security
                if ($this->isInternalUrl($redirectUrl)) {
                    error_log("Redirecting to: {$redirectUrl}");

                    // Use direct Response object to ensure immediate redirect
                    $response = Response::redirect($redirectUrl);
                    $response->send();
                    exit;
                }
            }

            error_log("Redirecting to dashboard");

            // Use direct Response object to ensure immediate redirect
            $response = Response::redirect('/dashboard');
            $response->send();
            exit;
        } catch (InvalidArgumentException $exception) {
            error_log("Exception in authenticate: " . $exception->getMessage());
            return $this->redirectWithErrors('/login', $exception->getMessage(), [
                'email' => $this->getPostValue('email')
            ]);
        } catch (\Exception $e) {
            error_log("Unexpected exception in authenticate: " . $e->getMessage());
            return $this->redirectWithErrors('/login', 'An unexpected error occurred', [
                'email' => $this->getPostValue('email')
            ]);
        }
    }

    private function collectLoginData(): array
    {
        return [
            'email' => $this->getPostValue('email'),
            'password' => $this->getPostValue('password')
        ];
    }

    public function logout(): Response
    {
        try {
            // Get the CSRF token from the request
            $token = $this->getPostValue('csrf_token');

            // Skip CSRF validation for logout to avoid issues with session destruction
            // This is a security trade-off, but for logout it's acceptable
            // since the worst case is that someone logs the user out

            // Log the logout attempt
            error_log("Logout attempt for user ID: " . ($_SESSION['user_id'] ?? 'unknown'));

            // Perform logout
            $this->authService->logout();

            // Redirect to login page
            return $this->redirect('/login');
        } catch (\Exception $e) {
            // Log the error
            error_log("Error during logout: " . $e->getMessage());

            // Redirect to login page anyway
            return $this->redirect('/login');
        }
    }

    public function register(): Response
    {
        if ($this->authService->isAuthenticated()) {
            return $this->redirect('/dashboard');
        }

        return $this->view('auth/register', [
            'csrf_token' => $this->getCsrfToken()
        ]);
    }

    public function store(): Response
    {
        // Validate CSRF token
        $csrfResponse = $this->validateCsrfWithRedirect(
            $this->getPostValue('csrf_token'),
            '/register',
            'Invalid security token'
        );
        if ($csrfResponse !== null) {
            return $csrfResponse;
        }

        try {
            $data = $this->collectRegistrationData();
            $userData = $this->validateRegistrationData($data);

            return $this->completeRegistration($userData);
        } catch (InvalidArgumentException $exception) {
            return $this->handleRegistrationError($exception);
        }
    }

    private function completeRegistration(array $userData): Response
    {
        $success = $this->registerUser($userData);
        if (!$success) {
            throw new InvalidArgumentException('Registration failed');
        }

        $this->authService->login($userData['email'], $userData['password']);
        return $this->redirect('/dashboard', 'Registration successful');
    }

    private function handleRegistrationError(InvalidArgumentException $exception): Response
    {
        return $this->redirectWithErrors('/register', $exception->getMessage(), [
            'name' => $this->getPostValue('name'),
            'email' => $this->getPostValue('email')
        ]);
    }

    private function collectRegistrationData(): array
    {
        return [
            'name' => $this->getPostValue('name'),
            'email' => $this->getPostValue('email'),
            'password' => $this->getPostValue('password'),
            'password_confirm' => $this->getPostValue('password_confirm')
        ];
    }

    private function validateRegistrationData(array $data): array
    {
        $rules = $this->registerValidator->getRules();
        $rules['password_confirm'] = ['required', 'matches:password'];
        $validator = new Validator($rules);
        return $validator->validate($data);
    }

    private function registerUser(array $userData): bool
    {
        return $this->authService->register(
            $userData['name'],
            $userData['email'],
            $userData['password'],
            $userData['password_confirm']
        );
    }

    // Password reset methods
    public function resetPassword(): Response
    {
        return $this->view('auth/password/reset', [
            'csrf_token' => $this->getCsrfToken()
        ]);
    }

    public function processResetPassword(): Response
    {
        // Validate CSRF token
        $csrfResponse = $this->validateCsrfWithRedirect(
            $this->getPostValue('csrf_token'),
            '/password/reset',
            'Invalid security token'
        );
        if ($csrfResponse !== null) {
            return $csrfResponse;
        }

        try {
            $email = $this->getPostValue('email');

            if (empty($email)) {
                return $this->redirect('/password/reset', 'Email is required', 'error');
            }

            $this->validateEmail($email);
            return $this->handlePasswordReset($email);
        } catch (InvalidArgumentException $exception) {
            return $this->redirectWithErrors('/password/reset', $exception->getMessage(), [
                'email' => $this->getPostValue('email')
            ]);
        } catch (\Exception $e) {
            LogManager::getLogger()?->error("Error processing password reset", [
                'exception' => $e->getMessage()
            ]);
            return $this->redirect('/password/reset', 'An error occurred while processing your request', 'error');
        }
    }

    private function handlePasswordReset(string $email): Response
    {
        $user = User::findByEmail($email);

        if (!$user) {
            return $this->sendGenericResetResponse();
        }

        $resetToken = User::generateResetToken($email);
        if (!$resetToken) {
            return $this->redirect('/password/reset', 'Failed to generate reset token', 'error');
        }

        $this->logResetLink($email, $resetToken);
        return $this->redirect('/login', 'Password reset instructions sent to your email');
    }

    private function sendGenericResetResponse(): Response
    {
        return $this->redirect(
            '/password/reset',
            'If your email is registered, you will receive a password reset link',
            'success'
        );
    }

    private function logResetLink(string $email, string $resetToken): void
    {
        $resetLink = "/password/new?email=" . urlencode($email) . "&token=" . urlencode($resetToken);
        LogManager::getLogger()?->info("Password reset link generated", [
            'email' => $email,
            'reset_link' => $resetLink
        ]);
    }

    private function validateEmail(string $email): void
    {
        $validator = new Validator(['email' => ['required', 'email']]);
        $validator->validate(['email' => $email]);
    }

    public function newPassword(): Response
    {
        $token = $this->getQueryValue('token');
        $user = $this->authService->validateResetToken($token);
        if (!$user) {
            return $this->redirect('/password/reset', 'Invalid or expired reset token');
        }

        return $this->view('auth/password/new', [
            'token' => $token,
            'csrf_token' => $this->getCsrfToken()
        ]);
    }

    public function updatePassword(): Response
    {
        // Validate CSRF token
        $csrfResponse = $this->validateCsrfWithRedirect(
            $this->getPostValue('csrf_token'),
            '/password/reset',
            'Invalid security token'
        );
        if ($csrfResponse !== null) {
            return $csrfResponse;
        }

        try {
            $data = $this->collectPasswordData();

            $this->validatePasswordData($data);
            return $this->processPasswordUpdate($data);
        } catch (InvalidArgumentException $exception) {
            return $this->redirectWithErrors('/password/reset', $exception->getMessage(), []);
        }
    }

    private function collectPasswordData(): array
    {
        return [
            'token' => $this->getPostValue('token'),
            'password' => $this->getPostValue('password'),
            'password_confirm' => $this->getPostValue('password_confirm')
        ];
    }

    private function processPasswordUpdate(array $data): Response
    {
        $user = $this->authService->validateResetToken($data['token']);

        if (!$user) {
            throw new InvalidArgumentException('Invalid or expired reset token');
        }

        $this->authService->resetPassword($user['id'], $data['password']);
        return $this->redirect('/login', 'Password has been reset successfully');
    }

    private function validatePasswordData(array $data): void
    {
        $validator = new Validator([
            'password' => self::PASSWORD_RULES,
            'password_confirm' => ['required', 'matches:password']
        ]);
        $validator->validate($data);
    }

    private function initializeValidators(): void
    {
        $this->registerValidator = new Validator([
            'name' => ['required', 'string', 'min:2', 'max:100'],
            'email' => ['required', 'email'],
            'password' => self::PASSWORD_RULES
        ]);

        $this->loginValidator = new Validator([
            'email' => ['required', 'email'],
            'password' => ['required']
        ]);
    }

    /**
     * Debug method to check remember tokens
     *
     * @return Response
     */
    public function debugRememberTokens(): Response
    {
        // Require authentication
        $authResponse = $this->requireAuthentication('Please login to access debug information');
        if ($authResponse !== null) {
            return $authResponse;
        }

        $userId = $this->getCurrentUserId();
        $output = [];

        // Check if there's a remember token cookie
        $cookieName = $this->authConfig['remember_cookie']['name'] ?? 'remember_token';
        $output[] = "Remember token cookie: " . (isset($_COOKIE[$cookieName]) ? 'Present' : 'Not present');

        if (isset($_COOKIE[$cookieName])) {
            $token = $_COOKIE[$cookieName];
            $output[] = "Token value: " . substr($token, 0, 8) . "...";
            $output[] = "Full token length: " . strlen($token) . " characters";

            // Check if the token exists in the database
            $tokenData = User::findRememberToken($token);
            if ($tokenData) {
                $output[] = "Token found in database: Yes";
                $output[] = "Token ID: " . $tokenData['id'];
                $output[] = "Token user ID: " . $tokenData['user_id'];
                $output[] = "Token created at: " . $tokenData['created_at'];
                $output[] = "Token expires at: " . $tokenData['expires_at'];

                // Check if user agent is stored correctly
                if (isset($tokenData['user_agent']) && !empty($tokenData['user_agent'])) {
                    $output[] = "User agent stored: " . substr($tokenData['user_agent'], 0, 50) . "...";
                    $output[] = "User agent matches current: " .
                        (($tokenData['user_agent'] === ($_SERVER['HTTP_USER_AGENT'] ?? '')) ? 'Yes' : 'No');
                } else {
                    $output[] = "User agent: Not set";
                }

                // Check if IP address is stored correctly
                if (isset($tokenData['ip_address']) && !empty($tokenData['ip_address'])) {
                    $output[] = "IP address stored: " . $tokenData['ip_address'];
                    $output[] = "IP address matches current: " .
                        (($tokenData['ip_address'] === ($_SERVER['REMOTE_ADDR'] ?? '')) ? 'Yes' : 'No');
                } else {
                    $output[] = "IP address: Not set";
                }

                // Check token expiration
                $now = new \DateTime();
                $expiresAt = new \DateTime($tokenData['expires_at']);
                $daysRemaining = $now->diff($expiresAt)->days;
                $output[] = "Token expires in: " . $daysRemaining . " days";
            } else {
                $output[] = "Token found in database: No";
                $output[] = "Possible reasons: Token expired, invalid, or database error";
            }
        }

        // Check for tokens for the current user
        $userTokens = User::findRememberTokenByUserId($userId);
        if ($userTokens) {
            $output[] = "User has tokens in database: Yes";
            $output[] = "User token ID: " . $userTokens['id'];
            $output[] = "User token value: " . substr($userTokens['token'], 0, 8) . "...";
            $output[] = "Token created at: " . $userTokens['created_at'];
            $output[] = "Token expires at: " . $userTokens['expires_at'];

            // Check if user agent is stored correctly
            if (isset($userTokens['user_agent']) && !empty($userTokens['user_agent'])) {
                $output[] = "User agent stored: " . substr($userTokens['user_agent'], 0, 50) . "...";
            } else {
                $output[] = "User agent: Not set";
            }

            // Check if IP address is stored correctly
            if (isset($userTokens['ip_address']) && !empty($userTokens['ip_address'])) {
                $output[] = "IP address stored: " . $userTokens['ip_address'];
            } else {
                $output[] = "IP address: Not set";
            }

            // Check if cookie token matches database token
            if (isset($_COOKIE[$cookieName])) {
                $output[] = "Cookie token matches database token: " .
                    (($_COOKIE[$cookieName] === $userTokens['token']) ? 'Yes' : 'No');
            }
        } else {
            $output[] = "User has tokens in database: No";
        }

        // Check database table structure
        $output[] = "Checking remember_tokens table structure...";
        try {
            $tableExists = User::checkTableExists('remember_tokens');
            $output[] = "Table exists: " . ($tableExists ? 'Yes' : 'No');

            if ($tableExists) {
                $output[] = "Ensuring table columns...";
                $columnsEnsured = User::ensureTableColumns();
                $output[] = "Columns ensured: " . ($columnsEnsured ? 'Yes' : 'No');
            }
        } catch (\Exception $e) {
            $output[] = "Error checking table structure: " . $e->getMessage();
        }

        // Create a new token for testing using the centralized management method
        $output[] = "Creating a new token for testing...";
        $expiryDays = $this->authConfig['remember_token_expiry_days'] ?? 30;
        $tokenData = User::manageRememberToken($userId, 'create', null, $expiryDays);

        if (isset($tokenData['error']) && $tokenData['error']) {
            $output[] = "Error creating token: " . ($tokenData['message'] ?? 'Unknown error');
        } else {
            $output[] = "New token created: " . substr($tokenData['token'], 0, 8) . "...";
            $output[] = "Token ID: " . $tokenData['id'];
            $output[] = "Token expires at: " . $tokenData['expires_at'];
            $output[] = "Token length: " . strlen($tokenData['token']) . " characters";
        }

        return $this->json([
            'user_id' => $userId,
            'debug_info' => $output
        ]);
    }

    /**
     * Debug method to check session status
     *
     * @return Response
     */
    public function debugSession(): Response
    {
        $output = [];

        // Check session status
        $output[] = "Session active: " . (session_status() === PHP_SESSION_ACTIVE ? 'Yes' : 'No');
        $output[] = "Session ID: " . session_id();

        // Check session data
        $output[] = "Session data: " . json_encode($_SESSION);

        // Check cookies
        $output[] = "Cookies: " . json_encode($_COOKIE);

        // Check session cookie parameters
        $params = session_get_cookie_params();
        $output[] = "Session cookie parameters: " . json_encode($params);

        // Check if session cookie exists
        $sessionName = session_name();
        $output[] = "Session cookie name: " . $sessionName;
        $output[] = "Session cookie exists: " . (isset($_COOKIE[$sessionName]) ? 'Yes' : 'No');

        if (isset($_COOKIE[$sessionName])) {
            $output[] = "Session cookie value: " . $_COOKIE[$sessionName];
        }

        // Check authentication status
        $isAuthenticated = $this->checkAuthentication();
        $output[] = "Is authenticated: " . ($isAuthenticated ? 'Yes' : 'No');

        // Get user ID
        $userId = $this->getCurrentUserId();
        $output[] = "User ID: " . ($userId ?? 'Not set');

        // Check remember token
        $cookieName = $this->authConfig['remember_cookie']['name'] ?? 'remember_token';
        $output[] = "Remember token cookie: " . (isset($_COOKIE[$cookieName]) ? 'Present' : 'Not present');

        if (isset($_COOKIE[$cookieName])) {
            $token = $_COOKIE[$cookieName];
            $output[] = "Token value: " . substr($token, 0, 8) . "...";

            // Check if the token exists in the database
            $tokenData = User::findRememberToken($token);
            if ($tokenData) {
                $output[] = "Token found in database: Yes";
                $output[] = "Token expires at: " . $tokenData['expires_at'];

                // Check token expiration
                $now = new \DateTime();
                $expiresAt = new \DateTime($tokenData['expires_at']);
                $daysRemaining = $now->diff($expiresAt)->days;
                $output[] = "Token expires in: " . $daysRemaining . " days";
            } else {
                $output[] = "Token found in database: No";
            }
        }

        // Check session timeout
        if (isset($_SESSION['last_activity'])) {
            $timeSinceLastActivity = time() - $_SESSION['last_activity'];
            $sessionTimeout = $this->authConfig['session_timeout'] ?? 1800;
            $output[] = "Time since last activity: {$timeSinceLastActivity}s (timeout: {$sessionTimeout}s)";
        } else {
            $output[] = "Last activity: Not set";
        }

        // Check remember_me flag
        $rememberMeFlag = isset($_SESSION['remember_me'])
            ? ($_SESSION['remember_me'] ? 'true' : 'false')
            : 'Not set';
        $output[] = "Remember me flag: " . $rememberMeFlag;

        return $this->json([
            'debug_info' => $output
        ]);
    }
}
