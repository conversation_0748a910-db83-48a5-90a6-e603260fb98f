<?php

declare(strict_types=1);

namespace App\Core\Security;

use PDO;
use PDOStatement;
use App\Exceptions\DatabaseException;

final class PreparedStatement
{
    private PDO $pdo;
    private SecurityLogger $securityLogger;
    private bool $logQueries;

    public function __construct(PDO $pdo, ?SecurityLogger $securityLogger = null, bool $logQueries = false)
    {
        $this->pdo = $pdo;
        $this->securityLogger = $securityLogger ?? new SecurityLogger();
        $this->logQueries = $logQueries;
    }

    public function execute(string $sql, array $params = []): PDOStatement
    {
        try {
            // Validate SQL to prevent dangerous operations
            $this->validateSql($sql);
            
            // Log query if enabled (without sensitive data)
            if ($this->logQueries) {
                error_log("Executing query: " . $this->sanitizeQueryForLogging($sql));
            }
            
            $stmt = $this->pdo->prepare($sql);
            
            if ($stmt === false) {
                throw new DatabaseException('Failed to prepare statement: ' . $sql);
            }
            
            // Bind parameters with proper types
            $this->bindParameters($stmt, $params);
            
            $result = $stmt->execute();
            
            if ($result === false) {
                $errorInfo = $stmt->errorInfo();
                throw new DatabaseException(
                    'Query execution failed: ' . ($errorInfo[2] ?? 'Unknown error')
                );
            }
            
            return $stmt;
            
        } catch (\PDOException $e) {
            $this->securityLogger->logDatabaseError($sql, $e->getMessage(), [
                'params_count' => count($params),
                'error_code' => $e->getCode()
            ]);
            
            throw new DatabaseException(
                'Database query failed: ' . $e->getMessage(),
                (int)$e->getCode(),
                $e
            );
        }
    }

    public function fetchOne(string $sql, array $params = []): ?array
    {
        $stmt = $this->execute($sql, $params);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result === false ? null : $result;
    }

    public function fetchMany(string $sql, array $params = []): array
    {
        $stmt = $this->execute($sql, $params);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function fetchColumn(string $sql, array $params = [], int $column = 0): mixed
    {
        $stmt = $this->execute($sql, $params);
        return $stmt->fetchColumn($column);
    }

    public function insert(string $table, array $data): int
    {
        $this->validateTableName($table);
        $this->validateColumnNames(array_keys($data));
        
        $columns = implode(', ', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $sql = "INSERT INTO `{$table}` ({$columns}) VALUES ({$placeholders})";
        $this->execute($sql, $data);
        
        return (int)$this->pdo->lastInsertId();
    }

    public function update(string $table, array $data, array $where): int
    {
        $this->validateTableName($table);
        $this->validateColumnNames(array_keys($data));
        $this->validateColumnNames(array_keys($where));
        
        $setClause = implode(', ', array_map(fn($col) => "`{$col}` = :{$col}", array_keys($data)));
        $whereClause = implode(' AND ', array_map(fn($col) => "`{$col}` = :where_{$col}", array_keys($where)));
        
        // Prefix where parameters to avoid conflicts
        $whereParams = [];
        foreach ($where as $key => $value) {
            $whereParams["where_{$key}"] = $value;
        }
        
        $params = array_merge($data, $whereParams);
        
        $sql = "UPDATE `{$table}` SET {$setClause} WHERE {$whereClause}";
        $stmt = $this->execute($sql, $params);
        
        return $stmt->rowCount();
    }

    public function delete(string $table, array $where): int
    {
        $this->validateTableName($table);
        $this->validateColumnNames(array_keys($where));
        
        $whereClause = implode(' AND ', array_map(fn($col) => "`{$col}` = :{$col}", array_keys($where)));
        
        $sql = "DELETE FROM `{$table}` WHERE {$whereClause}";
        $stmt = $this->execute($sql, $where);
        
        return $stmt->rowCount();
    }

    public function beginTransaction(): bool
    {
        return $this->pdo->beginTransaction();
    }

    public function commit(): bool
    {
        return $this->pdo->commit();
    }

    public function rollback(): bool
    {
        return $this->pdo->rollBack();
    }

    public function inTransaction(): bool
    {
        return $this->pdo->inTransaction();
    }

    private function validateSql(string $sql): void
    {
        $sql = strtolower(trim($sql));
        
        // Block dangerous SQL operations
        $dangerousPatterns = [
            '/\b(drop|truncate|alter)\s+/',
            '/\binto\s+outfile\b/',
            '/\bload_file\s*\(/',
            '/\bsystem\s*\(/',
            '/\bexec\s*\(/',
            '/\beval\s*\(/',
            '/\bunion\s+.*select\s+.*from\s+information_schema/',
            '/\bselect\s+.*from\s+mysql\./'
        ];
        
        foreach ($dangerousPatterns as $pattern) {
            if (preg_match($pattern, $sql)) {
                $this->securityLogger->logSuspiciousActivity(
                    'Dangerous SQL pattern detected',
                    ['pattern' => $pattern, 'sql_hash' => hash('sha256', $sql)]
                );
                throw new DatabaseException('SQL query contains dangerous operations');
            }
        }
    }

    private function validateTableName(string $table): void
    {
        if (!preg_match('/^[a-zA-Z_][a-zA-Z0-9_]*$/', $table)) {
            throw new DatabaseException('Invalid table name: ' . $table);
        }
    }

    private function validateColumnNames(array $columns): void
    {
        foreach ($columns as $column) {
            if (!preg_match('/^[a-zA-Z_][a-zA-Z0-9_]*$/', $column)) {
                throw new DatabaseException('Invalid column name: ' . $column);
            }
        }
    }

    private function bindParameters(PDOStatement $stmt, array $params): void
    {
        foreach ($params as $key => $value) {
            $paramName = is_int($key) ? $key + 1 : $key;
            
            // Determine parameter type
            $type = match (true) {
                is_int($value) => PDO::PARAM_INT,
                is_bool($value) => PDO::PARAM_BOOL,
                is_null($value) => PDO::PARAM_NULL,
                default => PDO::PARAM_STR
            };
            
            $stmt->bindValue($paramName, $value, $type);
        }
    }

    private function sanitizeQueryForLogging(string $sql): string
    {
        // Remove potential sensitive data patterns for logging
        $patterns = [
            '/\bpassword\s*=\s*[^\s,)]+/i' => 'password=***',
            '/\bemail\s*=\s*[^\s,)]+/i' => 'email=***',
            '/\btoken\s*=\s*[^\s,)]+/i' => 'token=***'
        ];
        
        foreach ($patterns as $pattern => $replacement) {
            $sql = preg_replace($pattern, $replacement, $sql);
        }
        
        return $sql;
    }
}