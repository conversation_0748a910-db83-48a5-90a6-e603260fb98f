<h2>Export Expense Data</h2>

<?php include __DIR__ . '/components/messages.php'; ?>

<div class="export-options">
    <div class="export-option-card">
        <h3>Export Expenses</h3>
        <p>Export your expenses data with various filtering options.</p>

        <form method="GET" action="/reports/export" id="exportForm">
            <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($csrf_token) ?>">

            <div class="form-group">
                <label for="format">Export Format</label>
                <select name="format" id="format" required>
                    <option value="csv">CSV (Comma Separated Values)</option>
                    <option value="json">JSON</option>
                    <option value="pdf">PDF</option>
                </select>
            </div>

            <div class="form-group">
                <label for="date_range">Date Range</label>
                <select name="date_range" id="date_range">
                    <option value="all">All Time</option>
                    <option value="current_month">Current Month</option>
                    <option value="previous_month">Previous Month</option>
                    <option value="current_year">Current Year</option>
                    <option value="previous_year">Previous Year</option>
                    <option value="custom">Custom Range</option>
                </select>
            </div>

            <div id="custom_date_container" class="form-group hidden">
                <div class="date-range-selectors">
                    <div class="form-group">
                        <label for="start_date">Start Date</label>
                        <input type="date" name="start_date" id="start_date"
                               value="<?= date('Y-m-01') ?>">
                    </div>
                    <div class="form-group">
                        <label for="end_date">End Date</label>
                        <input type="date" name="end_date" id="end_date"
                               value="<?= date('Y-m-d') ?>">
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="category_id">Category</label>
                <select name="category_id" id="category_id">
                    <option value="">All Categories</option>
                    <?php foreach ($categories as $category) : ?>
                        <option value="<?= htmlspecialchars($category['id']) ?>">
                            <?= htmlspecialchars($category['name']) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div class="form-group">
                <label>Include Data</label>
                <div class="checkbox-group">
                    <label class="checkbox-label">
                        <input type="checkbox" name="include_items" value="1" checked>
                        Include line items
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox" name="include_notes" value="1" checked>
                        Include notes
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox" name="include_merchants" value="1" checked>
                        Include merchant details
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox" name="include_payment_methods" value="1" checked>
                        Include payment methods
                    </label>
                </div>
            </div>

            <div class="form-actions">
                <button type="submit" class="button primary">Generate Export</button>
            </div>
        </form>
    </div>

    <div class="export-option-card">
        <h3>Export Summary</h3>
        <p>Generate summary reports of your expenses categorized by month, category, or merchant.</p>

        <form method="GET" action="/reports/export" id="summaryForm">
            <input type="hidden" name="csrf_token" value="<?= htmlspecialchars($csrf_token) ?>">

            <div class="form-group">
                <label for="summary_type">Summary Type</label>
                <select name="summary_type" id="summary_type" required>
                    <option value="monthly">Monthly Summary</option>
                    <option value="category">Category Summary</option>
                    <option value="merchant">Merchant Summary</option>
                    <option value="payment_method">Payment Method Summary</option>
                </select>
            </div>

            <div class="form-group">
                <label for="summary_date_range">Date Range</label>
                <select name="summary_date_range" id="summary_date_range">
                    <option value="all">All Time</option>
                    <option value="current_year" selected>Current Year</option>
                    <option value="previous_year">Previous Year</option>
                    <option value="custom">Custom Range</option>
                </select>
            </div>

            <div id="summary_custom_date_container" class="form-group hidden">
                <div class="date-range-selectors">
                    <div class="form-group">
                        <label for="summary_start_date">Start Date</label>
                        <input type="date" name="summary_start_date" id="summary_start_date"
                               value="<?= date('Y-01-01') ?>">
                    </div>
                    <div class="form-group">
                        <label for="summary_end_date">End Date</label>
                        <input type="date" name="summary_end_date" id="summary_end_date"
                               value="<?= date('Y-m-d') ?>">
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="summary_format">Export Format</label>
                <select name="summary_format" id="summary_format" required>
                    <option value="csv">CSV (Comma Separated Values)</option>
                    <option value="pdf">PDF</option>
                    <option value="json">JSON</option>
                </select>
            </div>

            <div class="form-actions">
                <button type="submit" class="button primary">Generate Summary</button>
            </div>
        </form>
    </div>
</div>

<?php if (!empty($previous_exports)) : ?>
<div class="previous-exports">
    <h3>Previous Exports</h3>
    <div class="table-responsive">
        <table aria-label="Previous Export Files">
            <thead>
                <tr>
                    <th scope="col">Filename</th>
                    <th scope="col">Type</th>
                    <th scope="col">Date Created</th>
                    <th scope="col">Format</th>
                    <th scope="col">Size</th>
                    <th scope="col">Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($previous_exports as $export) : ?>
                    <tr>
                        <td><?= htmlspecialchars($export['filename']) ?></td>
                        <td><?= htmlspecialchars($export['type']) ?></td>
                        <td><?= date('M d, Y g:i A', strtotime($export['created_at'])) ?></td>
                        <td><?= strtoupper(htmlspecialchars($export['format'])) ?></td>
                        <td><?= formatFileSize($export['size']) ?></td>
                        <td class="actions">
                            <a href="/reports/export/download/<?= htmlspecialchars($export['id']) ?>"
                               class="button small">
                                Download
                            </a>
                            <form method="POST"
                                action="/reports/export/delete/<?= htmlspecialchars($export['id']) ?>"
                                class="inline-form"
                                onsubmit="return confirm('Are you sure you want to delete this export file?');">
                                <input type="hidden"
                                       name="csrf_token"
                                       value="<?= htmlspecialchars($csrf_token) ?>">
                                <button type="submit" class="button small danger">Delete</button>
                            </form>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
</div>
<?php endif; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle date range toggle for expense export
    const dateRangeSelect = document.getElementById('date_range');
    const customDateContainer = document.getElementById('custom_date_container');

    dateRangeSelect.addEventListener('change', function() {
        if (this.value === 'custom') {
            customDateContainer.classList.remove('hidden');
        } else {
            customDateContainer.classList.add('hidden');
        }
    });

    // Handle date range toggle for summary export
    const summaryDateRangeSelect = document.getElementById('summary_date_range');
    const summaryCustomDateContainer = document.getElementById('summary_custom_date_container');

    summaryDateRangeSelect.addEventListener('change', function() {
        if (this.value === 'custom') {
            summaryCustomDateContainer.classList.remove('hidden');
        } else {
            summaryCustomDateContainer.classList.add('hidden');
        }
    });
});
</script>

<style>
.export-options {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 30px;
}

.export-option-card {
    flex: 1;
    min-width: 300px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.export-option-card h3 {
    margin-top: 0;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.date-range-selectors {
    display: flex;
    gap: 15px;
}

.date-range-selectors .form-group {
    flex: 1;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.previous-exports {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-top: 20px;
}

.previous-exports h3 {
    margin-top: 0;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.hidden {
    display: none;
}
</style>

<?php
// Helper function to format file size
function formatFileSize($bytes)
{
    if ($bytes >= 1048576) {
        return round($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return round($bytes / 1024, 2) . ' KB';
    } else {
        return "{$bytes} bytes";
    }
}
?>
