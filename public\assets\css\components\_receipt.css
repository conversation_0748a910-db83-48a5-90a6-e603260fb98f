/* ===== RECEIPT STYLING ===== */

.generated-receipt {
  margin-bottom: var(--spacing-lg);
}

.receipt-header {
  margin-bottom: var(--spacing);
}

.receipt-header h5 {
  font-size: var(--font-size-md);
  margin-bottom: var(--spacing-xs);
  color: var(--text-color);
}

.receipt-note {
  color: var(--text-muted);
  font-size: var(--font-size-sm);
  margin-bottom: var(--spacing);
}

.styled-receipt {
  display: flex;
  justify-content: center;
  margin-bottom: var(--spacing);
}

.receipt-paper {
  background-color: white;
  padding: var(--spacing-lg);
  border-radius: var(--border-radius);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
  font-family: 'Courier New', monospace;
  position: relative;
  overflow: hidden;
}

.receipt-paper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 8px;
  background: repeating-linear-gradient(
    90deg,
    var(--grey-300),
    var(--grey-300) 10px,
    transparent 10px,
    transparent 20px
  );
}

.receipt-paper::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 8px;
  background: repeating-linear-gradient(
    90deg,
    var(--grey-300),
    var(--grey-300) 10px,
    transparent 10px,
    transparent 20px
  );
}

.receipt-merchant {
  text-align: center;
  font-size: 1.2em;
  font-weight: bold;
  margin-bottom: var(--spacing);
  padding-bottom: var(--spacing);
  text-transform: uppercase;
}

.receipt-divider {
  height: 1px;
  background-color: var(--grey-300);
  margin: var(--spacing) 0;
}

.receipt-divider.dashed {
  background: repeating-linear-gradient(
    90deg,
    var(--grey-300),
    var(--grey-300) 5px,
    transparent 5px,
    transparent 10px
  );
  height: 1px;
}

.receipt-details {
  margin-bottom: var(--spacing);
}

.receipt-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-xs);
}

.receipt-label {
  font-weight: normal;
  color: var(--text-muted);
}

.receipt-value {
  text-align: right;
}

.receipt-items {
  margin-bottom: var(--spacing);
}

.items-header {
  display: flex;
  justify-content: space-between;
  font-weight: bold;
  margin-bottom: var(--spacing-sm);
  padding-bottom: var(--spacing-xs);
  border-bottom: 1px solid var(--grey-300);
}

.receipt-item-header {
  display: flex;
  justify-content: space-between;
  font-weight: bold;
  margin-bottom: var(--spacing-xs);
  padding-bottom: var(--spacing-xs);
  border-bottom: 1px solid var(--grey-300);
}

.receipt-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-xs);
}

.item-name {
  flex: 1;
  padding-right: var(--spacing);
}

.item-price {
  text-align: right;
  white-space: nowrap;
  width: 70px; /* Fixed width to ensure alignment */
  min-width: 70px; /* Ensure minimum width for price column */
  flex-shrink: 0; /* Prevent shrinking */
}

.receipt-total {
  display: flex;
  justify-content: space-between;
  font-weight: bold;
  font-size: 1.1em;
  margin: var(--spacing) 0;
}

.receipt-notes {
  font-size: 0.9em;
  color: var(--text-muted);
  margin-top: var(--spacing);
  white-space: pre-line;
}

.receipt-footer {
  margin-top: var(--spacing-lg);
  text-align: center;
}

.receipt-barcode {
  height: 40px;
  margin-bottom: var(--spacing);
  background: repeating-linear-gradient(
    90deg,
    black,
    black 2px,
    white 2px,
    white 4px,
    black 4px,
    black 6px,
    white 6px,
    white 8px
  );
}

.receipt-thank-you {
  font-size: 0.9em;
  text-align: center;
  margin-top: var(--spacing);
}

.receipt-actions-container {
  margin-top: var(--spacing-md);
  text-align: center;
}

.receipt-actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing);
}

.upload-section {
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing);
  border-top: 1px solid var(--border-color);
}

.upload-section h5 {
  font-size: var(--font-size-md);
  margin-bottom: var(--spacing-xs);
  color: var(--text-color);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .receipt-paper {
    padding: var(--spacing);
  }

  .receipt-merchant {
    font-size: 1em;
  }

  .receipt-total {
    font-size: 1em;
  }
}

/* Plain style for date display */
.expense-date.plain {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
  color: var(--text-muted);
}