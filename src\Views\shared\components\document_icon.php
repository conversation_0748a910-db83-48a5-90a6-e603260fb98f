<?php

/**
 * Document icon component
 * Displays the appropriate icon based on file extension
 *
 * @param string $filename The filename to determine the icon for
 * @param string $size Icon size (sm, md, lg, xl) - default: md
 */

// Default parameters
$filename = $filename ?? '';
$size = $size ?? 'md';

// Determine file extension
$extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));

// Map extension to icon
$iconId = match ($extension) {
    'pdf' => 'icon-file-pdf',
    'txt' => 'icon-file-text',
    'csv' => 'icon-file-csv',
    'log' => 'icon-file-text',
    default => 'icon-file-generic'
};

// Determine size class
$sizeClass = match ($size) {
    'sm' => 'icon-sm',
    'lg' => 'icon-lg',
    'xl' => 'icon-xl',
    default => ''
};
?>

<svg class="icon <?= $sizeClass ?>"><use xlink:href="/assets/icons.svg#<?= $iconId ?>"></use></svg>
