<?php

declare(strict_types=1);

namespace App\Core\Error;

use App\Core\LogManager;
use ErrorException;
use Throwable;

final class Handler
{
    private const ERROR_VIEW_PATH = '/../../Views/errors/500.php';
    private const DEBUG_ERROR_STYLES = <<<CSS
        body{font-family:sans-serif;line-height:1.6;color:#333;background:#f5f5f5;margin:0;padding:20px}
        .error-details{max-width:1000px;margin:0 auto;background:#fff;border-radius:8px;
            box-shadow:0 2px 10px rgba(0,0,0,0.1);padding:25px;border-left:5px solid #e74c3c}
        h1{color:#e74c3c;margin-top:0;border-bottom:1px solid #eee;padding-bottom:10px;font-size:24px}
        .error-section{margin-bottom:20px}
        h2{font-size:18px;color:#555;margin-bottom:8px}
        .exception-type{font-family:monospace;font-weight:bold;background:#f8d7da;padding:8px 12px;
            border-radius:4px;border-left:3px solid #e74c3c}
        .error-message{background:#f8f9fa;padding:12px;border-radius:4px;border-left:3px solid #6c757d}
        .stack-trace{background:#2d2d2d;color:#f8f8f2;border-radius:4px;overflow:auto;max-height:400px}
        pre{margin:0;padding:15px;white-space:pre-wrap;font-family:monospace;font-size:14px}
        .file-info{font-family:monospace;background:#f8f9fa;padding:8px 12px;border-radius:4px;margin-top:10px}
    CSS;

    public static function handleError(int $errno, string $errstr, string $errfile, int $errline): bool
    {
        throw new ErrorException($errstr, $errno, $errno, $errfile, $errline);
    }

    public static function handleException(Throwable $exception): never
    {
        $isDebug = filter_var($_ENV['APP_DEBUG'] ?? false, FILTER_VALIDATE_BOOLEAN);
        $requestInfo = self::collectRequestInfo();

        LogManager::logException($exception, 'Uncaught exception', array_merge(
            ['debug_mode' => $isDebug],
            $requestInfo
        ));

        if ($isDebug) {
            self::renderDebugError($exception);
        } else {
            http_response_code(500);
            require_once __DIR__ . self::ERROR_VIEW_PATH;
        }

        exit;
    }

    private static function collectRequestInfo(): array
    {
        return [
            'uri' => $_SERVER['REQUEST_URI'] ?? 'unknown',
            'method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown',
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
        ];
    }

    private static function renderDebugError(Throwable $exception): void
    {
        $exceptionData = [
            'class' => get_class($exception),
            'message' => htmlspecialchars($exception->getMessage(), ENT_QUOTES),
            'trace' => htmlspecialchars($exception->getTraceAsString(), ENT_QUOTES),
            'file' => htmlspecialchars($exception->getFile(), ENT_QUOTES),
            'line' => $exception->getLine()
        ];

        echo self::generateDebugErrorHtml($exceptionData);
    }

    private static function generateDebugErrorHtml(array $exceptionData): string
    {
        return '<!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Error Details</title>
            <style>' .
                self::DEBUG_ERROR_STYLES .
            '</style>
        </head>
        <body>
            <div class="error-details">
                <h1>Error Details</h1>
                <div class="error-section">
                    <h2>Exception Type</h2>
                    <p class="exception-type">' . $exceptionData['class'] . '</p>
                </div>
                <div class="error-section">
                    <h2>Message</h2>
                    <p class="error-message">' . $exceptionData['message'] . '</p>
                    <p class="file-info">in ' . $exceptionData['file'] . ' on line ' . $exceptionData['line'] . '</p>
                </div>
                <div class="error-section">
                    <h2>Stack Trace</h2>
                    <div class="stack-trace">
                        <pre>' . $exceptionData['trace'] . '</pre>
                    </div>
                </div>
            </div>
        </body>
        </html>';
    }
}
