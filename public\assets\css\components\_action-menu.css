/* Action Menu Styles */
.action-menu-wrapper {
  position: relative;
  display: inline-block;
}

.action-button.table-action {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: var(--table-action-button-size);
  height: var(--table-action-button-size);
  border-radius: 50%;
  transition: all 0.2s ease;
  padding: 0;
  margin: 0 auto;
  box-shadow: var(--box-shadow-sm);
}

.action-button.table-action:hover {
  background-color: var(--grey-300);
  transform: translateY(0);
  color: var(--primary-color);
  box-shadow: var(--box-shadow);
}

.action-button.table-action:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.action-button.table-action .svg-icon {
  width: var(--table-action-icon-size);
  height: var(--table-action-icon-size);
  margin: 0;
  color: var(--text-color);
  fill: currentColor;
  display: block;
}

.action-button.table-action:hover .svg-icon {
  color: var(--primary-color);
}

/* Make the action button more visible */
.action-button.table-action.secondary {
  color: var(--text-color);
}

/* Ensure the icon is properly centered */
.icon-only-svg {
  display: block;
  margin: 0 auto;
  width: var(--table-action-icon-size);
  height: var(--table-action-icon-size);
  fill: currentColor;
  stroke: currentColor;
  stroke-width: 0;
}

/* Action Menu Dropdown Styles */
.action-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--white);
  border: 1px solid var(--border-color);
  border-radius: var(--radius);
  box-shadow: var(--box-shadow);
  min-width: 120px;
  margin-top: var(--spacing-xs);
  transition: opacity 150ms ease-in-out;
  opacity: 0;
  pointer-events: none;
  z-index: 1000;
}

.action-menu.show {
  opacity: 1;
  pointer-events: auto;
}

.action-menu.flip-up {
  top: auto;
  bottom: 100%;
  margin-top: 0;
  margin-bottom: var(--spacing-xs);
}

.action-menu.flip-left {
  right: auto;
  left: 0;
}

.action-menu button {
  width: 100%;
  padding: 8px 12px;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
}

.action-menu button:hover,
.action-menu button:focus {
  background: var(--grey-100);
}

