# Enable URL rewriting
RewriteEngine On

# Serve existing files directly
RewriteCond %{REQUEST_FILENAME} -f
RewriteRule ^ - [L]

# Serve existing directories directly
RewriteCond %{REQUEST_FILENAME} -d
RewriteRule ^ - [L]

# Handle favicon.ico directly
RewriteRule ^favicon\.ico$ - [L]

# Redirect all other requests to index.php (front controller)
RewriteRule ^(.*)$ index.php [QSA,L]

# Prevent directory listing
Options -Indexes

# Set default charset
AddDefaultCharset UTF-8

# Set cache control for static assets
<IfModule mod_headers.c>
    <FilesMatch "\.(css|js|jpg|jpeg|png|gif|ico|svg)$">
        Header set Cache-Control "max-age=31536000, public"
    </FilesMatch>
</IfModule>

# Compress text files
<IfModule mod_deflate.c>
    <IfModule mod_filter.c>
        AddOutputFilterByType DEFLATE text/html text/plain text/xml text/css application/javascript application/json
    </IfModule>
</IfModule>
