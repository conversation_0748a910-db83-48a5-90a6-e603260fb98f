/* Modern Table Styles */
.modern-table {
  background-color: var(--card-bg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow-sm);
  margin-bottom: var(--spacing-xl);
  overflow: hidden;
}

.modern-table table {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
  font-size: var(--font-size);
  font-size: var(--table-font-size);
}

.modern-table .select-all-column,
.modern-table .select-row-column {
  width: 5%;
  text-align: center;
  vertical-align: middle;
}

.modern-table .select-all-column input[type="checkbox"],
.modern-table .select-row-column input[type="checkbox"] {
  cursor: pointer;
  width: 18px;
  height: 18px;
  margin: 0;
  padding: 0;
  vertical-align: middle;
}

.modern-table:not(.select-mode) .select-all-column input,
.modern-table:not(.select-mode) .select-row-column input,
.modern-table:not(.select-mode) .select-all,
.modern-table:not(.select-mode) .row-selector {
  display: none !important;
}

.modern-table.select-mode .select-all-column input,
.modern-table.select-mode .select-row-column input,
.modern-table.select-mode .select-all,
.modern-table.select-mode .row-selector {
  display: inline-block !important;
}

.modern-table .date-column {
  width: 18%;
}

.modern-table .description-column {
  width: 28%;
}

.modern-table .category-column {
  width: 20%;
}

.modern-table .category-column .badge {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.modern-table .amount-column {
  width: 18%;
}

.modern-table .actions-column {
  width: 11%;
}

.modern-table th {
  background-color: var(--grey-100);
  font-weight: var(--font-weight-semibold);
  color: var(--text-color);
  padding: var(--table-cell-padding);
  text-align: left;
  position: sticky;
  top: 0;
  z-index: 10;
  border-bottom: 1px solid var(--border-color);
  white-space: normal;
  overflow-wrap: break-word;
  word-break: normal;
  hyphens: none;
}

.modern-table .th-content {
  display: inline-flex;
  align-items: center;
  gap: 0.25em;
  position: relative;
}

.modern-table .sort-button {
  background: none;
  border: none;
  padding: 0.25em 0.5em;
  border-radius: 4px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  gap: 0.25em;
  font-weight: inherit;
  color: inherit;
  transition: all 0.2s ease;
  margin: -0.25em 0;
}

.modern-table .sort-button:hover .sort-icon::after {
  color: var(--primary-color);
}

.modern-table .sort-button.active {
  color: var(--primary-color);
  font-weight: var(--font-weight-bold);
}

.modern-table .sort-icon {
  display: inline-flex;
  align-items: center;
  margin-left: 0.4em;
  vertical-align: middle;
  transition: all 0.2s ease;
}

/* Style the SVG icon */
.modern-table .sort-icon svg {
  width: 1.2em;
  height: 1.2em;
  fill: currentColor;
  opacity: 0.5;
  transition: all 0.2s ease;
  transform-origin: center;
  vertical-align: middle;
  display: inline-block; /* Ensure the icon is always visible */
}

/* Style for ascending sort */
.modern-table th[aria-sort="ascending"] .sort-icon svg {
  opacity: 1;
  color: var(--primary-color);
  transform: rotate(0deg);
  filter: drop-shadow(0 0 1px rgba(var(--primary-rgb), 0.3));
}

/* Style for descending sort */
.modern-table th[aria-sort="descending"] .sort-icon svg {
  opacity: 1;
  color: var(--primary-color);
  transform: rotate(180deg);
  filter: drop-shadow(0 0 1px rgba(var(--primary-rgb), 0.3));
}

/* Add hover effect for sort buttons */
.modern-table .sort-button:hover .sort-icon svg {
  opacity: 0.8;
  color: var(--primary-color);
}

.modern-table tbody tr {
  transition: var(--transition-fast);
  border-bottom: 1px solid var(--border-color);
}

.modern-table tbody tr:last-child {
  border-bottom: none;
}

.modern-table tbody tr:hover {
  background-color: var(--grey-50);
}

/* Selected row styling */
.modern-table tbody tr.selected {
  background-color: var(--primary-color-light, rgba(0, 123, 255, 0.1));
  border-left: 3px solid var(--primary-color);
}

.modern-table tbody tr.selected:hover {
  background-color: var(--primary-color-light-hover, rgba(0, 123, 255, 0.15));
}

.modern-table td {
  padding: var(--table-cell-padding);
  vertical-align: middle;
  white-space: normal;
  overflow-wrap: break-word;
  word-break: normal;
  hyphens: none;
}

/* Column specific styles */
.modern-table .category-column {
  text-align: center;
}

.modern-table .amount-column {
  text-align: right;
}

.modern-table .actions-column {
  text-align: center;
  vertical-align: middle;
}

/* Expense description wrapper */
.expense-description-wrapper {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing);
}

.expense-description {
  color: var(--text-color);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: var(--transition-fast);
  flex: 1;
}

.expense-description:hover {
  color: var(--primary-color);
}




/* Amount styling */
.amount {
  font-weight: var(--font-weight-semibold);
}

.amount.positive {
  color: var(--success-color);
}

.amount.negative {
  color: var(--danger-color);
}

/* Action menu */
.action-menu {
  position: relative;
}

.action-menu-toggle {
  background: none;
  border: none;
  padding: var(--spacing-xs);
  border-radius: var(--border-radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-muted);
  cursor: pointer;
  transition: var(--transition-fast);
}

.action-menu-toggle:hover {
  background-color: var(--grey-200);
  color: var(--text-color);
}

.action-menu-wrapper {
  position: relative;
}

.action-menu {
  background-color: var(--card-bg);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow-lg);
  min-width: 150px;
  max-width: 250px;
  z-index: 1000;
  border: 1px solid var(--border-color);
  display: none;
  overflow-x: hidden;
  overflow-y: auto;
  max-height: 80vh;
}

#menu-container .action-menu {
  display: block;
  position: absolute;
  z-index: 9999;
}

.action-menu.show {
  display: block;
}

/* Flip up (menu appears above the toggle) */
.action-menu.flip-up {
  top: auto;
  bottom: 100%;
  transform-origin: bottom center;
}

/* Flip left (menu appears to the left of the toggle) */
.action-menu.flip-left {
  right: 0;
  left: auto;
  transform-origin: top right;
}

.action-menu-items {
  padding: 2px 0;
}

.action-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  color: var(--text-color);
  text-decoration: none;
  transition: var(--transition-fast);
  cursor: pointer;
  border: none;
  background: none;
  width: 100%;
  text-align: left;
  font-size: 14px;
  white-space: nowrap;
  line-height: 1.4;
}

.action-item .svg-icon {
  width: 14px;
  height: 14px;
}

.action-item:hover {
  background-color: var(--grey-100);
}

.action-item.danger {
  color: var(--danger-color);
}

.action-item.danger:hover {
  background-color: hsl(6, 75%, 97%);
}

.action-form {
  margin: 0;
}

.action-divider {
  height: 1px;
  background-color: var(--border-color);
  margin: 2px 0;
}

/* Multi-select toolbar styles */
.multi-select-toolbar {
  display: flex;
  align-items: center;
  gap: var(--spacing);
  margin-bottom: var(--spacing);
  padding: var(--spacing);
  background-color: var(--grey-100);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.multi-select-toolbar .selection-counter {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-color);
}

.multi-select-toolbar .selection-counter .count {
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
}

.multi-select-toolbar .toolbar-spacer {
  flex-grow: 1;
}

.multi-select-toolbar .btn {
  padding: var(--spacing-xs) var(--spacing);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: var(--transition-fast);
}

.multi-select-toolbar .bulk-delete {
  background-color: var(--danger-color);
  color: white;
  border: none;
}

.multi-select-toolbar .bulk-delete:hover {
  background-color: var(--danger-dark);
  box-shadow: var(--box-shadow-lg);
  transform: translateY(-2px);
}

.multi-select-toolbar .cancel-select {
  background-color: var(--grey-300);
  color: var(--text-color);
  border: none;
}

.multi-select-toolbar .cancel-select:hover {
  background-color: var(--grey-400);
}

/* Responsive styles */
@media (max-width: 992px) {
  /* Percentage widths will scale naturally */
}

@media (max-width: 768px) {
  /* Percentage widths will scale naturally */
}

@media (max-width: 576px) {
  /* Remove any overflow and set all table elements to block */
  .modern-table,
  .modern-table table,
  .modern-table thead,
  .modern-table tbody,
  .modern-table th,
  .modern-table td,
  .modern-table tr,
  .modern-table caption {
    display: block;
    overflow-x: visible;
  }

  /* Caption-specific styles to prevent overflow */
  .modern-table caption {
    width: 100%;
    white-space: normal;
    word-wrap: break-word;
    margin-top: var(--spacing-xs);
  }

  /* Hide the header row completely */
  .modern-table thead {
    position: absolute;
    top: -9999px;
    left: -9999px;
  }

  /* Style each row as a card */
  .modern-table tr {
    background-color: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing);
    padding: var(--spacing);
    position: relative;
  }

  .modern-table tr:hover {
    background-color: var(--grey-50);
  }

  /* Style table cells with labels */
  .modern-table td {
    padding: var(--spacing-sm) 0;
    border: none;
    position: relative;
    padding-left: var(--table-mobile-label-width);
    min-height: 24px;
  }

  .modern-table td::before {
    content: attr(data-label);
    font-weight: var(--font-weight-semibold);
    position: absolute;
    left: 0;
    top: var(--spacing-sm);
    width: calc(var(--table-mobile-label-width) - var(--table-mobile-padding));
    color: var(--text-muted);
  }

  /* Specific column label overrides */
  .modern-table .select-row-column::before {
    content: "";
    display: none;
  }

  .modern-table .date-column::before {
    content: "Date:";
  }

  .modern-table .description-column::before {
    content: "Description:";
  }

  .modern-table .category-column::before {
    content: "Category:";
  }

  .modern-table .amount-column::before {
    content: "Amount:";
  }

  .modern-table .amount-column {
    text-align: left;
  }

  /* Actions column positioning */
  .modern-table .actions-column {
    padding-left: 0;
    text-align: right;
    margin-top: var(--spacing-sm);
    border-top: 1px solid var(--border-color);
    padding-top: var(--spacing-sm);
  }

  .modern-table .actions-column::before {
    content: "";
    display: none;
  }

  /* For mobile screens, ensure the action menu is always visible */
  .action-menu-dropdown {
    width: var(--dropdown-fluid-width);
  }

  /* Make action items larger and easier to tap on mobile */
  .action-item {
    padding: var(--table-mobile-padding) calc(var(--table-mobile-padding) * 1.5);
  }
}
