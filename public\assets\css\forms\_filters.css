/* ===== FILTER COMPONENTS ===== */
/* Main filter bar container */
.filter-bar {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: #FAFBFC;
  border: none;
  border-bottom: 1px solid #E1E8ED;
  box-shadow: 0 1px 3px rgba(0,0,0,0.04);
  margin-bottom: var(--spacing-md);
  padding: 16px 24px;
  height: 72px;
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
  transition: all 0.15s ease-out;
}

/* Filter controls container */
.filter-bar__controls {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing);
  align-items: center;
  width: 100%;
}

/* Individual filter item */
.filter-item {
  display: flex;
  flex-direction: column;
  flex: 0 0 auto;
}

/* Filter item specific widths */
.filter-item--date {
  width: 180px;
}

.filter-item--category {
  width: 160px;
}

.filter-item--amount {
  width: 200px;
}

.filter-item--search {
  width: 240px;
}

/* Filter item label */
.filter-item__label {
  font-size: 11px;
  font-weight: 500;
  color: #6B7280;
  margin-bottom: 2px;
  letter-spacing: 0.025em;
  text-transform: none;
}

/* Filter item content */
.filter-item__content {
  position: relative;
}

/* Filter dropdown toggle button */
.filter-dropdown__toggle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 40px;
  padding: 0 12px;
  background-color: #FFFFFF;
  border: 1.5px solid #CBD6E2;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.15s ease-out;
}

.filter-dropdown__toggle:hover {
  border-color: #4A90E2;
}

.filter-dropdown__toggle[aria-expanded="true"] {
  border-color: #4A90E2;
  box-shadow: 0 0 0 3px rgba(74,144,226,0.1);
}

/* Filter dropdown text */
.filter-dropdown__text {
  font-size: 14px;
  font-weight: 400;
  color: #1F2937;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.filter-dropdown__text--default {
  color: #6B7280;
  font-style: normal;
}

/* Filter dropdown arrow */
.filter-dropdown__arrow {
  font-size: 12px;
  color: #6B7280;
  margin-left: 8px;
  transition: transform 0.15s ease-out;
  margin-right: 0;
}

.filter-dropdown__toggle[aria-expanded="true"] .filter-dropdown__arrow {
  transform: rotate(180deg);
}

/* Filter dropdown panel */
.filter-dropdown__panel {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background-color: white;
  border: 1px solid #CBD6E2;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
  z-index: 20;
  padding: 8px 0;
  margin-top: 4px;
  display: none;
}

/* Make category dropdown wider */
.filter-item--category .filter-dropdown__panel {
  width: 220px;
}

/* Make amount dropdown wider */
.filter-item--amount .filter-dropdown__panel {
  width: 240px;
}

.filter-dropdown__panel.show {
  display: block;
  animation: fadeIn 0.2s ease-out;
}

/* Date presets */
.date-presets {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.date-presets__group {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.date-presets__heading {
  font-size: 12px;
  font-weight: 500;
  color: #6B7280;
  margin: 8px 12px 4px;
}

.date-preset {
  background: none;
  border: none;
  text-align: left;
  padding: 0 12px;
  height: 40px;
  line-height: 40px;
  cursor: pointer;
  font-size: 14px;
  color: #1F2937;
  transition: all 0.15s ease-out;
  width: 100%;
}

.date-preset:hover {
  background-color: #F8FAFC;
}

.date-preset.selected {
  background-color: #4A90E2;
  color: white;
}

.date-preset--primary {
  font-weight: 500;
}

/* Custom date range */
.date-custom {
  border-top: 1px solid var(--border-color);
  padding-top: var(--spacing);
}

.date-custom__label {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--text-muted);
  margin-bottom: 0.5rem;
  display: block;
}

.date-custom__inputs {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.date-separator {
  font-size: 0.75rem;
  color: var(--text-muted);
}

/* Category options */
.category-options {
  max-height: 300px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.category-option {
  display: flex;
  align-items: center;
  height: 32px;
  padding: 0 12px;
  cursor: pointer;
  transition: all 0.15s ease-out;
}

.category-option:hover {
  background-color: #F8FAFC;
}

.category-option__checkbox {
  position: absolute;
  opacity: 0;
}

.category-option__indicator {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  border-radius: 3px;
  border: 1px solid #CBD6E2;
  margin-right: 8px;
  overflow: hidden;
}

.category-option__color {
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  left: -4px;
  top: 50%;
  transform: translateY(-50%);
}

/* Category specific colors */
.category-option[data-category="HOUSING"] .category-option__color {
  background-color: #F59E0B;
}

.category-option[data-category="FOOD"] .category-option__color {
  background-color: #EAB308;
}

.category-option[data-category="ENTERTAINMENT"] .category-option__color {
  background-color: #3B82F6;
}

.category-option[data-category="GIVING"] .category-option__color {
  background-color: #F97316;
}

.category-option__checkmark {
  position: relative;
  color: white;
  font-size: 10px;
  opacity: 0;
  transform: scale(0);
  transition: all 0.15s ease-out;
}

.category-option__checkbox:checked + .category-option__indicator {
  background-color: #4A90E2;
  border-color: #4A90E2;
}

.category-option__checkbox:checked + .category-option__indicator .category-option__checkmark {
  opacity: 1;
  transform: scale(1);
}

.category-option__name {
  font-size: 14px;
  color: #1F2937;
  flex: 1;
}

.category-option__count {
  font-size: 12px;
  color: #6B7280;
}

/* Amount slider */
.amount-slider-container {
  padding: 12px;
  height: 80px;
}

.amount-slider {
  margin: 24px 0;
  position: relative;
  height: 4px;
  background-color: #E5E7EB;
  border-radius: 2px;
}

.amount-slider__range {
  position: absolute;
  height: 100%;
  background-color: #4A90E2;
  border-radius: 2px;
}

.amount-slider__handle {
  position: absolute;
  top: 50%;
  width: 20px;
  height: 20px;
  background-color: white;
  border: 2px solid #4A90E2;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  cursor: pointer;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.15s ease-out;
}

.amount-slider__handle:hover,
.amount-slider__handle:focus {
  box-shadow: 0 0 0 5px rgba(74, 144, 226, 0.25);
}

.amount-inputs {
  display: flex;
  align-items: center;
  gap: 8px;
}

.amount-inputs input {
  width: 60px;
  height: 32px;
  padding: 0 8px;
  border: 1px solid #CBD6E2;
  border-radius: 4px;
  font-size: 14px;
  color: #1F2937;
}

.amount-separator {
  font-size: 12px;
  color: #6B7280;
}

/* Amount presets */
.amount-presets {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.amount-preset {
  height: 32px;
  padding: 0 12px;
  background: none;
  border: 1px solid #CBD6E2;
  border-radius: 16px;
  font-size: 13px;
  color: #1F2937;
  cursor: pointer;
  transition: all 0.15s ease-out;
}

.amount-preset:hover {
  border-color: #4A90E2;
  color: #4A90E2;
}

.amount-preset.selected {
  background-color: #4A90E2;
  border-color: #4A90E2;
  color: white;
}

/* Search input */
.search-input-container {
  position: relative;
  width: 100%;
  height: 40px;
}

.search-input__icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #6B7280;
  font-size: 16px;
  pointer-events: none;
}

.filter-input--search {
  height: 40px;
  padding: 0 12px 0 44px;
  width: 100%;
  border: 1.5px solid #CBD6E2;
  border-radius: 8px;
  font-size: 14px;
  color: #1F2937;
  transition: all 0.15s ease-out;
}

.filter-input--search::placeholder {
  color: #9CA3AF;
}

.filter-input--search:focus {
  border-color: #4A90E2;
  box-shadow: 0 0 0 3px rgba(74,144,226,0.1);
  outline: none;
}

.filter-input--search:focus + .search-input__icon {
  color: #4A90E2;
}

.search-input__clear {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #6B7280;
  font-size: 16px;
  cursor: pointer;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.15s ease-out;
}

.search-input__clear:hover {
  color: #EF4444;
}

/* Filter inputs */
.filter-input {
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.filter-input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.25);
  outline: none;
}

.filter-input--date,
.filter-input--amount {
  width: 100%;
  min-width: 0;
}

/* Active filters */
.active-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-right: 16px;
}

.filter-tag {
  display: inline-flex;
  align-items: center;
  height: 28px;
  padding: 6px 8px 6px 12px;
  background-color: #EEF2FF;
  border: 1px solid #C7D2FE;
  border-radius: 14px;
  font-size: 12px;
  color: #3730A3;
  font-weight: 500;
  max-width: 200px;
}

.filter-label {
  font-weight: 500;
  margin-right: 4px;
}

.filter-value {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

.filter-remove {
  background: none;
  border: none;
  margin-left: 4px;
  cursor: pointer;
  font-size: 14px;
  color: #6366F1;
  line-height: 1;
  padding: 0;
  width: 14px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.15s ease-out;
}

.filter-remove:hover {
  color: #EF4444;
}

/* Filter clear all button */
.filter-clear-all {
  background: none;
  border: none;
  font-size: 13px;
  color: #6B7280;
  cursor: pointer;
  padding: 0;
  transition: all 0.15s ease-out;
  margin-left: 16px;
}

.filter-clear-all:hover {
  color: #4A90E2;
  text-decoration: underline;
}

/* Results counter and filter summary */
.filter-summary {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 16px;
}

.results-counter {
  font-size: 13px;
  color: #6B7280;
  margin-left: auto;
  transition: opacity 0.2s ease-out;
}

.filter-breadcrumb {
  font-size: 13px;
  color: #1F2937;
  background-color: #F3F4F6;
  padding: 4px 12px;
  border-radius: 16px;
  display: none;
  margin-right: 16px;
}

/* Filter notification */
.filter-notification {
  position: fixed;
  bottom: 1rem;
  right: 1rem;
  background-color: white;
  border-left: 4px solid var(--warning-color);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  padding: 0.75rem 1rem;
  border-radius: var(--border-radius);
  font-size: 0.875rem;
  max-width: 300px;
  z-index: 100;
  opacity: 0;
  transform: translateY(1rem);
  transition: all 0.3s ease;
}

.filter-notification--visible {
  opacity: 1;
  transform: translateY(0);
}

/* Empty state */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
  background-color: var(--light-color);
  border-radius: var(--border-radius);
  margin-bottom: var(--spacing-lg);
}

.empty-state__icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: var(--text-muted);
}

.empty-state__title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.empty-state__message {
  color: var(--text-muted);
  max-width: 400px;
  margin-bottom: 1.5rem;
}

.empty-state__actions {
  display: flex;
  gap: 1rem;
}

.empty-state__action {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  background-color: var(--primary-color);
  color: white;
  border-radius: var(--border-radius);
  font-weight: 600;
  text-decoration: none;
  transition: all 0.2s ease;
}

.empty-state__action:hover {
  background-color: var(--primary-dark-color);
}

.empty-state__action--secondary {
  background-color: white;
  color: var(--text-color);
  border: 1px solid var(--border-color);
}

.empty-state__action--secondary:hover {
  background-color: var(--light-color);
  color: var(--text-color);
}

.empty-state__action--clear {
  background-color: var(--light-color);
  color: var(--danger-color);
  border: 1px solid var(--danger-color);
}

.empty-state__action--clear:hover {
  background-color: var(--danger-color);
  color: white;
}

/* Loading states */
.filter-loading {
  position: relative;
}

.filter-loading::after {
  content: '';
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  border: 2px solid rgba(74, 144, 226, 0.2);
  border-top-color: #4A90E2;
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

.filter-loading .filter-dropdown__arrow,
.filter-loading .search-input__clear {
  display: none;
}

@keyframes spin {
  to { transform: translateY(-50%) rotate(360deg); }
}

/* Empty results state */
.filter-empty {
  border-color: #F87171 !important;
  transition: border-color 0.5s ease-out;
}

/* Mobile styles */
@media (max-width: 1024px) {
  .filter-item--search {
    width: 180px;
  }
  
  .filter-item--amount {
    width: 160px;
  }
  
  .filter-tag {
    max-width: 160px;
  }
  
  .filter-bar {
    height: auto;
    min-height: 72px;
    max-height: 96px;
    overflow: hidden;
  }
  
  .filter-bar.has-tags {
    max-height: 96px;
  }
}

@media (max-width: 768px) {
  .filter-bar {
    flex-direction: column;
    align-items: stretch;
    height: auto;
    max-height: none;
    padding: 12px;
  }
  
  .filter-bar__mobile-toggle {
    display: block;
  }
  
  .filter-bar__controls {
    display: none;
    flex-direction: column;
    gap: 12px;
  }
  
  .filter-bar.mobile-expanded .filter-bar__controls {
    display: flex;
  }
  
  .mobile-filter-toggle {
    display: flex;
    align-items: center;
    gap: 8px;
    background: none;
    border: none;
    padding: 0;
    font-weight: 500;
    font-size: 14px;
    color: #1F2937;
    cursor: pointer;
  }
  
  .mobile-filter-toggle__icon {
    font-size: 16px;
    color: #6B7280;
  }
  
  .filter-item {
    width: 100% !important;
  }
  
  .filter-dropdown__panel {
    position: static;
    width: 100% !important;
    box-shadow: none;
    border: none;
    padding: 8px 0;
    margin-top: 4px;
  }
  
  .date-custom__inputs,
  .amount-inputs {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .date-separator,
  .amount-separator {
    display: none;
  }
  
  .filter-summary {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .results-counter {
    margin-left: 0;
  }
}

/* Legacy filter styles for backward compatibility */
.filters {
  margin-bottom: var(--spacing-md);
  padding: var(--spacing);
  background-color: var(--light-color);
  border-radius: var(--border-radius);
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing);
  align-items: center;
}

.filters select,
.filters input[type="text"],
.filters input[type="search"] {
  padding: 0.5rem;
  border: 1px solid #ced4da;
  border-radius: var(--border-radius);
  max-width: 300px;
  background-color: white;
  flex: 1;
  min-width: 150px;
}

.filters input[type="search"] {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%236c757d' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: calc(100% - 8px) center;
  padding-right: 32px;
}