/* ===== STATUS INDICATORS ===== */
.status {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 50px;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
  }

  .status::before {
    content: "";
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
  }

  .status.active {
    background-color: rgba(39, 174, 96, 0.15);
    color: hsl(145, 63%, 30%);
  }

  .status.active::before {
    background-color: var(--success-color);
  }

  .status.pending {
    background-color: rgba(243, 156, 18, 0.15);
    color: hsl(37, 90%, 40%);
  }

  .status.pending::before {
    background-color: var(--warning-color);
  }

  .status.inactive {
    background-color: rgba(189, 195, 199, 0.15);
    color: hsl(204, 8%, 40%);
  }

  .status.inactive::before {
    background-color: #95a5a6;
  }
  