// c:/wamp64/www/personal-expense-tracker/public/assets/js/components/action_menu.js

// Using a self-executing function to avoid polluting the global namespace
(function () {
    // Initialize when the DOM is ready
    document.addEventListener('DOMContentLoaded', function () {
        // Find all action buttons
        const actionButtons = document.querySelectorAll('.action-button.table-action');

        // Add click event listeners to each button
        actionButtons.forEach(button => {
            const menu = button.nextElementSibling;
            if (menu && menu.classList.contains('action-menu')) {
                // Set up ARIA attributes
                button.setAttribute('aria-haspopup', 'true');
                button.setAttribute('aria-expanded', 'false');

                // Add click event to toggle menu
                button.addEventListener('click', function (e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Close any open menus first
                    closeAllMenus();

                    // Toggle this menu
                    menu.classList.add('show');
                    button.setAttribute('aria-expanded', 'true');

                    // Position the menu
                    positionMenu(button, menu);
                });

                // Set up menu items
                const menuItems = menu.querySelectorAll('[role="menuitem"]');
                menuItems.forEach((item, index) => {
                    // Add click handler for menu items
                    item.addEventListener('click', function (e) {
                        e.preventDefault();
                        e.stopPropagation();

                        // Handle the action
                        const action = item.dataset.action;
                        const url = item.dataset.url;

                        handleMenuAction(action, url, button);
                        closeAllMenus();
                    });

                    // Add keyboard navigation
                    item.addEventListener('keydown', function (e) {
                        const items = Array.from(menuItems);

                        switch (e.key) {
                            case 'ArrowDown':
                                e.preventDefault();
                                items[(index + 1) % items.length].focus();
                                break;
                            case 'ArrowUp':
                                e.preventDefault();
                                items[(index - 1 + items.length) % items.length].focus();
                                break;
                            case 'Escape':
                                e.preventDefault();
                                closeAllMenus();
                                button.focus();
                                break;
                        }
                    });
                });
            }
        });

        // Close menus when clicking outside
        document.addEventListener('click', function (e) {
            if (!e.target.closest('.action-menu') && !e.target.closest('.action-button.table-action')) {
                closeAllMenus();
            }
        });
    });

    // Function to position the menu
    function positionMenu(button, menu)
    {
        // Create a container for the menu
        let container = document.getElementById('menu-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'menu-container';
            container.style.position = 'absolute';
            container.style.top = '0';
            container.style.left = '0';
            container.style.width = '100%';
            container.style.height = '0';
            container.style.overflow = 'visible';
            container.style.pointerEvents = 'none';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
        }

        // Clone the menu and add it to the container
        const clonedMenu = menu.cloneNode(true);
        clonedMenu.id = 'cloned-menu';
        clonedMenu.style.display = 'block';
        clonedMenu.style.position = 'absolute';
        clonedMenu.style.pointerEvents = 'auto';
        clonedMenu.style.maxHeight = '300px'; // Set a reasonable max height
        clonedMenu.style.overflowY = 'auto';  // Enable scrolling if needed

        // Clear the container
        container.innerHTML = '';
        container.appendChild(clonedMenu);

        // Get button position
        const buttonRect = button.getBoundingClientRect();
        const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
        const scrollLeft = window.pageXOffset || document.documentElement.scrollLeft;

        // Get viewport dimensions
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        // Temporarily position the menu to measure its dimensions
        clonedMenu.style.top = '0px';
        clonedMenu.style.left = '0px';
        clonedMenu.style.visibility = 'hidden';

        // Get menu dimensions
        const menuRect = clonedMenu.getBoundingClientRect();
        const menuWidth = menuRect.width;
        const menuHeight = menuRect.height;

        // Calculate position
        let top = buttonRect.bottom + scrollTop;
        let left = buttonRect.left + scrollLeft;

        // Check if menu would go off the bottom of the viewport
        if (buttonRect.bottom + menuHeight > viewportHeight) {
            // Position above the button if it would go off the bottom
            top = buttonRect.top + scrollTop - menuHeight;

            // If it would also go off the top, position at the bottom of the button
            // and limit the height
            if (top < scrollTop) {
                top = buttonRect.bottom + scrollTop;
                const availableHeight = viewportHeight - buttonRect.bottom;
                clonedMenu.style.maxHeight = (availableHeight - 20) + 'px'; // 20px buffer
            }
        }

        // Check if menu would go off the right of the viewport
        if (buttonRect.left + menuWidth > viewportWidth) {
            // Align with the right edge of the button
            left = buttonRect.right + scrollLeft - menuWidth;
        }

        // Apply the final position
        clonedMenu.style.top = top + 'px';
        clonedMenu.style.left = left + 'px';
        clonedMenu.style.visibility = 'visible';

        // Add event listeners to cloned menu items
        const menuItems = clonedMenu.querySelectorAll('[role="menuitem"]');
        menuItems.forEach((item, index) => {
            item.addEventListener('click', function (e) {
                e.preventDefault();
                e.stopPropagation();

                // Find the original menu item and trigger its click
                const originalItems = menu.querySelectorAll('[role="menuitem"]');
                if (originalItems[index]) {
                    originalItems[index].click();
                }
            });
        });

        // Hide the original menu
        menu.style.display = 'none';
    }

    // Function to close all menus
    function closeAllMenus()
    {
        // Hide all menus
        document.querySelectorAll('.action-menu.show').forEach(menu => {
            menu.classList.remove('show');
            menu.style.display = '';

            const button = menu.previousElementSibling;
            if (button && button.classList.contains('action-button')) {
                button.setAttribute('aria-expanded', 'false');
            }
        });

        // Remove the cloned menu
        const container = document.getElementById('menu-container');
        if (container) {
            container.innerHTML = '';
        }
    }

    // Function to handle menu actions
    function handleMenuAction(action, url, button)
    {
        // For select action, we don't need a URL
        if (!action || (action !== 'select' && !url)) {
            console.warn('Missing action or URL for non-select action');
            return;
        }

        switch (action) {
            case 'view':
            case 'edit':
                window.location = url;
                break;
            case 'delete':
                if (confirm('Are you sure you want to delete this expense?')) {
                    submitDeleteForm(url);
                }
                break;
            case 'select':
                const row = button.closest('tr[data-expense-id]');
                const expenseId = row ? row.getAttribute('data-expense-id') : null;
                document.dispatchEvent(new CustomEvent('expense:enter-select', {
                    detail: { expenseId }
                }));
                break;
            default:
                console.warn('Unknown action menu item:', action);
        }
    }

    // Function to submit delete form
    function submitDeleteForm(url)
    {
        const csrfTokenElement = document.querySelector('meta[name="csrf-token"]');
        const csrfToken = csrfTokenElement ? csrfTokenElement.getAttribute('content') : null;

        const form = document.createElement('form');
        form.method = 'POST';
        form.action = url;
        form.style.display = 'none';

        // Spoof DELETE method
        const methodInput = document.createElement('input');
        methodInput.type = 'hidden';
        methodInput.name = '_method';
        methodInput.value = 'DELETE';
        form.appendChild(methodInput);

        // CSRF token, if available
        if (csrfToken) {
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = '_token';
            csrfInput.value = csrfToken;
            form.appendChild(csrfInput);
        }

        document.body.appendChild(form);
        form.submit();
    }
})();
