import { initializeModal } from './modal.js';
import { initializePagination } from './pagination.js';
import { initializeMessages } from './messages.js';
import { initializeForms } from './form.js';
import { initializeBehavioralTriggers } from './behavioral-triggers.js';

function initializeSharedComponents()
{
    initializeAllModals();
    initializeAllPagination();
    initializeMessages();
    initializeForms();
    initializeBehavioralTriggers();
}

function initializeAllModals()
{
    document.querySelectorAll('.modal').forEach(modal => {
        initializeModal(modal);
    });
}

function initializeAllPagination()
{
    document.querySelectorAll('.pagination').forEach(pagination => {
        initializePagination(pagination);
    });
}

export { initializeSharedComponents };
