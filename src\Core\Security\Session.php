<?php

declare(strict_types=1);

namespace App\Core\Security;

final class Session
{
    private const DEFAULT_OPTIONS = [
        'cookie_httponly' => 1,
        'cookie_secure' => 0,
        'use_strict_mode' => 1,
        'cookie_samesite' => 'Lax',
        'gc_maxlifetime' => 7200, // 2 hours
        'cookie_lifetime' => 0, // Session cookie (expires when browser closes)
    ];

    // Special session key to track browser session
    private const BROWSER_SESSION_KEY = 'browser_session_id';
    private const SESSION_FINGERPRINT_KEY = 'session_fingerprint';
    private const SESSION_LAST_REGENERATION_KEY = 'last_regeneration';
    private const REGENERATION_INTERVAL = 300; // 5 minutes
    
    private static SecurityLogger $securityLogger;
    
    private static function getSecurityLogger(): SecurityLogger
    {
        if (!isset(self::$securityLogger)) {
            self::$securityLogger = new SecurityLogger();
        }
        return self::$securityLogger;
    }


    public static function start(array $options = []): bool
    {
        if (self::isActive()) {
            return true;
        }

        // CRITICAL: Always set cookie_lifetime to 0 to ensure session expires when browser is closed
        // This is the default behavior for sessions without "remember me"
        $sessionOptions = array_merge(self::DEFAULT_OPTIONS, [
            'cookie_lifetime' => 0 // Force session to expire when browser is closed
        ], $options);

        // Set secure cookie in HTTPS environments
        if (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') {
            $sessionOptions['cookie_secure'] = 1;
        }

        // Start the session with our forced settings
        $result = session_start($sessionOptions);

        // Check if this is a new browser session by looking for our browser session ID
        $isNewBrowserSession = !isset($_SESSION[self::BROWSER_SESSION_KEY]);

        // Initialize browser session ID if it doesn't exist
        if ($result && $isNewBrowserSession) {
            $_SESSION[self::BROWSER_SESSION_KEY] = bin2hex(random_bytes(16));
            $_SESSION['browser_session_start_time'] = time();
            $_SESSION['is_new_browser_session'] = true;
        }

        // Log session information for debugging
        if ($result) {
            $sessionCookieParams = session_get_cookie_params();


            // Check if there's a remember_me cookie but no session flag
            if (isset($_COOKIE['remember_token']) && !isset($_SESSION['remember_me'])) {
                // The remember token will be validated by the Authenticator class
                // We don't need to do anything here
            } elseif (isset($_SESSION['remember_me']) && $_SESSION['remember_me'] === true) {
                // If this is a remember_me session, update the session cookie expiry
                self::updateSessionCookieExpiry();
            } else {
                // Ensure the session cookie is set to expire when browser is closed
                $params = session_get_cookie_params();
                setcookie(
                    session_name(),
                    session_id(),
                    [
                        'expires' => 0, // 0 means "until the browser is closed"
                        'path' => $params['path'],
                        'domain' => $params['domain'],
                        'secure' => $params['secure'],
                        'httponly' => $params['httponly'],
                        'samesite' => $params['samesite'] ?? 'Lax'
                    ]
                );
            }
        }

        // Initialize session security
        if ($result) {
            self::initializeSessionSecurity();
        }

        return $result;
    }

    private static function initializeSessionSecurity(): void
    {
        $fingerprint = self::generateFingerprint();
        
        if (!isset($_SESSION[self::SESSION_FINGERPRINT_KEY])) {
            $_SESSION[self::SESSION_FINGERPRINT_KEY] = $fingerprint;
            $_SESSION[self::SESSION_LAST_REGENERATION_KEY] = time();
            self::getSecurityLogger()->logSessionEvent('created');
        } else {
            // Validate session fingerprint
            if (!self::validateFingerprint($_SESSION[self::SESSION_FINGERPRINT_KEY])) {
                self::getSecurityLogger()->logSessionEvent('hijack_detected', [
                    'expected_fingerprint' => $_SESSION[self::SESSION_FINGERPRINT_KEY],
                    'actual_fingerprint' => $fingerprint
                ]);
                self::destroy();
                return;
            }
            
            // Check if session needs regeneration
            if (self::shouldRegenerateSession()) {
                self::regenerateId();
            }
        }
    }

    public static function regenerateId(bool $deleteOldSession = true): bool
    {
        if (!self::isActive()) {
            return false;
        }
        
        $result = session_regenerate_id($deleteOldSession);
        
        if ($result) {
            $_SESSION[self::SESSION_LAST_REGENERATION_KEY] = time();
            $_SESSION[self::SESSION_FINGERPRINT_KEY] = self::generateFingerprint();
            self::getSecurityLogger()->logSessionEvent('regenerated');
        }
        
        return $result;
    }

    private static function shouldRegenerateSession(): bool
    {
        if (!isset($_SESSION[self::SESSION_LAST_REGENERATION_KEY])) {
            return true;
        }
        
        $lastRegeneration = $_SESSION[self::SESSION_LAST_REGENERATION_KEY];
        return (time() - $lastRegeneration) > self::REGENERATION_INTERVAL;
    }

    private static function generateFingerprint(): string
    {
        $components = [
            $_SERVER['HTTP_USER_AGENT'] ?? '',
            $_SERVER['HTTP_ACCEPT_LANGUAGE'] ?? '',
            $_SERVER['HTTP_ACCEPT_ENCODING'] ?? '',
            $_SERVER['REMOTE_ADDR'] ?? ''
        ];
        
        return hash('sha256', implode('|', $components));
    }

    private static function validateFingerprint(string $storedFingerprint): bool
    {
        $currentFingerprint = self::generateFingerprint();
        return hash_equals($storedFingerprint, $currentFingerprint);
    }

    public static function isActive(): bool
    {
        return session_status() === PHP_SESSION_ACTIVE;
    }


    public static function set(string $key, mixed $value): void
    {
        self::ensureActive();
        $_SESSION[$key] = $value;
    }


    public static function get(string $key, mixed $default = null): mixed
    {
        self::ensureActive();
        return $_SESSION[$key] ?? $default;
    }


    public static function has(string $key): bool
    {
        self::ensureActive();
        return isset($_SESSION[$key]);
    }


    public static function remove(string $key): void
    {
        self::ensureActive();
        unset($_SESSION[$key]);
    }


    public static function setFlash(string $key, mixed $value): void
    {
        self::ensureActive();
        $_SESSION['_flash'][$key] = $value;
    }


    public static function getFlash(string $key, mixed $default = null): mixed
    {
        self::ensureActive();
        return $_SESSION['_flash'][$key] ?? $default;
    }


    public static function hasFlash(string $key): bool
    {
        self::ensureActive();
        return isset($_SESSION['_flash'][$key]);
    }


    public static function clearFlash(): void
    {
        self::ensureActive();
        unset($_SESSION['_flash']);
    }


    public static function regenerateId(bool $deleteOldSession = true): bool
    {
        self::ensureActive();
        $result = session_regenerate_id($deleteOldSession);

        // If this is a persistent session (remember_me is set), ensure the cookie lifetime is maintained
        if ($result && isset($_SESSION['remember_me']) && $_SESSION['remember_me'] === true) {
            self::updateSessionCookieExpiry();
        }

        return $result;
    }

    /**
     * Update the session cookie expiry for persistent sessions
     *
     * This is used for "remember me" functionality to extend the session cookie lifetime
     * beyond the browser session.
     *
     * @param int $days Number of days to extend the session cookie
     * @return bool True if the cookie was updated
     */
    private static function updateSessionCookieExpiry(int $days = 30): bool
    {
        $params = session_get_cookie_params();
        $result = setcookie(
            session_name(),
            session_id(),
            [
                'expires' => time() + 86400 * $days, // Convert days to seconds
                'path' => $params['path'],
                'domain' => $params['domain'],
                'secure' => $params['secure'],
                'httponly' => $params['httponly'],
                'samesite' => $params['samesite'] ?? 'Lax'
            ]
        );

        if ($result) {
        }

        return $result;
    }


    public static function destroy(): bool
    {
        if (!self::isActive()) {
            return true;
        }

        self::getSecurityLogger()->logSessionEvent('destroyed');

        // Clear session data
        self::clearAllData();

        // Delete the session cookie
        self::deleteSessionCookie();

        // Destroy the session
        return session_destroy();
    }

    private static function clearAllData(): void
    {
        foreach (array_keys($_SESSION) as $key) {
            unset($_SESSION[$key]);
        }
    }

    private static function deleteSessionCookie(): void
    {
        if (!ini_get('session.use_cookies')) {
            return;
        }

        $params = session_get_cookie_params();
        setcookie(
            session_name(),
            '',
            [
                'expires' => time() - 42000,
                'path' => $params['path'],
                'domain' => $params['domain'],
                'secure' => $params['secure'],
                'httponly' => $params['httponly'],
                'samesite' => $params['samesite'] ?? 'Lax'
            ]
        );
    }

    /**
     * Set a remember-me cookie
     *
     * @param string $name Cookie name
     * @param string $value Cookie value
     * @param int $expiry Expiry time in seconds from now
     * @return bool True if the cookie was set
     */
    public static function setRememberCookie(string $name, string $value, int $expiry): bool
    {
        $secure = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on';

        return setcookie(
            $name,
            $value,
            [
                'expires' => time() + $expiry,
                'path' => '/',
                'domain' => '',
                'secure' => $secure,
                'httponly' => true,
                'samesite' => 'Lax'
            ]
        );
    }

    /**
     * Delete a remember-me cookie
     *
     * @param string $name Cookie name
     * @return bool True if the cookie was deleted
     */
    public static function deleteRememberCookie(string $name): bool
    {
        if (!isset($_COOKIE[$name])) {
            error_log("Remember cookie '$name' not found, nothing to delete");
            return true;
        }

        error_log("Deleting remember cookie: $name");
        $result = setcookie(
            $name,
            '',
            [
                'expires' => time() - 3600,
                'path' => '/',
                'domain' => '',
                'secure' => isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on',
                'httponly' => true,
                'samesite' => 'Lax'
            ]
        );

        if ($result) {
            error_log("Remember cookie '$name' deleted successfully");
        } else {
            error_log("Failed to delete remember cookie '$name'");
        }

        return $result;
    }


    private static function ensureActive(): void
    {
        if (!self::isActive() && !self::start()) {
            throw new \RuntimeException('Could not start session');
        }
    }

    /**
     * Check if this is a new browser session
     *
     * This is used to determine if the user has closed all browser tabs/windows
     * and opened a new one. If they didn't check "remember me", we should
     * invalidate their session.
     *
     * @param string|null $storedBrowserId A previously stored browser session ID to compare against
     * @return bool True if this is a new browser session
     */
    public static function isNewBrowserSession(?string $storedBrowserId = null): bool
    {
        self::ensureActive();

        // If we've explicitly marked this as a new browser session, return true
        if (isset($_SESSION['is_new_browser_session']) && $_SESSION['is_new_browser_session'] === true) {
            // Clear the flag so we only detect it once
            $_SESSION['is_new_browser_session'] = false;

            return true;
        }

        // If no browser session ID exists, this is definitely a new session
        if (!isset($_SESSION[self::BROWSER_SESSION_KEY])) {
            return true;
        }

        // If a stored browser ID was provided, compare it with the current one
        if ($storedBrowserId !== null) {
            $result = $_SESSION[self::BROWSER_SESSION_KEY] !== $storedBrowserId;
            if ($result) {
            }
            return $result;
        }

        // If no stored ID was provided, check if this session was just created
        // We consider a session "new" if it was created within the last 5 seconds
        $isNew = isset($_SESSION['browser_session_start_time']) &&
                 $_SESSION['browser_session_start_time'] > (time() - 5); // Within the last 5 seconds

        if ($isNew) {
            error_log('Browser session was just created - this is a new session');
        }

        return $isNew;
    }

    /**
     * Get the current browser session ID
     *
     * @return string|null The browser session ID or null if not set
     */
    public static function getBrowserSessionId(): ?string
    {
        self::ensureActive();
        return $_SESSION[self::BROWSER_SESSION_KEY] ?? null;
    }
}
