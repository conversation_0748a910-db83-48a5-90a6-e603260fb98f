/**
 * Interface Standards Module
 * Provides consistent UI behavior across the application
 */
document.addEventListener('DOMContentLoaded', function () {
    // Initialize tooltips
    initializeTooltips();

    // Initialize help buttons
    initializeHelpButtons();

    // Initialize form validation visual feedback
    enhanceFormValidation();

    // Initialize responsive tables
    makeTablesResponsive();

    // Initialize accessible dropdowns
    makeDropdownsAccessible();
});

/**
 * Initialize tooltips across the application
 */
function initializeTooltips()
{
    const tooltipTriggers = document.querySelectorAll('[data-tooltip]');

    tooltipTriggers.forEach(trigger => {
        trigger.addEventListener('mouseenter', showTooltip);
        trigger.addEventListener('mouseleave', hideTooltip);
        trigger.addEventListener('focus', showTooltip);
        trigger.addEventListener('blur', hideTooltip);
    });

    function showTooltip()
    {
        const tooltipText = this.getAttribute('data-tooltip');
        if (!tooltipText) {
            return;
        }

        // Create tooltip element
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip';
        tooltip.textContent = tooltipText;

        // Position the tooltip
        this.appendChild(tooltip);

        // Position adjustments
        const rect = tooltip.getBoundingClientRect();
        if (rect.right > window.innerWidth) {
            tooltip.style.left = 'auto';
            tooltip.style.right = '0';
        }

        // Add visible class after a small delay for transition
        setTimeout(() => {
            tooltip.classList.add('visible');
        }, 10);
    }

    function hideTooltip()
    {
        const tooltip = this.querySelector('.tooltip');
        if (tooltip) {
            tooltip.classList.remove('visible');

            // Remove after transition
            setTimeout(() => {
                if (tooltip.parentNode === this) {
                    this.removeChild(tooltip);
                }
            }, 200);
        }
    }
}

/**
 * Initialize help buttons with tooltips
 */
function initializeHelpButtons()
{
    const helpButtons = document.querySelectorAll('.help-button');
    const closeButtons = document.querySelectorAll('.close-tooltip');
    const actionButtons = document.querySelectorAll('.tooltip-action-button');

    helpButtons.forEach(button => {
        button.addEventListener('click', function (e) {
            e.preventDefault();
            const tooltip = this.closest('.help-tooltip').querySelector('.tooltip-content');

            if (tooltip) {
                const isVisible = tooltip.classList.contains('visible');

                // Hide all other tooltips first
                document.querySelectorAll('.tooltip-content.visible').forEach(t => {
                    if (t !== tooltip) {
                        t.classList.remove('visible');
                        t.setAttribute('aria-hidden', 'true');
                        t.closest('.help-tooltip').querySelector('.help-button').setAttribute('aria-expanded', 'false');
                    }
                });

                // Toggle this tooltip
                tooltip.classList.toggle('visible');
                const newState = !isVisible;
                tooltip.setAttribute('aria-hidden', newState ? 'false' : 'true');
                this.setAttribute('aria-expanded', newState ? 'true' : 'false');
            }
        });
    });

    // Close buttons inside tooltips
    closeButtons.forEach(button => {
        button.addEventListener('click', function () {
            const tooltip = this.closest('.tooltip-content');
            if (tooltip) {
                tooltip.classList.remove('visible');
                tooltip.setAttribute('aria-hidden', 'true');
                tooltip.closest('.help-tooltip').querySelector('.help-button').setAttribute('aria-expanded', 'false');
            }
        });
    });

    // Action buttons inside tooltips (usually "Got it" buttons)
    actionButtons.forEach(button => {
        button.addEventListener('click', function () {
            const tooltip = this.closest('.tooltip-content');
            if (tooltip) {
                tooltip.classList.remove('visible');
                tooltip.setAttribute('aria-hidden', 'true');
                tooltip.closest('.help-tooltip').querySelector('.help-button').setAttribute('aria-expanded', 'false');
            }
        });
    });

    // Close tooltips when clicking outside
    document.addEventListener('click', function (e) {
        if (!e.target.closest('.help-tooltip')) {
            document.querySelectorAll('.tooltip-content.visible').forEach(tooltip => {
                tooltip.classList.remove('visible');
                tooltip.setAttribute('aria-hidden', 'true');
                tooltip.closest('.help-tooltip').querySelector('.help-button').setAttribute('aria-expanded', 'false');
            });
        }
    });

    // Close tooltips with Escape key
    document.addEventListener('keydown', function (e) {
        if (e.key === 'Escape') {
            document.querySelectorAll('.tooltip-content.visible').forEach(tooltip => {
                tooltip.classList.remove('visible');
                tooltip.setAttribute('aria-hidden', 'true');
                tooltip.closest('.help-tooltip').querySelector('.help-button').setAttribute('aria-expanded', 'false');
            });
        }
    });
}

/**
 * Enhance form validation with visual feedback
 */
function enhanceFormValidation()
{
    const forms = document.querySelectorAll('form');

    forms.forEach(form => {
        const inputs = form.querySelectorAll('input, select, textarea');

        inputs.forEach(input => {
            // Skip submit buttons and hidden fields
            if (input.type === 'submit' || input.type === 'hidden') {
                return;
            }

            // Add validation classes on blur
            input.addEventListener('blur', function () {
                if (this.value.trim() === '') {
                    this.classList.remove('is-valid');
                    return;
                }

                if (this.checkValidity()) {
                    this.classList.add('is-valid');
                    this.classList.remove('is-invalid');
                } else {
                    this.classList.add('is-invalid');
                    this.classList.remove('is-valid');
                }
            });

            // Remove validation classes on focus
            input.addEventListener('focus', function () {
                this.classList.remove('is-invalid');
            });
        });
    });
}

/**
 * Make tables responsive
 */
function makeTablesResponsive()
{
    const tables = document.querySelectorAll('table:not(.no-responsive)');

    tables.forEach(table => {
        if (!table.parentElement.classList.contains('table-responsive')) {
            const wrapper = document.createElement('div');
            wrapper.className = 'table-responsive';
            table.parentNode.insertBefore(wrapper, table);
            wrapper.appendChild(table);
        }
    });
}

/**
 * Make dropdowns accessible
 */
function makeDropdownsAccessible()
{
    const dropdownTriggers = document.querySelectorAll('.dropdown-trigger');

    dropdownTriggers.forEach(trigger => {
        const dropdown = trigger.nextElementSibling;
        if (!dropdown || !dropdown.classList.contains('dropdown-menu')) {
            return;
        }

        // Set ARIA attributes
        trigger.setAttribute('aria-haspopup', 'true');
        trigger.setAttribute('aria-expanded', 'false');
        dropdown.setAttribute('aria-hidden', 'true');

        // Toggle dropdown on click
        trigger.addEventListener('click', function (e) {
            e.preventDefault();
            e.stopPropagation();

            const isExpanded = this.getAttribute('aria-expanded') === 'true';

            // Close all other dropdowns first
            document.querySelectorAll('.dropdown-trigger[aria-expanded="true"]').forEach(t => {
                if (t !== this) {
                    t.setAttribute('aria-expanded', 'false');
                    t.nextElementSibling.classList.remove('show');
                    t.nextElementSibling.setAttribute('aria-hidden', 'true');
                }
            });

            // Toggle this dropdown
            this.setAttribute('aria-expanded', !isExpanded);
            dropdown.classList.toggle('show');
            dropdown.setAttribute('aria-hidden', isExpanded);
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', function (e) {
            if (!e.target.closest('.dropdown')) {
                trigger.setAttribute('aria-expanded', 'false');
                dropdown.classList.remove('show');
                dropdown.setAttribute('aria-hidden', 'true');
            }
        });

        // Close dropdown with Escape key
        document.addEventListener('keydown', function (e) {
            if (e.key === 'Escape' && dropdown.classList.contains('show')) {
                trigger.setAttribute('aria-expanded', 'false');
                dropdown.classList.remove('show');
                dropdown.setAttribute('aria-hidden', 'true');
            }
        });
    });
}