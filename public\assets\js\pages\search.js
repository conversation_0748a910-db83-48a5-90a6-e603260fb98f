document.addEventListener('DOMContentLoaded', initializeSearchPage);

function initializeSearchPage()
{
    setupSearchForm();
    setupNotesModal();
    enhanceFilterTags();
    setupDateRangePickers();
}

function setupSearchForm()
{
    const searchForm = document.querySelector('.search-form');
    if (!searchForm) {
        return;
    }

    setupFormValidation(searchForm);
    setupResetButton(searchForm);
}

function setupFormValidation(form)
{
    form.addEventListener('submit', e => {
        const searchQuery = document.getElementById('search_query');

        if (!searchQuery ? .value.trim()) {
            e.preventDefault();
            showErrorMessage(form, searchQuery);
        }
    });
}

function showErrorMessage(form, searchQuery)
{
    let errorMessage = document.querySelector('.search-error');

    if (!errorMessage) {
        errorMessage = document.createElement('div');
        errorMessage.className = 'notice';
        errorMessage.textContent = 'Please enter a search term.';
        form.prepend(errorMessage);
    }

    searchQuery.focus();

    setTimeout(() => {
        if (errorMessage.parentNode) {
            errorMessage.remove();
        }
    }, 3000);
}

function setupResetButton(form)
{
    const resetButton = form.querySelector('.button.secondary');
    if (!resetButton) {
        return;
    }

    resetButton.addEventListener('click', e => {
        e.preventDefault();
        form.querySelectorAll('input, select').forEach(field => field.value = '');
        window.location.href = '/search';
    });
}

function setupNotesModal()
{
    const modal = document.getElementById('notesModal');
    if (!modal) {
        return;
    }

    const notesContent = document.getElementById('notesContent');
    const notesButtons = document.querySelectorAll('.show-notes');
    const closeButton = modal.querySelector('.close');

    setupNotesButtons(notesButtons, notesContent, modal);
    setupModalCloseHandlers(modal, closeButton);
}

function setupNotesButtons(buttons, contentElement, modal)
{
    buttons.forEach(button => {
        button.addEventListener('click', () => {
            contentElement.textContent = button.getAttribute('data-notes');
            openModal(modal);
        });
    });
}

function setupModalCloseHandlers(modal, closeButton)
{
    if (closeButton) {
        closeButton.addEventListener('click', () => closeModal(modal));
    }

    window.addEventListener('click', event => {
        if (event.target === modal) {
            closeModal(modal);
        }
    });

    document.addEventListener('keydown', event => {
        if (event.key === 'Escape' && modal.style.display === 'block') {
            closeModal(modal);
        }
    });
}

function openModal(modal)
{
    modal.style.display = 'block';
    document.body.classList.add('modal-open');
}

function closeModal(modal)
{
    modal.style.display = 'none';
    document.body.classList.remove('modal-open');
}

function enhanceFilterTags()
{
    const filterTags = document.querySelectorAll('.filter-tag');
    if (filterTags.length === 0) {
        return;
    }

    setupClearAllButton();
}

function setupClearAllButton()
{
    const activeFilters = document.querySelector('.active-filters');
    if (!activeFilters) {
        return;
    }

    const clearAllButton = activeFilters.querySelector('.button.small');
    if (!clearAllButton) {
        return;
    }

    clearAllButton.addEventListener('click', e => {
        e.preventDefault();

        const urlParams = new URLSearchParams(window.location.search);
        const query = urlParams.get('query');

        window.location.href = ` / search ? query = ${encodeURIComponent(query || '')}`;
    });
}

function setupDateRangePickers()
{
    const dateFrom = document.getElementById('date_from');
    const dateTo = document.getElementById('date_to');

    if (!dateFrom || !dateTo) {
        return;
    }

    const today = new Date().toISOString().split('T')[0];
    setupInitialDateLimits(dateFrom, dateTo, today);
    setupDateChangeHandlers(dateFrom, dateTo, today);
}

function setupInitialDateLimits(dateFrom, dateTo, today)
{
    dateFrom.max = today;
    dateTo.max = today;
}

function setupDateChangeHandlers(dateFrom, dateTo, today)
{
    dateFrom.addEventListener('change', () => {
        if (dateFrom.value) {
            dateTo.min = dateFrom.value;
        } else {
            dateTo.removeAttribute('min');
        }
    });

    dateTo.addEventListener('change', () => {
        if (dateTo.value) {
            dateFrom.max = dateTo.value;
        } else {
            dateFrom.max = today;
        }
    });
}
