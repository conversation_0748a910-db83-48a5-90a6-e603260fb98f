<?php

// Import the CSRF class for token generation
use App\Core\Security\CSRF;

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <link rel="stylesheet" href="/assets/css/main.css?v=<?= time() ?>">
    
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Personal Expense Tracker - Track and manage your expenses efficiently">
    <meta name="theme-color" content="#4a6cf7">
    <meta name="csrf-token" content="<?= htmlspecialchars($csrf_token ?? CSRF::generateToken(), ENT_QUOTES, 'UTF-8') ?>">
    <title><?= htmlspecialchars($title ?? 'Personal Expense Tracker') ?></title>

    <!-- Session management is handled by tab-session-manager.js -->

    <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="/assets/css/icons.css?v=<?= time() ?>">
    <?php if (!empty($stylesheets)) : ?>
        <?php foreach ($stylesheets as $stylesheet) : ?>
            <link rel="stylesheet" href="<?= htmlspecialchars($stylesheet) ?>?v=<?= time() ?>">
        <?php endforeach; ?>
    <?php endif; ?>
    <!-- Inline SVG favicon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'><path d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1.41 16.09V20h-2.67v-1.93c-1.71-.36-3.16-1.46-3.27-3.4h1.96c.1 1.05.82 1.87 2.65 1.87 1.96 0 2.4-.98 2.4-1.59 0-.83-.44-1.61-2.67-2.14-2.48-.6-4.18-1.62-4.18-3.67 0-1.72 1.39-2.84 3.11-3.21V4h2.67v1.95c1.86.45 2.79 1.86 2.85 3.39H14.3c-.05-1.11-.64-1.87-2.22-1.87-1.5 0-2.4.68-2.4 1.64 0 .84.65 1.39 2.67 1.91s4.18 1.39 4.18 3.91c-.01 1.83-1.38 2.83-3.12 3.16z' fill='%234a6cf7'/></svg>" type="image/svg+xml">

    <!-- Immediate session check script - runs before any content is displayed -->
    <script>
    (function() {
        function isPublicPage(path) {
            const publicPaths = ['/', '/login', '/register', '/password/reset', '/password/new', '/how'];
            return publicPaths.includes(path);
        }

        function isLoginToDashboard(path, referrer) {
            return path === '/dashboard' && referrer.includes('/login');
        }

        function createBrowserSessionId() {
            return Date.now().toString(36) + Math.random().toString(36).substring(2);
        }

        function hideContent() {
            document.documentElement.style.visibility = 'hidden';

            const style = document.createElement('style');
            style.id = 'session-check-style';
            style.textContent = 'html { visibility: hidden !important; }';
            document.head.appendChild(style);
        }

        // Main execution logic
        const currentPath = window.location.pathname;

        if (isPublicPage(currentPath)) {
            return;
        }

        if (isLoginToDashboard(currentPath, document.referrer)) {
            console.log('Coming from login page to dashboard, skipping session check');
            sessionStorage.setItem('browserSessionId', createBrowserSessionId());
            return;
        }

        if (!sessionStorage.getItem('browserSessionId')) {
            hideContent();
        }
    })();
    </script>
</head>
<?php
    // Ensure the remember_me flag is properly set
    // This is critical for the session invalidation to work correctly
    $rememberMe = isset($_SESSION['remember_me']) && $_SESSION['remember_me'] === true ? 'true' : 'false';

?>
<body data-remember-me="<?= $rememberMe ?>">
    <?php
    // Pass the CSRF token to the header
    $headerData = ['csrf_token' => $csrf_token ?? CSRF::generateToken()];
    extract($headerData);
    include 'partials/header.php';
    ?>
    <?php
    // Pass the CSRF token to the sidebar
    $sidebarData = ['csrf_token' => $csrf_token ?? CSRF::generateToken()];
    extract($sidebarData);
    include 'partials/sidebar.php';
    ?>

    <main class="container">
         <?= $content ?? '' ?>
    </main>

    <?php
    // Pass the CSRF token to the footer
    $footerData = ['csrf_token' => $csrf_token ?? CSRF::generateToken()];
    extract($footerData);
    include 'partials/footer.php';
    ?>
    <script src="/assets/js/app.js?v=<?= time() ?>" type="module"></script>
    <script src="/assets/js/ui-extensions.js?v=<?= time() ?>" defer></script>
    <script src="/assets/js/interface-standards.js?v=<?= time() ?>" defer></script>
    <script src="/assets/js/session-manager.js?v=<?= time() ?>" defer></script>
    <script src="/assets/js/tab-session-manager.js?v=<?= time() ?>"></script>
    <script src="/assets/js/document-preview.js?v=<?= time() ?>" defer></script>

    <?php if (!empty($scripts)) : ?>
        <?php foreach ($scripts as $script) : ?>
            <script src="<?= htmlspecialchars($script) ?>?v=<?= time() ?>" defer></script>
        <?php endforeach; ?>
    <?php endif; ?>

    <?php if (isset($_ENV['APP_ENV']) && $_ENV['APP_ENV'] === 'development') : ?>
        <script>
            console.log('Page rendered in ' + (performance.now() / 1000).toFixed(2) + ' seconds');
        </script>
    <?php endif; ?>
</body>
</html>
