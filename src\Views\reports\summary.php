<?php

/**
 * Summary Report View
 * Shows yearly summary of expenses
 */

?>

<h2>Annual Expense Summary</h2>

<div class="report-filters">
    <form method="GET" action="/reports/summary">
        <div class="filter-group">
            <label for="year">Year</label>
            <select name="year" id="year" onchange="this.form.submit()">
                <?php foreach (range(date('Y'), date('Y') - 5) as $yr) : ?>
                    <option value="<?= $yr ?>" <?= $yr == $year ? 'selected' : '' ?>>
                        <?= $yr ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>
    </form>
</div>

<?php if (empty($monthlyTotals)) : ?>
    <div class="notice">
        No expense data available for <?= $year ?>.
    </div>
<?php else : ?>
    <div class="report-summary">
        <div class="summary-stats">
            <div class="stat-item">
                <span class="stat-label">Total for <?= $year ?></span>
                <span class="stat-value">$<?= number_format($total, 2) ?></span>
            </div>
            
            <div class="stat-item">
                <span class="stat-label">Monthly Average</span>
                <span class="stat-value">$<?= number_format($total / count($monthlyTotals), 2) ?></span>
            </div>
            
            <div class="stat-item">
                <span class="stat-label">Categories</span>
                <span class="stat-value"><?= count($categoryTotals) ?></span>
            </div>
            
            <div class="stat-item">
                <span class="stat-label">Merchants</span>
                <span class="stat-value"><?= count($merchantTotals) ?></span>
            </div>
        </div>
    </div>

    <div class="chart-container">
        <canvas id="monthlySummaryChart" width="400" height="200"></canvas>
    </div>
    
    <h3>Monthly Breakdown</h3>
    <div class="table-responsive">
        <table aria-label="Monthly Expense Totals">
            <thead>
                <tr>
                    <th scope="col">Month</th>
                    <th scope="col">Total Amount</th>
                    <th scope="col">% of Annual Total</th>
                    <th scope="col">Number of Expenses</th>
                    <th scope="col">Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($monthlyTotals as $month) : ?>
                    <tr>
                        <td><?= $month['month_name'] ?></td>
                        <td>$<?= number_format($month['total'], 2) ?></td>
                        <td><?= number_format(($month['total'] / $total) * 100, 1) ?>%</td>
                        <td><?= $month['count'] ?></td>
                        <td>
                            <a href="/expenses?month=<?= $month['month'] ?>&year=<?= $year ?>" class="button small">
                                View Expenses
                            </a>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
            <tfoot>
                <tr>
                    <th>Total</th>
                    <th>$<?= number_format($total, 2) ?></th>
                    <th>100%</th>
                    <th><?= array_sum(array_column($monthlyTotals, 'count')) ?></th>
                    <th></th>
                </tr>
            </tfoot>
        </table>
    </div>
    
    <h3>Category Breakdown</h3>
    <div class="chart-container">
        <canvas id="categorySummaryChart" width="400" height="200"></canvas>
    </div>
    
    <div class="table-responsive">
        <table aria-label="Category Expense Totals">
            <thead>
                <tr>
                    <th scope="col">Category</th>
                    <th scope="col">Total Amount</th>
                    <th scope="col">% of Annual Total</th>
                    <th scope="col">Number of Expenses</th>
                    <th scope="col">Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($categoryTotals as $category) : ?>
                    <tr>
                        <td><?= htmlspecialchars($category['name']) ?></td>
                        <td>$<?= number_format($category['total'], 2) ?></td>
                        <td><?= number_format(($category['total'] / $total) * 100, 1) ?>%</td>
                        <td><?= $category['count'] ?></td>
                        <td>
                            <a href="/expenses?category_id=<?= $category['id'] ?>&year=<?= $year ?>" class="button small">
                                View Expenses
                            </a>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    
    <h3>Merchant Breakdown</h3>
    <div class="table-responsive">
        <table aria-label="Merchant Expense Totals">
            <thead>
                <tr>
                    <th scope="col">Merchant</th>
                    <th scope="col">Total Amount</th>
                    <th scope="col">% of Annual Total</th>
                    <th scope="col">Number of Expenses</th>
                    <th scope="col">Actions</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($merchantTotals as $merchant) : ?>
                    <tr>
                        <td><?= htmlspecialchars($merchant['name']) ?></td>
                        <td>$<?= number_format($merchant['total'], 2) ?></td>
                        <td><?= number_format(($merchant['total'] / $total) * 100, 1) ?>%</td>
                        <td><?= $merchant['count'] ?></td>
                        <td>
                            <a href="/expenses?merchant_id=<?= $merchant['id'] ?>&year=<?= $year ?>" class="button small">
                                View Expenses
                            </a>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    
    <div class="download-section">
        <h3>Download Report</h3>
        <div class="download-options">
            <a href="/reports/export?type=summary&year=<?= $year ?>&format=csv" class="button">
                Export as CSV
            </a>
            <a href="/reports/export?type=summary&year=<?= $year ?>&format=pdf" class="button">
                Export as PDF
            </a>
        </div>
    </div>
<?php endif; ?>

<div class="navigation-links">
    <a href="/reports" class="button secondary">Back to Reports</a>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    <?php if (!empty($monthlyTotals)) : ?>
    // Monthly chart
    const monthlyCtx = document.getElementById('monthlySummaryChart').getContext('2d');
    
    // Prepare data for chart
    const months = <?= json_encode(array_column($monthlyTotals, 'month_name')) ?>;
    const amounts = <?= json_encode(array_column($monthlyTotals, 'total')) ?>;
    
    // Create chart
    const monthlyChart = new Chart(monthlyCtx, {
        type: 'bar',
        data: {
            labels: months,
            datasets: [{
                label: 'Monthly Expenses',
                data: amounts,
                backgroundColor: 'rgba(54, 162, 235, 0.5)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return '$' + context.raw.toLocaleString();
                        }
                    }
                }
            }
        }
    });
    
    // Category chart
    const categoryCtx = document.getElementById('categorySummaryChart').getContext('2d');
    
    // Prepare data for chart
    const categories = <?= json_encode(array_column($categoryTotals, 'name')) ?>;
    const categoryAmounts = <?= json_encode(array_column($categoryTotals, 'total')) ?>;
    const backgroundColors = [
        'rgba(54, 162, 235, 0.5)',
        'rgba(255, 99, 132, 0.5)',
        'rgba(255, 206, 86, 0.5)',
        'rgba(75, 192, 192, 0.5)',
        'rgba(153, 102, 255, 0.5)',
        'rgba(255, 159, 64, 0.5)',
        'rgba(199, 199, 199, 0.5)',
        'rgba(83, 102, 255, 0.5)',
        'rgba(40, 159, 64, 0.5)',
        'rgba(255, 99, 255, 0.5)'
    ];
    
    // Create chart
    const categoryChart = new Chart(categoryCtx, {
        type: 'pie',
        data: {
            labels: categories,
            datasets: [{
                data: categoryAmounts,
                backgroundColor: backgroundColors,
                borderColor: backgroundColors.map(color => color.replace('0.5', '1')),
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'right',
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const value = context.raw;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((value / total) * 100).toFixed(1);
                            return `${context.label}: $${value.toLocaleString()} (${percentage}%)`;
                        }
                    }
                }
            }
        }
    });
    <?php endif; ?>
});
</script>

<style>
.report-filters {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.filter-group {
    display: flex;
    flex-direction: column;
    min-width: 150px;
}

.filter-group label {
    margin-bottom: 5px;
    font-weight: bold;
    font-size: 0.9em;
}

.report-summary {
    margin-bottom: 30px;
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 15px;
}

.stat-item {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.stat-label {
    font-size: 0.9em;
    color: #666;
    display: block;
    margin-bottom: 5px;
}

.stat-value {
    font-size: 1.6em;
    font-weight: bold;
    color: #333;
    display: block;
}

.chart-container {
    margin-bottom: 30px;
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

h3 {
    margin-top: 30px;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.download-section {
    margin-top: 30px;
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
}

.download-options {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.navigation-links {
    margin-top: 30px;
}
</style>
