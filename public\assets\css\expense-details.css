/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(var(--primary-rgb), 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(var(--primary-rgb), 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(var(--primary-rgb), 0);
    }
}

@keyframes reveal {
    0% {
        opacity: 0;
        transform: translateY(10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Premium Expense Header Styles - Aligned with document-viewer.css */
.expense-premium-header {
    display: flex;
    justify-content: space-between;
    background: linear-gradient(135deg, var(--primary-dark), var(--primary-color));
    color: white;
    border-radius: var(--border-radius);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    box-shadow: var(--box-shadow);
    position: relative;
    overflow: hidden;
    transition: var(--transition);
    animation: fadeIn 0.6s ease-out;
}

.expense-premium-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('/assets/images/pattern.svg');
    opacity: 0.05;
    pointer-events: none;
}

.expense-premium-header:hover {
    box-shadow: var(--box-shadow-lg);
    transform: translateY(-2px);
}

.expense-details-column {
    flex: 1;
}

.expense-title-wrapper {
    margin-bottom: var(--spacing);
}

.expense-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    margin: 0;
    line-height: 1.2;
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.expense-meta {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.expense-date-badge,
.expense-merchant-badge {
    display: flex;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.15);
    border-radius: var(--border-radius-pill);
    padding: var(--spacing-xs) var(--spacing-sm);
    font-size: var(--font-size-sm);
}

.expense-date-badge .icon,
.expense-merchant-badge .icon {
    width: 14px;
    height: 14px;
    margin-right: var(--spacing-xs);
    fill: white;
}

.expense-amount-column {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-end;
    padding-left: var(--spacing-lg);
}

.amount-wrapper {
    text-align: right;
}

.amount-label {
    font-size: var(--font-size-sm);
    text-transform: uppercase;
    letter-spacing: 1px;
    opacity: 0.8;
    margin-bottom: var(--spacing-xs);
}

.amount-value {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: white;
}

/* Feature Badges - Aligned with document-viewer.css tags-container */
.feature-badges {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.feature-badge {
    display: flex;
    align-items: center;
    background-color: var(--grey-100);
    border-radius: var(--border-radius);
    padding: var(--spacing-xs) var(--spacing-sm);
    box-shadow: var(--box-shadow-sm);
    transition: var(--transition-fast);
    font-size: var(--font-size-sm);
}

.feature-badge:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
    background-color: var(--grey-200);
}

.feature-badge.category {
    border-left: 3px solid var(--success-color);
}

.feature-badge.payment {
    border-left: 3px solid var(--primary-color);
}

.feature-badge.document {
    border-left: 3px solid var(--warning-color);
}

.badge-icon {
    width: 20px;
    height: 20px;
    margin-right: var(--spacing-sm);
    display: flex;
    align-items: center;
    justify-content: center;
}

.badge-icon svg {
    width: 16px;
    height: 16px;
    fill: var(--text-muted);
}

.feature-badge.category .badge-icon svg {
    fill: var(--success-color);
}

.feature-badge.payment .badge-icon svg {
    fill: var(--primary-color);
}

.feature-badge.document .badge-icon svg {
    fill: var(--warning-color);
}

.badge-text {
    font-weight: var(--font-weight-medium);
    color: var(--text-color);
}

/* Floating Action Buttons - Aligned with document-viewer.css action-button */
.floating-action-buttons {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.fab {
    display: flex;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing);
    border-radius: var(--border-radius-pill);
    font-weight: var(--font-weight-medium);
    text-decoration: none;
    color: white;
    box-shadow: var(--box-shadow-sm);
    transition: var(--transition-fast);
    border: none;
    cursor: pointer;
    font-size: var(--font-size-sm);
}

.fab svg {
    width: 18px;
    height: 18px;
    margin-right: var(--spacing-sm);
    fill: currentColor;
}

.fab.edit {
    background-color: var(--primary-color);
}

.fab.view {
    background-color: var(--success-color);
}

.fab.delete {
    background-color: var(--danger-color);
}

.fab:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
    opacity: 0.9;
}

.fab:active {
    transform: translateY(0);
    box-shadow: var(--box-shadow-sm);
}

/* Responsive adjustments - Aligned with document-viewer.css */
@media (max-width: 768px) {
    .expense-premium-header {
        flex-direction: column;
    }

    .expense-amount-column {
        align-items: flex-start;
        padding-left: 0;
        margin-top: var(--spacing);
    }

    .amount-wrapper {
        text-align: left;
    }

    .feature-badges {
        flex-direction: column;
    }

    .floating-action-buttons {
        flex-wrap: wrap;
    }

    .document-file-info {
        flex-direction: column;
        align-items: flex-start;
    }

    .file-icon-container {
        margin-bottom: var(--spacing-sm);
    }

    .file-info-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .extraction-status {
        margin-top: var(--spacing-xs);
    }

    .file-info-details {
        flex-direction: column;
        gap: var(--spacing-xs);
    }

    .action-buttons-grid {
        grid-template-columns: 1fr;
    }

    .tab-button {
        padding: var(--spacing) var(--spacing-sm);
    }

    .tab-label {
        font-size: var(--font-size-sm);
    }
}

/* Document Components */
.document-summary-container {
    margin-bottom: var(--spacing-lg);
}

.document-header-card {
    display: flex;
    flex-direction: column;
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-sm);
    overflow: hidden;
    margin-bottom: var(--spacing-lg);
    border: 1px solid var(--border-color);
    transition: var(--transition-medium);
}

.document-header-card:hover {
    box-shadow: var(--box-shadow);
    transform: translateY(-2px);
}

/* Receipt Preview Styles - Aligned with document-viewer.css */
.receipt-preview {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-sm);
    overflow: hidden;
    margin-bottom: var(--spacing-lg);
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.receipt-preview:hover {
    box-shadow: var(--box-shadow);
    transform: translateY(-2px);
}

.receipt-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing) var(--spacing-lg);
    background-color: var(--grey-100);
    border-bottom: 1px solid var(--border-color);
}

.receipt-header h4 {
    display: flex;
    align-items: center;
    margin: 0;
    font-size: var(--font-size);
    font-weight: var(--font-weight-medium);
    color: var(--text-color);
}

.receipt-header svg {
    width: 18px;
    height: 18px;
    margin-right: var(--spacing-sm);
    fill: var(--text-muted);
}

.view-full-receipt {
    display: flex;
    align-items: center;
    color: var(--primary-color);
    text-decoration: none;
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    transition: var(--transition-fast);
}

.view-full-receipt:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

.view-full-receipt svg {
    width: 16px;
    height: 16px;
    margin-left: var(--spacing-xs);
    fill: currentColor;
}

.receipt-preview-content {
    padding: var(--spacing-lg);
}

.receipt-merchant-preview {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    text-align: center;
    margin-bottom: var(--spacing);
    padding-bottom: var(--spacing);
    border-bottom: 1px dashed var(--border-color);
}

.receipt-details-preview {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.receipt-row {
    display: flex;
    justify-content: space-between;
    font-size: var(--font-size-sm);
}

.receipt-label {
    color: var(--text-muted);
    font-weight: var(--font-weight-medium);
}

.receipt-value {
    font-weight: var(--font-weight-medium);
    color: var(--text-color);
}

.total-row {
    margin-top: var(--spacing-sm);
    padding-top: var(--spacing-sm);
    border-top: 1px solid var(--border-color);
    font-size: var(--font-size);
    font-weight: var(--font-weight-bold);
}

.total-row .receipt-value {
    color: var(--primary-color);
}

.extraction-summary {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: var(--spacing);
    padding: var(--spacing-sm);
    background-color: var(--success-light);
    border-radius: var(--border-radius);
    color: var(--success-dark);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
}

.extraction-summary svg {
    width: 16px;
    height: 16px;
    margin-right: var(--spacing-xs);
    fill: currentColor;
}

/* Document file info and thumbnail - Aligned with document-viewer.css */
.document-file-info {
    display: flex;
    align-items: center;
    padding: var(--spacing);
    margin-bottom: var(--spacing-lg);
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-color);
}

.file-icon-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    background-color: var(--grey-100);
    border-radius: var(--border-radius);
    margin-right: var(--spacing);
    flex-shrink: 0;
}

.file-icon-container .document-icon {
    width: 32px;
    height: 32px;
    fill: var(--primary-color);
}

.file-info-container {
    flex: 1;
}

.file-info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xs);
}

.file-name {
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size);
    color: var(--text-color);
}

.extraction-status {
    display: flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-pill);
    font-size: var(--font-size-xs);
    background-color: var(--success-light);
    color: var(--success-dark);
}

.extraction-status .status-icon {
    display: flex;
    margin-right: var(--spacing-xs);
}

.extraction-status .status-icon svg {
    width: 14px;
    height: 14px;
    fill: currentColor;
}

.file-info-details {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing) var(--spacing-lg);
    color: var(--text-muted);
    font-size: var(--font-size-xs);
}

/* Document thumbnail - Aligned with document-viewer.css */
.document-thumbnail {
    margin-top: var(--spacing);
}

.thumbnail-container {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--box-shadow-sm);
    border: 1px solid var(--border-color);
}

.thumbnail-image {
    width: 100%;
    height: auto;
    display: block;
}

/* Content Tabs - Aligned with document-viewer.css */
.content-tabs {
    display: flex;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing);
    border-bottom: 1px solid var(--border-color);
    position: relative;
}

.tab-button {
    display: flex;
    align-items: center;
    padding: var(--spacing) var(--spacing-lg);
    background: none;
    border: none;
    font-size: var(--font-size);
    font-weight: var(--font-weight-medium);
    color: var(--text-muted);
    cursor: pointer;
    position: relative;
    transition: var(--transition-fast);
}

.tab-button:hover {
    color: var(--primary-color);
}

.tab-button.active {
    color: var(--primary-color);
}

.tab-button.active .tab-indicator {
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: var(--primary-color);
    border-radius: var(--border-radius) var(--border-radius) 0 0;
}

.tab-icon-container {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: var(--spacing-sm);
    position: relative;
}

.tab-icon-container .svg-icon {
    width: 20px;
    height: 20px;
    fill: currentColor;
}

.document-indicator {
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    background-color: var(--success-color);
    border-radius: 50%;
}

.tab-content {
    display: none;
    padding: var(--spacing-lg) 0;
}

.tab-content.active {
    display: block;
}

/* Document Actions Section - Aligned with document-viewer.css */
.document-actions-section {
    margin-top: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.section-title {
    display: flex;
    align-items: center;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-medium);
    margin-bottom: var(--spacing);
    color: var(--text-color);
}

.section-title .action-icon {
    width: 24px;
    height: 24px;
    margin-right: var(--spacing-sm);
    fill: var(--primary-color);
}

.action-buttons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: var(--spacing);
    padding: var(--spacing);
}

.action-button {
    display: flex;
    align-items: center;
    padding: var(--spacing);
    border-radius: var(--border-radius);
    text-decoration: none;
    transition: var(--transition-fast);
    color: var(--text-color);
}

.action-button:hover {
    background-color: var(--grey-200);
    transform: translateY(-2px);
    box-shadow: var(--box-shadow-sm);
}

.action-icon-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--grey-200);
    margin-right: var(--spacing);
    flex-shrink: 0;
}

.primary-action-icon {
    background-color: var(--primary-light);
}

.action-icon-container .svg-icon {
    width: 24px;
    height: 24px;
    fill: var(--primary-color);
}

.action-text {
    flex: 1;
}

.action-title {
    display: block;
    font-weight: var(--font-weight-medium);
    margin-bottom: var(--spacing-xs);
}

.action-description {
    display: block;
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

/* Enhanced UI Elements */
.action-button.document-viewer-link {
    background: linear-gradient(to right, var(--primary-light), var(--primary-color));
    color: white;
}

.action-button.document-viewer-link:hover {
    background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
    transform: translateY(-3px);
    box-shadow: var(--box-shadow);
}

.action-button.document-viewer-link .action-icon-container {
    background-color: rgba(255, 255, 255, 0.2);
}

.action-button.document-viewer-link .svg-icon {
    fill: white;
}

.action-button.document-viewer-link .action-title,
.action-button.document-viewer-link .action-description {
    color: white;
}

.emotional-design {
    transition: all 0.3s ease;
}

.emotional-design:hover {
    transform: translateY(-3px);
}

.tab-button.has-document .document-indicator {
    animation: pulse 2s infinite;
}

/* Card Effect - Aligned with document-viewer.css */
.card-effect {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-sm);
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.card-effect:hover {
    box-shadow: var(--box-shadow);
    transform: translateY(-2px);
    border-color: var(--primary-light);
}

/* Reveal Effect - Aligned with document-viewer.css */
.reveal-effect {
    opacity: 0;
    transform: translateY(10px);
    animation: reveal 0.5s ease forwards;
}

/* Pulse Animation */

.pulse-once {
    animation: pulse 2s 1;
}

/* Enhanced UI Components */
.expense-amount-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    border-radius: var(--border-radius-pill);
    padding: var(--spacing-xs) var(--spacing);
    font-weight: var(--font-weight-bold);
    box-shadow: var(--box-shadow-sm);
    transition: var(--transition-fast);
}

.expense-amount-badge:hover {
    transform: translateY(-2px);
    box-shadow: var(--box-shadow);
}

.expense-amount-badge .amount-value {
    font-size: var(--font-size);
}

.page-header {
    margin-bottom: var(--spacing-lg);
}

.animate-entry {
    animation: fadeInDown 0.5s ease-out;
}

/* Enhanced Card Styles */
.card-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
}

.expense-card {
    background-color: var(--card-bg);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-sm);
    overflow: hidden;
    transition: var(--transition);
    border: 1px solid var(--border-color);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.expense-card:hover {
    box-shadow: var(--box-shadow);
    transform: translateY(-2px);
    border-color: var(--primary-light);
}

.card-header {
    padding: var(--spacing) var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    background-color: var(--grey-100);
}

.card-title {
    display: flex;
    align-items: center;
    margin: 0;
    font-size: var(--font-size);
    font-weight: var(--font-weight-medium);
    color: var(--text-color);
}

.card-title .svg-icon {
    width: 20px;
    height: 20px;
    margin-right: var(--spacing-sm);
    fill: var(--primary-color);
}

.card-body {
    padding: var(--spacing-lg);
}

.amount-highlight {
    background-color: var(--primary-light-faded);
    padding: var(--spacing);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-lg);
    text-align: center;
}

.amount-highlight .amount-label {
    display: block;
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    margin-bottom: var(--spacing-xs);
}

.amount-highlight .amount-value {
    display: block;
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
}

.card-content {
    margin-bottom: var(--spacing-lg);
}

.card-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--spacing-sm);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--border-color-light);
}

.card-row:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.card-label {
    color: var(--text-muted);
    font-weight: var(--font-weight-medium);
}

.card-value {
    font-weight: var(--font-weight-medium);
    color: var(--text-color);
    text-align: right;
}

.metadata-section {
    margin-top: var(--spacing);
}

.metadata-section summary {
    cursor: pointer;
    color: var(--primary-color);
    font-weight: var(--font-weight-medium);
    padding: var(--spacing-sm) 0;
}

.metadata-section summary:hover {
    color: var(--primary-dark);
}

.metadata-content {
    padding: var(--spacing) 0;
    background-color: var(--grey-50);
    border-radius: var(--border-radius);
    padding: var(--spacing);
    margin-top: var(--spacing-sm);
}

.notes-content {
    white-space: pre-line;
    line-height: 1.5;
}

/* Document Placeholder */
.document-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-xl);
    text-align: center;
    background-color: var(--grey-100);
    border-radius: var(--border-radius);
    margin-bottom: var(--spacing-lg);
    border: 1px dashed var(--border-color);
    transition: var(--transition);
}

.document-placeholder.emotional-design:hover {
    border-color: var(--primary-color);
    background-color: var(--primary-light-faded);
    transform: translateY(-3px);
    box-shadow: var(--box-shadow);
}

/* Placeholder Components */
.placeholder-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background-color: var(--primary-light);
    border-radius: 50%;
    margin-bottom: var(--spacing-lg);
    color: var(--primary-color);
    box-shadow: 0 0 0 8px rgba(var(--primary-rgb), 0.1);
}

.placeholder-icon.animated {
    animation: pulse 2s infinite;
}

.placeholder-icon .svg-icon {
    width: 40px;
    height: 40px;
    fill: var(--primary-color);
}

.placeholder-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-bold);
    color: var(--text-color);
    margin-bottom: var(--spacing);
}

.placeholder-content {
    margin-bottom: var(--spacing-lg);
}

.info-message {
    margin-bottom: var(--spacing-lg);
    color: var(--text-muted);
    max-width: 500px;
    line-height: 1.5;
}

/* Benefits Section */
.benefits-container {
    margin-bottom: var(--spacing-lg);
    background-color: white;
    padding: var(--spacing);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-sm);
    width: 100%;
    max-width: 500px;
}

.benefits-title {
    font-size: var(--font-size);
    font-weight: var(--font-weight-medium);
    margin-bottom: var(--spacing);
    color: var(--text-color);
}

.benefits-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.benefit-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.benefit-icon {
    width: 18px;
    height: 18px;
    fill: var(--success-color);
}

/* Placeholder Actions */
.placeholder-actions {
    width: 100%;
    max-width: 500px;
}

.document-add-action {
    background-color: var(--primary-color);
    color: white;
}

.document-add-action:hover {
    background-color: var(--primary-dark);
    color: white;
}