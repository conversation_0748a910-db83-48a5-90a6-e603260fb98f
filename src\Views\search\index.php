<div class="page-title-section">
    <div class="title-wrapper">
        <h2>Global Search</h2>
        <p class="subtitle">Search across all your expenses</p>
    </div>
    <div class="action-buttons">
        <a href="/dashboard" class="btn btn-outline" aria-label="Back">
            <i class="icon-arrow-left"></i>
            Back to Dashboard
        </a>
        <a href="/expenses/new" class="btn btn-primary" aria-label="Add Expense">
            <i class="icon-plus"></i>
            Add Expense
        </a>
    </div>
</div>

<?php include __DIR__ . '/components/messages.php'; ?>

<div class="search-container">
    <div class="search-filters">
        <h3>Search Filters</h3>
        <form method="GET" action="/search" role="search" class="search-form">
            <div class="form-group">
                <label for="search_query">Search Term</label>
                <input type="text" name="query" id="search_query"
                       value="<?= htmlspecialchars($_GET['query'] ?? '') ?>"
                       placeholder="Description, notes, merchant..."
                       required>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="date_from">From Date</label>
                    <input type="date" name="date_from" id="date_from"
                           value="<?= htmlspecialchars($_GET['date_from'] ?? '') ?>">
                </div>
                <div class="form-group">
                    <label for="date_to">To Date</label>
                    <input type="date" name="date_to" id="date_to"
                           value="<?= htmlspecialchars($_GET['date_to'] ?? '') ?>">
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="min_amount">Min Amount</label>
                    <div class="currency-input">
                        <div class="currency-symbol" aria-hidden="true">
                            <span>$</span>
                        </div>
                        <input type="number" step="0.01" min="0" name="min_amount" id="min_amount"
                               value="<?= htmlspecialchars($_GET['min_amount'] ?? '') ?>"
                               placeholder="0.00">
                    </div>
                </div>
                <div class="form-group">
                    <label for="max_amount">Max Amount</label>
                    <div class="currency-input">
                        <div class="currency-symbol" aria-hidden="true">
                            <span>$</span>
                        </div>
                        <input type="number" step="0.01" min="0" name="max_amount" id="max_amount"
                               value="<?= htmlspecialchars($_GET['max_amount'] ?? '') ?>"
                               placeholder="0.00">
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label for="category_id">Category</label>
                <select name="category_id" id="category_id">
                    <option value="">All Categories</option>
                    <?php foreach ($categories as $category) : ?>
                        <option value="<?= htmlspecialchars($category['id']) ?>"
                            <?= (isset($_GET['category_id']) && $_GET['category_id'] == $category['id']) ? 'selected' : '' ?>>
                            <?= htmlspecialchars($category['name']) ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div class="form-group">
                <label for="has_document">Document Status</label>
                <select name="has_document" id="has_document">
                    <option value="">Any</option>
                    <option value="1" <?= (isset($_GET['has_document']) && $_GET['has_document'] == '1') ? 'selected' : '' ?>>
                        With Documents
                    </option>
                    <option value="0" <?= (isset($_GET['has_document']) && $_GET['has_document'] == '0') ? 'selected' : '' ?>>
                        Without Documents
                    </option>
                </select>
            </div>

            <div class="form-actions">
                <button type="submit" class="button primary">Search</button>
                <a href="/search" class="button secondary">Reset</a>
            </div>
        </form>
    </div>

    <div class="search-instructions">
        <h3>Search Tips</h3>
        <div class="card">
            <ul>
                <li>Enter keywords to search through expense descriptions, merchant names, and notes</li>
                <li>Use the date range filters to narrow down your search to a specific time period</li>
                <li>Filter by category to focus on specific expense types</li>
                <li>Set minimum and maximum amounts to find expenses within a specific budget range</li>
                <li>Filter to show only expenses with or without attached documents</li>
            </ul>
        </div>

        <h3>Recent Searches</h3>
        <div class="card">
            <p class="text-muted">Your recent searches will appear here.</p>
        </div>
    </div>
</div>

<?php if (isset($query) && !empty($query)) : ?>
    <div class="search-summary">
        <p>
            Found <strong><?= count($expenses) ?></strong> result<?= count($expenses) != 1 ? 's' : '' ?>
            for "<strong><?= htmlspecialchars($query) ?></strong>"
            <?php if (!empty($_GET['date_from']) || !empty($_GET['date_to'])) : ?>
                in date range
                <?= !empty($_GET['date_from']) ? 'from ' . htmlspecialchars($_GET['date_from']) : '' ?>
                <?= !empty($_GET['date_to']) ? ' to ' . htmlspecialchars($_GET['date_to']) : '' ?>
            <?php endif; ?>
            <?php if (!empty($_GET['category_id'])) : ?>
                in category "<?= htmlspecialchars($categoryName ?? '') ?>"
            <?php endif; ?>
        </p>
        <?php if (!empty($filters)) : ?>
            <div class="active-filters">
                <span>Active filters:</span>
                <?php foreach ($filters as $filter) : ?>
                    <span class="filter-tag"><?= htmlspecialchars($filter) ?></span>
                <?php endforeach; ?>
                <a href="/search?query=<?= htmlspecialchars($query) ?>" class="button small">Clear Filters</a>
            </div>
        <?php endif; ?>
    </div>

    <?php if (empty($expenses)) : ?>
        <div class="notice" role="alert">
            No expenses found matching your search criteria. Try adjusting your filters or search terms.
        </div>
    <?php else : ?>
        <div class="table-responsive">
            <table aria-label="Search Results">
                <thead>
                    <tr>
                        <th scope="col">ID</th>
                        <th scope="col">Amount (USD)</th>
                        <th scope="col">Description</th>
                        <th scope="col">Date</th>
                        <th scope="col">Category</th>
                        <th scope="col">Notes</th>
                        <th scope="col">Document</th>
                        <th scope="col">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($expenses as $expense) : ?>
                        <tr>
                            <td><?= htmlspecialchars($expense['id']) ?></td>
                            <td>$<?= number_format($expense['amount'], 2) ?></td>
                            <td>
                                <?= htmlspecialchars($expense['description']) ?>
                                <?php if (!empty($expense['merchant_name'])) : ?>
                                    <span class="meta-info"><?= htmlspecialchars($expense['merchant_name']) ?></span>
                                <?php endif; ?>
                            </td>
                            <td><?= date('M d, Y', strtotime($expense['date'])) ?></td>
                            <td><?= htmlspecialchars($expense['category_name'] ?? 'Uncategorized') ?></td>
                            <td>
                                <?php if (!empty($expense['notes'])) : ?>
                                    <button type="button" class="show-notes"
                                            data-notes="<?= htmlspecialchars($expense['notes']) ?>"
                                            aria-label="View notes for expense <?= htmlspecialchars($expense['id']) ?>">
                                        View Notes
                                    </button>
                                <?php else : ?>
                                    <span class="text-muted">None</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if (!empty($expense['file_id'])) : ?>
                                    <span class="document-indicator" aria-label="Has document">📄</span>
                                <?php else : ?>
                                    <span class="text-muted">None</span>
                                <?php endif; ?>
                            </td>
                            <td class="actions">
                                <a href="/expenses/<?= htmlspecialchars($expense['id']) ?>" class="button small" aria-label="View expense details">
                                    View
                                </a>
                                <a href="/expenses/<?= htmlspecialchars($expense['id']) ?>/edit" class="button small" aria-label="Edit expense">
                                    Edit
                                </a>

                                <?php if (!empty($expense['file_id'])) : ?>
                                    <a href="/expenses/<?= htmlspecialchars($expense['id']) ?>/view" class="button small" aria-label="View document">
                                        Document
                                    </a>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <?php if (isset($pagination)) : ?>
        <nav aria-label="Pagination" class="pagination">
            <?php if ($pagination['currentPage'] > 1) : ?>
                <a href="/search?query=<?= htmlspecialchars($query) ?>&page=<?= $pagination['currentPage'] - 1 ?><?= http_build_query(array_diff_key($_GET, ['query' => '', 'page' => ''])) ? '&' . http_build_query(array_diff_key($_GET, ['query' => '', 'page' => ''])) : '' ?>" class="button pagination-button">&laquo; Previous</a>
            <?php endif; ?>

            <span class="pagination-info">Page <?= $pagination['currentPage'] ?> of <?= $pagination['totalPages'] ?></span>

            <?php if ($pagination['currentPage'] < $pagination['totalPages']) : ?>
                <a href="/search?query=<?= htmlspecialchars($query) ?>&page=<?= $pagination['currentPage'] + 1 ?><?= http_build_query(array_diff_key($_GET, ['query' => '', 'page' => ''])) ? '&' . http_build_query(array_diff_key($_GET, ['query' => '', 'page' => ''])) : '' ?>" class="button pagination-button">Next &raquo;</a>
            <?php endif; ?>
        </nav>
        <?php endif; ?>
    <?php endif; ?>
<?php else : ?>
    <div class="search-instructions">
        <h3>Search Tips</h3>
        <ul>
            <li>Enter keywords to search through expense descriptions, merchant names, and notes</li>
            <li>Use the date range filters to narrow down your search to a specific time period</li>
            <li>Filter by category to focus on specific expense types</li>
            <li>Set minimum and maximum amounts to find expenses within a specific budget range</li>
            <li>Filter to show only expenses with or without attached documents</li>
        </ul>
    </div>
<?php endif; ?>

<!-- Modal for displaying notes -->
<div id="notesModal" class="modal" role="dialog" aria-labelledby="notesModalTitle" aria-hidden="true">
    <div class="modal-content">
        <span class="close" aria-label="Close">&times;</span>
        <h3 id="notesModalTitle">Expense Notes</h3>
        <pre id="notesContent"></pre>
    </div>
</div>

<?php
// Add search-specific scripts
$scripts ??= [];
$scripts[] = '/assets/js/pages/search.js';
?>
