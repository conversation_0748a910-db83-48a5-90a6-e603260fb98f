/* ===== ERROR PAGES STYLES ===== */

/* Error Page Layout */
.error-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
  max-width: 600px;
  margin: 0 auto;
}

/* Error Title */
.error-title {
  text-align: center;
  margin-bottom: var(--spacing-md);
  color: var(--dark-color);
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-xxl);
}

/* Error Container */
.error-container {
  width: 100%;
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: var(--spacing-lg);
  border-top: 4px solid var(--danger-color);
}

/* Error Message */
.error-message {
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-md);
  line-height: 1.5;
}

/* Error Details */
.error-details {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  margin-bottom: var(--spacing-md);
  padding: var(--spacing);
  background-color: var(--grey-100);
  border-radius: var(--border-radius);
  border-left: 3px solid var(--grey-300);
}

/* Error Actions */
.error-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-md);
  text-align: center;
}

.error-actions a {
  display: inline-block;
  padding: var(--spacing) var(--spacing-lg);
  background-color: var(--primary-color);
  color: white;
  text-decoration: none;
  border-radius: var(--border-radius);
  font-weight: var(--font-weight-medium);
  transition: var(--transition);
}

.error-actions a:hover {
  background-color: var(--primary-dark);
  transform: translateY(-2px);
}

.error-actions a.secondary {
  background-color: var(--grey-200);
  color: var(--text-color);
}

.error-actions a.secondary:hover {
  background-color: var(--grey-300);
}

/* Error Code Display */
.error-code {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  margin-bottom: var(--spacing-xs);
  text-align: center;
  display: block;
}

/* Error Icon */
.error-icon {
  font-size: 4rem;
  color: var(--danger-color);
  margin-bottom: var(--spacing);
  text-align: center;
  display: block;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .error-title {
    font-size: var(--font-size-xl);
  }

  .error-container {
    padding: var(--spacing);
  }
}
