/* ===== MAIN CONTENT AREA ===== */
main {
  padding-top: var(--spacing);
}

/* ===== CONTAINER ===== */
.container {
  width: 100%;
  max-width: var(--container-max-width);
  padding: var(--spacing-lg);
  margin: 0 auto;
  flex: 1 0 auto;
  transition: var(--transition);
}

@media (max-width: 992px) {
  .container {
    margin-left: 0;
    padding: var(--spacing);
  }
}

/* ===== DASHBOARD GRID ===== */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-sm) var(--spacing);
}

.dashboard-card {
  background-color: var(--light-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow);
  padding: var(--spacing-md);
  height: 100%;
  transition: var(--transition);
  border: 1px solid var(--grey-200);
}

.dashboard-card:hover {
  box-shadow: var(--box-shadow-lg);
  transform: translateY(-2px);
}

/* ===== STATS GRID ===== */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: var(--spacing);
}

.stat-card {
  background-color: var(--grey-100);
  border-radius: var(--border-radius);
  padding: var(--spacing);
  transition: var(--transition-fast);
}

.stat-card:hover {
  box-shadow: var(--box-shadow);
  transform: translateY(-3px);
  transition: var(--transition-bounce);
}

.stat-title {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
  margin-bottom: var(--spacing-xs);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--dark-color);
  margin-bottom: var(--spacing-xs);
}

.stat-projected {
  font-size: var(--font-size-xs);
  color: var(--primary-color);
}

.stat-label {
  font-size: var(--font-size-xs);
  color: var(--text-muted);
}

/* ===== EXPENSE LIST ===== */
.expense-list {
  margin-bottom: var(--spacing);
}

.expense-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing);
  border-bottom: 1px solid var(--grey-200);
  transition: var(--transition-fast);
}

.expense-item:last-child {
  border-bottom: none;
}

.expense-item:hover {
  background-color: var(--grey-100);
}

.expense-info {
  display: flex;
  align-items: center;
}

.expense-date {
  min-width: 60px;
  color: var(--text-muted);
  font-size: var(--font-size-sm);
}

.expense-details {
  display: flex;
  flex-direction: column;
  margin-left: var(--spacing);
}

.expense-description {
  font-weight: var(--font-weight-medium);
  margin-bottom: 2px;
}

.expense-category {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  background-color: var(--grey-200);
  padding: 2px 8px;
  border-radius: 12px;
  display: inline-block;
  font-weight: var(--font-weight-medium);
  text-transform: capitalize;
}

.expense-amount {
  font-weight: var(--font-weight-semibold);
  color: var(--dark-color);
  font-family: 'Nunito', monospace;
  letter-spacing: -0.5px;
}

.widget-footer {
  margin-top: var(--spacing);
  text-align: center;
}

/* ===== DASHBOARD ACTIONS ===== */
.dashboard-actions {
  display: flex;
  gap: var(--spacing);
  margin: 0 var(--spacing-lg) var(--spacing);
}

/* ===== QUICK INSIGHTS ===== */
.quick-insights {
  background-color: var(--light-color);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--box-shadow);
  padding: var(--spacing-md);
  margin: 0 var(--spacing-lg) var(--spacing);
}

.insight-card {
  margin-bottom: var(--spacing);
}

.chart-container {
  height: 200px;
  margin: var(--spacing) 0;
  padding: var(--spacing) 0;
  border-bottom: 1px solid var(--grey-200);
  border-top: 1px solid var(--grey-200);
}

.category-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing);
}

.category-list li {
  margin-bottom: var(--spacing);
}

.category-name {
  display: block;
  font-weight: var(--font-weight-medium);
  margin-bottom: 4px;
}

.category-amount {
  display: block;
  color: var(--text-muted);
  font-size: var(--font-size-sm);
  margin-bottom: 4px;
}

.progress-bar {
  height: 6px;
  background-color: var(--grey-200);
  border-radius: 3px;
  overflow: hidden;
}

.progress {
  position: relative;
  height: 100%;
  background-color: var(--primary-color);
  border-radius: 3px;
  transition: width 0.3s ease;
  overflow: hidden;
}

.progress::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to right, transparent, rgba(255,255,255,0.2), transparent);
  animation: progress-shine 2s infinite linear;
}

@keyframes progress-shine {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}