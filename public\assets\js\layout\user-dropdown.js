function initializeUserDropdown()
{
    const dropdownElements = getDropdownElements();
    if (!dropdownElements) {
        return;
    }

    const { dropdownToggle, dropdownMenu } = dropdownElements;

    setupUserDropdownToggle(dropdownToggle, dropdownMenu);
    setupUserDropdownKeyboardNavigation(dropdownMenu);
}

function getDropdownElements()
{
    const dropdownToggle = document.querySelector('.user-dropdown-toggle');
    const dropdownMenu = document.getElementById('user-dropdown');

    if (!dropdownToggle || !dropdownMenu) {
        return null;
    }

    return { dropdownToggle, dropdownMenu };
}

function setupUserDropdownToggle(dropdownToggle, dropdownMenu)
{
    dropdownToggle.addEventListener('click', e => {
        e.preventDefault();
        e.stopPropagation();

        toggleDropdown(dropdownToggle, dropdownMenu);
    });

    setupEscapeKeyHandler(dropdownToggle, dropdownMenu);
}

function toggleDropdown(dropdownToggle, dropdownMenu)
{
    const isExpanded = dropdownToggle.getAttribute('aria-expanded') === 'true';
    const newExpandedState = !isExpanded;

    updateDropdownState(dropdownToggle, dropdownMenu, newExpandedState);

    if (newExpandedState) {
        setupOutsideClickHandler(dropdownToggle, dropdownMenu);
        focusFirstMenuItem(dropdownMenu);
    }
}

function updateDropdownState(dropdownToggle, dropdownMenu, isExpanded)
{
    dropdownToggle.setAttribute('aria-expanded', isExpanded);
    dropdownMenu.setAttribute('aria-hidden', !isExpanded);
    dropdownMenu.setAttribute('aria-expanded', isExpanded);

    if (isExpanded) {
        dropdownMenu.classList.add('show');
    } else {
        dropdownMenu.classList.remove('show');
    }
}

function setupOutsideClickHandler(dropdownToggle, dropdownMenu)
{
    document.addEventListener('click', function closeDropdownOnOutsideClick(e)
    {
        if (!dropdownToggle.contains(e.target) && !dropdownMenu.contains(e.target)) {
            closeDropdown(dropdownToggle, dropdownMenu);
            document.removeEventListener('click', closeDropdownOnOutsideClick);
        }
    });
}

function focusFirstMenuItem(dropdownMenu)
{
    setTimeout(() => {
        const firstItem = dropdownMenu.querySelector('[role="menuitem"]');
        if (firstItem) {
            firstItem.focus();
        }
    }, 100);
}

function setupEscapeKeyHandler(dropdownToggle, dropdownMenu)
{
    document.addEventListener('keydown', e => {
        if (e.key === 'Escape' && dropdownToggle.getAttribute('aria-expanded') === 'true') {
            closeDropdown(dropdownToggle, dropdownMenu);
            dropdownToggle.focus();
        }
    });
}

function closeDropdown(dropdownToggle, dropdownMenu)
{
    updateDropdownState(dropdownToggle, dropdownMenu, false);
}

function setupUserDropdownKeyboardNavigation(dropdownMenu)
{
    const menuItems = dropdownMenu.querySelectorAll('[role="menuitem"]');
    if (menuItems.length === 0) {
        return;
    }

    menuItems.forEach((item, index) => {
        item.addEventListener('keydown', e => {
            handleMenuItemKeypress(e, index, menuItems);
        });
    });
}

function handleMenuItemKeypress(event, currentIndex, menuItems)
{
    switch (event.key) {
        case 'ArrowUp':
            if (currentIndex > 0) {
                event.preventDefault();
                menuItems[currentIndex - 1].focus();
            }
            break;

        case 'ArrowDown':
            if (currentIndex < menuItems.length - 1) {
                event.preventDefault();
                menuItems[currentIndex + 1].focus();
            }
            break;

        case 'Home':
            event.preventDefault();
            menuItems[0].focus();
            break;

        case 'End':
            event.preventDefault();
            menuItems[menuItems.length - 1].focus();
            break;
    }
}

export { initializeUserDropdown };
