<?php

/**
 * Standardized messages component
 *
 * Displays success, error, warning, and info messages from session flash data
 * and directly passed variables
 */

// Helper function to safely escape values
if (!function_exists('e')) {
    function e($value)
    {
        if (is_null($value)) {
            return '';
        }
        if (is_array($value)) {
            return '';
        }
        if (is_object($value) && !method_exists($value, '__toString')) {
            return '';
        }
        return htmlspecialchars((string)$value, ENT_QUOTES, 'UTF-8');
    }
}

use App\Core\Security\Session;

// Define message types and their corresponding CSS classes
$messageTypes = [
    'success' => 'success',
    'error' => 'error',
    'warning' => 'warning',
    'info' => 'info'
];

// Check if we have any messages to display
$hasMessages = false;

// Check for directly passed messages
foreach ($messageTypes as $type => $class) {
    if (!empty($$type)) {
        $hasMessages = true;
        break;
    }
}

// Check for session messages
foreach ($messageTypes as $type => $class) {
    if (Session::has($type)) {
        $hasMessages = true;
        break;
    }
}

// Check for validation errors
$hasErrors = Session::has('errors');
$hasMessages = $hasMessages || $hasErrors;

// If no messages, return early
if (!$hasMessages) {
    return;
}
?>

<div class="messages-container">
    <!-- Direct passed messages -->
    <?php foreach ($messageTypes as $type => $class) : ?>
        <?php if (!empty($$type)) : ?>
            <div class="message <?= $class ?>" role="alert">
                <div class="message-content">
                    <div class="message-icon">
                        <?php if ($type === 'success') : ?>
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"/>
                            </svg>
                        <?php elseif ($type === 'error') : ?>
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"/>
                            </svg>
                        <?php elseif ($type === 'warning') : ?>
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                                <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/>
                            </svg>
                        <?php elseif ($type === 'info') : ?>
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/>
                            </svg>
                        <?php endif; ?>
                    </div>
                    <div class="message-text">
                        <?= e($$type) ?>
                    </div>
                </div>
                <button type="button" class="message-close" aria-label="Close">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"/>
                    </svg>
                </button>
            </div>
        <?php endif; ?>
    <?php endforeach; ?>

    <!-- Session messages -->
    <?php foreach ($messageTypes as $type => $class) : ?>
        <?php if (Session::has($type)) : ?>
            <?php $message = Session::get($type); ?>
            <div class="message <?= $class ?>" role="alert">
                <div class="message-content">
                    <div class="message-icon">
                        <?php if ($type === 'success') : ?>
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z"/>
                            </svg>
                        <?php elseif ($type === 'error') : ?>
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"/>
                            </svg>
                        <?php elseif ($type === 'warning') : ?>
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                                <path d="M1 21h22L12 2 1 21zm12-3h-2v-2h2v2zm0-4h-2v-4h2v4z"/>
                            </svg>
                        <?php elseif ($type === 'info') : ?>
                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-6h2v6zm0-8h-2V7h2v2z"/>
                            </svg>
                        <?php endif; ?>
                    </div>
                    <div class="message-text">
                        <?= e($message) ?>
                    </div>
                </div>
                <button type="button" class="message-close" aria-label="Close">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                        <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"/>
                    </svg>
                </button>
            </div>
            <?php Session::remove($type); ?>
        <?php endif; ?>
    <?php endforeach; ?>

    <!-- Validation errors -->
    <?php if ($hasErrors) : ?>
        <?php $errors = Session::get('errors'); ?>
        <?php if (!is_iterable($errors)) {
            $errors = [$errors];
        } ?>
        <div class="message error" role="alert">
            <div class="message-content">
                <div class="message-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 15h-2v-2h2v2zm0-4h-2V7h2v6z"/>
                    </svg>
                </div>
                <div class="message-text">
                    <?php if (is_array($errors)) : ?>
                        <ul class="error-list">
                            <?php foreach ($errors as $error) : ?>
                                <li><?= e($error) ?></li>
                            <?php endforeach; ?>
                        </ul>
                    <?php else : ?>
                        <?= e($errors) ?>
                    <?php endif; ?>
                </div>
            </div>
            <button type="button" class="message-close" aria-label="Close">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24">
                    <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12 19 6.41z"/>
                </svg>
            </button>
        </div>
        <?php Session::remove('errors'); ?>
    <?php endif; ?>
</div>

<script>
    // Add event listeners to close buttons
    document.addEventListener('DOMContentLoaded', function() {
        const closeButtons = document.querySelectorAll('.message-close');
        closeButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const message = button.closest('.message');
                message.classList.add('message-hidden');
                setTimeout(function() {
                    message.remove();
                }, 300);
            });
        });

        // Auto-dismiss success messages after 5 seconds
        const successMessages = document.querySelectorAll('.message.success');
        successMessages.forEach(function(message) {
            setTimeout(function() {
                message.classList.add('message-hidden');
                setTimeout(function() {
                    message.remove();
                }, 300);
            }, 5000);
        });
    });
</script>
