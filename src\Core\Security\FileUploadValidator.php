<?php

declare(strict_types=1);

namespace App\Core\Security;

use App\Exceptions\FileUploadException;

final class FileUploadValidator
{
    private const ALLOWED_MIME_TYPES = [
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp',
        'application/pdf',
        'text/plain',
        'text/csv',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ];

    private const ALLOWED_EXTENSIONS = [
        'jpg', 'jpeg', 'png', 'gif', 'webp',
        'pdf', 'txt', 'csv', 'xls', 'xlsx'
    ];

    private const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
    private const MAX_FILENAME_LENGTH = 255;

    private array $allowedMimeTypes;
    private array $allowedExtensions;
    private int $maxFileSize;
    private SecurityLogger $securityLogger;

    public function __construct(
        ?array $allowedMimeTypes = null,
        ?array $allowedExtensions = null,
        ?int $maxFileSize = null,
        ?SecurityLogger $securityLogger = null
    ) {
        $this->allowedMimeTypes = $allowedMimeTypes ?? self::ALLOWED_MIME_TYPES;
        $this->allowedExtensions = $allowedExtensions ?? self::ALLOWED_EXTENSIONS;
        $this->maxFileSize = $maxFileSize ?? self::MAX_FILE_SIZE;
        $this->securityLogger = $securityLogger ?? new SecurityLogger();
    }

    public function validate(array $file): array
    {
        $this->validateUploadError($file);
        $this->validateFileSize($file);
        $this->validateFileName($file);
        $this->validateMimeType($file);
        $this->validateFileExtension($file);
        $this->validateFileContent($file);
        
        $sanitizedFile = $this->sanitizeFile($file);
        
        $this->securityLogger->logFileUpload(
            $sanitizedFile['name'],
            $sanitizedFile['size'],
            $sanitizedFile['type'],
            true,
            ['validation' => 'passed']
        );
        
        return $sanitizedFile;
    }

    private function validateUploadError(array $file): void
    {
        if (!isset($file['error'])) {
            throw new FileUploadException('Upload error information missing');
        }

        switch ($file['error']) {
            case UPLOAD_ERR_OK:
                return;
            case UPLOAD_ERR_INI_SIZE:
            case UPLOAD_ERR_FORM_SIZE:
                throw new FileUploadException('File size exceeds maximum allowed size');
            case UPLOAD_ERR_PARTIAL:
                throw new FileUploadException('File was only partially uploaded');
            case UPLOAD_ERR_NO_FILE:
                throw new FileUploadException('No file was uploaded');
            case UPLOAD_ERR_NO_TMP_DIR:
                throw new FileUploadException('Missing temporary folder');
            case UPLOAD_ERR_CANT_WRITE:
                throw new FileUploadException('Failed to write file to disk');
            case UPLOAD_ERR_EXTENSION:
                throw new FileUploadException('File upload stopped by extension');
            default:
                throw new FileUploadException('Unknown upload error');
        }
    }

    private function validateFileSize(array $file): void
    {
        if (!isset($file['size']) || !is_int($file['size'])) {
            throw new FileUploadException('Invalid file size information');
        }

        if ($file['size'] <= 0) {
            throw new FileUploadException('File is empty');
        }

        if ($file['size'] > $this->maxFileSize) {
            throw new FileUploadException(
                sprintf('File size (%d bytes) exceeds maximum allowed size (%d bytes)', 
                    $file['size'], $this->maxFileSize)
            );
        }
    }

    private function validateFileName(array $file): void
    {
        if (!isset($file['name']) || !is_string($file['name'])) {
            throw new FileUploadException('Invalid filename');
        }

        $filename = $file['name'];
        
        if (empty($filename)) {
            throw new FileUploadException('Filename cannot be empty');
        }

        if (strlen($filename) > self::MAX_FILENAME_LENGTH) {
            throw new FileUploadException('Filename is too long');
        }

        // Check for dangerous characters
        if (preg_match('/[\x00-\x1f\x7f-\x9f\/\\:*?"<>|]/', $filename)) {
            throw new FileUploadException('Filename contains invalid characters');
        }

        // Check for dangerous filenames
        $dangerousNames = ['con', 'prn', 'aux', 'nul', 'com1', 'com2', 'com3', 'com4', 'com5', 'com6', 'com7', 'com8', 'com9', 'lpt1', 'lpt2', 'lpt3', 'lpt4', 'lpt5', 'lpt6', 'lpt7', 'lpt8', 'lpt9'];
        $nameWithoutExt = pathinfo($filename, PATHINFO_FILENAME);
        
        if (in_array(strtolower($nameWithoutExt), $dangerousNames)) {
            throw new FileUploadException('Filename is not allowed');
        }
    }

    private function validateMimeType(array $file): void
    {
        if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
            throw new FileUploadException('Invalid uploaded file');
        }

        // Get MIME type using finfo (more reliable than $_FILES['type'])
        $finfo = finfo_open(FILEINFO_MIME_TYPE);
        if ($finfo === false) {
            throw new FileUploadException('Unable to determine file type');
        }

        $mimeType = finfo_file($finfo, $file['tmp_name']);
        finfo_close($finfo);

        if ($mimeType === false) {
            throw new FileUploadException('Unable to determine file MIME type');
        }

        if (!in_array($mimeType, $this->allowedMimeTypes)) {
            $this->securityLogger->logSuspiciousActivity(
                'Disallowed file type upload attempt',
                ['mime_type' => $mimeType, 'filename' => $file['name']]
            );
            throw new FileUploadException(
                sprintf('File type "%s" is not allowed', $mimeType)
            );
        }
    }

    private function validateFileExtension(array $file): void
    {
        $filename = $file['name'];
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));

        if (empty($extension)) {
            throw new FileUploadException('File must have an extension');
        }

        if (!in_array($extension, $this->allowedExtensions)) {
            $this->securityLogger->logSuspiciousActivity(
                'Disallowed file extension upload attempt',
                ['extension' => $extension, 'filename' => $filename]
            );
            throw new FileUploadException(
                sprintf('File extension "%s" is not allowed', $extension)
            );
        }
    }

    private function validateFileContent(array $file): void
    {
        $tmpName = $file['tmp_name'];
        
        // Read first few bytes to check for malicious content
        $handle = fopen($tmpName, 'rb');
        if ($handle === false) {
            throw new FileUploadException('Unable to read uploaded file');
        }

        $header = fread($handle, 1024);
        fclose($handle);

        if ($header === false) {
            throw new FileUploadException('Unable to read file header');
        }

        // Check for PHP tags in any file
        if (strpos($header, '<?php') !== false || strpos($header, '<?=') !== false) {
            $this->securityLogger->logSuspiciousActivity(
                'PHP code detected in uploaded file',
                ['filename' => $file['name']]
            );
            throw new FileUploadException('File contains potentially dangerous content');
        }

        // Check for script tags
        if (stripos($header, '<script') !== false) {
            $this->securityLogger->logSuspiciousActivity(
                'Script tags detected in uploaded file',
                ['filename' => $file['name']]
            );
            throw new FileUploadException('File contains potentially dangerous content');
        }

        // Additional checks for image files
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (in_array($extension, ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
            $this->validateImageFile($tmpName, $file['name']);
        }
    }

    private function validateImageFile(string $tmpName, string $filename): void
    {
        // Verify it's actually an image
        $imageInfo = getimagesize($tmpName);
        if ($imageInfo === false) {
            throw new FileUploadException('File is not a valid image');
        }

        // Check image dimensions (prevent extremely large images)
        [$width, $height] = $imageInfo;
        $maxDimension = 4096; // 4K resolution
        
        if ($width > $maxDimension || $height > $maxDimension) {
            throw new FileUploadException(
                sprintf('Image dimensions (%dx%d) exceed maximum allowed (%dx%d)',
                    $width, $height, $maxDimension, $maxDimension)
            );
        }

        // Verify MIME type matches extension
        $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
        $expectedMimeTypes = [
            'jpg' => 'image/jpeg',
            'jpeg' => 'image/jpeg',
            'png' => 'image/png',
            'gif' => 'image/gif',
            'webp' => 'image/webp'
        ];

        if (isset($expectedMimeTypes[$extension]) && $imageInfo['mime'] !== $expectedMimeTypes[$extension]) {
            throw new FileUploadException('File extension does not match image type');
        }
    }

    private function sanitizeFile(array $file): array
    {
        // Sanitize filename
        $filename = $file['name'];
        $extension = pathinfo($filename, PATHINFO_EXTENSION);
        $basename = pathinfo($filename, PATHINFO_FILENAME);
        
        // Remove any remaining dangerous characters and normalize
        $basename = preg_replace('/[^a-zA-Z0-9._-]/', '_', $basename);
        $basename = trim($basename, '._-');
        
        // Ensure filename is not empty after sanitization
        if (empty($basename)) {
            $basename = 'file_' . uniqid();
        }
        
        $sanitizedFilename = $basename . '.' . $extension;
        
        return [
            'name' => $sanitizedFilename,
            'type' => $file['type'],
            'size' => $file['size'],
            'tmp_name' => $file['tmp_name'],
            'error' => $file['error'],
            'original_name' => $file['name']
        ];
    }

    public function getAllowedMimeTypes(): array
    {
        return $this->allowedMimeTypes;
    }

    public function getAllowedExtensions(): array
    {
        return $this->allowedExtensions;
    }

    public function getMaxFileSize(): int
    {
        return $this->maxFileSize;
    }
}