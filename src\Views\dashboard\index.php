<div class="dashboard-header">
    <h2>Dashboard</h2>
    <div class="dashboard-actions">
        <button id="refreshDashboard" class="button secondary">
            <svg class="icon"><use xlink:href="/assets/icons.svg#icon-chart"></use></svg> Refresh
        </button>
        <a href="/expenses/new" class="button primary">
            <svg class="icon"><use xlink:href="/assets/icons.svg#icon-add"></use></svg> Add Expense
        </a>
    </div>
</div>

<?php include __DIR__ . '/../shared/messages.php'; ?>

<div class="dashboard-filter">
    <form method="GET" action="/dashboard" class="date-filter-form">
        <label for="dashboardDateFilter">Time Period:</label>
        <select name="period" id="dashboardDateFilter">
            <option value="month" <?= ($period ?? 'month') === 'month' ? 'selected' : '' ?>>This Month</option>
            <option value="quarter" <?= ($period ?? '') === 'quarter' ? 'selected' : '' ?>>This Quarter</option>
            <option value="year" <?= ($period ?? '') === 'year' ? 'selected' : '' ?>>This Year</option>
            <option value="all" <?= ($period ?? '') === 'all' ? 'selected' : '' ?>>All Time</option>
        </select>
    </form>
</div>

<div class="dashboard-grid">
    <div class="dashboard-column main-column">
        <?php include __DIR__ . '/widgets/summary_stats.php'; ?>
        <?php include __DIR__ . '/widgets/expense_chart.php'; ?>
    </div>

    <div class="dashboard-column side-column">
        <?php include __DIR__ . '/widgets/recent_expenses.php'; ?>
        <?php include __DIR__ . '/widgets/category_breakdown.php'; ?>
    </div>
</div>

<div class="quick-actions">
    <h3>Quick Actions</h3>
    <div class="action-buttons">
        <a href="/expenses/new" class="action-button">
            <span class="action-icon">
                <svg class="icon icon-expense"><use xlink:href="/assets/icons.svg#icon-expense"></use></svg>
            </span>
            <span class="action-text">New Expense</span>
        </a>
        <a href="/reports" class="action-button">
            <span class="action-icon">
                <svg class="icon icon-lg"><use xlink:href="/assets/icons.svg#icon-reports"></use></svg>
            </span>
            <span class="action-text">Reports</span>
        </a>
        <a href="/categories" class="action-button">
            <span class="action-icon">
                <svg class="icon icon-lg"><use xlink:href="/assets/icons.svg#icon-categories"></use></svg>
            </span>
            <span class="action-text">Categories</span>
        </a>
        <a href="/search" class="action-button">
            <span class="action-icon">
                <svg class="icon icon-lg"><use xlink:href="/assets/icons.svg#icon-search"></use></svg>
            </span>
            <span class="action-text">Search</span>
        </a>
    </div>
</div>

<?php
// Add Chart.js and dashboard.js to the scripts array
$scripts ??= [];
$scripts[] = 'https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js';
$scripts[] = '/assets/js/pages/dashboard.js';
?>
