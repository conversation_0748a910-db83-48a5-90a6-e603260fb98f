<?php

declare(strict_types=1);

namespace App\Services;

use App\Exceptions\InvalidArgumentException;
use App\Exceptions\NotFoundException;
use App\Models\Category;
use App\Models\Merchant;
use App\Models\PaymentMethod;
use App\Models\Receipt;

class ReceiptParserService
{
    private const MERCHANT_PATTERNS = [
        '/(?:Merchant|Store|Restaurant|Vendor|Business):\s*([^\n]+)/',
        '/(?:Thank you for shopping at|Receipt from|Sold by):\s*([^\n]+)/',
        '/(?:Welcome to|Receipt)\s+-\s+([^\n]+)/',
    ];
    private const SIMILARITY_THRESHOLD = 70;

    private array $merchantNames = [];
    private array $merchantPatterns = [];
    private int $currentUserId = 0;
    private FileService $fileService;
    private ExpenseService $expenseService;
    private MerchantService $merchantService;

    public function __construct(FileService $fileService, ExpenseService $expenseService, MerchantService $merchantService)
    {
        $this->fileService = $fileService;
        $this->expenseService = $expenseService;
        $this->merchantService = $merchantService;
        $this->merchantPatterns = self::MERCHANT_PATTERNS;
    }

    public function processReceiptFile(int $fileId, int $userId): array
    {
        try {
            $fileInfo = $this->validateFileAccess($fileId, $userId);
            $fileContent = $this->getValidFileContent($fileId);

            return $this->parseReceiptContent(
                $fileContent,
                $userId,
                $fileInfo['original_name'],
                $fileId
            );
        } catch (NotFoundException $e) {
            throw $e;
        } catch (\Exception $e) {
            throw new InvalidArgumentException("Error processing receipt file: " . $e->getMessage());
        }
    }

    private function validateFileAccess(int $fileId, int $userId): array
    {
        $fileInfo = $this->fileService->getFileInfo($fileId);
        if (!$fileInfo) {
            throw new NotFoundException("File not found: ID {$fileId}");
        }

        if ($fileInfo['user_id'] !== $userId) {
            throw new InvalidArgumentException("File does not belong to user");
        }

        return $fileInfo;
    }

    private function getValidFileContent(int $fileId): string
    {
        $fileContent = $this->fileService->getFileContent($fileId);
        if (empty($fileContent)) {
            throw new InvalidArgumentException("File is empty or could not be read");
        }
        return $fileContent;
    }

    public function parseReceiptContent(
        string $content,
        int $userId,
        string $filename,
        ?int $fileId = null
    ): array {
        if (empty($content)) {
            throw new InvalidArgumentException('Receipt file is empty');
        }

        $this->ensureUserDataLoaded($userId);
        $parsedData = $this->parseAndEnhanceContent($content, $userId);

        return $this->buildReceiptData($parsedData, $userId, $filename, $fileId, $content);
    }

    private function parseAndEnhanceContent(string $content, int $userId): array
    {
        $parsedData = Receipt::parseContent($content);
        if (empty($parsedData)) {
            throw new InvalidArgumentException('Failed to parse receipt content');
        }

        if (isset($parsedData['merchant'])) {
            $parsedData['merchant'] = $this->enhanceMerchantDetection(
                $parsedData['merchant'],
                $content,
                $userId
            );
        }

        return $parsedData;
    }

    private function buildReceiptData(array $parsedData, int $userId, string $filename, ?int $fileId, string $content): array
    {
        return array_merge($parsedData, [
            'user_id' => $userId,
            'filename' => $filename,
            'file_id' => $fileId,
            'content' => $content,
        ]);
    }

    private function enhanceMerchantDetection(string $detectedMerchant, string $content, int $userId): string
    {
        if ($this->isValidMerchant($detectedMerchant)) {
            return $detectedMerchant;
        }

        $bestMatch = $this->findBestMerchantMatch($content, $userId);
        if ($bestMatch !== null) {
            return $bestMatch;
        }

        $merchantFromPatterns = $this->extractMerchantFromPatterns($content);
        if ($merchantFromPatterns !== null) {
            return $merchantFromPatterns;
        }

        return $this->extractMerchantFromLines($content) ?? $detectedMerchant;
    }

    private function isValidMerchant(string $merchant): bool
    {
        return strlen($merchant) > 5 && $merchant !== 'Unknown Merchant';
    }

    private function findBestMerchantMatch(string $content, int $userId): ?string
    {
        return $this->findBestMerchantMatch($content, $userId);
    }

    private function extractMerchantFromPatterns(string $content): ?string
    {
        foreach ($this->merchantPatterns as $pattern) {
            if (preg_match($pattern, $content, $matches)) {
                if (isset($matches[1])) {
                    $merchantName = trim($matches[1]);
                    if (strlen($merchantName) > 3) {
                        return $merchantName;
                    }
                }
            }
        }
        return null;
    }

    private function extractMerchantFromLines(string $content): ?string
    {
        $lines = array_filter(explode("\n", $content), 'trim');
        if (empty($lines)) {
            return null;
        }

        $firstLine = trim($lines[0]);
        if (strlen($firstLine) > 3 && strlen($firstLine) < 50) {
            return $firstLine;
        }

        if (count($lines) > 1) {
            $secondLine = trim($lines[1]);
            if (strlen($secondLine) > 3 && strlen($secondLine) < 50) {
                return $secondLine;
            }
        }

        return null;
    }

    public function prepareExpenseData(array $receiptData): array
    {
        error_log("Receipt data: " . json_encode($receiptData));

        $merchantId = $this->resolveMerchant($receiptData['merchant'] ?? 'Unknown Merchant', $receiptData['user_id']);
        $categoryId = $this->resolveCategory($receiptData['category'] ?? null);
        $paymentMethodId = $this->resolvePaymentMethod($receiptData['payment_method'] ?? null);

        $amount = $this->validateAmount($receiptData['amount'] ?? null);
        $date = $this->validateDate($receiptData['date'] ?? null);
        $description = $this->generateDescription($receiptData);
        $notes = $this->formatItemsAsNotes($receiptData['items'] ?? [], $receiptData['payment_method'] ?? null);

        $expenseData = [
            'user_id' => $receiptData['user_id'],
            'category_id' => $categoryId,
            'user_category_id' => 0,
            'amount' => $amount,
            'description' => $description,
            'date' => $date,
            'merchant_id' => $merchantId,
            'file_id' => $receiptData['file_id'] ?? null,
            'receipt_text' => $receiptData['content'] ?? '',
            'notes' => $notes,
            'payment_method_id' => $paymentMethodId,
        ];

        error_log("Prepared expense data: " . json_encode($expenseData));
        return $expenseData;
    }

    private function validateAmount($amount): float
    {
        return isset($amount) && is_numeric($amount) ? (float)$amount : 0.01;
    }

    private function validateDate($date): string
    {
        return isset($date) && !empty($date) ? $date : date('Y-m-d');
    }

    private function generateDescription(array $receiptData): string
    {
        if (!empty($receiptData['merchant'])) {
            $description = $receiptData['merchant'];
            if (!empty($receiptData['items']) && count($receiptData['items']) > 0) {
                $description .= ' - ' . $receiptData['items'][0]['name'];
            }
            return $description;
        }

        if (!empty($receiptData['description'])) {
            return $receiptData['description'];
        }

        return 'Receipt Upload';
    }

    public function resolveMerchant(string $merchantName, int $userId): int
    {
        try {
            $merchantTrimmed = trim($merchantName);
            return $this->findOrCreateMerchant($merchantTrimmed, $userId);
        } catch (\Exception $e) {
            error_log("Error resolving merchant: " . $e->getMessage());
            return $this->getDefaultMerchant($userId);
        }
    }

    private function findOrCreateMerchant(string $merchantName, int $userId): int
    {
        if ($this->isUnknownMerchant($merchantName)) {
            $bestMatch = $this->findBestMerchantMatch('', $userId);
            if ($bestMatch !== null) {
                return $this->getMerchantId($bestMatch, $userId);
            }
        }

        return $this->getMerchantId($merchantName, $userId);
    }

    private function isUnknownMerchant(string $merchantName): bool
    {
        return empty($merchantName) || $merchantName === 'Unknown Merchant';
    }

    private function getMerchantId(string $merchantName, int $userId): int
    {
        $merchant = Merchant::findByName($merchantName, $userId);
        if ($merchant) {
            return $merchant['id'];
        }

        return Merchant::create([
            'name' => $merchantName,
            'user_id' => $userId
        ]);
    }

    private function getDefaultMerchant(int $userId): int
    {
        return $this->getMerchantId('Unknown Merchant', $userId);
    }

    public function resolveCategory(?string $categoryName): int
    {
        if (!$categoryName) {
            return $this->getDefaultCategoryId();
        }

        $category = Category::findByName($categoryName);
        if ($category) {
            return $category['id'];
        }

        return $this->getDefaultCategoryId();
    }

    private function getDefaultCategoryId(): int
    {
        $category = Category::findByName('Other');
        if ($category) {
            return $category['id'];
        }
        return 1;
    }

    public function resolvePaymentMethod(?string $paymentMethod): int
    {
        if (!$paymentMethod) {
            return $this->getDefaultPaymentMethodId();
        }

        $method = PaymentMethod::findByMethod($paymentMethod);
        if ($method) {
            return $method['id'];
        }

        return $this->getDefaultPaymentMethodId();
    }

    private function getDefaultPaymentMethodId(): int
    {
        $method = PaymentMethod::findByMethod('Cash');
        if ($method) {
            return $method['id'];
        }
        return 1;
    }

    public function createExpenseFromReceipt(array $receiptData, int $userId): int
    {
        $expense = $this->expenseService->findById($receiptData['expense_id'] ?? 0);
        if (!$expense || $expense['user_id'] !== $userId) {
            throw new NotFoundException('Expense not found');
        }

        $expenseData = $this->prepareExpenseData($receiptData);
        return $this->expenseService->create($expenseData);
    }

    private function ensureUserDataLoaded(int $userId): void
    {
        if ($this->currentUserId === $userId) {
            return;
        }

        $this->currentUserId = $userId;
        $this->loadUserMerchants($userId);
    }

    private function loadUserMerchants(int $userId): void
    {
        $this->merchantNames = [];
        $merchants = Merchant::findByUserId($userId);

        if (empty($merchants)) {
            return;
        }

        foreach ($merchants as $merchant) {
            $this->merchantNames[] = $merchant['name'];
        }
    }

    // ... existing code ...

    private function formatItemsAsNotes(array $items, ?string $paymentMethod): string
    {
        $notes = [];

        if (!empty($items)) {
            $notes[] = "Items:";
            foreach ($items as $item) {
                $quantity = $item['quantity'] > 1 ? "{$item['quantity']} x " : "";
                $notes[] = "- {$quantity}{$item['name']}: \${$item['price']}";
            }
        }

        if ($paymentMethod) {
            $notes[] = "Payment Method: {$paymentMethod}";
        }

        return implode("\n", $notes);
    }

    // ... rest of existing methods ...
}
