function debounce(func, delay)
{
    let timeoutId;
    return function (...args) {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => func.apply(this, args), delay);
    };
}

document.addEventListener('DOMContentLoaded', initializeExpensesPage);

// Global filter state for client-side filtering
let filterState = {
    dateFrom: '',
    dateTo: '',
    categoryIds: [],
    minAmount: '',
    maxAmount: '',
    searchTerm: ''
};

let isClientSideFiltering = false;

// Category color mapping for visual consistency
const categoryColorMap = new Map();

function initializeExpensesPage()
{
    setupNotesModal();
    setupDocumentPreview();
    setupDropdowns();
    setupTabPanels();
    enhanceExpenseRows();
    setupSelectModeObserver();
    setupDateFilterClear();
    setupMobileFilterToggle();

    // Check if we should use client-side or server-side filtering
    if (window.expenseFilterData && window.expenseFilterData.totalCount <= 1000) {
        isClientSideFiltering = true;
        FilterManager.init();
    } else {
        setupFilterAutoSubmit();
    }
}

function setupMobileFilterToggle() {
    const mobileToggle = document.querySelector('.mobile-filter-toggle');
    const filterBar = document.querySelector('.filter-bar');
    
    if (mobileToggle && filterBar) {
        mobileToggle.addEventListener('click', () => {
            const isExpanded = filterBar.classList.toggle('mobile-expanded');
            mobileToggle.setAttribute('aria-expanded', isExpanded.toString());
            
            // Find the controls element
            const controls = document.getElementById('filter-controls');
            if (controls) {
                controls.setAttribute('aria-hidden', (!isExpanded).toString());
            }
        });
    }
}

// FilterManager class to coordinate all filter components
class FilterManager {
    static init() {
        this.initializeCategoryColors();
        this.setupClientSideFilters();
        this.initializeComponents();
    }

    static initializeCategoryColors() {
        if (window.expenseFilterData && window.expenseFilterData.categories) {
            const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4', '#84CC16', '#F97316'];
            window.expenseFilterData.categories.forEach((category, index) => {
                categoryColorMap.set(category.id.toString(), colors[index % colors.length]);
            });
        }
    }

    static initializeComponents() {
        this.dateFilter = new DateFilter();
        this.categoryFilter = new CategoryFilter();
        this.amountFilter = new AmountFilter();
        this.searchFilter = new SearchFilter();
        this.tagList = new TagList();
        this.resultsCounter = new ResultsCounter();

        // Initialize range slider
        this.initializeRangeSlider();
    }

    static initializeRangeSlider() {
        const sliderContainer = document.querySelector('.amount-slider-container');
        if (!sliderContainer || !window.expenseFilterData?.amountBounds) {
            return;
        }

        const { min, max } = window.expenseFilterData.amountBounds;
        const slider = sliderContainer.querySelector('.dual-range-slider');
        
        if (slider && typeof noUiSlider !== 'undefined') {
            noUiSlider.create(slider, {
                start: [min, max],
                connect: true,
                range: {
                    'min': min,
                    'max': max
                },
                format: {
                    to: value => Math.round(value),
                    from: value => Number(value)
                }
            });

            slider.noUiSlider.on('update', (values) => {
                filterState.minAmount = values[0];
                filterState.maxAmount = values[1];
                document.dispatchEvent(new CustomEvent('filterChanged'));
            });
        }
    }

    static setupClientSideFilters() {
        const filterForm = document.querySelector('.filter-form');
        if (!filterForm) {
            return;
        }

        // Prevent form submission
        filterForm.addEventListener('submit', (e) => {
            e.preventDefault();
        });

        // Initialize filter state from current form values
        this.initializeFilterState();

        // Setup event listeners for all filter controls
        this.setupFilterEventListeners();

        // Listen for filter change events
        document.addEventListener('filterChanged', () => {
            filterTable();
            this.tagList.updateActiveFilters();
            this.resultsCounter.updateResultsCounter();
        });

        // Initial filter application
        filterTable();
    }

    static initializeFilterState() {
        const form = document.querySelector('.filter-form');
        if (!form) {
            return;
        }

        // Date filters
        const dateFromInput = form.querySelector('#date_from');
        const dateToInput = form.querySelector('#date_to');
        const dateFilterInput = form.querySelector('#date_filter');

        // Check if any date filter is already applied
        let hasDateFilter = false;
        
        if (dateFilterInput && dateFilterInput.value) {
            // Handle preset date filter
            const presetValue = dateFilterInput.value;
            const dates = convertPresetToDateRange(presetValue);
            if (dates) {
                filterState.dateFrom = dates.from;
                filterState.dateTo = dates.to;
                hasDateFilter = true;
            }
        } else if ((dateFromInput && dateFromInput.value) || (dateToInput && dateToInput.value)) {
            // Handle manual date range
            filterState.dateFrom = dateFromInput ? dateFromInput.value : '';
            filterState.dateTo = dateToInput ? dateToInput.value : '';
            hasDateFilter = true;
        } else {
            // Apply default filter of last 6 months if no date filter is set
            const defaultDates = convertPresetToDateRange('last_6_months');
            if (defaultDates) {
                filterState.dateFrom = defaultDates.from;
                filterState.dateTo = defaultDates.to;
                
                // Update the form inputs to reflect the default filter
                if (dateFromInput) dateFromInput.value = defaultDates.from;
                if (dateToInput) dateToInput.value = defaultDates.to;
                
                // Add a visual indicator that a default filter has been applied
                const dateFilterToggle = form.querySelector('[data-filter="date"] .filter-dropdown__text');
                if (dateFilterToggle) {
                    dateFilterToggle.textContent = 'Last 6 Months (Default)';
                    dateFilterToggle.classList.add('filter-dropdown__text--default');
                }
            }
        }

        // Category filters (multi-select)
        const categoryInputs = form.querySelectorAll('input[name="category_ids[]"]:checked');
        filterState.categoryIds = Array.from(categoryInputs).map(input => input.value);

        // Amount filters
        const minAmountInput = form.querySelector('#min_amount');
        const maxAmountInput = form.querySelector('#max_amount');
        filterState.minAmount = minAmountInput ? minAmountInput.value : '';
        filterState.maxAmount = maxAmountInput ? maxAmountInput.value : '';

        // Search term
        const searchInput = form.querySelector('#search');
        filterState.searchTerm = searchInput ? searchInput.value : '';
    }

    static setupFilterEventListeners() {
        const form = document.querySelector('.filter-form');
        if (!form) {
            return;
        }

        // Date preset selection
        const datePresets = form.querySelectorAll('.date-preset');
        datePresets.forEach(preset => {
            preset.addEventListener('click', () => {
                // Remove selected class from all presets
                datePresets.forEach(p => p.classList.remove('selected'));
                
                // Add selected class to clicked preset
                preset.classList.add('selected');
                
                const value = preset.getAttribute('data-preset');
                const dates = convertPresetToDateRange(value);
                if (dates) {
                    filterState.dateFrom = dates.from;
                    filterState.dateTo = dates.to;
                    
                    // Update the date inputs
                    const dateFromInput = form.querySelector('#date_from');
                    const dateToInput = form.querySelector('#date_to');
                    if (dateFromInput) dateFromInput.value = dates.from;
                    if (dateToInput) dateToInput.value = dates.to;
                    
                    // Update the dropdown text
                    const dateToggle = form.querySelector('[data-filter="date"] .filter-dropdown__text');
                    if (dateToggle) {
                        const presetName = preset.textContent;
                        dateToggle.textContent = presetName;
                    }
                    
                    document.dispatchEvent(new CustomEvent('filterChanged'));
                }
            });
        });

        // Amount presets
        const amountPresets = form.querySelectorAll('.amount-preset');
        amountPresets.forEach(preset => {
            preset.addEventListener('click', () => {
                // Remove selected class from all presets
                amountPresets.forEach(p => p.classList.remove('selected'));
                
                // Add selected class to clicked preset
                preset.classList.add('selected');
                
                const min = preset.getAttribute('data-min');
                const max = preset.getAttribute('data-max');
                
                // Update the amount inputs
                const minInput = form.querySelector('#min_amount');
                const maxInput = form.querySelector('#max_amount');
                
                if (minInput) minInput.value = min;
                if (maxInput) maxInput.value = max;
                
                // Update filter state
                filterState.minAmount = min;
                filterState.maxAmount = max;
                
                // Update the dropdown text
                const amountToggle = form.querySelector('[data-filter="amount"] .filter-dropdown__text');
                if (amountToggle) {
                    amountToggle.textContent = preset.textContent;
                }
                
                document.dispatchEvent(new CustomEvent('filterChanged'));
            });
        });

        // Search input - debounced update
        const searchInput = form.querySelector('#search');
        if (searchInput) {
            const debouncedSearchFilter = debounce(() => {
                filterState.searchTerm = searchInput.value;
                document.dispatchEvent(new CustomEvent('filterChanged'));
            }, 300);

            searchInput.addEventListener('input', debouncedSearchFilter);
            
            // Add clear button functionality
            const clearButton = form.querySelector('.search-input__clear');
            if (clearButton) {
                clearButton.addEventListener('click', () => {
                    searchInput.value = '';
                    filterState.searchTerm = '';
                    clearButton.style.display = 'none';
                    document.dispatchEvent(new CustomEvent('filterChanged'));
                });
                
                // Show/hide clear button based on input
                searchInput.addEventListener('input', () => {
                    clearButton.style.display = searchInput.value ? 'flex' : 'none';
                });
            }
        }

        // Category checkboxes
        const categoryInputs = form.querySelectorAll('input[name="category_ids[]"]');
        categoryInputs.forEach(input => {
            input.addEventListener('change', () => {
                const categoryInputs = form.querySelectorAll('input[name="category_ids[]"]:checked');
                filterState.categoryIds = Array.from(categoryInputs).map(input => input.value);
                
                // Update the dropdown text
                const categoryToggle = form.querySelector('[data-filter="category"] .filter-dropdown__text');
                if (categoryToggle) {
                    const count = filterState.categoryIds.length;
                    if (count === 0) {
                        categoryToggle.textContent = 'All Categories';
                    } else {
                        categoryToggle.textContent = count === 1 ? 
                            input.closest('.category-option').querySelector('.category-option__name').textContent :
                            `${count} selected`;
                    }
                }
                
                document.dispatchEvent(new CustomEvent('filterChanged'));
            });
        });
    }

    static toggleDropdownPanel(panelElement, isOpen) {
        if (isOpen) {
            panelElement.classList.add('show');
            panelElement.setAttribute('aria-hidden', 'false');
        } else {
            panelElement.classList.remove('show');
            panelElement.setAttribute('aria-hidden', 'true');
        }
    }

    static flashErrorState(element) {
        element.style.border = '2px solid #EF4444';
        element.style.transition = 'border-color 0.5s ease';
        
        setTimeout(() => {
            element.style.border = '';
            element.style.transition = '';
        }, 500);
    }

    static toggleMobileFilters() {
        const filterBar = document.querySelector('.filter-bar');
        const mobileToggle = document.querySelector('.mobile-filter-toggle');
        
        if (filterBar && mobileToggle) {
            const isExpanded = filterBar.classList.toggle('mobile-expanded');
            mobileToggle.setAttribute('aria-expanded', isExpanded.toString());
            filterBar.setAttribute('aria-hidden', (!isExpanded).toString());
        }
    }
}

function setupDateFilterClear()
{
    const clearBtn = document.getElementById('date_filter_clear');
    if (clearBtn) {
        clearBtn.addEventListener('click', () => {
            if (isClientSideFiltering) {
                clearDateFilter();
            } else {
                const form = clearBtn.closest('form');
                if (form) {
                    // Clear both date fields
                    const dateFromInput = form.querySelector('#date_from');
                    const dateToInput = form.querySelector('#date_to');
                    const dateInput = form.querySelector('#date_filter');

                    if (dateFromInput) {
                        dateFromInput.value = '';
                    }
                    if (dateToInput) {
                        dateToInput.value = '';
                    }
                    if (dateInput) {
                        dateInput.value = '';
                    }

                    form.submit();
                }
            }
        });
    }
}

// Individual filter component classes
class DateFilter {
    constructor() {
        this.setupEventListeners();
    }

    setupEventListeners() {
        const dateInputs = document.querySelectorAll('#date_from, #date_to, #date_filter');
        dateInputs.forEach(input => {
            input.addEventListener('change', () => {
                this.updateFilterState();
                document.dispatchEvent(new CustomEvent('filterChanged'));
            });
        });
    }

    updateFilterState() {
        const form = document.querySelector('.filter-form');
        if (!form) return;

        const dateFromInput = form.querySelector('#date_from');
        const dateToInput = form.querySelector('#date_to');
        const dateFilterInput = form.querySelector('#date_filter');

        filterState.dateFrom = dateFromInput ? dateFromInput.value : '';
        filterState.dateTo = dateToInput ? dateToInput.value : '';

        if (dateFilterInput && dateFilterInput.value) {
            const dates = convertPresetToDateRange(dateFilterInput.value);
            if (dates) {
                filterState.dateFrom = dates.from;
                filterState.dateTo = dates.to;
            }
        }
    }
}

class CategoryFilter {
    constructor() {
        this.setupEventListeners();
    }

    setupEventListeners() {
        const categoryInputs = document.querySelectorAll('input[name="category_ids[]"]');
        categoryInputs.forEach(input => {
            input.addEventListener('change', () => {
                this.updateFilterState();
                document.dispatchEvent(new CustomEvent('filterChanged'));
            });
        });
    }

    updateFilterState() {
        const form = document.querySelector('.filter-form');
        if (!form) return;

        const categoryInputs = form.querySelectorAll('input[name="category_ids[]"]:checked');
        filterState.categoryIds = Array.from(categoryInputs).map(input => input.value);
    }
}

class AmountFilter {
    constructor() {
        this.setupEventListeners();
    }

    setupEventListeners() {
        const amountInputs = document.querySelectorAll('#min_amount, #max_amount');
        const debouncedAmountFilter = debounce(() => {
            this.updateFilterState();
            document.dispatchEvent(new CustomEvent('filterChanged'));
        }, 300);

        amountInputs.forEach(input => {
            input.addEventListener('input', debouncedAmountFilter);
        });
    }

    updateFilterState() {
        const form = document.querySelector('.filter-form');
        if (!form) return;

        const minAmountInput = form.querySelector('#min_amount');
        const maxAmountInput = form.querySelector('#max_amount');
        filterState.minAmount = minAmountInput ? minAmountInput.value : '';
        filterState.maxAmount = maxAmountInput ? maxAmountInput.value : '';
    }
}

class SearchFilter {
    constructor() {
        this.setupEventListeners();
    }

    setupEventListeners() {
        const searchInput = document.querySelector('#search');
        if (searchInput) {
            const debouncedSearchFilter = debounce(() => {
                this.updateFilterState();
                document.dispatchEvent(new CustomEvent('filterChanged'));
            }, 300);

            searchInput.addEventListener('input', debouncedSearchFilter);
        }
    }

    updateFilterState() {
        const searchInput = document.querySelector('#search');
        filterState.searchTerm = searchInput ? searchInput.value : '';
    }
}

class TagList {
    updateActiveFilters() {
        const activeFiltersContainer = document.querySelector('#active-filters');
        if (!activeFiltersContainer) {
            return;
        }

        activeFiltersContainer.innerHTML = '';

        // Date range filter tag
        if (filterState.dateFrom || filterState.dateTo) {
            const dateText = formatDateRangeText(filterState.dateFrom, filterState.dateTo);
            this.addFilterTag(activeFiltersContainer, 'Date', dateText, () => clearDateFilter());
        }

        // Category filter tags
        filterState.categoryIds.forEach(categoryId => {
            const categoryName = getCategoryName(categoryId);
            const categoryColor = categoryColorMap.get(categoryId) || '#6B7280';
            this.addFilterTag(activeFiltersContainer, 'Category', categoryName, () => clearCategoryFilter(categoryId), categoryColor);
        });

        // Amount range filter tag
        if (filterState.minAmount || filterState.maxAmount) {
            const amountText = formatAmountRangeText(filterState.minAmount, filterState.maxAmount);
            this.addFilterTag(activeFiltersContainer, 'Amount', amountText, () => clearAmountFilter());
        }

        // Search filter tag
        if (filterState.searchTerm) {
            this.addFilterTag(activeFiltersContainer, 'Search', filterState.searchTerm, () => clearSearchFilter());
        }
    }

    addFilterTag(container, label, value, onRemove, color = null) {
        const tag = document.createElement('span');
        tag.className = 'filter-tag';
        
        if (color) {
            tag.style.borderColor = color;
            tag.style.color = color;
        }
        
        tag.innerHTML = `
            <span class="filter-label">${label}:</span>
            <span class="filter-value">${value}</span>
            <button type="button" class="filter-remove" aria-label="Remove ${label} filter">×</button>
        `;

        tag.querySelector('.filter-remove').addEventListener('click', (e) => {
            e.preventDefault();
            onRemove();
        });
        container.appendChild(tag);
    }
}

class ResultsCounter {
    updateResultsCounter() {
        const tableBody = document.querySelector('table tbody');
        if (!tableBody) return;

        const visibleRows = tableBody.querySelectorAll('tr:not([style*="display: none"])');
        const count = visibleRows.length;
        const total = window.expenseFilterData ? window.expenseFilterData.totalCount : count;
        
        // Update counter text
        const counter = document.querySelector('#results-counter');
        if (counter) {
            counter.textContent = `Showing ${count} of ${total} expenses`;
        }
        
        // Update filter breadcrumb summary
        this.updateFilterBreadcrumb();
        
        // Show empty state if no results
        this.toggleEmptyState(count === 0);
    }
    
    updateFilterBreadcrumb() {
        const breadcrumb = document.querySelector('#filter-breadcrumb');
        if (!breadcrumb) return;
        
        // Only show breadcrumb when multiple filters are active
        const activeFilterCount = this.countActiveFilters();
        
        if (activeFilterCount >= 2) {
            let summary = 'Filtered by: ';
            
            if (filterState.dateFrom || filterState.dateTo) {
                summary += 'Date';
                if (activeFilterCount > 1) summary += ', ';
            }
            
            if (filterState.categoryIds.length > 0) {
                summary += `${filterState.categoryIds.length} Categories`;
                if ((filterState.minAmount || filterState.maxAmount) || filterState.searchTerm) summary += ', ';
            }
            
            if (filterState.minAmount || filterState.maxAmount) {
                summary += 'Amount';
                if (filterState.searchTerm) summary += ', ';
            }
            
            if (filterState.searchTerm) {
                summary += 'Search';
            }
            
            breadcrumb.textContent = summary;
            breadcrumb.style.display = 'block';
        } else {
            breadcrumb.style.display = 'none';
        }
    }
    
    countActiveFilters() {
        let count = 0;
        if (filterState.dateFrom || filterState.dateTo) count++;
        if (filterState.categoryIds.length > 0) count++;
        if (filterState.minAmount || filterState.maxAmount) count++;
        if (filterState.searchTerm) count++;
        return count;
    }
    
    toggleEmptyState(isEmpty) {
        const emptyState = document.querySelector('.empty-state');
        const table = document.querySelector('.expense-table');
        
        if (!emptyState || !table) return;
        
        if (isEmpty) {
            // Show empty state with filter-specific message
            emptyState.style.display = 'block';
            table.style.display = 'none';
            
            // Update empty state message based on active filters
            const messageElement = emptyState.querySelector('.empty-state__message');
            if (messageElement) {
                if (this.countActiveFilters() > 0) {
                    messageElement.textContent = 'No expenses match your current filters. Try adjusting your filter criteria or clear filters to see all expenses.';
                    
                    // Ensure the clear filters button is visible
                    const clearButton = emptyState.querySelector('.empty-state__action');
                    if (clearButton) {
                        clearButton.style.display = 'inline-flex';
                        clearButton.addEventListener('click', () => {
                            const clearAllButton = document.querySelector('.filter-clear-all');
                            if (clearAllButton) clearAllButton.click();
                        });
                    }
                } else {
                    messageElement.textContent = 'No expenses found. Add your first expense to get started.';
                    
                    // Hide the clear filters button when no filters are active
                    const clearButton = emptyState.querySelector('.empty-state__action');
                    if (clearButton) {
                        clearButton.style.display = 'none';
                    }
                }
            }
        } else {
            // Hide empty state, show table
            emptyState.style.display = 'none';
            table.style.display = 'table';
        }
    }
}


function convertPresetToDateRange(preset)
{
    const today = new Date();
    const formatDate = (date) => date.toISOString().split('T')[0];

    switch (preset) {
        case 'today':
            return { from: formatDate(today), to: formatDate(today) };
        case 'yesterday':
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);
            return { from: formatDate(yesterday), to: formatDate(yesterday) };
        case 'this_week':
            const startOfWeek = new Date(today);
            startOfWeek.setDate(today.getDate() - today.getDay());
            return { from: formatDate(startOfWeek), to: formatDate(today) };
        case 'last_week':
            const startOfLastWeek = new Date(today);
            startOfLastWeek.setDate(today.getDate() - today.getDay() - 7);
            const endOfLastWeek = new Date(today);
            endOfLastWeek.setDate(today.getDate() - today.getDay() - 1);
            return { from: formatDate(startOfLastWeek), to: formatDate(endOfLastWeek) };
        case 'this_month':
            const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
            return { from: formatDate(startOfMonth), to: formatDate(today) };
        case 'last_30_days':
            const thirtyDaysAgo = new Date(today);
            thirtyDaysAgo.setDate(today.getDate() - 30);
            return { from: formatDate(thirtyDaysAgo), to: formatDate(today) };
        case 'last_month':
            const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
            const endOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0);
            return { from: formatDate(lastMonth), to: formatDate(endOfLastMonth) };
        case 'last_3_months':
            const threeMonthsAgo = new Date(today);
            threeMonthsAgo.setMonth(today.getMonth() - 3);
            return { from: formatDate(threeMonthsAgo), to: formatDate(today) };
        case 'this_year':
            const startOfYear = new Date(today.getFullYear(), 0, 1);
            return { from: formatDate(startOfYear), to: formatDate(today) };
        case 'last_year':
            const lastYear = new Date(today.getFullYear() - 1, 0, 1);
            const endOfLastYear = new Date(today.getFullYear() - 1, 11, 31);
            return { from: formatDate(lastYear), to: formatDate(endOfLastYear) };
        case 'last_6_months': // Default filter
            const sixMonthsAgo = new Date(today);
            sixMonthsAgo.setMonth(today.getMonth() - 6);
            return { from: formatDate(sixMonthsAgo), to: formatDate(today) };
        default:
            return null;
    }
}

function filterTable()
{
    const tableBody = document.querySelector('table tbody');
    if (!tableBody) {
        return;
    }

    // Show loading state
    showLoadingState(true);
    
    // Use setTimeout to allow the UI to update before filtering
    setTimeout(() => {
        const rows = tableBody.querySelectorAll('tr');
        let visibleCount = 0;

        rows.forEach(row => {
            const isVisible = matchesFilters(row);
            row.style.display = isVisible ? '' : 'none';
            if (isVisible) {
                visibleCount++;
            }
        });
        
        // Hide loading state
        showLoadingState(false);
        
        // Show empty state if no results
        if (visibleCount === 0) {
            showEmptyResultsState();
        }
    }, 200);
}

function showLoadingState(isLoading) {
    const filterControls = document.querySelectorAll('.filter-dropdown__toggle, .search-input-container');
    
    filterControls.forEach(control => {
        if (isLoading) {
            control.classList.add('filter-loading');
        } else {
            control.classList.remove('filter-loading');
        }
    });
}

function showEmptyResultsState() {
    const filterControls = document.querySelectorAll('.filter-dropdown__toggle, .filter-input--search');
    
    // Flash red border on all filter controls
    filterControls.forEach(control => {
        control.classList.add('filter-empty');
        
        setTimeout(() => {
            control.classList.remove('filter-empty');
        }, 500);
    });
    
    // Update results counter text
    const counter = document.querySelector('#results-counter');
    if (counter) {
        counter.textContent = 'No expenses found';
        counter.style.color = '#EF4444';
        
        setTimeout(() => {
            counter.style.color = '#6B7280';
        }, 2000);
    }
}

function matchesFilters(row)
{
    // Validate filter combinations first
    validateFilterCombinations();
    
    // Date filter
    if (filterState.dateFrom || filterState.dateTo) {
        const rowDate = row.getAttribute('data-date');
        if (rowDate) {
            if (filterState.dateFrom && rowDate < filterState.dateFrom) {
                return false;
            }
            if (filterState.dateTo && rowDate > filterState.dateTo) {
                return false;
            }
        }
    }

    // Category filter
    if (filterState.categoryIds.length > 0) {
        const rowCategoryId = row.getAttribute('data-category-id');
        if (!rowCategoryId || !filterState.categoryIds.includes(rowCategoryId)) {
            return false;
        }
    }

    // Amount filter
    const rowAmount = parseFloat(row.getAttribute('data-amount'));
    if (!isNaN(rowAmount)) {
        if (filterState.minAmount && rowAmount < parseFloat(filterState.minAmount)) {
            return false;
        }
        if (filterState.maxAmount && rowAmount > parseFloat(filterState.maxAmount)) {
            return false;
        }
    }

    // Search filter
    if (filterState.searchTerm) {
        const searchTerm = filterState.searchTerm.toLowerCase();
        const rowText = row.textContent.toLowerCase();
        if (!rowText.includes(searchTerm)) {
            return false;
        }
    }

    return true;
}

/**
 * Validates and corrects impossible filter combinations
 */
function validateFilterCombinations() {
    // Check for impossible date range
    if (filterState.dateFrom && filterState.dateTo && filterState.dateFrom > filterState.dateTo) {
        // Swap dates if from date is after to date
        const temp = filterState.dateFrom;
        filterState.dateFrom = filterState.dateTo;
        filterState.dateTo = temp;
        
        // Update form inputs
        const form = document.querySelector('.filter-form');
        if (form) {
            const dateFromInput = form.querySelector('#date_from');
            const dateToInput = form.querySelector('#date_to');
            
            if (dateFromInput) dateFromInput.value = filterState.dateFrom;
            if (dateToInput) dateToInput.value = filterState.dateTo;
        }
        
        // Show notification
        showFilterNotification('Date range corrected: start date was after end date');
    }
    
    // Check for impossible amount range
    if (filterState.minAmount && filterState.maxAmount && 
        parseFloat(filterState.minAmount) > parseFloat(filterState.maxAmount)) {
        // Swap amounts if min is greater than max
        const temp = filterState.minAmount;
        filterState.minAmount = filterState.maxAmount;
        filterState.maxAmount = temp;
        
        // Update form inputs
        const form = document.querySelector('.filter-form');
        if (form) {
            const minAmountInput = form.querySelector('#min_amount');
            const maxAmountInput = form.querySelector('#max_amount');
            
            if (minAmountInput) minAmountInput.value = filterState.minAmount;
            if (maxAmountInput) maxAmountInput.value = filterState.maxAmount;
        }
        
        // Show notification
        showFilterNotification('Amount range corrected: minimum was greater than maximum');
    }
}

/**
 * Shows a temporary notification about filter corrections
 */
function showFilterNotification(message) {
    const existingNotification = document.querySelector('.filter-notification');
    if (existingNotification) {
        existingNotification.remove();
    }
    
    const notification = document.createElement('div');
    notification.className = 'filter-notification';
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Fade in
    setTimeout(() => {
        notification.classList.add('filter-notification--visible');
    }, 10);
    
    // Fade out and remove after delay
    setTimeout(() => {
        notification.classList.remove('filter-notification--visible');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}


function formatDateRangeText(from, to)
{
    if (from && to) {
        return from === to ? from : `${from} to ${to}`;
    } else if (from) {
        return `from ${from}`;
    } else if (to) {
        return `until ${to}`;
    }
    return '';
}

function formatAmountRangeText(min, max)
{
    if (min && max) {
        return `$${min} - $${max}`;
    } else if (min) {
        return `≥ $${min}`;
    } else if (max) {
        return `≤ $${max}`;
    }
    return '';
}

function getCategoryName(categoryId)
{
    if (window.expenseFilterData && window.expenseFilterData.categories) {
        const category = window.expenseFilterData.categories.find(cat => cat.id == categoryId);
        return category ? category.name : `Category ${categoryId}`;
    }
    return `Category ${categoryId}`;
}

function clearDateFilter()
{
    const form = document.querySelector('.filter-form');
    if (form) {
        const dateFromInput = form.querySelector('#date_from');
        const dateToInput = form.querySelector('#date_to');
        const dateFilterInput = form.querySelector('#date_filter');

        if (dateFromInput) {
            dateFromInput.value = '';
        }
        if (dateToInput) {
            dateToInput.value = '';
        }
        if (dateFilterInput) {
            dateFilterInput.value = '';
        }
    }

    filterState.dateFrom = '';
    filterState.dateTo = '';
    document.dispatchEvent(new CustomEvent('filterChanged'));
}

function clearCategoryFilter(categoryId)
{
    const form = document.querySelector('.filter-form');
    if (form) {
        const categoryInput = form.querySelector(`input[name="category_ids[]"][value="${categoryId}"]`);
        if (categoryInput) {
            categoryInput.checked = false;
        }
    }

    filterState.categoryIds = filterState.categoryIds.filter(id => id !== categoryId);
    document.dispatchEvent(new CustomEvent('filterChanged'));
}

function clearAmountFilter()
{
    const form = document.querySelector('.filter-form');
    if (form) {
        const minAmountInput = form.querySelector('#min_amount');
        const maxAmountInput = form.querySelector('#max_amount');

        if (minAmountInput) {
            minAmountInput.value = '';
        }
        if (maxAmountInput) {
            maxAmountInput.value = '';
        }
    }

    filterState.minAmount = '';
    filterState.maxAmount = '';
    document.dispatchEvent(new CustomEvent('filterChanged'));
}

function clearSearchFilter()
{
    const form = document.querySelector('.filter-form');
    if (form) {
        const searchInput = form.querySelector('#search');
        if (searchInput) {
            searchInput.value = '';
        }
    }

    filterState.searchTerm = '';
    document.dispatchEvent(new CustomEvent('filterChanged'));
}

function setupFilterAutoSubmit()
{
    const filterForm = document.querySelector('.filter-form');
    if (!filterForm) {
        return;
    }

    // Immediate submission for select and date inputs
    const selectAndDateInputs = filterForm.querySelectorAll('select, input[type="date"]');
    selectAndDateInputs.forEach(input => {
        input.addEventListener('change', () => {
            filterForm.submit();
        });
    });

    // Debounced submission for number inputs
    const numberInputs = filterForm.querySelectorAll('input[type="number"]');
    const debouncedSubmit = debounce(() => {
        filterForm.submit();
    }, 300);

    numberInputs.forEach(input => {
        input.addEventListener('input', debouncedSubmit);
    });
}

function setupNotesModal()
{
    const modal = document.getElementById('notesModal');
    const notesContent = document.getElementById('notesContent');

    if (!modal || !notesContent) {
        return;
    }

    document.querySelectorAll('.show-notes').forEach(button => {
        button.addEventListener('click', () => {
            notesContent.textContent = button.getAttribute('data-notes');
            showModal(modal);
        });
    });

    setupModalCloseBehavior(modal);
}

function setupDocumentPreview()
{
    const modal = document.getElementById('documentPreviewModal');
    const contentContainer = document.getElementById('documentContent');
    const downloadLink = document.getElementById('downloadDocumentLink');

    if (!modal || !contentContainer || !downloadLink) {
        return;
    }

    document.querySelectorAll('.document-preview').forEach(button => {
        button.addEventListener('click', () => {
            const expenseId = button.getAttribute('data-expense-id');
            showDocumentPreview(expenseId, modal, contentContainer, downloadLink);
        });
    });

    setupModalCloseBehavior(modal, '.modal-footer .primary');
}

function showDocumentPreview(expenseId, modal, contentContainer, downloadLink)
{
    contentContainer.innerHTML = '<div class="loading-spinner"></div>';
    showModal(modal);

    downloadLink.href = ` / expenses / ${expenseId} / document / download`;

    fetch(` / expenses / ${expenseId} / document / preview`)
        .then(response => response.text())
        .then(html => {
            contentContainer.innerHTML = html;
        })
        .catch(error => {
            contentContainer.innerHTML = ` < div class = "error-message" > Error loading document: ${error.message} < / div > `;
        });
}

function setupModalCloseBehavior(modal, additionalSelectors = '')
{
    const closeSelector = '.close-button, .modal-footer button' +
        (additionalSelectors ? `, ${additionalSelectors}` : '');

    modal.querySelectorAll(closeSelector).forEach(button => {
        button.addEventListener('click', () => hideModal(modal));
    });

    modal.addEventListener('click', event => {
        if (event.target === modal) {
            hideModal(modal);
        }
    });
}

function setupDropdowns()
{
    setupDropdownToggles();
    setupDropdownOutsideClickHandler();
}

function setupDropdownToggles()
{
    document.querySelectorAll('.dropdown-toggle').forEach(button => {
        button.addEventListener('click', e => {
            e.preventDefault();
            e.stopPropagation();

            closeOtherDropdowns(button);
            toggleDropdown(button);
        });
    });
}

function closeOtherDropdowns(currentButton)
{
    document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
        if (menu !== currentButton.nextElementSibling) {
            menu.classList.remove('show');
            menu.setAttribute('aria-hidden', 'true');
        }
    });
}

function toggleDropdown(button)
{
    const dropdownMenu = button.nextElementSibling;
    if (!dropdownMenu) {
        return;
    }

    dropdownMenu.classList.toggle('show');
    const isExpanded = dropdownMenu.classList.contains('show');
    dropdownMenu.setAttribute('aria-hidden', isExpanded ? 'false' : 'true');
    button.setAttribute('aria-expanded', isExpanded ? 'true' : 'false');
}

function setupDropdownOutsideClickHandler()
{
    document.addEventListener('click', event => {
        if (event.target.matches('.dropdown-toggle')) {
            return;
        }

        document.querySelectorAll('.dropdown-menu.show').forEach(menu => {
            menu.classList.remove('show');
            menu.setAttribute('aria-hidden', 'true');

            const toggleButton = menu.previousElementSibling;
            if (toggleButton ? .classList.contains('dropdown-toggle')) {
                toggleButton.setAttribute('aria-expanded', 'false');
            }
        });
    });
}

function setupTabPanels()
{
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabPanels = document.querySelectorAll('.tab-panel');

    if (tabButtons.length === 0 || tabPanels.length === 0) {
        return;
    }

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            deactivateAllTabs(tabButtons, tabPanels);
            activateTab(button);
        });
    });
}

function deactivateAllTabs(tabButtons, tabPanels)
{
    tabButtons.forEach(btn => {
        btn.classList.remove('active');
        btn.setAttribute('aria-selected', 'false');
    });

    tabPanels.forEach(panel => {
        panel.classList.remove('active');
        panel.hidden = true;
    });
}

function activateTab(button)
{
    button.classList.add('active');
    button.setAttribute('aria-selected', 'true');

    const panelId = button.getAttribute('aria-controls');
    const panel = document.getElementById(panelId);
    if (panel) {
        panel.classList.add('active');
        panel.hidden = false;
    }
}

function enhanceExpenseRows()
{
    document.querySelectorAll('table tbody tr').forEach(row => {
        addRowHoverEffect(row);
        makeDescriptionCellClickable(row);
        setupRowClickForSelection(row);
    });
}

function addRowHoverEffect(row)
{
    row.addEventListener('mouseenter', () => {
        row.style.backgroundColor = 'var(--grey-100)';
    });

    row.addEventListener('mouseleave', () => {
        row.style.backgroundColor = '';
    });
}

function makeDescriptionCellClickable(row)
{
    const descriptionCell = row.querySelector('.description-column');
    if (!descriptionCell) {
        return;
    }

    const link = descriptionCell.querySelector('a');
    if (!link) {
        return;
    }

    descriptionCell.style.cursor = 'pointer';
    descriptionCell.addEventListener('click', e => {
        if (e.target.closest('button, .icon-button')) {
            return;
        }

        // Check if table is in select mode
        const tableContainer = row.closest('.modern-table, .table');
        if (tableContainer && tableContainer.classList.contains('select-mode')) {
            // In select mode, don't navigate - let row click handle selection
            return;
        }

        link.click();
    });
}

function showModal(modal)
{
    modal.classList.add('show');
    modal.setAttribute('aria-hidden', 'false');
    document.body.style.overflow = 'hidden';
}

function setupRowClickForSelection(row)
{
    row.addEventListener('click', e => {
        // Check if table is in select mode
        const tableContainer = row.closest('.modern-table, .table');
        if (!tableContainer || !tableContainer.classList.contains('select-mode')) {
            return;
        }

        // Don't handle if clicking on interactive elements
        if (e.target.closest('button, .icon-button, input, a')) {
            return;
        }

        // Toggle the row's checkbox
        const checkbox = row.querySelector('.row-selector');
        if (checkbox) {
            checkbox.checked = !checkbox.checked;
            checkbox.dispatchEvent(new Event('change', { bubbles: true }));
        }
    });
}

function updateRowCursorForSelectMode()
{
    const tableContainer = document.querySelector('.modern-table, .table');
    if (!tableContainer) {
        return;
    }

    const rows = tableContainer.querySelectorAll('tbody tr');
    const isSelectMode = tableContainer.classList.contains('select-mode');

    rows.forEach(row => {
        if (isSelectMode) {
            row.style.cursor = 'pointer';
        } else {
            row.style.cursor = '';
        }
    });
}

function hideModal(modal)
{
    modal.classList.remove('show');
    modal.setAttribute('aria-hidden', 'true');
    document.body.style.overflow = '';
}

function setupSelectModeObserver()
{
    // Observer to watch for select mode changes
    const tableContainer = document.querySelector('.modern-table, .table');
    if (!tableContainer) {
        return;
    }

    const observer = new MutationObserver(mutations => {
        mutations.forEach(mutation => {
            if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                updateRowCursorForSelectMode();
            }
        });
    });

    observer.observe(tableContainer, {
        attributes: true,
        attributeFilter: ['class']
    });
}

document.addEventListener('keydown', event => {
    if (event.key !== 'Escape') {
        return;
    }

    const visibleModal = document.querySelector('.modal.show');
    if (visibleModal) {
        hideModal(visibleModal);
    }
});
