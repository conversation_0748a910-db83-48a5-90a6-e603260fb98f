/**
 * Document Preview Module
 * Handles document preview functionality for expense receipts and attachments
 */
document.addEventListener('DOMContentLoaded', function () {
    // Document viewer functionality
    const documentViewers = document.querySelectorAll('.document-viewer');
    const documentTabs = document.querySelectorAll('[data-tab="document"]');

    if (documentViewers.length > 0) {
        console.log('Document viewer initialized');

        // Handle document zoom functionality
        const zoomControls = document.querySelectorAll('.zoom-control');
        zoomControls.forEach(control => {
            control.addEventListener('click', function () {
                const action = this.getAttribute('data-zoom');
                const viewer = this.closest('.document-viewer');
                const image = viewer.querySelector('.document-image');

                if (!image) {
                    return;
                }

                let currentZoom = parseFloat(image.getAttribute('data-zoom') || 1);

                if (action === 'in') {
                    currentZoom = Math.min(currentZoom + 0.25, 3);
                } else if (action === 'out') {
                    currentZoom = Math.max(currentZoom - 0.25, 0.5);
                } else if (action === 'reset') {
                    currentZoom = 1;
                }

                image.style.transform = `scale(${currentZoom})`;
                image.setAttribute('data-zoom', currentZoom);

                // Update zoom percentage display if it exists
                const zoomDisplay = viewer.querySelector('.zoom-percentage');
                if (zoomDisplay) {
                    zoomDisplay.textContent = `${Math.round(currentZoom * 100)} % `;
                }
            });
        });

        // Handle document rotation
        const rotateControls = document.querySelectorAll('.rotate-control');
        rotateControls.forEach(control => {
            control.addEventListener('click', function () {
                const viewer = this.closest('.document-viewer');
                const image = viewer.querySelector('.document-image');

                if (!image) {
                    return;
                }

                let currentRotation = parseInt(image.getAttribute('data-rotation') || 0);
                currentRotation = (currentRotation + 90) % 360;

                image.style.transform = `rotate(${currentRotation}deg)`;
                image.setAttribute('data-rotation', currentRotation);
            });
        });
    }

    // If document tab has indicator, make it active by default
    if (documentTabs.length > 0) {
        documentTabs.forEach(tab => {
            if (tab.querySelector('.indicator')) {
                // Only trigger if not already on the page because of URL parameter
                if (!window.location.search.includes('tab=document')) {
                    setTimeout(() => {
                        tab.click();
                    }, 100);
                }
            }
        });
    }
});