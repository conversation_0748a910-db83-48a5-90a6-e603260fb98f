/* ===== FOOTER STYLES ===== */
.main-footer {
  background-color: var(--light-color);
  width: 100vw;
  border-top: 1px solid var(--grey-300);
  padding: var(--spacing) var(--spacing-sm);
  margin-left: var(--sidebar-width);
  transition: var(--transition);
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: var(--container-max-width);
  margin: 0 auto;
}

.footer-info {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

.footer-info p{
  margin-bottom: 0;
}

.footer-links {
  display: flex;
  gap: var(--spacing-md);
}

.footer-links a {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

.footer-links a:hover {
  color: var(--primary-color);
}

@media (max-width: 992px) {
  .main-footer {
    margin-left: 0;
  }

  .footer-content {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing);
  }
}

/* ===== MODAL ===== */
.modal-container {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  justify-content: center;
  align-items: center;
  padding: var(--spacing);
}

.modal-container[aria-hidden="false"] {
  display: flex;
}

/* ===== TOAST NOTIFICATIONS ===== */
.toast-container {
  position: fixed;
  bottom: var(--spacing-lg);
  right: var(--spacing-lg);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: var(--spacing);
}