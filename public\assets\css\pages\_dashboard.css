/* ===== DASHBOARD PAGES STYLES ===== */

/* Dashboard Layout */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.dashboard-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.dashboard-filter {
  margin-bottom: var(--spacing-md);
  background-color: var(--grey-100);
  padding: var(--spacing);
  border-radius: var(--border-radius);
  border: 1px solid var(--grey-200);
}

.date-filter-form {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.dashboard-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.dashboard-column {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

/* Widget Styles */
.widget {
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  border: 1px solid var(--grey-200);
  overflow: hidden;
}

.widget-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing) var(--spacing-md);
  border-bottom: 1px solid var(--grey-200);
  background-color: var(--grey-100);
}

.widget-header h3 {
  margin: 0;
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-semibold);
  color: var(--dark-color);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.widget-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.widget-actions {
  display: flex;
  gap: var(--spacing-xs);
}

.toggle-widget {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  font-size: var(--font-size-md);
  line-height: 1;
  padding: var(--spacing-xs);
  border-radius: var(--border-radius-sm);
  transition: var(--transition);
}

.toggle-widget:hover {
  background-color: var(--grey-200);
  color: var(--dark-color);
}

.widget-content {
  padding: var(--spacing-md);
  transition: max-height 0.3s ease, padding 0.3s ease;
  overflow: hidden;
}

.widget-content.collapsed {
  max-height: 0;
  padding-top: 0;
  padding-bottom: 0;
  overflow: hidden;
}

.widget-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
  color: var(--text-muted);
}

.spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid rgba(var(--primary-hue), 70%, 60%, 0.2);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.widget-empty {
  padding: var(--spacing-lg);
  text-align: center;
  color: var(--text-muted);
  background-color: var(--grey-100);
  border-radius: var(--border-radius);
}

.widget-footer {
  padding: var(--spacing) var(--spacing-md);
  border-top: 1px solid var(--grey-200);
  display: flex;
  justify-content: flex-end;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing);
}

.stat-card {
  background-color: var(--grey-100);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  border: 1px solid var(--grey-200);
  transition: var(--transition);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

.stat-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-muted);
  margin-bottom: var(--spacing-xs);
}

.stat-value {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--dark-color);
  display: block;
  margin-bottom: var(--spacing-xs);
}

.stat-projected {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  margin-top: var(--spacing-xs);
}

.stat-label {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
}

/* Expense List */
.expense-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.expense-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing) 0;
  border-bottom: 1px solid var(--grey-200);
}

.expense-item:last-child {
  border-bottom: none;
}

.expense-info {
  display: flex;
  align-items: center;
  gap: var(--spacing);
}

.expense-date {
  background-color: var(--grey-200);
  color: var(--text-color);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  min-width: 3.5rem;
  text-align: center;
}

.expense-details {
  display: flex;
  flex-direction: column;
}

.expense-description {
  font-weight: var(--font-weight-medium);
  color: var(--dark-color);
}

.expense-category {
  font-size: var(--font-size-sm);
  color: var(--text-muted);
  margin-top: var(--spacing-xs);
}

.expense-amount {
  font-weight: var(--font-weight-semibold);
  color: var(--dark-color);
}

/* Chart Container */
.chart-container {
  width: 100%;
  height: 300px;
  position: relative;
}

/* Quick Actions */
.quick-actions {
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  border: 1px solid var(--grey-200);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.quick-actions h3 {
  margin-top: 0;
  margin-bottom: var(--spacing-md);
  font-size: var(--font-size-md);
  font-weight: var(--font-weight-semibold);
  color: var(--dark-color);
}

.action-buttons {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: var(--spacing);
}

.action-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-md) var(--spacing);
  background-color: var(--grey-100);
  border: 1px solid var(--grey-200);
  border-radius: var(--border-radius);
  text-decoration: none;
  color: var(--text-color);
  transition: var(--transition);
}

.action-button:hover {
  transform: translateY(-3px);
  box-shadow: var(--box-shadow);
  background-color: var(--grey-200);
  color: var(--dark-color);
}

.action-icon {
  font-size: 2rem;
  margin-bottom: var(--spacing-sm);
}

.action-text {
  font-weight: var(--font-weight-medium);
}

/* Responsive Styles */
@media (max-width: 992px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .dashboard-actions {
    margin-top: var(--spacing);
    width: 100%;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    grid-template-columns: repeat(2, 1fr);
  }
}
