/* ===== SIDEBAR STYLES ===== */
.sidebar {
  position: fixed;
  left: 0;
  top: var(--header-height);
  bottom: 0;
  width: var(--sidebar-width);
  background-color: var(--light-color);
  border-right: 1px solid var(--grey-300);
  box-shadow: 1px 0 3px rgba(0, 0, 0, 0.05);
  z-index: 99;
  transition: var(--transition);
  display: flex;
  flex-direction: column;
}

.sidebar-nav {
  flex: 1;
  padding: var(--spacing);
  overflow-y: auto;
}

.nav-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.nav-item {
  margin-bottom: var(--spacing-xs);
}

.nav-link {
  display: flex;
  align-items: center;
  padding: var(--spacing) var(--spacing-lg);
  border-radius: var(--border-radius);
  color: var(--text-color);
  font-weight: var(--font-weight-medium);
  transition: var(--transition-fast);
  margin-bottom: 2px;
}

.nav-link:hover {
  background-color: var(--grey-200);
  color: var(--primary-color);
}

.nav-link.active {
  background-color: rgba(var(--primary-hue), 59%, 52%, 0.12);
  color: var(--primary-color);
  font-weight: var(--font-weight-semibold);
}

.nav-link .text {
  margin-left: var(--spacing-sm);
}

.nav-separator {
  height: 1px;
  background: linear-gradient(to right, transparent, var(--grey-300), transparent);
  margin: var(--spacing) 0;
}

.sidebar-footer {
  padding: var(--spacing);
  border-top: 1px solid var(--grey-300);
}

.quick-add-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: var(--spacing) var(--spacing-md);
  background-color: var(--secondary-color);
  color: var(--light-color);
  border-radius: var(--border-radius);
  font-weight: var(--font-weight-semibold);
  transition: var(--transition-fast);
  box-shadow: var(--box-shadow-sm);
  transform: scale(1.02);
}

.quick-add-button:hover {
  background-color: hsl(154, 65%, 40%);
  color: var(--light-color);
  transform: scale(1.04) translateY(-2px);
  box-shadow: var(--box-shadow);
}

.quick-add-button:active {
  transform: translateY(0);
}

.quick-add-button .svg-icon {
  margin-right: var(--spacing-sm);
}

@media (max-width: 992px) {
  .sidebar {
    transform: translateX(-100%);
    box-shadow: var(--box-shadow-lg);
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .sidebar-toggle {
    display: block;
  }
}