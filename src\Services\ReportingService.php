<?php

declare(strict_types=1);

namespace App\Services;

use App\Exceptions\InvalidArgumentException;

class ReportingService
{
    private ExpenseService $expenseService;
    private CategoryService $categoryService;
    private MerchantService $merchantService;

    public function __construct(
        ExpenseService $expenseService,
        CategoryService $categoryService,
        MerchantService $merchantService
    ) {
        $this->expenseService = $expenseService;
        $this->categoryService = $categoryService;
        $this->merchantService = $merchantService;
    }

    public function getDashboardData(int $userId): array
    {
        if ($userId <= 0) {
            throw new InvalidArgumentException('Invalid user ID');
        }
        $monthlyTotal = $this->expenseService->getMonthlyTotal($userId);
        $currentMonth = (int)date('n');

        return [
            'recentExpenses' => $this->expenseService->getRecentExpenses($userId, 5),
            'monthlyTotal' => $monthlyTotal,
            'yearlyTotal' => $this->expenseService->getYearlyTotal($userId),
            'weeklyTotal' => $this->expenseService->getWeeklyTotal($userId),
            'expenseCount' => $this->expenseService->getExpenseCount($userId, 30),
            'categoryBreakdown' => $this->categoryService->getCategoryExpenseBreakdown($userId),
            'yearProjection' => $this->calculateYearProjection($monthlyTotal, $currentMonth)
        ];
    }

    public function getMonthlyReport(int $userId, string $period): array
    {
        if ($userId <= 0) {
            throw new InvalidArgumentException('Invalid user ID');
        }
        $parts = explode('-', $period);
        if (count($parts) !== 2 || !ctype_digit($parts[0]) || !ctype_digit($parts[1])) {
            throw new InvalidArgumentException('Invalid period format. Expected YYYY-MM');
        }
        return $this->generateMonthlyReport($userId, $parts[0], $parts[1]);
    }

    public function getCategoryReport(int $userId, int $categoryId): array
    {
        if ($userId <= 0 || $categoryId <= 0) {
            throw new InvalidArgumentException('Invalid user or category ID');
        }
        $categoryData = $this->categoryService->getCategoryWithExpenses($categoryId, $userId);
        if (!$categoryData) {
            throw new InvalidArgumentException('Category not found');
        }
        return [
            'category' => $categoryData,
            'expenses' => $categoryData['recent_expenses'] ?? []
        ];
    }

    public function getSummaryReport(int $userId): array
    {
        if ($userId <= 0) {
            throw new InvalidArgumentException('Invalid user ID');
        }
        return $this->generateSummaryReport($userId, date('Y'));
    }

    public function getCategoryWiseExpenses(int $userId): array
    {
        if ($userId <= 0) {
            throw new InvalidArgumentException('Invalid user ID');
        }
        return $this->categoryService->getCategoryExpenseBreakdown($userId);
    }

    public function getDetailedExportData(int $userId): array
    {
        if ($userId <= 0) {
            throw new InvalidArgumentException('Invalid user ID');
        }
        $expenses = $this->expenseService->getUserExpenses($userId);
        $categories = $this->categoryService->getAllCategories();
        $merchants = $this->merchantService->getUserMerchants($userId);
        $categoryMap = array_column($categories, 'name', 'id');
        $merchantMap = array_column($merchants, 'name', 'id');
        return $this->mapExpensesToExportFormat($expenses, $categoryMap, $merchantMap);
    }

    public function getSummaryExportData(int $userId): array
    {
        if ($userId <= 0) {
            throw new InvalidArgumentException('Invalid user ID');
        }
        $categoryData = $this->getCategoryWiseExpenses($userId);
        $monthlyData = $this->expenseService->getMonthlyTotalsForYear($userId, date('Y'));
        $exportData = [['Summary Type', 'Name', 'Total Amount', 'Count']];
        foreach ($categoryData as $category) {
            $exportData[] = [
                'Category', $category['name'], $category['total'], $category['count']
            ];
        }
        foreach ($monthlyData as $month) {
            $exportData[] = [
                'Month', $month['month_name'], $month['total'], $month['count'] ?? 0
            ];
        }
        return $exportData;
    }

    public function formatExportData(array $data, string $format): string
    {
        if (!in_array($format, ['csv', 'json'], true)) {
            throw new InvalidArgumentException('Invalid export format');
        }
        if (empty($data)) {
            return $format === 'csv' ? '' : '[]';
        }
        return $format === 'csv'
            ? $this->formatAsCsv($data)
            : json_encode($data, JSON_PRETTY_PRINT);
    }

    public function generateMonthlyReport(int $userId, string $year, string $month): array
    {
        $monthlyData = $this->expenseService->getMonthlyExpenses($userId, $year, $month);
        $categoryTotals = $this->expenseService->getCategoryTotals($userId, 'month', $year, $month);

        return [
            'monthlyData' => $monthlyData,
            'categoryTotals' => $categoryTotals,
            'selectedYear' => $year,
            'selectedMonth' => $month
        ];
    }

    public function generateSummaryReport(int $userId, string $year): array
    {
        $monthlyTotals = $this->expenseService->getMonthlyTotalsForYear($userId, $year);
        $categoryTotals = $this->expenseService->getCategoryTotals($userId, 'year', $year);
        $merchantTotals = $this->expenseService->getMerchantTotalsForYear($userId, $year);
        $total = array_sum(array_column($monthlyTotals, 'total'));

        return [
            'monthlyTotals' => $monthlyTotals,
            'categoryTotals' => $categoryTotals,
            'merchantTotals' => $merchantTotals,
            'year' => $year,
            'total' => $total
        ];
    }

    private function mapExpensesToExportFormat(array $expenses, array $categoryMap, array $merchantMap): array
    {
        $exportData = [];
        foreach ($expenses as $expense) {
            $categoryId = $expense['category_id'] ?? 0;
            $merchantId = $expense['merchant_id'] ?? 0;

            $exportData[] = [
                'Date' => $expense['date'],
                'Amount' => $expense['amount'],
                'Description' => $expense['description'],
                'Category' => $categoryMap[$categoryId] ?? 'Uncategorized',
                'Merchant' => $merchantMap[$merchantId] ?? '',
                'Notes' => $expense['notes'] ?? ''
            ];
        }

        return $exportData;
    }

    private function formatAsCsv(array $data): string
    {
        $output = fopen('php://temp', 'r+');
        fprintf($output, chr(0xEF) . chr(0xBB) . chr(0xBF)); // UTF-8 BOM for Excel

        fputcsv($output, array_keys(reset($data)));
        foreach ($data as $row) {
            fputcsv($output, $row);
        }

        rewind($output);
        $content = stream_get_contents($output);
        fclose($output);

        return $content;
    }

    private function calculateYearProjection(float $monthlyTotal, int $currentMonth): float
    {
        if ($currentMonth === 0 || $monthlyTotal === 0) {
            return 0;
        }

        return ($monthlyTotal / $currentMonth) * 12;
    }
}
