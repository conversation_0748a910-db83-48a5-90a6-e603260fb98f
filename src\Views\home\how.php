<div class="container how-to-guide">
    <header class="page-header text-center">
        <h1>How to Use Personal Expense Tracker</h1>
        <p class="text-muted mb-lg">Follow these simple steps to take control of your finances</p>
    </header>

    <!-- Progress Steps - Visual Journey -->
    <div class="progress-steps" data-active-step="0">
        <div class="progress-step active">
            <div class="progress-step-indicator">1</div>
            <div class="progress-step-label">Get Started</div>
        </div>
        <div class="progress-step">
            <div class="progress-step-indicator">2</div>
            <div class="progress-step-label">Track Expenses</div>
        </div>
        <div class="progress-step">
            <div class="progress-step-indicator">3</div>
            <div class="progress-step-label">Analyze Data</div>
        </div>
        <div class="progress-step">
            <div class="progress-step-indicator">4</div>
            <div class="progress-step-label">Advanced Features</div>
        </div>
    </div>

    <!-- Getting Started Section -->
    <section id="getting-started" class="guide-section card">
        <div class="card-header">
            <h2 class="card-title">Getting Started</h2>
        </div>
        <div class="card-body">
            <!-- Trust Indicator - Social Proof -->
            <div class="trust-indicator mb-md">
                <svg class="trust-indicator-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z" fill="currentColor"/>
                </svg>
                <div class="trust-indicator-text">
                    <span class="trust-indicator-count">5,000+</span> users have successfully set up their accounts
                </div>
            </div>

            <div class="step-container">
                <div class="step card interactive">
                    <div class="card-body">
                        <h3 class="text-primary">1. Create an Account</h3>
                        <p>Start by registering for a free account. You'll need to provide:</p>
                        <ul class="feature-list">
                            <li>Your name</li>
                            <li>Email address</li>
                            <li>Secure password</li>
                        </ul>

                        <!-- Achievement Badge - Recognition -->
                        <div class="achievement-badge" data-achievement-details="Creating an account unlocks all basic features of the Personal Expense Tracker.">
                            <svg class="achievement-badge-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z" fill="currentColor"/>
                            </svg>
                            Account Created
                        </div>
                    </div>
                </div>

                <div class="step card interactive">
                    <div class="card-body">
                        <h3 class="text-primary">2. Set Up Your Profile</h3>
                        <p>Customize your experience by:</p>
                        <ul class="feature-list">
                            <li>Setting your preferred currency (USD, EUR, GBP, JPY, CAD, AUD)</li>
                            <li>Choosing date format for consistent display</li>
                            <li>Setting monthly budget goals to track spending limits</li>
                            <li>Configuring notification preferences for alerts</li>
                        </ul>

                        <!-- Example - Real-world Context -->
                        <div class="example-box">
                            <h4>Example</h4>
                            <p>Sarah set her monthly budget to $2,000 and enabled budget alerts. Now she receives notifications when she reaches 80% of her budget.</p>
                        </div>

                        <!-- Nudge - Subtle Direction -->
                        <div class="nudge right">
                            Setting a budget goal increases your chances of financial success by 73%
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Managing Expenses Section -->
    <section id="managing-expenses" class="guide-section card">
        <div class="card-header">
            <h2 class="card-title">Managing Expenses</h2>
        </div>
        <div class="card-body">
            <!-- Progress Bar - Completion Motivation -->
            <div class="progress-bar mb-md">
                <div class="progress-bar-fill" data-percentage="50"></div>
            </div>

            <div class="step-container">
                <div class="step card interactive">
                    <div class="card-body">
                        <h3 class="text-primary">3. Adding Expenses</h3>
                        <p>Record your expenses easily:</p>
                        <ul class="feature-list">
                            <li>Click "Add New Expense" button from any page</li>
                            <li>Enter amount and description of your purchase</li>
                            <li>Select from existing categories or create a new one</li>
                            <li>Upload receipt images for digital record-keeping</li>
                            <li>Add payment method for better tracking</li>
                            <li>Include merchant information for detailed reports</li>
                            <li>Add notes for additional context about the expense</li>
                        </ul>

                        <!-- Troubleshooting Tips -->
                        <div class="troubleshooting-box">
                            <h4>Troubleshooting</h4>
                            <p><strong>Receipt upload failed?</strong> Make sure your image is under 5MB and in JPG, PNG, or PDF format.</p>
                        </div>

                        <!-- Achievement Badge - Recognition -->
                        <div class="achievement-badge silver" data-achievement-details="Adding your first expense is the first step toward financial awareness.">
                            <svg class="achievement-badge-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z" fill="currentColor"/>
                            </svg>
                            First Expense Tracked
                        </div>
                    </div>
                </div>

                <div class="step card interactive">
                    <div class="card-body">
                        <h3 class="text-primary">4. Organizing Categories</h3>
                        <p>Keep your expenses organized:</p>
                        <ul class="feature-list">
                            <li>Use system categories for standard expense types</li>
                            <li>Create custom categories for your unique needs</li>
                            <li>Assign expenses to appropriate categories</li>
                            <li>View spending breakdowns by category</li>
                            <li>Identify spending patterns in each category</li>
                            <li>Set category-specific budget limits</li>
                        </ul>

                        <!-- Example - Real-world Context -->
                        <div class="example-box">
                            <h4>Example</h4>
                            <p>John created custom categories for "Coffee" and "Dining Out" to track his discretionary spending separately from essential "Groceries".</p>
                        </div>

                        <!-- Urgency Indicator - Scarcity -->
                        <div class="urgency-indicator">
                            <svg class="urgency-indicator-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z" fill="currentColor"/>
                            </svg>
                            Pro Tip: Create categories that match your spending habits for better insights
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Tracking & Analysis Section -->
    <section id="tracking-analysis" class="guide-section card">
        <div class="card-header">
            <h2 class="card-title">Tracking & Analysis</h2>
        </div>
        <div class="card-body">
            <div class="step-container">
                <div class="step card interactive">
                    <div class="card-body">
                        <h3 class="text-primary">5. View Reports & Visualizations</h3>
                        <p>Analyze your spending patterns with powerful visual tools:</p>
                        <ul class="feature-list">
                            <li>View monthly expense summaries with bar charts</li>
                            <li>Analyze category breakdowns with pie charts</li>
                            <li>Track spending trends over time with line graphs</li>
                            <li>Monitor budget progress with visual indicators</li>
                            <li>Compare spending across different time periods</li>
                            <li>Identify your top spending categories at a glance</li>
                        </ul>

                        <!-- Example - Real-world Context -->
                        <div class="example-box">
                            <h4>Example</h4>
                            <p>Maria noticed from her category breakdown chart that she was spending 35% of her budget on dining out. This visual insight helped her adjust her habits and save $200 monthly.</p>
                        </div>

                        <!-- Achievement Badge - Recognition -->
                        <div class="achievement-badge gold" data-achievement-details="Reviewing your financial reports regularly leads to better financial decisions.">
                            <svg class="achievement-badge-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z" fill="currentColor"/>
                            </svg>
                            Financial Analyst
                        </div>
                    </div>
                </div>

                <div class="step card interactive">
                    <div class="card-body">
                        <h3 class="text-primary">6. Budget Management</h3>
                        <p>Take control of your spending with budgets:</p>
                        <ul class="feature-list">
                            <li>Set monthly overall budget limits</li>
                            <li>Create category-specific budget allocations</li>
                            <li>Track spending progress with visual indicators</li>
                            <li>Receive alerts when approaching budget limits</li>
                            <li>Analyze budget vs. actual spending patterns</li>
                            <li>Adjust budgets based on historical data</li>
                        </ul>

                        <!-- Example - Real-world Context -->
                        <div class="example-box">
                            <h4>Example</h4>
                            <p>David set category budgets of $400 for groceries and $200 for entertainment. The app alerted him when he reached 80% of his entertainment budget, helping him avoid overspending.</p>
                        </div>

                        <!-- Trust Indicator - Social Proof -->
                        <div class="trust-indicator">
                            <svg class="trust-indicator-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z" fill="currentColor"/>
                            </svg>
                            <div class="trust-indicator-text">
                                <span class="trust-indicator-count">78%</span> of users report better financial control after setting up budgets
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Document Management Section -->
    <section id="document-management" class="guide-section card">
        <div class="card-header">
            <h2 class="card-title">Document Management</h2>
        </div>
        <div class="card-body">
            <div class="step-container">
                <div class="step card interactive">
                    <div class="card-body">
                        <h3 class="text-primary">7. Receipt & Document Management</h3>
                        <p>Keep your financial documents organized:</p>
                        <ul class="feature-list">
                            <li>Upload digital receipts in JPG, PNG, or PDF formats</li>
                            <li>Auto-extract receipt details with smart recognition</li>
                            <li>Access receipts by category, date, or merchant</li>
                            <li>Search through receipt text for specific items</li>
                            <li>Attach multiple documents to a single expense</li>
                            <li>View receipt thumbnails in expense listings</li>
                        </ul>

                        <!-- Troubleshooting Tips -->
                        <div class="troubleshooting-box">
                            <h4>Troubleshooting</h4>
                            <p><strong>Text extraction not working?</strong> Make sure your receipt image is clear and well-lit for better recognition.</p>
                        </div>

                        <!-- Trust Indicator - Social Proof -->
                        <div class="trust-indicator">
                            <svg class="trust-indicator-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z" fill="currentColor"/>
                            </svg>
                            <div class="trust-indicator-text">
                                Users save an average of <span class="trust-indicator-count">3 hours</span> per month with our receipt management
                            </div>
                        </div>
                    </div>
                </div>

                <div class="step card interactive">
                    <div class="card-body">
                        <h3 class="text-primary">8. Data Export & Sharing</h3>
                        <p>Access and share your financial data:</p>
                        <ul class="feature-list">
                            <li>Export expense data in CSV format for spreadsheets</li>
                            <li>Generate PDF reports for printing or sharing</li>
                            <li>Export data in JSON format for other applications</li>
                            <li>Schedule automatic monthly report generation</li>
                            <li>Email reports to yourself or your accountant</li>
                            <li>Back up your expense data securely</li>
                        </ul>

                        <!-- Example - Real-world Context -->
                        <div class="example-box">
                            <h4>Example</h4>
                            <p>Lisa exports her expense data to CSV at tax time and shares the PDF summary with her accountant, making tax preparation much faster and more accurate.</p>
                        </div>

                        <!-- Achievement Badge - Recognition -->
                        <div class="achievement-badge bronze" data-achievement-details="Exporting and sharing your data helps you leverage your financial information across different platforms.">
                            <svg class="achievement-badge-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm-1-13h2v6h-2zm0 8h2v2h-2z" fill="currentColor"/>
                            </svg>
                            Data Master
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Advanced Features Section -->
    <section id="advanced-features" class="guide-section card">
        <div class="card-header">
            <h2 class="card-title">Advanced Features</h2>
        </div>
        <div class="card-body">
            <div class="step-container">
                <div class="step card interactive">
                    <div class="card-body">
                        <h3 class="text-primary">9. Multi-Currency Support</h3>
                        <p>Track expenses in different currencies:</p>
                        <ul class="feature-list">
                            <li>Set your preferred base currency</li>
                            <li>Record expenses in foreign currencies</li>
                            <li>Automatic conversion to your base currency</li>
                            <li>View reports in your preferred currency</li>
                            <li>Track exchange rate fluctuations</li>
                        </ul>

                        <!-- Example - Real-world Context -->
                        <div class="example-box">
                            <h4>Example</h4>
                            <p>Alex traveled to Europe and recorded expenses in Euros. The app automatically converted them to USD in his reports while maintaining the original currency information.</p>
                        </div>
                    </div>
                </div>

                <div class="step card interactive">
                    <div class="card-body">
                        <h3 class="text-primary">10. Notification System</h3>
                        <p>Stay informed about your finances:</p>
                        <ul class="feature-list">
                            <li>Receive weekly spending summaries</li>
                            <li>Get alerts when approaching budget limits</li>
                            <li>Set up recurring expense reminders</li>
                            <li>Customize notification preferences</li>
                            <li>Receive unusual spending pattern alerts</li>
                        </ul>

                        <!-- Troubleshooting Tips -->
                        <div class="troubleshooting-box">
                            <h4>Troubleshooting</h4>
                            <p><strong>Not receiving notifications?</strong> Check your notification preferences in your profile settings and ensure your email address is correct.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section - Behavioral Triggers & Persuasion -->
    <div class="cta-section card">
        <div class="card-body text-center">
            <h2>Ready to Take Control of Your Finances?</h2>
            <p class="mb-md">Join thousands of users who have transformed their financial habits with Personal Expense Tracker.</p>

            <!-- Progress Bar - Completion Motivation -->
            <div class="progress-bar mb-md">
                <div class="progress-bar-fill success" data-percentage="100"></div>
            </div>

            <div class="cta-buttons">
                <a href="/register" class="button primary-cta">Create Free Account</a>
                <a href="/login" class="button secondary">Already Have an Account? Login</a>
            </div>

            <!-- Urgency Indicator - Scarcity & FOMO -->
            <div class="urgency-indicator mt-md">
                <svg class="urgency-indicator-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M11.99 2C6.47 2 2 6.48 2 12s4.47 10 9.99 10C17.52 22 22 17.52 22 12S17.52 2 11.99 2zM12 20c-4.42 0-8-3.58-8-8s3.58-8 8-8 8 3.58 8 8-3.58 8-8 8zm.5-13H11v6l5.25 3.15.75-1.23-4.5-2.67z" fill="currentColor"/>
                </svg>
                Start tracking today and don't miss another expense
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize progress steps
    const progressSteps = document.querySelector('.progress-steps');
    const steps = progressSteps.querySelectorAll('.progress-step');

    // Make steps clickable to navigate sections
    steps.forEach((step, index) => {
        step.addEventListener('click', () => {
            // Update active step
            document.querySelectorAll('.progress-step').forEach((s, i) => {
                if (i <= index) {
                    s.classList.add(i === index ? 'active' : 'completed');
                    s.classList.remove(i === index ? 'completed' : 'active');
                } else {
                    s.classList.remove('active', 'completed');
                }
            });

            // Update progress steps data attribute
            progressSteps.setAttribute('data-active-step', index);

            // Scroll to corresponding section
            const sections = ['getting-started', 'managing-expenses', 'tracking-analysis', 'document-management', 'advanced-features'];
            const targetSection = document.getElementById(sections[index]);
            if (targetSection) {
                window.scrollTo({
                    top: targetSection.offsetTop - 80,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Add hover effects to steps
    document.querySelectorAll('.step.card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.boxShadow = 'var(--box-shadow-lg)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = '';
            this.style.boxShadow = '';
        });
    });

    // Update progress steps based on scroll position
    window.addEventListener('scroll', function() {
        const sections = [
            document.getElementById('getting-started'),
            document.getElementById('managing-expenses'),
            document.getElementById('tracking-analysis'),
            document.getElementById('document-management'),
            document.getElementById('advanced-features')
        ];

        const scrollPosition = window.scrollY + 200; // Offset for better detection

        sections.forEach((section, index) => {
            if (!section) return;

            const sectionTop = section.offsetTop;
            const sectionBottom = sectionTop + section.offsetHeight;

            if (scrollPosition >= sectionTop && scrollPosition < sectionBottom) {
                progressSteps.setAttribute('data-active-step', index);

                steps.forEach((step, i) => {
                    if (i < index) {
                        step.classList.add('completed');
                        step.classList.remove('active');
                    } else if (i === index) {
                        step.classList.add('active');
                        step.classList.remove('completed');
                    } else {
                        step.classList.remove('active', 'completed');
                    }
                });
            }
        });
    });
});
</script>

<link rel="stylesheet" href="/assets/css/main.css?v=<?= time() ?>">
<link rel="stylesheet" href="/assets/css/icons.css?v=<?= time() ?>">
<style>
body{
    display:block;
}
.how-to-guide {
    max-width: 900px;
    margin: 0 auto;
    padding: var(--spacing-lg) var(--spacing);
}

.guide-section {
    margin-bottom: var(--spacing-lg);
}

.step-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-md);
}

.feature-list {
    list-style: none;
    padding: 0;
    margin: var(--spacing) 0;
}

.feature-list li {
    position: relative;
    padding: var(--spacing-xs) 0 var(--spacing-xs) var(--spacing-lg);
    margin-bottom: var(--spacing-xs);
}

.feature-list li::before {
    content: "";
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 20px;
    height: 20px;
    background-color: var(--primary-light);
    mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath d='M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z'/%3E%3C/svg%3E");
    mask-size: contain;
    mask-repeat: no-repeat;
    mask-position: center;
    -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath d='M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z'/%3E%3C/svg%3E");
    -webkit-mask-size: contain;
    -webkit-mask-repeat: no-repeat;
    -webkit-mask-position: center;
}

.example-box {
    background-color: var(--grey-100);
    border-left: 3px solid var(--accent-calm);
    padding: var(--spacing-sm) var(--spacing);
    margin: var(--spacing) 0;
    border-radius: var(--border-radius-sm);
}

.example-box h4 {
    color: var(--accent-calm);
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-sm);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.troubleshooting-box {
    background-color: var(--grey-100);
    border-left: 3px solid var(--warning-color);
    padding: var(--spacing-sm) var(--spacing);
    margin: var(--spacing) 0;
    border-radius: var(--border-radius-sm);
}

.troubleshooting-box h4 {
    color: var(--warning-color);
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-sm);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.cta-section {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
    padding: var(--spacing-lg);
    border-radius: var(--border-radius);
    margin-top: var(--spacing-xl);
}

.cta-section h2,
.cta-section p {
    color: white;
}

.cta-section .card-body {
    background: transparent;
}

.cta-buttons {
    display: flex;
    gap: var(--spacing);
    justify-content: center;
    margin: var(--spacing-lg) 0;
}

.cta-section .urgency-indicator {
    background-color: rgba(255, 255, 255, 0.15);
    border-left-color: white;
    color: white;
    max-width: 500px;
    margin: 0 auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .step-container {
        grid-template-columns: 1fr;
    }

    .cta-buttons {
        flex-direction: column;
    }

    .how-to-guide {
        padding: var(--spacing);
    }

    .feature-list li {
        padding-left: var(--spacing);
    }

    .feature-list li::before {
        width: 16px;
        height: 16px;
    }

    .example-box,
    .troubleshooting-box {
        padding: var(--spacing-xs) var(--spacing-sm);
    }
}
</style>
