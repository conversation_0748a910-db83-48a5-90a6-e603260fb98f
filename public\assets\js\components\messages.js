function initializeMessages()
{
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(setupAlert);
}

function setupAlert(alert)
{
    if (needsDismissButton(alert)) {
        addDismissButton(alert);
    }

    if (shouldAutoDismiss(alert)) {
        setAutoDismissTimer(alert);
    }
}

function needsDismissButton(alert)
{
    return !alert.querySelector('.alert-dismiss') && !alert.classList.contains('alert-persistent');
}

function shouldAutoDismiss(alert)
{
    return (alert.classList.contains('alert-success') || alert.classList.contains('alert-info')) &&
           !alert.classList.contains('alert-persistent');
}

function setAutoDismissTimer(alert)
{
    setTimeout(() => dismissAlert(alert), 5000);
}

function addDismissButton(alert)
{
    const dismissButton = createDismissButton();
    setupDismissButtonBehavior(dismissButton, alert);
    appendDismissButton(dismissButton, alert);
}

function createDismissButton()
{
    const dismissButton = document.createElement('button');
    dismissButton.className = 'alert-dismiss';
    dismissButton.setAttribute('type', 'button');
    dismissButton.setAttribute('aria-label', 'Close');
    dismissButton.innerHTML = '&times;';
    return dismissButton;
}

function setupDismissButtonBehavior(button, alert)
{
    button.addEventListener('click', () => dismissAlert(alert));
}

function appendDismissButton(button, alert)
{
    alert.classList.add('alert-dismissible');
    alert.appendChild(button);
}

function dismissAlert(alert)
{
    alert.classList.add('alert-dismissing');
    setTimeout(() => removeAlert(alert), 300);
}

function removeAlert(alert)
{
    if (alert.parentNode) {
        alert.parentNode.removeChild(alert);
    }
}

function showAlert(message, type = 'info', persistent = false, container = null)
{
    const alert = createAlertElement(message, type, persistent);
    addDismissButton(alert);
    addAlertToContainer(alert, container);

    if (isAutoClosingAlert(type, persistent)) {
        setAutoDismissTimer(alert);
    }

    return alert;
}

function createAlertElement(message, type, persistent)
{
    const alert = document.createElement('div');
    alert.className = `alert alert - ${type}`;

    if (persistent) {
        alert.classList.add('alert-persistent');
    }

    const messageElement = document.createElement('p');
    messageElement.textContent = message;
    alert.appendChild(messageElement);

    return alert;
}

function addAlertToContainer(alert, container)
{
    const targetContainer = container || document.body;

    if (targetContainer.firstChild) {
        targetContainer.insertBefore(alert, targetContainer.firstChild);
    } else {
        targetContainer.appendChild(alert);
    }
}

function isAutoClosingAlert(type, persistent)
{
    return (type === 'success' || type === 'info') && !persistent;
}

export { initializeMessages, showAlert, dismissAlert };
