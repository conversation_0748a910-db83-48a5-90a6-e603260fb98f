<?php

/**
 * Standardized form field component for expenses
 *
 * Parameters:
 * Can be passed as individual variables or as a single $field array:
 * @param string $type Input type (text, email, password, etc.)
 * @param string $name Field name
 * @param string $label Field label
 * @param string $value Field value
 * @param bool $required Whether field is required
 * @param string $id Field ID (defaults to name)
 * @param string $placeholder Field placeholder
 * @param string $helpText Help text to display below field
 * @param string $errorMessage Error message to display
 * @param array $options Options for select fields
 * @param string $class Additional CSS classes
 * @param array $attributes Additional HTML attributes
 */

// Check if field data is passed as an array
if (isset($field) && is_array($field)) {
// Extract variables from the field array
    extract($field);
}

// Set defaults
$type = $type ?? 'text';
$name = $name ?? '';
$label = $label ?? '';
$id = $id ?? $name;
$value = $value ?? '';
$required = $required ?? false;
$placeholder = $placeholder ?? '';
$helpText = $helpText ?? '';
$errorMessage = $errorMessage ?? '';
$options = $options ?? [];
$class = $class ?? '';
$attributes = $attributes ?? [];
// Determine field state
$hasError = !empty($errorMessage);
$fieldClass = 'form-field' . ($hasError ? ' has-error' : '') . ($class ? ' ' . $class : '');
// Set field attributes
$fieldAttributes = array_merge([
    'id' => $id,
    'name' => $name,
    'placeholder' => $placeholder,
    'aria-describedby' => $helpText ? "{$id}-help" : null,
    'aria-invalid' => $hasError ? 'true' : null,
    'required' => $required ? true : null
], $attributes);
// Filter out null attributes
$fieldAttributes = array_filter($fieldAttributes, function ($value) {

    return $value !== null;
});
// Build attributes string
$attributesStr = '';
foreach ($fieldAttributes as $key => $value) {
    if ($value === true) {
        $attributesStr .= ' ' . htmlspecialchars($key);
    } else {
        $attributesStr .= ' ' . htmlspecialchars($key) . '="' . htmlspecialchars($value) . '"';
    }
}
?>

<div class="<?= $fieldClass ?>">
    <label for="<?= htmlspecialchars($id) ?>">
        <?= htmlspecialchars($label) ?>
        <?php if ($required) :
            ?>
            <span class="required">*</span>
            <?php
        endif; ?>
    </label>

    <?php if ($type === 'textarea') :
        ?>
        <textarea<?= $attributesStr ?>><?= htmlspecialchars($value) ?></textarea>
        <?php
    elseif ($type === 'select') :
        ?>
        <select<?= $attributesStr ?>>
            <?php if (!empty($placeholder)) :
                ?>
                <option value=""><?= htmlspecialchars($placeholder) ?></option>
                <?php
            endif; ?>
            
            <?php
            // Handle both array of objects and associative array formats
            if (is_array($options)) {
                if (is_callable($options)) {
                    $options = $options();
                }

                foreach ($options as $optionValue => $optionLabel) {
// Handle case where options is a numeric array of objects
                    if (is_array($optionLabel) && isset($optionLabel['id']) && isset($optionLabel['name'])) {
                        $optionValue = $optionLabel['id'];
                        $optionLabel = $optionLabel['name'];
                    }

                    $selected = (string)$value === (string)$optionValue ? ' selected' : '';
                    echo '<option value="' . htmlspecialchars($optionValue) . '"' . $selected . '>';
                    echo htmlspecialchars($optionLabel);
                    echo '</option>';
                }
            }
            ?>
        </select>
        <?php
    elseif ($type === 'checkbox') :
        ?>
        <div class="checkbox-wrapper">
            <input type="checkbox" <?= $attributesStr ?> value="1" <?= $value ? 'checked' : '' ?>>
            <span class="checkbox-label"><?= htmlspecialchars($label) ?></span>
        </div>
        <?php
    elseif ($type === 'radio') :
        ?>
        <div class="radio-group">
            <?php foreach ($options as $optionValue => $optionLabel) :
                ?>
                <div class="radio-wrapper">
                    <input
                        type="radio"
                        name="<?= htmlspecialchars($name) ?>"
                        id="<?= htmlspecialchars($id . '_' . $optionValue) ?>"
                        value="<?= htmlspecialchars($optionValue) ?>"
                        <?= $optionValue == $value ? 'checked' : '' ?>
                    >
                    <label for="<?= htmlspecialchars($id . '_' . $optionValue) ?>">
                        <?= htmlspecialchars($optionLabel) ?>
                    </label>
                </div>
                <?php
            endforeach; ?>
        </div>
        <?php
    elseif ($type === 'file') :
        ?>
        <div class="file-upload">
            <input type="file" <?= $attributesStr ?>>
            <div class="file-upload-info">
                <span class="file-name">No file selected</span>
                <button type="button" class="button secondary">Browse</button>
            </div>
        </div>
        <?php
    elseif ($type === 'date') :
        ?>
        <input type="date" <?= $attributesStr ?> value="<?= htmlspecialchars($value) ?>">
        <?php
    else :
        ?>
        <input type="<?= htmlspecialchars($type) ?>" <?= $attributesStr ?> value="<?= htmlspecialchars($value) ?>">
        <?php
    endif; ?>

    <?php if ($helpText) :
        ?>
        <div class="help-text" id="<?= htmlspecialchars($id) ?>-help">
            <?= htmlspecialchars($helpText) ?>
        </div>
        <?php
    endif; ?>

    <?php if ($hasError) :
        ?>
        <div class="error-message">
            <?= htmlspecialchars($errorMessage) ?>
        </div>
        <?php
    endif; ?>
</div>
