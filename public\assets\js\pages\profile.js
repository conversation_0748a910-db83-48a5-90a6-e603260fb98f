document.addEventListener('DOMContentLoaded', initializeProfilePage);

function initializeProfilePage()
{
    setupProfileImagePreview();
    setupNotificationToggles();
    enhanceProfileForm();
}

function setupProfileImagePreview()
{
    const imageInput = document.getElementById('profile_image');
    if (!imageInput) {
        return;
    }

    imageInput.addEventListener('change', () => {
        if (!imageInput.files || !imageInput.files[0]) {
            return;
        }

        const file = imageInput.files[0];
        if (!validateImageFile(file)) {
            imageInput.value = '';
            return;
        }

        createImagePreview(file, imageInput);
    });
}

function createImagePreview(file, imageInput)
{
    const reader = new FileReader();

    reader.onload = e => {
        let previewContainer = document.querySelector('.image-preview');

        if (!previewContainer) {
            previewContainer = document.createElement('div');
            previewContainer.className = 'image-preview';
            imageInput.parentNode.insertBefore(previewContainer, imageInput.nextSibling);
        }

        previewContainer.innerHTML = `
            < div class = "preview-container" >
                < img src = "${e.target.result}" alt = "Profile image preview" class = "profile-preview" >
                < p class = "preview-info" > Preview of your new profile image < / p >
            <  / div >
        `;
    };

    reader.readAsDataURL(file);
}

function validateImageFile(file)
{
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
    const maxSizeMB = 2;
    const fileSizeMB = file.size / 1024 / 1024;

    if (!allowedTypes.includes(file.type)) {
        showAlert('Please select a valid image file (JPG, PNG, or GIF).', 'error');
        return false;
    }

    if (fileSizeMB > maxSizeMB) {
        showAlert(`Image is too large. Maximum size is ${maxSizeMB}MB.`, 'error');
        return false;
    }

    return true;
}

function setupNotificationToggles()
{
    const notificationCheckboxes = document.querySelectorAll('input[name^="notify_"]');

    notificationCheckboxes.forEach(checkbox => {
        const label = checkbox.closest('label');
        if (!label) {
            return;
        }

        enhanceNotificationToggle(checkbox, label);
    });
}

function enhanceNotificationToggle(checkbox, label)
{
    label.classList.add('notification-toggle');

    const statusIndicator = document.createElement('span');
    statusIndicator.className = 'toggle-status';
    statusIndicator.textContent = checkbox.checked ? 'Enabled' : 'Disabled';
    label.appendChild(statusIndicator);

    checkbox.addEventListener('change', () => {
        statusIndicator.textContent = checkbox.checked ? 'Enabled' : 'Disabled';
        statusIndicator.className = `toggle - status ${checkbox.checked ? 'enabled' : 'disabled'}`;
    });
}

function enhanceProfileForm()
{
    const profileForm = document.querySelector('.form-container form');
    if (!profileForm) {
        return;
    }

    setupFormValidation(profileForm);
    setupImageRemoval();
}

function setupFormValidation(form)
{
    form.addEventListener('submit', e => {
        if (!validateNameAndEmail(e)) {
            return;
        }
        showLoadingState(form);
    });
}

function validateNameAndEmail(e)
{
    const nameInput = document.getElementById('name');
    const emailInput = document.getElementById('email');

    if (nameInput && !nameInput.value.trim()) {
        e.preventDefault();
        showAlert('Please enter your name.', 'error');
        nameInput.focus();
        return false;
    }

    if (emailInput) {
        const email = emailInput.value.trim();
        if (!email) {
            e.preventDefault();
            showAlert('Please enter your email address.', 'error');
            emailInput.focus();
            return false;
        }

        if (!isValidEmail(email)) {
            e.preventDefault();
            showAlert('Please enter a valid email address.', 'error');
            emailInput.focus();
            return false;
        }
    }

    return true;
}

function showLoadingState(form)
{
    const submitButton = form.querySelector('button[type="submit"]');
    if (submitButton) {
        submitButton.disabled = true;
        submitButton.innerHTML = '<span class="spinner"></span> Saving...';
    }
}

function setupImageRemoval()
{
    const removeImageCheckbox = document.getElementById('remove_image');
    const profileImageInput = document.getElementById('profile_image');

    if (!removeImageCheckbox || !profileImageInput) {
        return;
    }

    removeImageCheckbox.addEventListener('change', () => {
        profileImageInput.disabled = removeImageCheckbox.checked;
        profileImageInput.classList.toggle('disabled', removeImageCheckbox.checked);
    });
}

function isValidEmail(email)
{
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function showAlert(message, type = 'info')
{
    const alertContainer = getOrCreateAlertContainer();
    const alert = createAlertElement(message, type);

    alertContainer.appendChild(alert);
    setupAlertDismissal(alert);
}

function getOrCreateAlertContainer()
{
    let alertContainer = document.querySelector('.alert-container');

    if (!alertContainer) {
        alertContainer = document.createElement('div');
        alertContainer.className = 'alert-container';

        const formHeader = document.querySelector('h2');
        if (formHeader ? .nextElementSibling) {
            formHeader.parentNode.insertBefore(alertContainer, formHeader.nextElementSibling);
        } else {
            document.querySelector('.form-container').prepend(alertContainer);
        }
    }

    return alertContainer;
}

function createAlertElement(message, type)
{
    const alert = document.createElement('div');
    alert.className = `alert alert - ${type}`;
    alert.innerHTML = `
        < p > ${message} < / p >
        < button type = "button" class = "close-alert" aria - label = "Close alert" > × < / button >
    `;

    return alert;
}

function setupAlertDismissal(alert)
{
    alert.querySelector('.close-alert').addEventListener('click', () => {
        alert.remove();
    });

    setTimeout(() => {
        if (alert.parentNode) {
            alert.remove();
        }
    }, 5000);
}
