<?php

declare(strict_types=1);

namespace App\Config;

use Dotenv\Dotenv;
use App\Core\Container;
use App\Core\Error\Handler;
use App\Core\Logger;
use App\Core\LogManager;
use App\Core\Security\CSRF;
use App\Core\Security\Session;
use Psr\Log\LoggerInterface;

// Initialize application
require_once __DIR__ . '/../vendor/autoload.php';
loadEnvironmentVariables();
configureErrorReporting();
defineConstants();

// Security configuration
configureSecurityHeaders();

// Start session with security settings
// Force cookie_lifetime=0 to ensure it expires when browser is closed
// This is critical for the "remember me" functionality to work properly
ini_set('session.cookie_lifetime', '0');
ini_set('session.use_cookies', '1');
ini_set('session.use_only_cookies', '1');
Session::start();

// Setup core components
$logger = new Logger();
LogManager::setLogger($logger);
registerErrorHandlers();

// Initialize container and register services
$container = new Container();
$container->register(LoggerInterface::class, $logger);

// Initialize security and database
$_SESSION['csrf_token'] = CSRF::generateToken();
require_once __DIR__ . '/database.php';

// Clean up expired remember tokens (with a small probability to avoid doing it on every request)
if (mt_rand(1, 100) <= 5) { // 5% chance
    try {
        \App\Models\User::deleteExpiredRememberTokens();
    } catch (\Exception $e) {
        // Silently fail, this is just maintenance
    }
}

// Helper functions
function configureSecurityHeaders(): void
{
    header('X-Content-Type-Options: nosniff');
    header('X-Frame-Options: DENY');
    header('X-XSS-Protection: 1; mode=block');
    header('Referrer-Policy: strict-origin-when-cross-origin');
    $csp = "default-src 'self'; ";
    $csp .= "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; ";
    $csp .= "font-src 'self' data: https://fonts.gstatic.com; ";
    $csp .= "script-src 'self' 'unsafe-inline'; ";
    $csp .= "img-src 'self' data:;";
    header("Content-Security-Policy: {$csp}");
}

// Session security is now handled by the Session class

function loadEnvironmentVariables(): void
{
    $dotenv = Dotenv::createImmutable(__DIR__ . '/..');
    $dotenv->load();
}

function configureErrorReporting(): void
{
    $isDebug = filter_var($_ENV['APP_DEBUG'] ?? false, FILTER_VALIDATE_BOOLEAN);
    error_reporting($isDebug ? E_ALL : 0);
    ini_set('display_errors', $isDebug ? '1' : '0');
}

function defineConstants(): void
{
    define('DB_CONFIG', [
        'host' => $_ENV['DB_HOST'] ?? 'localhost',
        'name' => $_ENV['DB_NAME'] ?? '',
        'user' => $_ENV['DB_USER'] ?? '',
        'password' => $_ENV['DB_PASSWORD'] ?? ''
    ]);
}

function registerErrorHandlers(): void
{
    // Register the error and exception handlers
    set_error_handler([Handler::class, 'handleError']);
    set_exception_handler([Handler::class, 'handleException']);
}
