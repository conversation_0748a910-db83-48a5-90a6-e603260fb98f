<?php

declare(strict_types=1);

namespace App\Core;

use App\Exceptions\InvalidArgumentException;

class Container
{
    private array $bindings = [];
    private array $instances = [];
    private array $resolvedClasses = [];

    public function register(string $abstract, object|callable $concrete): self
    {
        $this->bindings[$abstract] = $concrete;
        return $this;
    }

    public function get(string $abstract)
    {
        if (isset($this->instances[$abstract])) {
            return $this->instances[$abstract];
        }

        if (!isset($this->bindings[$abstract])) {
            return $this->resolveClass($abstract);
        }

        return $this->resolveBinding($abstract);
    }

    public function has(string $abstract): bool
    {
        return isset($this->bindings[$abstract]) || isset($this->instances[$abstract]);
    }

    public function singleton(string $abstract, callable $factory): self
    {
        return $this->register($abstract, function ($container) use ($factory) {
            static $instance;
            $instance ??= $factory($container);
            return $instance;
        });
    }

    public function extend(string $abstract, callable $extender): self
    {
        if (!$this->has($abstract)) {
            throw new InvalidArgumentException("Cannot extend non-existent binding {$abstract}");
        }

        $original = $this->get($abstract);
        $this->register($abstract, fn() => $extender($original, $this));
        unset($this->instances[$abstract]);

        return $this;
    }

    private function resolveBinding(string $abstract)
    {
        $concrete = $this->bindings[$abstract];

        if (is_object($concrete) && !($concrete instanceof \Closure)) {
            $this->instances[$abstract] = $concrete;
            return $concrete;
        }

        if (is_callable($concrete)) {
            $instance = $concrete($this);
            $this->instances[$abstract] = $instance;
            return $instance;
        }

        throw new InvalidArgumentException("Invalid binding for {$abstract}");
    }

    private function resolveClass(string $class)
    {
        if (isset($this->resolvedClasses[$class])) {
            return $this->resolvedClasses[$class];
        }

        if (!class_exists($class)) {
            throw new InvalidArgumentException("No binding registered for {$class}");
        }

        $reflector = new \ReflectionClass($class);
        if (!$reflector->isInstantiable()) {
            throw new InvalidArgumentException("Class {$class} is not instantiable");
        }

        $constructor = $reflector->getConstructor();
        if (!$constructor) {
            return $this->registerInstance($class, new $class());
        }

        $dependencies = $this->resolveDependencies($constructor->getParameters(), $class);
        $instance = $reflector->newInstanceArgs($dependencies);
        $this->resolvedClasses[$class] = $instance;

        return $instance;
    }

    private function registerInstance(string $class, $instance)
    {
        $this->instances[$class] = $instance;
        return $instance;
    }

    private function resolveDependencies(array $parameters, string $class): array
    {
        $dependencies = [];
        foreach ($parameters as $parameter) {
            $dependencies[] = $this->resolveDependency($parameter, $class);
        }
        return $dependencies;
    }

    private function resolveDependency(\ReflectionParameter $parameter, string $class)
    {
        $type = $parameter->getType();
        if ($type && !$type->isBuiltin()) {
            try {
                return $this->get($type->getName());
            } catch (InvalidArgumentException) {
                return $this->resolveParameterWithFallbacks($parameter, $class, $type);
            }
        }

        if ($parameter->isDefaultValueAvailable()) {
            return $parameter->getDefaultValue();
        }

        throw new InvalidArgumentException("Cannot resolve parameter {$parameter->getName()} for {$class}");
    }

    private function resolveParameterWithFallbacks(
        \ReflectionParameter $parameter,
        string $class,
        \ReflectionType $type
    ) {
        if ($parameter->isDefaultValueAvailable()) {
            return $parameter->getDefaultValue();
        }

        if ($type instanceof \ReflectionNamedType && $type->allowsNull()) {
            return null;
        }

        $typeName = $type instanceof \ReflectionNamedType ? $type->getName() : 'unknown';
        throw new InvalidArgumentException(
            "Unable to resolve dependency {$typeName} for {$class}::{$parameter->getName()}"
        );
    }
}
